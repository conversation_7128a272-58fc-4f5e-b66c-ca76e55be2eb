package com.boyo.master.mapper;

import com.boyo.master.domain.ScreenWorkshop;

import java.util.List;

/**
 * 大屏车间Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
public interface ScreenWorkshopMapper
{
    /**
     * 查询大屏车间
     *
     * @param workshopId 大屏车间主键
     * @return 大屏车间
     */
    public ScreenWorkshop selectScreenWorkshopByWorkshopId(Long workshopId);

    /**
     * 查询大屏车间列表
     *
     * @param screenWorkshop 大屏车间
     * @return 大屏车间集合
     */
    public List<ScreenWorkshop> selectScreenWorkshopList(ScreenWorkshop screenWorkshop);

    /**
     * 新增大屏车间
     *
     * @param screenWorkshop 大屏车间
     * @return 结果
     */
    public int insertScreenWorkshop(ScreenWorkshop screenWorkshop);

    /**
     * 修改大屏车间
     *
     * @param screenWorkshop 大屏车间
     * @return 结果
     */
    public int updateScreenWorkshop(ScreenWorkshop screenWorkshop);

    /**
     * 删除大屏车间
     *
     * @param workshopId 大屏车间主键
     * @return 结果
     */
    public int deleteScreenWorkshopByWorkshopId(Long workshopId);

    /**
     * 批量删除大屏车间
     *
     * @param workshopIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScreenWorkshopByWorkshopIds(Long[] workshopIds);
}