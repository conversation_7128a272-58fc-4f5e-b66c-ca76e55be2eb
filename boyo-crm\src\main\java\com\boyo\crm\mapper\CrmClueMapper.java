package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmClue;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * CRM线索主表(CrmClue)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 10:48:28
 */
@Tenant
public interface CrmClueMapper extends BaseMapper<CrmClue>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmClue 实例对象
     * @return 对象列表
     */
    List<CrmClue> selectCrmClueList(CrmClue crmClue);


}

