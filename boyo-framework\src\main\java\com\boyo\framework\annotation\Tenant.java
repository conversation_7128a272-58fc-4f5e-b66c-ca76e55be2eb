package com.boyo.framework.annotation;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.boyo.framework.tenant.TenantHeaders;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义一个租户的注解类注解指定数据源
 * <AUTHOR>
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@DS(TenantHeaders.DS_VALUE)
public @interface Tenant {
    String value() default "2db4bfe2cbe14269b410e4747073ee47";
}

