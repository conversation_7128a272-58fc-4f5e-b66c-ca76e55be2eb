package com.boyo.crm.service.impl;

import com.boyo.common.annotation.DataScope;
import com.boyo.crm.entity.CrmClue;
import com.boyo.crm.entity.CrmVisit;
import com.boyo.crm.util.ActionEnum;
import com.boyo.crm.util.ActionUtil;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.entity.CrmContacts;
import com.boyo.crm.mapper.CrmContactsMapper;
import com.boyo.crm.service.ICrmContactsService;
import java.util.List;

/**
 * 联系人表(CrmContacts)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-25 17:57:52
 */
@Service("crmContactsService")
@AllArgsConstructor
public class CrmContactsServiceImpl extends ServiceImpl<CrmContactsMapper, CrmContacts> implements ICrmContactsService {
    private final CrmContactsMapper crmContactsMapper;
    private final ActionUtil actionUtil;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    @DataScope(columns = "owner_user_id,create_user_id")
    public List<CrmContacts> selectCrmContactsList(CrmContacts crmContacts) {
        return crmContactsMapper.selectCrmContactsList(crmContacts);
    }
    @Override
    public boolean save(CrmContacts entity) {
        super.save(entity);
        actionUtil.editRecord(null,null, ActionEnum.BUSINESS,entity.getContactsId(), null);
        return true;
    }
    @Override
    public boolean updateById(CrmContacts entity) {
        actionUtil.editRecord(super.getById(entity.getContactsId()),entity,ActionEnum.BUSINESS,entity.getContactsId(), CrmContacts.class);
        return super.updateById(entity);
    }

}
