package com.boyo.mes.controller;

import com.boyo.mes.entity.MesModulproduction;
import com.boyo.mes.service.IMesModulproductionService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 模具产品绑定关系(MesModulproduction)表控制层
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
@Api("模具产品绑定关系")
@RestController
@RequestMapping("/mes/mesModulproduction")
@AllArgsConstructor
public class MesModulproductionController extends BaseController{
    /**
     * 服务对象
     */
    private final IMesModulproductionService mesModulproductionService;

    /**
     * 查询模具产品绑定关系列表
     *
     */
    @ApiOperation("查询模具产品绑定关系列表")
    @GetMapping("/list")
    public TableDataInfo list(MesModulproduction mesModulproduction) {
        startPage();
        List<MesModulproduction> list = mesModulproductionService.selectMesModulproductionList(mesModulproduction);
        return getDataTable(list);
    }
    
    /**
     * 获取模具产品绑定关系详情
     */
    @ApiOperation("获取模具产品绑定关系详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(mesModulproductionService.getById(id));
    }

    /**
     * 新增模具产品绑定关系
     */
    @ApiOperation("新增模具产品绑定关系")
    @PostMapping
    public AjaxResult add(@RequestBody MesModulproduction mesModulproduction) {
        return toBooleanAjax(mesModulproductionService.save(mesModulproduction));
    }

    /**
     * 修改模具产品绑定关系
     */
    @ApiOperation("修改模具产品绑定关系")
    @PutMapping
    public AjaxResult edit(@RequestBody MesModulproduction mesModulproduction) {
        return toBooleanAjax(mesModulproductionService.updateById(mesModulproduction));
    }

    /**
     * 删除模具产品绑定关系
     */
    @ApiOperation("删除模具产品绑定关系")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(mesModulproductionService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 获取模具产品清单
     */
    @GetMapping("/getModulProduction")
    public AjaxResult getModulProduction(Integer id){
        return AjaxResult.success(mesModulproductionService.getModulProduction(id));
    }
}
