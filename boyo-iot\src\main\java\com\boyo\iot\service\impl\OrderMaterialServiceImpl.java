package com.boyo.iot.service.impl;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.iot.entity.OrderMaterial;
import com.boyo.iot.mapper.OrderMaterialMapper;
import com.boyo.iot.service.IOrderMaterialService;
import java.util.List;

/**
 * (OrderMaterial)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-28 09:16:06
 */
@Service("orderMaterialService")
@AllArgsConstructor
public class OrderMaterialServiceImpl extends ServiceImpl<OrderMaterialMapper, OrderMaterial> implements IOrderMaterialService {
    private final OrderMaterialMapper orderMaterialMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<OrderMaterial> selectOrderMaterialList(OrderMaterial orderMaterial) {
        return orderMaterialMapper.selectOrderMaterialList(orderMaterial);
    }

}

