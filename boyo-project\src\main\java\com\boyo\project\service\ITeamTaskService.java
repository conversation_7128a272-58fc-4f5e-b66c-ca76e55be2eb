package com.boyo.project.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.project.entity.TeamTask;
import java.util.List;

/**
 * 任务表(TeamTask)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-13 13:05:06
 */
public interface ITeamTaskService extends IService<TeamTask> {

    /**
     * 查询多条数据
     *
     * @param teamTask 对象信息
     * @return 对象列表
     */
    List<TeamTask> selectTeamTaskList(TeamTask teamTask);

    List<TeamTask> listMyTask();


}
