<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.iot.mapper.IorealDataMapper">

    <resultMap type="com.boyo.iot.entity.IorealData" id="IorealDataResult">
        <result property="key" column="key"/>
        <result property="deviceCode" column="device_code"/>
        <result property="tag" column="tag"/>
        <result property="val" column="val"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectIorealDataList" parameterType="com.boyo.iot.entity.IorealData" resultMap="IorealDataResult">
        select
        `key`, device_code, tag, val,update_time
        from iot_real_data
        <where>
            <if test="deviceCode != null and deviceCode != ''">
                and device_code = #{deviceCode}
            </if>
            <if test="tag != null and tag != ''">
                and tag = #{tag}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>
    <insert id="saveOrUpdate">
        insert into iot_real_data(`key`,device_code, tag, val,update_time)
        values
        <foreach collection="list" item="p" index="index" separator=",">
            (
            #{p.key},
            #{p.deviceCode},
            #{p.tag},
            #{p.val},
            NOW())
        </foreach>
        ON DUPLICATE KEY UPDATE
        device_code = values(device_code),
        tag = values(tag),
        val = values(val),
        update_time = values(update_time)
    </insert>
</mapper>

