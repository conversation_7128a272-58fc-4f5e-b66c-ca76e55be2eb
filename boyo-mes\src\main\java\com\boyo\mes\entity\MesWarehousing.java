package com.boyo.mes.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 生产入库记录(MesWarehousing)实体类
 *
 * <AUTHOR>
 * @since 2023-01-06 10:34:00
 */
@Data
@TableName(value = "t_mes_warehousing")
public class MesWarehousing implements Serializable {
    private static final long serialVersionUID = -70318478740213958L;
            
    @TableId
    private Integer id;
    
    /**
    * 日期
    */
    @TableField(value="rq")
    private String rq;
    /**
     * 设备id
     */
    @TableField(value="equipment_id")
    private Integer equipmentId;
    /**
    * 班次
    */
    @TableField(value="shift")
    private String shift;
    /**
    * 产品id
    */
    @TableField(value="production_id")
    private Integer productionId;
    /**
    * 入库量
    */
    @TableField(value="in_count")
    private Integer inCount;
    /**
    * 报废数
    */
    @TableField(value="scrap_count")
    private Integer scrapCount;

    /**
     * 产品名称
     */
    @TableField(exist = false)
    private String productionName;

}
