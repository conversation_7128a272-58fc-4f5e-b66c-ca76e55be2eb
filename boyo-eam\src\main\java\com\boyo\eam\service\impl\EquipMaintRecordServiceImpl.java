package com.boyo.eam.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.eam.domain.*;
import com.boyo.eam.domain.VO.EquipMaintRecordTaskVO;
import com.boyo.eam.mapper.*;
import com.boyo.eam.service.IEquipMaintRecordService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 维保记录表(EquipMaintRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-19 14:56:16
 */
@Service("equipMaintRecordService")
@AllArgsConstructor
@Tenant
public class EquipMaintRecordServiceImpl extends ServiceImpl<EquipMaintRecordMapper, EquipMaintRecord> implements IEquipMaintRecordService {
    private final EquipMaintRecordMapper equipMaintRecordMapper;
    private final EquipMaintTaskMapper equipMaintTaskMapper;
    private final EquipMaintTaskItemMapper equipMaintTaskItemMapper;
    private final EquipMaintTaskItemDetailMapper equipMaintTaskItemDetailMapper;
    private final EquipLedgerMapper equipLedgerMapper;
    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipMaintRecord> selectEquipMaintRecordList(EquipMaintRecord equipMaintRecord) {
        return equipMaintRecordMapper.selectEquipMaintRecordList(equipMaintRecord);
    }

    @Override
    @Transactional
    public Boolean insertByTaskOpenid(String taskOpenid) {
        // 查询任务下的出所有项目
        List<EquipMaintTaskItem> itemList = equipMaintTaskItemMapper.selectList(
                Wrappers.<EquipMaintTaskItem>lambdaQuery()
                        .in(EquipMaintTaskItem::getEquipMaintTaskOpenid, taskOpenid)
        );
        if (itemList!=null && itemList.size()>0){
            List<String> itemOpenids = itemList.stream().map( e -> e.getOpenid()).collect(Collectors.toList());
            // 查询出项目下的所有明细
            List<EquipMaintTaskItemDetail> detailList = equipMaintTaskItemDetailMapper.selectList(
                    Wrappers.<EquipMaintTaskItemDetail>lambdaQuery()
                            .in(EquipMaintTaskItemDetail::getEquipMaintTaskItemOpenid, itemOpenids)
            );
            if (detailList!=null && detailList.size()>0){
                // 插入记录
                for (EquipMaintTaskItemDetail detail:detailList){
                    EquipMaintRecord record = new EquipMaintRecord();
                    record.setOpenid(generateOpenid());
                    record.setEquipMaintTaskOpenid(taskOpenid);
                    record.setEquipMaintTaskItemOpenid(detail.getEquipMaintTaskItemOpenid());
                    record.setEquipMaintTaskItemDetailOpenid(detail.getOpenid());
                    record.setBeginDate(new Date());
                    record.setCreateTime(new Date());
                    record.setCreateBy(SecurityUtils.getUsername());
                    equipMaintRecordMapper.insert(record);
                }
            }
        }
        return true;
    }

    @Override
    @Transactional
    public Boolean updateByTaskOpenid(String taskOpenid) {
        // 查询任务下的出所有项目
        List<EquipMaintTaskItem> itemList = equipMaintTaskItemMapper.selectList(
                Wrappers.<EquipMaintTaskItem>lambdaQuery()
                        .in(EquipMaintTaskItem::getEquipMaintTaskOpenid, taskOpenid)
        );
        if (itemList!=null&&itemList.size()>0){
            List<String> itemOpenids = itemList.stream().map( e -> e.getOpenid()).collect(Collectors.toList());
            // 查询出项目下的所有明细
            List<EquipMaintTaskItemDetail> detailList = equipMaintTaskItemDetailMapper.selectList(
                    Wrappers.<EquipMaintTaskItemDetail>lambdaQuery()
                            .in(EquipMaintTaskItemDetail::getEquipMaintTaskItemOpenid, itemOpenids)
            );
            if (detailList!=null&&detailList.size()>0){

                List<String> detailOpenids = detailList.stream().map(e -> e.getOpenid()).collect(Collectors.toList());
                // 根据明细查询出所有记录
                List<EquipMaintRecord> recordList = equipMaintRecordMapper.selectList(
                        Wrappers.<EquipMaintRecord>lambdaQuery()
                                .in(EquipMaintRecord::getEquipMaintTaskItemDetailOpenid, detailOpenids)
                );
                if (recordList!=null&&recordList.size()>0){
                    // 更新记录
                    for (EquipMaintRecord record:recordList){
                        Date nowDate = new Date();
                        record.setEndDate(nowDate);
                        long minute = DateUtil.between(record.getBeginDate(), nowDate, DateUnit.MINUTE);
                        record.setHandleMinute(new Long(minute).intValue());
                        equipMaintRecordMapper.updateById(record);
                    }
                }
            }
        }
        return true;
    }

    @Override
    public List<EquipMaintRecordTaskVO> selectTask(EquipMaintRecordTaskVO equipMaintRecordDetailVO) {
        List<EquipMaintRecordTaskVO> equipMaintRecordTaskVOS = equipMaintRecordMapper.selectTask(equipMaintRecordDetailVO);
        if (equipMaintRecordTaskVOS!=null && equipMaintRecordTaskVOS.size()>0){
            for (EquipMaintRecordTaskVO recordTaskVO:equipMaintRecordTaskVOS){
                // 设置设备名称
                String equipOpenids = recordTaskVO.getEquipLedgerOpenid();
                if (equipOpenids!=null && !"".equals(equipOpenids)){
                    if(equipOpenids!=null&& !"".equals(equipOpenids)){
                        String[] split = equipOpenids.split(",");
                        List<EquipLedger> equipLedgerList = equipLedgerMapper.selectList(
                                Wrappers.<EquipLedger>lambdaQuery()
                                        .in(EquipLedger::getOpenid, split)
                        );
                        StringBuffer buffer = new StringBuffer();
                        for (EquipLedger equip:equipLedgerList){
                            buffer.append(equip.getName()).append(",");
                        }
                        buffer.deleteCharAt(buffer.length()-1);
                        recordTaskVO.setEquipNames(buffer.toString());
                    }
                }
            }
        }
        return equipMaintRecordTaskVOS;
    }


    /**
     * 创建openid
     * @return
     */
    private String generateOpenid(){
        return UUID.randomUUID().toString().replaceAll("-","");
    }
}
