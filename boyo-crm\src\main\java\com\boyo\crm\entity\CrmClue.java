package com.boyo.crm.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import com.boyo.framework.annotation.PropertyMsg;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * CRM线索主表(CrmClue)实体类
 *
 * <AUTHOR>
 * @since 2021-12-15 10:48:28
 */
@Data
@TableName(value = "t_crm_clue")
public class CrmClue extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = 970518347387799052L;

    @TableId
    private Integer id;

    /**
     * 线索名称
     */
    @TableField(value = "clue_name")
    @PropertyMsg(value="线索名称")
    private String clueName;
    /**
     * 线索来源
     */
    @TableField(value = "clue_source")
    @PropertyMsg(value="线索来源",type = "base")
    private Integer clueSource;

    @TableField(exist = false)
    private String sourceName;
    /**
     * 联系邮箱
     */
    @TableField(value = "clue_email")
    @PropertyMsg(value="联系邮箱")
    private String clueEmail;
    /**
     * 联系电话
     */
    @TableField(value = "clue_phone")
    @PropertyMsg(value="联系电话")
    private String cluePhone;
    /**
     * 地址
     */
    @TableField(value = "clue_address")
    @PropertyMsg(value="地址")
    private String clueAddress;
    /**
     * 客户行业
     */
    @TableField(value = "clue_industry")
    @PropertyMsg(value="客户行业",type = "base")
    private Integer clueIndustry;

    @TableField(exist = false)
    private String industryName;
    /**
     * 客户级别
     */
    @TableField(value = "clue_level")
    @PropertyMsg(value="客户级别",type = "base")
    private Integer clueLevel;

    @TableField(exist = false)
    private String levelName;
    /**
     * 备注
     */
    @TableField(value = "clue_remark")
    private String clueRemark;
    /**
     * 下次联系时间
     */
    @TableField(value = "next_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @PropertyMsg(value="下次联系时间")
    private Date nextTime;
    /**
     * 创建人id
     */
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private Long createUserId;
    /**
     * 负责人id
     */
    @TableField(value = "owner_user_id", fill = FieldFill.INSERT)
    private Long ownerUserId;

    @TableField(exist = false)
    private String ownerUserName;
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
