package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipInspectionSpot;

import java.util.List;

/**
 * 点检表(EquipInspectionSpot)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-15 16:16:30
 */
public interface EquipInspectionSpotMapper extends BaseMapper<EquipInspectionSpot>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipInspectionSpot 实例对象
     * @return 对象列表
     */
    List<EquipInspectionSpot> selectEquipInspectionSpotList(EquipInspectionSpot equipInspectionSpot);


}

