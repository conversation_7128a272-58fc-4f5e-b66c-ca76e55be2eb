package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipType;

import java.util.List;

/**
 * 设备类型表(EquipType)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
public interface IEquipTypeService extends IService<EquipType> {

    /**
     * 查询多条数据
     *
     * @param equipType 对象信息
     * @return 对象列表
     */
    List<EquipType> selectEquipTypeList(EquipType equipType);


}
