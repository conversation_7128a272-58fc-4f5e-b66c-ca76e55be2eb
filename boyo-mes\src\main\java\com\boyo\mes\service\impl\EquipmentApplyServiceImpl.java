package com.boyo.mes.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.mapper.IotEquipmentMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.EquipmentApply;
import com.boyo.mes.mapper.EquipmentApplyMapper;
import com.boyo.mes.service.IEquipmentApplyService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 上下班记录(EquipmentApply)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:47:21
 */
@Service("equipmentApplyService")
@AllArgsConstructor
@Tenant
public class EquipmentApplyServiceImpl extends ServiceImpl<EquipmentApplyMapper, EquipmentApply> implements IEquipmentApplyService {
    private final EquipmentApplyMapper equipmentApplyMapper;
    private final IotEquipmentMapper equipmentMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipmentApply> selectEquipmentApplyList(EquipmentApply equipmentApply) {
        return equipmentApplyMapper.selectEquipmentApplyList(equipmentApply);
    }

    @Override
    public EquipmentApply scanApply(String openid) {
        EquipmentApply result = new EquipmentApply();
        QueryWrapper<IotEquipment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("open_id", openid);
        IotEquipment equipment = equipmentMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNull(equipment)) {
            throw new CustomException("设备不存在，请重新扫码二维码");
        }
        QueryWrapper<EquipmentApply> applyQueryWrapper = new QueryWrapper<>();
        applyQueryWrapper.eq("equipment_id", equipment.getId()).isNull("end_time").orderByDesc("id");
        List<EquipmentApply> applyList = equipmentApplyMapper.selectList(applyQueryWrapper);
        if (applyList != null && applyList.size() > 0) {
            result = applyList.get(0);
            result.setEquipmentId(equipment.getId());
            result.setEquipmentName(equipment.getEquipmentName());
            result.setEquipmentCode(equipment.getEquipmentCode());
            if (result.getUserId().equals(SecurityUtils.getUserId())) {
                result.setEquipmentStatus("2");
            } else {
                result.setEquipmentStatus("1");
                result.setEquipmentMsg("当前设备正在被用户【" + result.getUserName() + "】使用");
            }
        } else {
            result.setEquipmentId(equipment.getId());
            result.setEquipmentName(equipment.getEquipmentName());
            result.setEquipmentCode(equipment.getEquipmentCode());
            result.setEquipmentStatus("0");
        }
        return result;
    }

    @Override
    public List<IotEquipment> listUsedEquipment() {
        QueryWrapper<EquipmentApply> applyQueryWrapper = new QueryWrapper<>();
        applyQueryWrapper.eq("user_id",SecurityUtils.getUserId()).isNull("end_time").orderByDesc("id");
        List<EquipmentApply> applyList = equipmentApplyMapper.selectList(applyQueryWrapper);
        if(applyList != null && applyList.size() > 0){
            List<Long> ids = new ArrayList<>();
            for (EquipmentApply apply:applyList) {
                ids.add(apply.getEquipmentId());
            }
            QueryWrapper<IotEquipment> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id",ids);
            return equipmentMapper.selectList(queryWrapper);
        }else{
            return null;
        }
    }

    @Override
    public boolean save(EquipmentApply entity) {
        QueryWrapper<EquipmentApply> applyQueryWrapper = new QueryWrapper<>();
        applyQueryWrapper.eq("equipment_id", entity.getEquipmentId()).isNull("end_time").orderByDesc("id");
        List<EquipmentApply> applyList = equipmentApplyMapper.selectList(applyQueryWrapper);
        /**
         * 下班
         */
        if (entity.getApplyType().equals("0")) {
            if (applyList != null && applyList.size() > 0) {
                EquipmentApply apply = applyList.get(0);
                if (apply.getUserId().equals(SecurityUtils.getUserId())) {
                    apply.setEndTime(new Date());
                    equipmentApplyMapper.updateById(apply);
                    return true;
                } else {
                    throw new CustomException("该设备当前正被其他用户使用");
                }
            } else {
                throw new CustomException("该设备当前无上班记录");
            }
        } else {
//            上班
            if (applyList != null && applyList.size() > 0) {
                throw new CustomException("该设备当前正被其他用户使用");
            } else {
                entity.setStartTime(new Date());
                entity.setUserId(SecurityUtils.getUserId());
                entity.setUserName(SecurityUtils.getUsername());
            }
            return super.save(entity);
        }
    }
}
