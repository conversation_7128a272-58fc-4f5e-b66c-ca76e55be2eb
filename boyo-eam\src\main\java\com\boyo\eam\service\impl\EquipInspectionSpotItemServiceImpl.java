package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.*;
import com.boyo.eam.mapper.EquipInspectionSpotItemMapper;
import com.boyo.eam.service.IEquipInspectionSpotItemService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 点检-项目(EquipInspectionSpotItem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-15 16:16:30
 */
@Service("equipInspectionSpotItemService")
@AllArgsConstructor
@Tenant
public class EquipInspectionSpotItemServiceImpl extends ServiceImpl<EquipInspectionSpotItemMapper, EquipInspectionSpotItem> implements IEquipInspectionSpotItemService {
    private final EquipInspectionSpotItemMapper equipInspectionSpotItemMapper;
    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipInspectionSpotItem> selectEquipInspectionSpotItemList(EquipInspectionSpotItem equipInspectionSpotItem) {
        List<EquipInspectionSpotItem> equipInspectionSpotItems = equipInspectionSpotItemMapper.selectEquipInspectionSpotItemList(equipInspectionSpotItem);
        return equipInspectionSpotItems;
    }

    @Override
    public EquipInspectionSpotItem getItemAndRecord(Integer id) {
        return equipInspectionSpotItemMapper.getItemAndRecord(id);
    }

}
