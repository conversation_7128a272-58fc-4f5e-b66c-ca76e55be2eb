package com.boyo.master.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.framework.annotation.Tenant;
import com.boyo.master.domain.ModelWorkshop;
import com.boyo.master.mapper.ModelWorkshopMapper;
import com.boyo.master.service.IModelWorkshopService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 工厂模型-车间Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class ModelWorkshopServiceImpl extends ServiceImpl<ModelWorkshopMapper, ModelWorkshop> implements IModelWorkshopService {
    private final ModelWorkshopMapper modelWorkshopMapper;


    /**
     * 查询工厂模型-车间列表
     *
     * @param modelWorkshop 工厂模型-车间
     * @return modelWorkshop 列表
     */
    @Override
    public List<ModelWorkshop> selectModelWorkshopList(ModelWorkshop modelWorkshop) {
        return modelWorkshopMapper.selectModelWorkshopList(modelWorkshop);
    }
}
