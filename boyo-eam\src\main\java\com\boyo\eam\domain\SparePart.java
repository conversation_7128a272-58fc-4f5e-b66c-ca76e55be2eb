package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 备件总账表(SparePart)实体类
 *
 * <AUTHOR>
 * @since 2021-11-04 11:12:30
 */
@Data
@TableName(value = "spare_part")
public class SparePart implements Serializable {
    private static final long serialVersionUID = 956890925947396430L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 备件分类
    */
    @TableField(value="type")
    private String type;
    /**
    * 备件编码
    */
    @TableField(value="code")
    private String code;
    /**
    * 备件名称
    */
    @TableField(value="name")
    private String name;
    /**
    * 规格型号
    */
    @TableField(value="model")
    private String model;
    /**
    * 计量单位
    */
    @TableField(value="unit")
    private String unit;
    /**
     * 备注
     */
    @TableField(value="remark")
    private String remark;
    /**
    * 创建人
    */
    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

}
