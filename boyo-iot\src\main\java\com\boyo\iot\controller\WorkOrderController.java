package com.boyo.iot.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.boyo.common.core.text.Convert;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.common.utils.poi.ExcelUtil;
import com.boyo.iot.entity.WorkOrder;
import com.boyo.iot.service.IWorkOrderService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Arrays;

/**
 * 系统工单(WorkOrder)表控制层
 *
 * <AUTHOR>
 * @since 2022-04-08 15:10:06
 */
@Api("系统工单")
@RestController
@RequestMapping("/iot/workOrder")
@AllArgsConstructor
public class WorkOrderController extends BaseController{
    /**
     * 服务对象
     */
    private final IWorkOrderService workOrderService;

    /**
     * 查询系统工单列表
     *
     */
    @ApiOperation("查询系统工单列表")
    @GetMapping("/list")
    public TableDataInfo list(WorkOrder workOrder) {
        startPage();
        List<WorkOrder> list = workOrderService.selectWorkOrderList(workOrder);
        return getDataTable(list);
    }

    @ApiOperation("查询待认领工单")
    @GetMapping("/listWaitReceive")
    public TableDataInfo listWaitReceive(WorkOrder workOrder) {
        startPage();
        List<WorkOrder> list = workOrderService.listWaitReceive(workOrder);
        return getDataTable(list);
    }
    /**
     * 获取系统工单详情
     */
    @ApiOperation("获取系统工单详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        final WorkOrder workOrder = workOrderService.getWorkOrderById(id);
        return AjaxResult.success(workOrder);
    }

    /**
     * 新增系统工单
     */
    @ApiOperation("新增系统工单")
    @PostMapping
    public AjaxResult add(@RequestBody WorkOrder workOrder) {
        workOrder.setCreateUserId(SecurityUtils.getUserId());
        workOrderService.save(workOrder);
        workOrder.setNum("WO-P-" + DateUtil.format(new Date(), "yyyyMMdd") + "-" + StrUtil.fillBefore(Convert.toStr(workOrder.getId()), '0', 6));
        workOrderService.updateById(workOrder);
        return toBooleanAjax(true);
    }

    /**
     * 修改系统工单
     */
    @ApiOperation("修改系统工单")
    @PutMapping
    public AjaxResult edit(@RequestBody WorkOrder workOrder) {
        return toBooleanAjax(workOrderService.updateById(workOrder));
    }

    /**
     * 删除系统工单
     */
    @ApiOperation("删除系统工单")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(workOrderService.removeByIds(Arrays.asList(ids)));
    }

    @PostMapping(value = "/receiveOrder")
    public AjaxResult receiveOrder(Integer id){
        workOrderService.receiveOrder(id);
        return AjaxResult.success();
    }

    @PostMapping(value = "/completeOrder")
    public AjaxResult completeOrder(@RequestBody WorkOrder workOrder){
        workOrder.setCompleteTime(new Date());
        workOrderService.updateById(workOrder);
        return AjaxResult.success();
    }

    @PostMapping(value = "/cancelOrder")
    public AjaxResult cancelOrder(Integer id){
        workOrderService.cancelOrder(id);
        return AjaxResult.success();
    }


    /**
     * 导出工单
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, WorkOrder workOrder) throws IOException {
        List<WorkOrder> list = workOrderService.selectWorkOrderList(workOrder);
        ExcelUtil<WorkOrder> util = new ExcelUtil<WorkOrder>(WorkOrder.class);
        util.exportExcel( list, "工单管理");
    }
}
