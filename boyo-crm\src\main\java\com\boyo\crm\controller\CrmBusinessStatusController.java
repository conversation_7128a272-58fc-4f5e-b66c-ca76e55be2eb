package com.boyo.crm.controller;

import com.boyo.crm.entity.CrmBusinessStatus;
import com.boyo.crm.service.ICrmBusinessStatusService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 商机状态(CrmBusinessStatus)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-25 14:53:11
 */
@Api("商机状态")
@RestController
@RequestMapping("/crm/crmBusinessStatus")
@AllArgsConstructor
public class CrmBusinessStatusController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmBusinessStatusService crmBusinessStatusService;

    /**
     * 查询商机状态列表
     *
     */
    @ApiOperation("查询商机状态列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusinessStatus crmBusinessStatus) {
        startPage();
        List<CrmBusinessStatus> list = crmBusinessStatusService.selectCrmBusinessStatusList(crmBusinessStatus);
        return getDataTable(list);
    }
    
    /**
     * 获取商机状态详情
     */
    @ApiOperation("获取商机状态详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(crmBusinessStatusService.getById(id));
    }

    /**
     * 新增商机状态
     */
    @ApiOperation("新增商机状态")
    @PostMapping
    public AjaxResult add(@RequestBody CrmBusinessStatus crmBusinessStatus) {
        return toBooleanAjax(crmBusinessStatusService.save(crmBusinessStatus));
    }

    /**
     * 修改商机状态
     */
    @ApiOperation("修改商机状态")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmBusinessStatus crmBusinessStatus) {
        return toBooleanAjax(crmBusinessStatusService.updateById(crmBusinessStatus));
    }

    /**
     * 删除商机状态
     */
    @ApiOperation("删除商机状态")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmBusinessStatusService.removeByIds(Arrays.asList(ids)));
    }

}
