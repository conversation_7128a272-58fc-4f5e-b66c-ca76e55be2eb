package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.Media;
import com.boyo.eam.mapper.MediaMapper;
import com.boyo.eam.service.IMediaService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * (Media)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-05 10:30:24
 */
@Service("mediaService")
@AllArgsConstructor
@Tenant
public class MediaServiceImpl extends ServiceImpl<MediaMapper, Media> implements IMediaService {
    private final MediaMapper mediaMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<Media> selectMediaList(Media media) {
        return mediaMapper.selectMediaList(media);
    }

}
