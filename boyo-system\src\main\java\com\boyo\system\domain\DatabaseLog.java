package com.boyo.system.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 数据库变更管理
 * 表名 t_database_log
 *
 * <AUTHOR>
 */
@ApiModel("企业租户数据库变更")
@Data
@TableName("t_database_log")
public class DatabaseLog extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @ApiModelProperty("自增ID")
    @TableId
    private Long id;
    /**
     * OPENID
     */
    @ApiModelProperty("OPENID")
    @TableField(value = "database_openid")
    private String databaseOpenid;
    /**
     * 数据库版本号
     */
    @ApiModelProperty("数据库版本号")
    @TableField(value = "database_version_code")
    private Long databaseVersionCode;
    /**
     * 数据库版本名称
     */
    @ApiModelProperty("数据库版本名称")
    @TableField(value = "database_version_name")
    private String databaseVersionName;
    /**
     * 变更时间
     */
    @ApiModelProperty("变更时间")
    @TableField(value = "database_version_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date databaseVersionTime;
    /**
     * 数据库版本变更语句
     */
    @ApiModelProperty("数据库版本变更语句")
    @TableField(value = "database_change_sql")
    private String databaseChangeSql;
    /**
     * 数据库版本完整语句
     */
    @ApiModelProperty("数据库版本完整语句")
    @TableField(value = "database_full_sql")
    private String databaseFullSql;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(value = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}


