package com.boyo.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.domain.IotTsl;

import java.util.List;


/**
 * IoT物模型Mapper接口
 *
 * <AUTHOR>
 */
@Tenant
public interface IotTslMapper extends BaseMapper<IotTsl> {

    /**
     * 查询IoT物模型列表
     *
     * @param iotTsl IoT物模型
     * @return IotTsl集合
     */
    List<IotTsl> selectIotTslList(IotTsl iotTsl);

}
