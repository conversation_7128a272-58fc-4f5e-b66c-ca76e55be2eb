package com.boyo.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.mes.entity.ProductProcess;
import java.util.List;

/**
 * 工序管理(ProductProcess)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
public interface ProductProcessMapper extends BaseMapper<ProductProcess>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param productProcess 实例对象
     * @return 对象列表
     */
    List<ProductProcess> selectProductProcessList(ProductProcess productProcess);


}

