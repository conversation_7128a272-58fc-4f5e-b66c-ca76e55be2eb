<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.system.mapper.TSysCategoryMapper">

    <resultMap type="com.boyo.system.domain.TSysCategory" id="TSysCategoryResult">
        <result property="id" column="id"/>
        <result property="categoryOpenid" column="category_openid"/>
        <result property="categoryName" column="category_name"/>
        <result property="categoryCode" column="category_code"/>
        <result property="categoryIcon" column="category_icon"/>
        <result property="categoryImg" column="category_img"/>
        <result property="categoryDesc" column="category_desc"/>
        <result property="categoryStatus" column="category_status"/>
        <result property="categoryOrder" column="category_order"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTSysCategoryVo">
        select id,
               category_openid,
               category_name,
               category_code,
               category_icon,
               category_img,
               category_desc,
               category_status,
               category_order,
               create_time,
               update_time
        from t_sys_category
    </sql>

    <select id="selectTSysCategoryList" parameterType="com.boyo.system.domain.TSysCategory"
            resultMap="TSysCategoryResult">
        <include refid="selectTSysCategoryVo"/>
        <where>
            <if test="categoryOpenid != null  and categoryOpenid != ''">
                and category_openid = #{categoryOpenid}
            </if>
            <if test="categoryName != null  and categoryName != ''">
                and category_name like concat('%', #{categoryName}, '%')
            </if>
            <if test="categoryCode != null  and categoryCode != ''">
                and category_code = #{categoryCode}
            </if>
            <if test="categoryIcon != null  and categoryIcon != ''">
                and category_icon = #{categoryIcon}
            </if>
            <if test="categoryImg != null  and categoryImg != ''">
                and category_img = #{categoryImg}
            </if>
            <if test="categoryDesc != null  and categoryDesc != ''">
                and category_desc = #{categoryDesc}
            </if>
            <if test="categoryStatus != null  and categoryStatus != ''">
                and category_status = #{categoryStatus}
            </if>
            <if test="categoryOrder != null ">
                and category_order = #{categoryOrder}
            </if>
        </where>
    </select>
</mapper>
