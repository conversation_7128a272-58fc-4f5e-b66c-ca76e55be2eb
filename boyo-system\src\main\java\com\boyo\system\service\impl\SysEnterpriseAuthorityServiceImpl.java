package com.boyo.system.service.impl;

import java.util.Collection;
import java.util.List;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.system.mapper.SysEnterpriseAuthorityMapper;
import com.boyo.system.domain.SysEnterpriseAuthority;
import com.boyo.system.service.ISysEnterpriseAuthorityService;

/**
 * 企业权限管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class SysEnterpriseAuthorityServiceImpl extends ServiceImpl<SysEnterpriseAuthorityMapper, SysEnterpriseAuthority> implements ISysEnterpriseAuthorityService {
    private final SysEnterpriseAuthorityMapper sysEnterpriseAuthorityMapper;


    /**
     * 查询企业权限管理列表
     *
     * @param sysEnterpriseAuthority 企业权限管理
     * @return sysEnterpriseAuthority 列表
     */
    @Override
    public List<SysEnterpriseAuthority> selectSysEnterpriseAuthorityList(SysEnterpriseAuthority sysEnterpriseAuthority) {
        return sysEnterpriseAuthorityMapper.selectSysEnterpriseAuthorityList(sysEnterpriseAuthority);
    }

    /**
     * 先删除原始记录，然后保存新的授权信息
     *
     * @param entityList 权限信息
     * @return 成功失败
     */
    @Override
    public boolean saveBatch(Collection<SysEnterpriseAuthority> entityList) {
        if (CollUtil.isNotEmpty(entityList)) {
            QueryWrapper<SysEnterpriseAuthority> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("enterprise_openid", CollUtil.get(entityList, 0).getEnterpriseOpenid());
            sysEnterpriseAuthorityMapper.delete(queryWrapper);
        }
        return super.saveBatch(entityList);
    }
}
