package com.boyo.crm.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.entity.CrmPool;
import com.boyo.crm.mapper.CrmPoolMapper;
import com.boyo.crm.service.ICrmPoolService;
import java.util.List;

/**
 * 公海表(CrmPool)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-30 14:33:58
 */
@Service("crmPoolService")
@AllArgsConstructor
public class CrmPoolServiceImpl extends ServiceImpl<CrmPoolMapper, CrmPool> implements ICrmPoolService {
    private final CrmPoolMapper crmPoolMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<CrmPool> selectCrmPoolList(CrmPool crmPool) {
        return crmPoolMapper.selectCrmPoolList(crmPool);
    }

}
