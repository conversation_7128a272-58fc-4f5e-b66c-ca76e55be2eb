package com.boyo.wms.service.impl;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.exception.BaseException;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.wms.entity.WmsFlow;
import com.boyo.wms.entity.WmsMateriel;
import com.boyo.wms.mapper.WmsFlowMapper;
import com.boyo.wms.mapper.WmsMaterielMapper;
import com.boyo.wms.vo.WmsMaterielVO;
import com.boyo.wms.vo.WmsPlanVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.wms.mapper.WmsPlanMapper;
import com.boyo.wms.entity.WmsPlan;
import com.boyo.wms.service.IWmsPlanService;

/**
 * 出入库计划管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class WmsPlanServiceImpl extends ServiceImpl<WmsPlanMapper, WmsPlan> implements IWmsPlanService {
    private final WmsPlanMapper wmsPlanMapper;

    private final WmsMaterielMapper wmsMaterielMapper;

    private final WmsFlowMapper wmsFlowMapper;


    /**
     * 查询出入库计划管理列表
     *
     * @param wmsPlan 出入库计划管理
     * @return wmsPlan 列表
     */
    @Override
    public List<WmsPlanVO> selectWmsPlanList(WmsPlan wmsPlan) {
        return wmsPlanMapper.selectWmsPlanList(wmsPlan);
    }

    @Override
    public WmsPlan getById(Serializable id) {
        WmsPlan wmsPlan = super.getById(id);
        WmsMateriel wmsMateriel = new WmsMateriel();
        wmsMateriel.setDetailInoutOpenid(wmsPlan.getPlanOpenid());
        wmsPlan.setMaterielList(wmsMaterielMapper.selectWmsMaterielList(wmsMateriel));
        return wmsPlan;
    }

    @Override
    public boolean issuePlan(String id) {
        WmsPlan entity = wmsPlanMapper.selectById(id);
        if (entity.getPlanState().equals("1")) {
            entity.setPlanState("2");
        }
        return super.updateById(entity);
    }

    @Override
    public boolean withdrawPlan(String id) {
        WmsPlan entity = wmsPlanMapper.selectById(id);
        QueryWrapper<WmsFlow> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("plan_openid", entity.getPlanOpenid());
        if (wmsFlowMapper.selectCount(queryWrapper) > 0) {
            throw new CustomException("当前计划已有出入库记录，不允许撤回!");
        }
        entity.setPlanState("1");
        return super.updateById(entity);
    }

    @Override
    public boolean endPlan(String id) {
        WmsPlan entity = wmsPlanMapper.selectById(id);
        entity.setPlanState("4");
        return super.updateById(entity);
    }

    @Override
    public boolean save(WmsPlan entity) {
        entity.setPlanOpenid(IdUtil.simpleUUID());
        String orderNO = "";
        String materielNO = "";
        if (entity.getPlanType().equals("1")) {
//            入库
            orderNO = "I-";
            materielNO = "I-";
        } else {
//            出库
            orderNO = "O-";
            materielNO = "O-";
        }
        QueryWrapper<WmsPlan> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("created_at", DateUtil.format(new Date(), "yyyy-MM-dd") + " 00:00:00", DateUtil.format(new Date(), "yyyy-MM-dd") + " 23:59:59");
        Integer planCount = wmsPlanMapper.selectCount(queryWrapper) + 1;
        orderNO += DateUtil.format(new Date(), "yyMMddHHmmss");
        orderNO += "-" + StrUtil.fillBefore(Convert.toStr(planCount), '0', 3);
        entity.setPlanOrder(orderNO);
        entity.setCreatedAt(new Date());
        entity.setCreatedUser(SecurityUtils.getUserOpenid());
        entity.setPlanState("1");
        if (super.save(entity)) {
            List<WmsMaterielVO> detailList = entity.getMaterielList();
            if (detailList != null && detailList.size() > 0) {
                int size = detailList.size();
                for (int i = 0; i < size; i++) {
                    WmsMateriel obj = detailList.get(i);
                    QueryWrapper<WmsMateriel> materielQueryWrapper = new QueryWrapper<>();
                    materielQueryWrapper.and(o -> o.between("created_at", DateUtil.format(new Date(), "yyyy") + "-01-01 00:00:00", DateUtil.format(new Date(), "yyyy") + "-12-31 23:59:59")).and(o -> o.eq("detail_materiel_openid",  obj.getDetailMaterielOpenid()));
                    Integer materielCount = wmsMaterielMapper.selectCount(materielQueryWrapper) + 1;
                    materielNO += DateUtil.format(new Date(), "yyMMdd");
                    materielNO += "-" + StrUtil.fillBefore(Convert.toStr(materielCount), '0', 5);
                    obj.setDetailMaterielOrder(materielNO);
                    obj.setDetailType(entity.getPlanType());
                    obj.setDetailOpenid(IdUtil.fastSimpleUUID());
                    obj.setDetailInoutOpenid(entity.getPlanOpenid());
                    obj.setCreatedAt(new Date());
                    obj.setCreatedUser(SecurityUtils.getUserOpenid());
                    wmsMaterielMapper.insert(obj);
                }
            }
        }
        return true;
    }

    @Override
    public boolean updateById(WmsPlan entity) {
        WmsPlan temp = wmsPlanMapper.selectById(entity.getId());
        if (!temp.getPlanState().equals("1") ) {
            throw new BaseException("已下发的计划不允许修改");
        }
        String header = "";
        if (entity.getPlanType().equals("1")) {
//            入库
            header = "I-";
        } else {
//            出库
            header = "O-";
        }
        entity.setUpdatedAt(new Date());
        entity.setUpdatedUser(SecurityUtils.getUserOpenid());
        if (super.updateById(entity)) {
            QueryWrapper<WmsMateriel> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("detail_inout_openid", entity.getPlanOpenid());
            List<WmsMateriel> materielList = wmsMaterielMapper.selectList(queryWrapper);
            if(materielList != null && materielList.size() > 0){
                for (int i = 0; i < materielList.size(); i++) {
                    materielList.get(i).setDetailState("99");
                    wmsMaterielMapper.updateById(materielList.get(i));
                }
            }
            List<WmsMaterielVO> detailList = entity.getMaterielList();
            if (detailList != null && detailList.size() > 0) {
                int size = detailList.size();
                for (int i = 0; i < size; i++) {
                    WmsMateriel obj = detailList.get(i);
                    QueryWrapper<WmsMateriel> materielQueryWrapper = new QueryWrapper<>();
                    materielQueryWrapper.and(o -> o.between("created_at", DateUtil.format(new Date(), "yyyy") + "-01-01 00:00:00", DateUtil.format(new Date(), "yyyy") + "-12-31 23:59:59"));
                    Integer materielCount = wmsMaterielMapper.selectCount(materielQueryWrapper) + 1;
                    String materielNO = header + DateUtil.format(new Date(), "yyMMdd");
                    materielNO += "-" + StrUtil.fillBefore(Convert.toStr(materielCount), '0', 5);

                    obj.setDetailOpenid(IdUtil.fastSimpleUUID());
                    obj.setDetailMaterielOrder(materielNO);
                    obj.setDetailInoutOpenid(entity.getPlanOpenid());
                    obj.setCreatedAt(new Date());
                    obj.setCreatedUser(SecurityUtils.getUserOpenid());
                    wmsMaterielMapper.insert(obj);
                }
            }
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        List<WmsPlan> planList = wmsPlanMapper.selectBatchIds(idList);
        if (planList != null && planList.size() > 0) {
            for (int i = 0; i < planList.size(); i++) {
                WmsPlan temp = planList.get(i);
                if (!temp.getPlanState().equals("1")) {
                    throw new CustomException("已下发或已完成的计划不允许删除");
                }else{
                    temp.setPlanState("99");
                }
            }
        }
        return super.updateBatchById(planList);
    }
}
