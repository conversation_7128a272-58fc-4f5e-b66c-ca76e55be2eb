<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmInvoiceMapper">

    <resultMap type="com.boyo.crm.entity.CrmInvoice" id="CrmInvoiceResult">
        <result property="id" column="id"/>
        <result property="invoiceApplyNumber" column="invoice_apply_number"/>
        <result property="customerId" column="customer_id"/>
        <result property="contractId" column="contract_id"/>
        <result property="invoiceMoney" column="invoice_money"/>
        <result property="invoiceDate" column="invoice_date"/>
        <result property="invoiceType" column="invoice_type"/>
        <result property="remark" column="remark"/>
        <result property="titleType" column="title_type"/>
        <result property="invoiceTitle" column="invoice_title"/>
        <result property="taxNumber" column="tax_number"/>
        <result property="depositBank" column="deposit_bank"/>
        <result property="depositAccount" column="deposit_account"/>
        <result property="depositAddress" column="deposit_address"/>
        <result property="telephone" column="telephone"/>
        <result property="contactsName" column="contacts_name"/>
        <result property="contactsMobile" column="contacts_mobile"/>
        <result property="contactsAddress" column="contacts_address"/>
        <result property="examineRecordId" column="examine_record_id"/>
        <result property="checkStatus" column="check_status"/>
        <result property="ownerUserId" column="owner_user_id"/>
        <result property="invoiceNumber" column="invoice_number"/>
        <result property="realInvoiceDate" column="real_invoice_date"/>
        <result property="logisticsNumber" column="logistics_number"/>
        <result property="invoiceStatus" column="invoice_status"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="batchId" column="batch_id"/>

        <result property="contractName" column="contract_name"></result>
        <result property="customerName" column="customer_name"></result>
        <result property="contractCode" column="contract_code"></result>
        <result property="invoiceTypeName" column="invoice_type_name"></result>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmInvoiceList" parameterType="com.boyo.crm.entity.CrmInvoice" resultMap="CrmInvoiceResult">
        select t1.*,t2.customer_name,t3.name as contract_name,t3.num as contract_code,t4.base_desc as invoice_type_name
        from (
        select
        id, invoice_apply_number, customer_id, contract_id, invoice_money, invoice_date, invoice_type, remark,
        title_type, invoice_title, tax_number, deposit_bank, deposit_account, deposit_address, telephone, contacts_name,
        contacts_mobile, contacts_address, examine_record_id, check_status, owner_user_id, invoice_number,
        real_invoice_date, logistics_number, invoice_status, create_user_id, create_time, update_time, batch_id
        from t_crm_invoice
        <where>
            <if test="invoiceApplyNumber != null and invoiceApplyNumber != ''">
                and invoice_apply_number = #{invoiceApplyNumber}
            </if>
            <if test="customerId != null">
                and customer_id = #{customerId}
            </if>
            <if test="contractId != null">
                and contract_id = #{contractId}
            </if>
            <if test="invoiceMoney != null">
                and invoice_money = #{invoiceMoney}
            </if>
            <if test="invoiceDate != null">
                and invoice_date = #{invoiceDate}
            </if>
            <if test="invoiceType != null">
                and invoice_type = #{invoiceType}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="titleType != null">
                and title_type = #{titleType}
            </if>
            <if test="invoiceTitle != null and invoiceTitle != ''">
                and invoice_title = #{invoiceTitle}
            </if>
            <if test="taxNumber != null and taxNumber != ''">
                and tax_number = #{taxNumber}
            </if>
            <if test="depositBank != null and depositBank != ''">
                and deposit_bank = #{depositBank}
            </if>
            <if test="depositAccount != null and depositAccount != ''">
                and deposit_account = #{depositAccount}
            </if>
            <if test="depositAddress != null and depositAddress != ''">
                and deposit_address = #{depositAddress}
            </if>
            <if test="telephone != null and telephone != ''">
                and telephone = #{telephone}
            </if>
            <if test="contactsName != null and contactsName != ''">
                and contacts_name = #{contactsName}
            </if>
            <if test="contactsMobile != null and contactsMobile != ''">
                and contacts_mobile = #{contactsMobile}
            </if>
            <if test="contactsAddress != null and contactsAddress != ''">
                and contacts_address = #{contactsAddress}
            </if>
            <if test="examineRecordId != null">
                and examine_record_id = #{examineRecordId}
            </if>
            <if test="checkStatus != null">
                and check_status = #{checkStatus}
            </if>
            <if test="ownerUserId != null">
                and owner_user_id = #{ownerUserId}
            </if>
            <if test="invoiceNumber != null and invoiceNumber != ''">
                and invoice_number = #{invoiceNumber}
            </if>
            <if test="realInvoiceDate != null">
                and real_invoice_date = #{realInvoiceDate}
            </if>
            <if test="logisticsNumber != null and logisticsNumber != ''">
                and logistics_number = #{logisticsNumber}
            </if>
            <if test="invoiceStatus != null">
                and invoice_status = #{invoiceStatus}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="batchId != null and batchId != ''">
                and batch_id = #{batchId}
            </if>
            ${params.dataScope}

        </where>
        ) t1 left join t_crm_customer t2 on t1.customer_id = t2.id
        left join t_crm_contract t3 on t1.contract_id = t3.id
        left join (select * from t_base_dict where base_type = 'INVOICE_TYPE') t4 on t1.invoice_type = t4.id
    </select>

    <select id="selectById" resultMap="CrmInvoiceResult">
        select t1.*,
               t2.customer_name,
               t3.name      as contract_name,
               t3.num       as contract_code,
               t4.base_desc as invoice_type_name
        from (
                 select id,
                        invoice_apply_number,
                        customer_id,
                        contract_id,
                        invoice_money,
                        invoice_date,
                        invoice_type,
                        remark,
                        title_type,
                        invoice_title,
                        tax_number,
                        deposit_bank,
                        deposit_account,
                        deposit_address,
                        telephone,
                        contacts_name,
                        contacts_mobile,
                        contacts_address,
                        examine_record_id,
                        check_status,
                        owner_user_id,
                        invoice_number,
                        real_invoice_date,
                        logistics_number,
                        invoice_status,
                        create_user_id,
                        create_time,
                        update_time,
                        batch_id
                 from t_crm_invoice
                 where id = #{id}
             ) t1
                 left join t_crm_customer t2 on t1.customer_id = t2.id
                 left join t_crm_contract t3 on t1.contract_id = t3.id
                 left join (select * from t_base_dict where base_type = 'INVOICE_TYPE') t4 on t1.invoice_type = t4.id
    </select>
</mapper>

