package com.boyo.wms.mapper;

import java.util.List;

import com.boyo.wms.entity.WmsStock;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.wms.vo.StockWarnVO;
import com.boyo.wms.vo.WmsStockVO;


/**
 * 库存管理Mapper接口
 *
 * <AUTHOR>
 */
public interface WmsStockMapper extends BaseMapper<WmsStock> {

    /**
     * 查询库存管理列表
     *
     * @param wmsStock 库存管理
     * @return WmsStock集合
     */
    List<WmsStockVO> selectWmsStockList(WmsStockVO wmsStock);

    List<WmsStockVO> selectWmsStockByMateriel(WmsStock wmsStock);

    /**
     * 查询库存预警信息
     * @return
     */
    List<StockWarnVO> selectStockWarn();

}
