package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipMaintRecord;
import com.boyo.eam.domain.VO.EquipMaintRecordTaskVO;

import java.util.List;

/**
 * 维保记录表(EquipMaintRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-19 14:56:16
 */
public interface EquipMaintRecordMapper extends BaseMapper<EquipMaintRecord>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipMaintRecord 实例对象
     * @return 对象列表
     */
    List<EquipMaintRecord> selectEquipMaintRecordList(EquipMaintRecord equipMaintRecord);

    /**
     * 维保记录任务查询
     * @param equipMaintRecordDetailVO
     * @return
     */
    List<EquipMaintRecordTaskVO> selectTask(EquipMaintRecordTaskVO equipMaintRecordDetailVO);
}

