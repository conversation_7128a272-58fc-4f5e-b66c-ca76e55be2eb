package com.boyo.mes.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 生产工单执行(ProductOrderDetail)实体类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Data
@TableName(value = "t_product_order_detail")
public class ProductOrderDetail implements Serializable {
    private static final long serialVersionUID = 670980904895548262L;
            
    @TableId
    private Integer id;
    
    /**
    * 订单id
    */
    @TableField(value="order_id")
    private Integer orderId;
    /**
    * 工序id
    */
    @TableField(value="process_id")
    private Integer processId;
    /**
    * 设备id
    */
    @TableField(value="equipment_id")
    private Integer equipmentId;
    /**
    * 用户id
    */
    @TableField(value="user_id")
    private Long userId;
    /**
    * 用户名
    */
    @TableField(value="user_name")
    private String userName;
    /**
    * 开始时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(value="start_time")
    private Date startTime;
    /**
    * 结束时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(value="end_time")
    private Date endTime;

    @TableField(value = "task_id")
    private Integer taskId;

    @TableField(value = "status")
    private Integer status;

    /***
     *  procuct_num实际生产数量
     */
    @TableField(value =" product_num")
    private Integer productNum;
    /**
     * 设备名称
     */
    @TableField(exist = false)
    private String equipmentName;

    /**
     * 工序名称
     */
    @TableField(exist = false)
    private String processName;

    /**
     * 报工数量
     */
    @TableField(exist = false)
    private Integer reportNum;

    /**
     * 废品量
     */
    @TableField(exist = false)
    private Integer wasteNum;

    @TableField(exist = false)
    private String orderNum;

    @TableField(exist = false)
    private String productionName;

    @TableField(exist = false)
    private String customerName;
    @TableField(exist = false)
    private String defectCause;
    @TableField(exist = false)
    private String attachments;
    @TableField(exist = false)
    private String taskName;
    @TableField(exist = false)
    private String taskNum;
    /***
     * 暂停时间
     */
    @TableField(value = "stop_time")
    private Date stopTime;

    /***
     * 暂停时产量
     */
    @TableField(value = "stop_number")
    private Integer stopNumber;
}
