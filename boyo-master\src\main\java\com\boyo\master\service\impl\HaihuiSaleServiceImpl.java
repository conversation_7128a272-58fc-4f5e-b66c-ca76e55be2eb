package com.boyo.master.service.impl;

import com.boyo.master.domain.HaihuiSale;
import com.boyo.master.mapper.HaihuiSaleMapper;
import com.boyo.master.service.IHaihuiSaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class HaihuiSaleServiceImpl implements IHaihuiSaleService
{
    @Autowired
    private HaihuiSaleMapper haihuiSaleMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param htNo 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public HaihuiSale selectHaihuiSaleByHtNo(String htNo)
    {
        return haihuiSaleMapper.selectHaihuiSaleByHtNo(htNo);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param haihuiSale 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<HaihuiSale> selectHaihuiSaleList(HaihuiSale haihuiSale)
    {
        return haihuiSaleMapper.selectHaihuiSaleList(haihuiSale);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param haihuiSale 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertHaihuiSale(HaihuiSale haihuiSale)
    {
        return haihuiSaleMapper.insertHaihuiSale(haihuiSale);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param haihuiSale 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateHaihuiSale(HaihuiSale haihuiSale)
    {
        return haihuiSaleMapper.updateHaihuiSale(haihuiSale);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param htNos 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteHaihuiSaleByHtNos(String[] htNos)
    {
        return haihuiSaleMapper.deleteHaihuiSaleByHtNos(htNos);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param htNo 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteHaihuiSaleByHtNo(String htNo)
    {
        return haihuiSaleMapper.deleteHaihuiSaleByHtNo(htNo);
    }
}
