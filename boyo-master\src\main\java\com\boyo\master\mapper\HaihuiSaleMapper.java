package com.boyo.master.mapper;

import com.boyo.master.domain.HaihuiSale;

import java.util.List;

public interface HaihuiSaleMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param htNo 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public HaihuiSale selectHaihuiSaleByHtNo(String htNo);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param haihuiSale 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<HaihuiSale> selectHaihuiSaleList(HaihuiSale haihuiSale);

    /**
     * 新增【请填写功能名称】
     *
     * @param haihuiSale 【请填写功能名称】
     * @return 结果
     */
    public int insertHaihuiSale(HaihuiSale haihuiSale);

    /**
     * 修改【请填写功能名称】
     *
     * @param haihuiSale 【请填写功能名称】
     * @return 结果
     */
    public int updateHaihuiSale(HaihuiSale haihuiSale);

    /**
     * 删除【请填写功能名称】
     *
     * @param htNo 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteHaihuiSaleByHtNo(String htNo);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param htNos 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHaihuiSaleByHtNos(String[] htNos);
}