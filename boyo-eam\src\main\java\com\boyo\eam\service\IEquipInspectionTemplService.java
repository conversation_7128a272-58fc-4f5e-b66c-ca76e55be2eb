package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipInspectionTempl;

import java.util.List;

/**
 * 点检表(EquipInspectionTempl)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-29 10:21:50
 */
public interface IEquipInspectionTemplService extends IService<EquipInspectionTempl> {

    /**
     * 查询多条数据
     *
     * @param equipInspectionTempl 对象信息
     * @return 对象列表
     */
    List<EquipInspectionTempl> selectEquipInspectionTemplList(EquipInspectionTempl equipInspectionTempl);


}
