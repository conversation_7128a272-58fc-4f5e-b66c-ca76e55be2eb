package com.boyo.view.controller;

import com.boyo.view.entity.GoviewProjectData;
import com.boyo.view.service.IGoviewProjectDataService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 项目数据关联表(GoviewProjectData)表控制层
 *
 * <AUTHOR>
 * @since 2022-12-13 15:15:05
 */
@Api("项目数据关联表")
@RestController
@RequestMapping("/view/goviewProjectData")
@AllArgsConstructor
public class GoviewProjectDataController extends BaseController{
    /**
     * 服务对象
     */
    private final IGoviewProjectDataService goviewProjectDataService;

    /**
     * 查询项目数据关联表列表
     *
     */
    @ApiOperation("查询项目数据关联表列表")
    @GetMapping("/list")
    public TableDataInfo list(GoviewProjectData goviewProjectData) {
        startPage();
        List<GoviewProjectData> list = goviewProjectDataService.selectGoviewProjectDataList(goviewProjectData);
        return getDataTable(list);
    }
    
    /**
     * 获取项目数据关联表详情
     */
    @ApiOperation("获取项目数据关联表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(goviewProjectDataService.getById(id));
    }

    /**
     * 新增项目数据关联表
     */
    @ApiOperation("新增项目数据关联表")
    @PostMapping
    public AjaxResult add(@RequestBody GoviewProjectData goviewProjectData) {
        return toBooleanAjax(goviewProjectDataService.save(goviewProjectData));
    }

    /**
     * 修改项目数据关联表
     */
    @ApiOperation("修改项目数据关联表")
    @PutMapping
    public AjaxResult edit(@RequestBody GoviewProjectData goviewProjectData) {
        return toBooleanAjax(goviewProjectDataService.updateById(goviewProjectData));
    }

    /**
     * 删除项目数据关联表
     */
    @ApiOperation("删除项目数据关联表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(goviewProjectDataService.removeByIds(Arrays.asList(ids)));
    }

}
