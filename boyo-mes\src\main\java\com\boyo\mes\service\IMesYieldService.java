package com.boyo.mes.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.mes.entity.MesYield;
import java.util.List;

/**
 * 产量详情(MesYield)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-05 17:02:39
 */
public interface IMesYieldService extends IService<MesYield> {

    /**
     * 查询多条数据
     *
     * @param mesYield 对象信息
     * @return 对象列表
     */
    List<MesYield> selectMesYieldList(MesYield mesYield);

    /**
     * 查询所有未入库数量
     * @param mesYield
     * @return
     */
    List<MesYield> listLessYield(MesYield mesYield);
    List<MesYield> listYieldByDevice(String rq);


}
