package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.eam.domain.EquipInspectionRecord;
import com.boyo.eam.domain.EquipInspectionSpotItem;
import com.boyo.eam.mapper.EquipInspectionRecordMapper;
import com.boyo.eam.mapper.EquipInspectionSpotItemMapper;
import com.boyo.eam.service.IEquipInspectionRecordService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * (EquipInspectionRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-28 19:30:53
 */
@Service("equipInspectionRecordService")
@AllArgsConstructor
@Tenant
public class EquipInspectionRecordServiceImpl extends ServiceImpl<EquipInspectionRecordMapper, EquipInspectionRecord> implements IEquipInspectionRecordService {
    private final EquipInspectionRecordMapper equipInspectionRecordMapper;
    private final EquipInspectionSpotItemMapper equipInspectionSpotItemMapper;
    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipInspectionRecord> selectEquipInspectionRecordList(EquipInspectionRecord equipInspectionRecord) {
        return equipInspectionRecordMapper.selectEquipInspectionRecordList(equipInspectionRecord);
    }

    @Override
    public Boolean insertBySpotOpenid(String openid) {

        List<EquipInspectionSpotItem> itemList = equipInspectionSpotItemMapper.selectList(
                Wrappers.<EquipInspectionSpotItem>lambdaQuery()
                        .eq(EquipInspectionSpotItem::getEquipInspectionSpotOpenid, openid)
        );
        if (itemList!=null && itemList.size()>0){
            for (EquipInspectionSpotItem item:itemList){
                EquipInspectionRecord record = new EquipInspectionRecord();
                record.setEquipInspectionSpotOpenid(item.getEquipInspectionSpotOpenid());
                record.setEquipInspectionSpotItemOpenid(item.getOpenid());
                record.setOpenid(generateOpenid());
                record.setCreateBy(SecurityUtils.getUsername());
                record.setCreateTime(new Date());
                int num = equipInspectionRecordMapper.insert(record);
                if (num==0){
                    return false;
                }
            }
        }
        return true;
    }


    private String generateOpenid(){
        return UUID.randomUUID().toString().replaceAll("-","");
    }
}
