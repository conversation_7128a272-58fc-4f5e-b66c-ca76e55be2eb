<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.ObjectInfoPropertyMapper">

    <resultMap type="com.boyo.eam.domain.ObjectInfoProperty" id="ObjectInfoPropertyResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="modelOpenid" column="model_openid" />
        <result property="name" column="name" />
        <result property="identifier" column="identifier" />
        <result property="type" column="type" />
        <result property="chmode" column="chmode" />
        <result property="rangeMin" column="range_min" />
        <result property="rangeMax" column="range_max" />
        <result property="unit" column="unit" />
        <result property="parameter" column="parameter" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectObjectInfoPropertyList" parameterType="com.boyo.eam.domain.ObjectInfoProperty" resultMap="ObjectInfoPropertyResult">
        select
          id, openid, model_openid, name, identifier, type, chmode, range_min, range_max, unit, parameter, create_by, create_time, update_by, update_time
        from object_info_property
        <where>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
            <if test="modelOpenid != null and modelOpenid != ''">
                and model_openid = #{modelOpenid}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="identifier != null and identifier != ''">
                and identifier = #{identifier}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="chmode != null">
                and chmode = #{chmode}
            </if>
            <if test="rangeMin != null">
                and range_min = #{rangeMin}
            </if>
            <if test="rangeMax != null">
                and range_max = #{rangeMax}
            </if>
            <if test="unit != null and unit != ''">
                and unit = #{unit}
            </if>
            <if test="parameter != null">
                and parameter = #{parameter}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>
</mapper>

