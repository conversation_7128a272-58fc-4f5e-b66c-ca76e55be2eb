<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.wms.mapper.WmsFlowqcIndexMapper">

    <resultMap type="com.boyo.wms.entity.WmsFlowqcIndex" id="WmsFlowqcIndexResult">
        <result property="id" column="id" />
        <result property="flowId" column="flow_id" />
        <result property="qcName" column="qc_name" />
        <result property="createTime" column="create_time" />
        <result property="createUser" column="create_user" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectWmsFlowqcIndexList" parameterType="com.boyo.wms.entity.WmsFlowqcIndex" resultMap="WmsFlowqcIndexResult">
        select
          id, flow_id, qc_name, create_time, create_user
        from t_wms_flowqc_index
        <where>
            <if test="flowId != null">
                and flow_id = #{flowId}
            </if>
            <if test="qcName != null and qcName != ''">
                and qc_name = #{qcName}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUser != null and createUser != ''">
                and create_user = #{createUser}
            </if>
        </where>
    </select>
</mapper>

