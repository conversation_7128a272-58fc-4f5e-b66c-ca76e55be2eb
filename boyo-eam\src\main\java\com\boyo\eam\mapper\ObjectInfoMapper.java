package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.ObjectInfo;

import java.util.List;

/**
 * 物模型表(ObjectInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
public interface ObjectInfoMapper extends BaseMapper<ObjectInfo>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param objectInfo 实例对象
     * @return 对象列表
     */
    List<ObjectInfo> selectObjectInfoList(ObjectInfo objectInfo);


}

