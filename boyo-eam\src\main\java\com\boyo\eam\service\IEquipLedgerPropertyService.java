package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipLedger;
import com.boyo.eam.domain.EquipLedgerProperty;

import java.util.List;

/**
 * 设备台账属性表(EquipLedgerProperty)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
public interface IEquipLedgerPropertyService extends IService<EquipLedgerProperty> {

    /**
     * 查询多条数据
     *
     * @param equipLedgerProperty 对象信息
     * @return 对象列表
     */
    List<EquipLedgerProperty> selectEquipLedgerPropertyList(EquipLedgerProperty equipLedgerProperty);

    void removeEquipLedgerProperty(EquipLedger equipLedger);

}
