package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmInvoice;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * 发票表(CrmInvoice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-28 13:51:42
 */
@Tenant
public interface CrmInvoiceMapper extends BaseMapper<CrmInvoice>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmInvoice 实例对象
     * @return 对象列表
     */
    List<CrmInvoice> selectCrmInvoiceList(CrmInvoice crmInvoice);


}

