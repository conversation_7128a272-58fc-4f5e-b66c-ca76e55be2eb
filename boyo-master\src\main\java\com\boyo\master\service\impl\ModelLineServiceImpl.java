package com.boyo.master.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.framework.annotation.Tenant;
import com.boyo.master.domain.ModelLine;
import com.boyo.master.mapper.ModelLineMapper;
import com.boyo.master.service.IModelLineService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 工厂模型-产线Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class ModelLineServiceImpl extends ServiceImpl<ModelLineMapper, ModelLine> implements IModelLineService {
    private final ModelLineMapper modelLineMapper;


    /**
     * 查询工厂模型-产线列表
     *
     * @param modelLine 工厂模型-产线
     * @return modelLine 列表
     */
    @Override
    public List<ModelLine> selectModelLineList(ModelLine modelLine) {
        return modelLineMapper.selectModelLineList(modelLine);
    }
}
