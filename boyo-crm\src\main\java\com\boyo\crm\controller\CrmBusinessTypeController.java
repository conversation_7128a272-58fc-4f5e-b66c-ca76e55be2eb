package com.boyo.crm.controller;

import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.crm.entity.CrmBusinessType;
import com.boyo.crm.service.ICrmBusinessTypeService;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

/**
 * 商机状态组类别(CrmBusinessType)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-25 14:53:11
 */
@Api("商机状态组类别")
@RestController
@RequestMapping("/crm/crmBusinessType")
@AllArgsConstructor
public class CrmBusinessTypeController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmBusinessTypeService crmBusinessTypeService;
    private final IEnterpriseUserService enterpriseUserService;


    /**
     * 查询商机状态组类别列表
     *
     */
    @ApiOperation("查询商机状态组类别列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusinessType crmBusinessType) {
        startPage();
        List<CrmBusinessType> list = crmBusinessTypeService.selectCrmBusinessTypeList(crmBusinessType);
        if(list != null && list.size() > 0){
            List<Long> ids = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                ids.add(list.get(i).getCreateUserId());
            }
            List<EnterpriseUser> userList = enterpriseUserService.selectByIds(ids);
            if(userList != null && userList.size() > 0){
                for (int i = 0; i < list.size(); i++) {
                    for (int j = 0; j < userList.size(); j++) {
                        if(list.get(i).getCreateUserId().equals(userList.get(j).getId())){
                            list.get(i).setCreateUserName(userList.get(j).getUserFullName());
                            break;
                        }
                    }
                }
            }
        }
        return getDataTable(list);
    }
    
    /**
     * 获取商机状态组类别详情
     */
    @ApiOperation("获取商机状态组类别详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(crmBusinessTypeService.getById(id));
    }

    /**
     * 新增商机状态组类别
     */
    @ApiOperation("新增商机状态组类别")
    @PostMapping
    public AjaxResult add(@RequestBody CrmBusinessType crmBusinessType) {
        return toBooleanAjax(crmBusinessTypeService.save(crmBusinessType));
    }

    /**
     * 修改商机状态组类别
     */
    @ApiOperation("修改商机状态组类别")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmBusinessType crmBusinessType) {
        return toBooleanAjax(crmBusinessTypeService.updateById(crmBusinessType));
    }

    /**
     * 删除商机状态组类别
     */
    @ApiOperation("删除商机状态组类别")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmBusinessTypeService.removeByIds(Arrays.asList(ids)));
    }

}
