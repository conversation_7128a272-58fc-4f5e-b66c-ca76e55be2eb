package com.boyo.project.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.project.entity.TeamTaskStagesTemplate;
import java.util.List;

/**
 * 任务列表模板表(TeamTaskStagesTemplate)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-08 20:50:50
 */
public interface ITeamTaskStagesTemplateService extends IService<TeamTaskStagesTemplate> {

    /**
     * 查询多条数据
     *
     * @param teamTaskStagesTemplate 对象信息
     * @return 对象列表
     */
    List<TeamTaskStagesTemplate> selectTeamTaskStagesTemplateList(TeamTaskStagesTemplate teamTaskStagesTemplate);


}
