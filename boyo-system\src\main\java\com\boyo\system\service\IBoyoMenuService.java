package com.boyo.system.service;

import java.util.List;

import com.boyo.common.core.domain.entity.SysMenu;
import com.boyo.system.domain.BoyoMenu;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.system.domain.vo.RouterVo;

/**
 * 菜单管理Service接口
 *
 * <AUTHOR>
 */
public interface IBoyoMenuService extends IService<BoyoMenu> {
    /**
     * 根据条件查询查询菜单管理列表
     *
     * @param boyoMenu 菜单管理
     * @return 菜单管理集合
     */
    List<BoyoMenu> selectBoyoMenuList(BoyoMenu boyoMenu);

    List<BoyoMenu> selectMenuTreeByUserId(String systemOpenid,String userOpenid);

    List<RouterVo> buildMenus(List<BoyoMenu> menus);

    List<RouterVo> addSystem(List<RouterVo> routers);
}
