package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备状态表(EquipState)实体类
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Data
@TableName(value = "equip_state")
public class EquipState implements Serializable {
    private static final long serialVersionUID = 899420883766467150L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 类型编号
    */
    @TableField(value="code")
    private String code;
    /**
    * 类型名称
    */
    @TableField(value="name")
    private String name;
    /**
    * 说明
    */
    @TableField(value="remark")
    private String remark;
    /**
    * 图标
    */
    @TableField(value="icon")
    private String icon;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

}
