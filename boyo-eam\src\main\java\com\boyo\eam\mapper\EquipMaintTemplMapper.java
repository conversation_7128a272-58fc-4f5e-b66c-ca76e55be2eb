package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipMaintTempl;

import java.util.List;

/**
 * 设备-维保模板(EquipMaintTempl)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:30
 */
public interface EquipMaintTemplMapper extends BaseMapper<EquipMaintTempl>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipMaintTempl 实例对象
     * @return 对象列表
     */
    List<EquipMaintTempl> selectEquipMaintTemplList(EquipMaintTempl equipMaintTempl);


}

