package com.boyo.iot.service;

import com.boyo.iot.entity.HaihuiDibang;

import java.util.List;

public interface IHaihuiDibangService {

    /**
     * 查询磅单信息列表
     */
    public List<HaihuiDibang> selectHaihuiDibangList(HaihuiDibang haihuiDibang);

    /**
     * 新增磅单信息
     */
    public int insertHaihuiDibang(HaihuiDibang haihuiDibang);

    /**
     * 修改磅单信息
     */
    public int updateHaihuiDibang(HaihuiDibang haihuiDibang);

    /**
     * 批量删除磅单信息
     */
    public int deleteHaihuiDibangByIds(Long[] ids);

    /**
     * 根据ID查询磅单信息
     */
    public HaihuiDibang selectHaihuiDibangById(Long id);
    /**
     * 根据ID查询磅单信息
     */
    public HaihuiDibang selectHaihuiDibangByticketNumber(String ticketNumber);
}
