<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.TFactoryOrderMapper">

    <resultMap type="com.boyo.mes.entity.TFactoryOrder" id="TFactoryOrderMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orderNum" column="order_num" jdbcType="VARCHAR"/>
        <result property="orderName" column="order_name" jdbcType="VARCHAR"/>
        <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
        <result property="number" column="number" jdbcType="INTEGER"/>
        <result property="deliveryTime" column="delivery_time" jdbcType="TIMESTAMP"/>
        <result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
        <result property="orderManager" column="order_manager" jdbcType="VARCHAR"/>
        <result property="unit" column="unit" jdbcType="VARCHAR"/>
        <result property="imageIntroduction" column="image_introduction" jdbcType="VARCHAR"/>
        <result property="attachments" column="attachments" jdbcType="VARCHAR"/>
        <result property="finishNum" column="finish_num" jdbcType="INTEGER"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="deliveryCycle" column="delivery_cycle" jdbcType="INTEGER"/>
        <result property="orderSource" column="order_source" jdbcType="VARCHAR"/>
        <result property="overdue" column="overdue" jdbcType="VARCHAR"/>
        <result property="payMethod" column="pay_method" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="notes" column="notes" jdbcType="VARCHAR"/>
        <result property="workOrderNum" column="work_order_num" jdbcType="INTEGER"/>
        <result property="customerId" column="customer_id" jdbcType="INTEGER"/>
    </resultMap>

    <select id="selectFactoryOrderList" parameterType="com.boyo.mes.entity.TFactoryOrder" resultMap="TFactoryOrderMap">
        select
        * from t_factory_order
        <where>
            <if test="orderNum != null and orderNum != ''">
                and order_num like concat('%', #{orderNum}, '%')
            </if>
            <if test="orderName != null">
                and order_name like concat('%', #{orderName}, '%')
            </if>
            <if test="orderStatus != null ">
                and order_status = #{orderStatus}
            </if>
            <if test="overdue != null ">
                and overdue like concat('%', #{overdue}, '%')
            </if>
            <if test="startTime != null">
                and create_time between  #{startTime} and #{endTime}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>

