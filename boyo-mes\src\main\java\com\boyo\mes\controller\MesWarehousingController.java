package com.boyo.mes.controller;

import com.boyo.mes.entity.MesWarehousing;
import com.boyo.mes.service.IMesWarehousingService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 生产入库记录(MesWarehousing)表控制层
 *
 * <AUTHOR>
 * @since 2023-01-06 10:34:00
 */
@Api("生产入库记录")
@RestController
@RequestMapping("/mes/mesWarehousing")
@AllArgsConstructor
public class MesWarehousingController extends BaseController{
    /**
     * 服务对象
     */
    private final IMesWarehousingService mesWarehousingService;

    /**
     * 查询生产入库记录列表
     *
     */
    @ApiOperation("查询生产入库记录列表")
    @GetMapping("/list")
    public TableDataInfo list(MesWarehousing mesWarehousing) {
        startPage();
        List<MesWarehousing> list = mesWarehousingService.selectMesWarehousingList(mesWarehousing);
        return getDataTable(list);
    }
    
    /**
     * 获取生产入库记录详情
     */
    @ApiOperation("获取生产入库记录详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(mesWarehousingService.getById(id));
    }

    /**
     * 新增生产入库记录
     */
    @ApiOperation("新增生产入库记录")
    @PostMapping
    public AjaxResult add(@RequestBody MesWarehousing mesWarehousing) {
        return toBooleanAjax(mesWarehousingService.save(mesWarehousing));
    }

    /**
     * 修改生产入库记录
     */
    @ApiOperation("修改生产入库记录")
    @PutMapping
    public AjaxResult edit(@RequestBody MesWarehousing mesWarehousing) {
        return toBooleanAjax(mesWarehousingService.updateById(mesWarehousing));
    }

    /**
     * 删除生产入库记录
     */
    @ApiOperation("删除生产入库记录")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(mesWarehousingService.removeByIds(Arrays.asList(ids)));
    }

}
