package com.boyo.crm.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.crm.entity.CrmContract;
import java.util.List;

/**
 * 合同表(CrmContract)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-27 17:04:54
 */
public interface ICrmContractService extends IService<CrmContract> {

    /**
     * 查询多条数据
     *
     * @param crmContract 对象信息
     * @return 对象列表
     */
    List<CrmContract> selectCrmContractList(CrmContract crmContract);

    void update(CrmContract crmContract);


}
