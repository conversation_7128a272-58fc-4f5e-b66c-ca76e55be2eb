package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.ObjectInfo;

import java.util.List;

/**
 * 物模型表(ObjectInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
public interface IObjectInfoService extends IService<ObjectInfo> {

    /**
     * 查询多条数据
     *
     * @param objectInfo 对象信息
     * @return 对象列表
     */
    List<ObjectInfo> selectObjectInfoList(ObjectInfo objectInfo);


}
