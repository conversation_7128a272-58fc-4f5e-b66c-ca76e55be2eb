package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmAction;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * 操作记录表(CrmAction)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-28 18:36:06
 */
@Tenant
public interface CrmActionMapper extends BaseMapper<CrmAction>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmAction 实例对象
     * @return 对象列表
     */
    List<CrmAction> selectCrmActionList(CrmAction crmAction);


}

