package com.boyo.system.service.impl;

import java.util.List;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.system.mapper.TSysSystemMapper;
import com.boyo.system.domain.TSysSystem;
import com.boyo.system.service.ITSysSystemService;

/**
 * 系统管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class TSysSystemServiceImpl extends ServiceImpl<TSysSystemMapper, TSysSystem> implements ITSysSystemService {

    private final TSysSystemMapper tSysSystemMapper;


    /**
     * 查询系统管理列表
     *
     * @param tSysSystem 系统管理
     * @return tSysSystem 列表
     */
    @Override
    public List<TSysSystem> selectTSysSystemList(TSysSystem tSysSystem) {
        return tSysSystemMapper.selectTSysSystemList(tSysSystem);
    }

    @Override
    public List<TSysSystem> findEnterpriseSystem(String enterpriseOpenid) {
        List<TSysSystem> list = tSysSystemMapper.findEnterpriseSystem(enterpriseOpenid);
        return list;
    }

    @Override
    public List<TSysSystem> findUserSystem(String enterpriseOpenid, String userOpenid) {
        List<TSysSystem> list = tSysSystemMapper.findUserSystem(enterpriseOpenid,userOpenid);
        return list;
    }
}
