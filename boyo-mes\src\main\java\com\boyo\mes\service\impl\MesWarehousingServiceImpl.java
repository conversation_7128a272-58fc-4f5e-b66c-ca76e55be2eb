package com.boyo.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.exception.CustomException;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.MesWarehousing;
import com.boyo.mes.mapper.MesWarehousingMapper;
import com.boyo.mes.service.IMesWarehousingService;

import java.util.List;

/**
 * 生产入库记录(MesWarehousing)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-06 10:34:00
 */
@Service("mesWarehousingService")
@AllArgsConstructor
@Tenant
public class MesWarehousingServiceImpl extends ServiceImpl<MesWarehousingMapper, MesWarehousing> implements IMesWarehousingService {
    private final MesWarehousingMapper mesWarehousingMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<MesWarehousing> selectMesWarehousingList(MesWarehousing mesWarehousing) {
        return mesWarehousingMapper.selectMesWarehousingList(mesWarehousing);
    }

    @Override
    public boolean save(MesWarehousing entity) {
        QueryWrapper<MesWarehousing> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rq", entity.getRq()).eq("equipment_id", entity.getEquipmentId())
                .eq("shift", entity.getShift());
        List<MesWarehousing> list = mesWarehousingMapper.selectList(queryWrapper);
        if(list != null && list.size() > 0){
            MesWarehousing temp = list.get(0);
            temp.setInCount(entity.getInCount());
            temp.setScrapCount(entity.getScrapCount());
            return super.updateById(temp);
        }else{
            return super.save(entity);
        }
    }
}
