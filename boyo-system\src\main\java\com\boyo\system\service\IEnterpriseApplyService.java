package com.boyo.system.service;

import java.util.List;

import com.boyo.system.domain.EnterpriseApply;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 申请试用Service接口
 *
 * <AUTHOR>
 */
public interface IEnterpriseApplyService extends IService<EnterpriseApply> {
    /**
     * 根据条件查询查询申请试用列表
     *
     * @param enterpriseApply 申请试用
     * @return 申请试用集合
     */
    List<EnterpriseApply> selectEnterpriseApplyList(EnterpriseApply enterpriseApply);
}
