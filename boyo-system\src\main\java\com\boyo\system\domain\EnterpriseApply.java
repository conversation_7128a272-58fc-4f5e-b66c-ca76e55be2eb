package com.boyo.system.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 申请试用
 * 表名 t_enterprise_apply
 *
 * <AUTHOR>
 */
@ApiModel("申请试用记录")
@Data
@TableName("t_enterprise_apply")
public class EnterpriseApply extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @TableId
    private Long id;
    /**
     * 企业名称
     */
    @ApiModelProperty("企业名称")
    @TableField(value = "enterprise")
    private String enterprise;
    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    @TableField(value = "phone")
    private String phone;
    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    @TableField(value = "username")
    private String username;
    /**
     * 申请时间
     */
    @ApiModelProperty("申请时间")
    @TableField(value = "create_time")
    private Date createTime;
}
