<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipMaintTemplItemDetailMapper">

    <resultMap type="com.boyo.eam.domain.EquipMaintTemplItemDetail" id="EquipMaintTemplItemDetailResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="equipMaintTemplItemOpenid" column="equip_maint_templ_item_openid" />
        <result property="detail" column="detail" />
        <result property="value" column="value" />
        <result property="type" column="type" />
        <result property="maxNum" column="max_num" />
        <result property="minNum" column="min_num" />
        <result property="unit" column="unit" />
        <result property="standard" column="standard" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipMaintTemplItemDetailList" parameterType="com.boyo.eam.domain.EquipMaintTemplItemDetail" resultMap="EquipMaintTemplItemDetailResult">
        select
          id, openid, equip_maint_templ_item_openid, detail, value, type, max_num, min_num, unit, standard, create_by, create_time, update_by, update_time
        from equip_maint_templ_item_detail
        <where>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
            <if test="equipMaintTemplItemOpenid != null and equipMaintTemplItemOpenid != ''">
                and equip_maint_templ_item_openid = #{equipMaintTemplItemOpenid}
            </if>
            <if test="detail != null and detail != ''">
                and detail = #{detail}
            </if>
            <if test="value != null and value != ''">
                and value = #{value}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="maxNum != null">
                and max_num = #{maxNum}
            </if>
            <if test="minNum != null">
                and min_num = #{minNum}
            </if>
            <if test="unit != null and unit != ''">
                and unit = #{unit}
            </if>
            <if test="standard != null and standard != ''">
                and standard = #{standard}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>
</mapper>

