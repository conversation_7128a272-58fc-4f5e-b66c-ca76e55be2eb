package com.boyo.iot.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.iot.entity.Iofault;
import java.util.List;

/**
 * IoT故障清单(Iofault)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-07 15:16:28
 */
public interface IIofaultService extends IService<Iofault> {

    /**
     * 查询多条数据
     *
     * @param iofault 对象信息
     * @return 对象列表
     */
    List<Iofault> selectIofaultList(Iofault iofault);


}
