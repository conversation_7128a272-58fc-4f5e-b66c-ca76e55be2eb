package com.boyo.project.util;

public enum ExecuteStatus {
    WAIT("wait", "未开始"),
    DOING("doing", "进行中"),
    DONE("done", "已完成"),
    PAUSE("pause", "暂停"),
    CANCEL("cancel", "取消"),
    CLOSED("closed", "关闭");

    private String code;
    private String text;

    ExecuteStatus(String code,String text){
        this.code = code;
        this.text = text;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
    public static String getText(String code) {
        for (ExecuteStatus ele : values()) {
            if(ele.getCode().equals(code)) return ele.getText();
        }
        return null;
    }
}
