package com.boyo.project.service.impl;

import cn.hutool.core.util.IdUtil;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.project.entity.TeamProjeclog;
import com.boyo.project.mapper.TeamProjeclogMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.project.entity.TeamTaskFile;
import com.boyo.project.mapper.TeamTaskFileMapper;
import com.boyo.project.service.ITeamTaskFileService;

import java.util.Date;
import java.util.List;

/**
 * (TeamTaskFile)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-17 20:51:36
 */
@Service("teamTaskFileService")
@AllArgsConstructor
public class TeamTaskFileServiceImpl extends ServiceImpl<TeamTaskFileMapper, TeamTaskFile> implements ITeamTaskFileService {
    private final TeamTaskFileMapper teamTaskFileMapper;
    private final TeamProjeclogMapper teamProjeclogMapper;


    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<TeamTaskFile> selectTeamTaskFileList(TeamTaskFile teamTaskFile) {
        teamTaskFile.setDelFlag("0");
        return teamTaskFileMapper.selectTeamTaskFileList(teamTaskFile);
    }

    @Override
    public boolean save(TeamTaskFile entity) {
        entity.setCreateTime(new Date());
        entity.setCreateUser(SecurityUtils.getUserOpenid());
        TeamProjeclog log = new TeamProjeclog();
        log.setCreateTime(new Date());
        log.setRemark("添加附件:【"+entity.getFileName()+"】");
        log.setCode(IdUtil.fastSimpleUUID());
        log.setMemberCode(SecurityUtils.getUserOpenid());
        log.setActionType("task");
        log.setProjectCode(entity.getProjectCode());
        log.setSourceCode(entity.getTaskCode());
        teamProjeclogMapper.insert(log);
        return super.save(entity);
    }

    @Override
    public boolean updateById(TeamTaskFile entity) {
        entity.setDelFlag("1");
        entity.setDelTime(new Date());
        entity.setCreateTime(new Date());
        entity.setCreateUser(SecurityUtils.getUserOpenid());
        TeamProjeclog log = new TeamProjeclog();
        log.setCreateTime(new Date());
        log.setRemark("移除附件:【"+entity.getFileName()+"】");
        log.setCode(IdUtil.fastSimpleUUID());
        log.setMemberCode(SecurityUtils.getUserOpenid());
        log.setActionType("task");
        log.setProjectCode(entity.getProjectCode());
        log.setSourceCode(entity.getTaskCode());
        teamProjeclogMapper.insert(log);
        return super.updateById(entity);
    }
}
