import request from '@/utils/request'

const prefix = '/${module}'

// 查询(WmsFlowqcDetail)列表
export function listWmsFlowqcDetail(query) {
  return request({
    url: prefix + '/wmsFlowqcDetail/list',
    method: 'get',
    params: query,
  })
}

// 查询(WmsFlowqcDetail)详细
export function getWmsFlowqcDetail(id) {
  return request({
    url: prefix + '/wmsFlowqcDetail/' + id,
    method: 'get',
  })
}

// 新增(WmsFlowqcDetail)
export function addWmsFlowqcDetail(data) {
  return request({
    url: prefix + '/wmsFlowqcDetail',
    method: 'post',
    data: data,
  })
}

// 修改(WmsFlowqcDetail)
export function updateWmsFlowqcDetail(data) {
  return request({
    url: prefix + '/wmsFlowqcDetail',
    method: 'put',
    data: data,
  })
}

// 删除(WmsFlowqcDetail)
export function delWmsFlowqcDetail(id) {
  return request({
    url: prefix + '/wmsFlowqcDetail/' + id,
    method: 'delete',
  })
}
