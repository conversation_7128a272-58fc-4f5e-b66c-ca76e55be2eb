<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.system.mapper.TSysSystemMapper">

    <resultMap type="com.boyo.system.domain.TSysSystem" id="TSysSystemResult">
        <result property="id" column="id"/>
        <result property="systemOpenid" column="system_openid"/>
        <result property="categoryOpenid" column="category_openid"/>
        <result property="systemName" column="system_name"/>
        <result property="systemCode" column="system_code"/>
        <result property="systemUrl" column="system_url"/>
        <result property="systemOpentype" column="system_opentype"/>
        <result property="systemIcon" column="system_icon"/>
        <result property="systemImg" column="system_img"/>
        <result property="systemApply" column="system_apply"/>
        <result property="systemPrice" column="system_price"/>
        <result property="systemDesc" column="system_desc"/>
        <result property="systemStatus" column="system_status"/>
        <result property="systemOrder" column="system_order"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="pcShow" column="pc_show"/>
    </resultMap>

    <sql id="selectTSysSystemVo">
        select id,
               system_openid,
               category_openid,
               system_name,
               system_code,
               system_url,
               system_opentype,
               system_icon,
               system_img,
               system_apply,
               system_price,
               system_desc,
               system_status,
               system_order,
               create_time,
               update_time,
               pc_show
        from t_sys_system
    </sql>

    <select id="selectTSysSystemList" parameterType="com.boyo.system.domain.TSysSystem" resultMap="TSysSystemResult">
        <include refid="selectTSysSystemVo"/>
        <where>
            <if test="systemOpenid != null  and systemOpenid != ''">
                and system_openid = #{systemOpenid}
            </if>
            <if test="categoryOpenid != null  and categoryOpenid != ''">
                and category_openid = #{categoryOpenid}
            </if>
            <if test="systemName != null  and systemName != ''">
                and system_name like concat('%', #{systemName}, '%')
            </if>
            <if test="systemCode != null  and systemCode != ''">
                and system_code = #{systemCode}
            </if>
            <if test="systemUrl != null  and systemUrl != ''">
                and system_url = #{systemUrl}
            </if>
            <if test="systemOpentype != null  and systemOpentype != ''">
                and system_opentype = #{systemOpentype}
            </if>
            <if test="systemIcon != null  and systemIcon != ''">
                and system_icon = #{systemIcon}
            </if>
            <if test="systemImg != null  and systemImg != ''">
                and system_img = #{systemImg}
            </if>
            <if test="systemApply != null  and systemApply != ''">
                and system_apply = #{systemApply}
            </if>
            <if test="systemPrice != null ">
                and system_price = #{systemPrice}
            </if>
            <if test="systemDesc != null  and systemDesc != ''">
                and system_desc = #{systemDesc}
            </if>
            <if test="systemStatus != null  and systemStatus != ''">
                and system_status = #{systemStatus}
            </if>
            <if test="systemOrder != null ">
                and system_order = #{systemOrder}
            </if>
        </where>
    </select>

    <select id="findEnterpriseSystem" resultMap="TSysSystemResult">
        select *
        from t_sys_system
        where system_apply = '0'
           or system_openid in
              (select system_openid from t_sys_enterprise_authority where enterprise_openid = #{enterpriseOpenid})
order by system_order
    </select>

    <select id="findUserSystem" resultMap="TSysSystemResult">
        SELECT
            *
        FROM
            t_sys_system
        WHERE
                system_openid IN (
                SELECT DISTINCT
                    ( system_openid )
                FROM
                    t_sys_menu
                WHERE
                        menu_id IN (
                        SELECT DISTINCT
                            ( t3.function_openid )
                        FROM
                            t_enterprise_role t1,
                            t_enterprise_user_role t2,
                            t_enterprise_role_function t3
                        WHERE
                            t1.role_openid = t2.role_openid
                          AND t3.role_openid = t1.role_openid
                            and t2.user_openid = #{userOpenid}
                    )
            )
          and (system_apply = '0'
            or system_openid in
               (select system_openid from t_sys_enterprise_authority where enterprise_openid = #{enterpriseOpenid}))
        order by system_order
    </select>
</mapper>
