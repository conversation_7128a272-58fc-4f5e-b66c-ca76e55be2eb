package com.boyo.mes.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 生产产品表(MesProduction)实体类
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
@Data
@TableName(value = "t_mes_production")
public class MesProduction implements Serializable {
    private static final long serialVersionUID = -71154895738033847L;
            
    @TableId
    private Integer id;
    
    /**
    * 产品名称
    */
    @TableField(value="prodution_name")
    private String produtionName;
    /**
    * 产品编码
    */
    @TableField(value="production_code")
    private String productionCode;
    /**
    * 创建时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @TableField(value="create_time",fill = FieldFill.INSERT)
    private Date createTime;
    /**
    * 创建人id
    */
    @TableField(value="create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 部门id
    */
    @TableField(value="dept_id",fill = FieldFill.INSERT)
    private String deptId;

}
