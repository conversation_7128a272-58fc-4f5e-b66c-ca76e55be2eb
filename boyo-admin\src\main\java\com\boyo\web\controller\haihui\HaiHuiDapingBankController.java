package com.boyo.web.controller.haihui;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.text.Convert;
import com.boyo.master.entity.PurchaseOrders;
import com.boyo.master.entity.SaleOrder;
import com.boyo.master.service.impl.PurchaseOrdersServiceImpl;
import com.boyo.master.service.impl.SaleOrderServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@RestController
@RequestMapping("/haihui/screen/bank")
public class HaiHuiDapingBankController {
    @Autowired
    private SaleOrderServiceImpl saleOrderService;
    @Autowired
    private PurchaseOrdersServiceImpl purchaseOrdersService;

    private String enterpriseId = "1845014766829256705";

    /**
     * 获取销售订单信息
     * 该方法用于获取过去六个月的销售订单统计数据，包括每月的订单数量和订单金额
     *
     * @return 返回包含月份列表、订单数量列表和订单金额列表的JSON对象
     */
    @GetMapping("/saleOrder/info")
    private AjaxResult saleOrderInfo() {
        // 创建一个JSONObject对象来存储最终的响应数据
        JSONObject ans = new JSONObject();
        // 创建一个分页对象，用于处理大量数据查询
        IPage page = new Page<>(1, 10000);
        // 创建一个参数映射，用于传递查询条件
        Map<String, String> params = new HashMap<>();
        // 将当前企业的ID放入参数映射中
        params.put("enterpriseId", enterpriseId);
        // 创建三个列表，分别用于存储月份、订单数量和订单金额
        List<String> monthList = new ArrayList();
        List<Integer> orderCountList = new ArrayList();
        List<String> orderMoneyList = new ArrayList();

        // 获取当前日期时间
        final DateTime dateNow = DateUtil.date();
        try {
            // 循环6次，分别获取过去六个月的数据
            for (int i = 0; i < 6; i++) {
                // 将月份添加到月份列表中
                monthList.add(DateUtil.format(DateUtil.offsetMonth(dateNow, -i), "MM") + "月");
                // 计算当前月的开始和结束日期时间
                final DateTime start = DateUtil.beginOfMonth(DateUtil.offsetMonth(dateNow, -i));
                final DateTime end = DateUtil.endOfMonth(DateUtil.offsetMonth(dateNow, -i));
                // 将开始和结束日期放入参数映射中
                params.put("dbilldateStart", DateUtil.format(start, "yyyy-MM-dd"));
                params.put("dbilldateEnd", DateUtil.format(end, "yyyy-MM-dd"));
                params.put("childrenCompany","10103");

                // 调用服务方法获取当前月的销售订单数据
                final Object dataAssetList = saleOrderService.getDataAssetList(page, params);
                // 获取查询结果记录
                List records = page.getRecords();

                params.put("childrenCompany","10170");
                Object dataAssetList2= saleOrderService.getDataAssetList(page, params);
                records.addAll(page.getRecords());

                // 计算订单数量并添加到订单数量列表中
                int total = records.size();
                orderCountList.add(total);
                // 计算订单总金额并添加到订单金额列表中
                double totalMoney = 0;
                for (Object record : records) {
                    SaleOrder oneSaleOrder = (SaleOrder) record;
                    totalMoney += oneSaleOrder.getNsummny().doubleValue();
                }
                orderMoneyList.add(String.format("%.3f", totalMoney / 10000));//单位：万元
            }
        } catch (Exception e) {
            // 捕获并处理异常
//            return AjaxResult.error("获取销售订单信息失败：" + e.getMessage());
        }
        // 将月份列表、订单数量列表和订单金额列表放入响应数据中
        ans.put("monthList", monthList);
        ans.put("orderCountList", orderCountList);
        ans.put("orderMoneyList", orderMoneyList);
        // 返回成功响应
        return AjaxResult.success(ans);

    }


    /**
     * 获取采购订单信息
     * 该方法用于获取过去六个月的采购订单统计数据，包括每月的采购订单数量和金额
     *
     * @return 返回包含月份、采购订单数量和金额的JSON对象
     */
    @GetMapping("/purchaseOrder/info")
    private AjaxResult purchaseOrderInfo() {
        try {
            // 创建JSON对象用于存储最终的响应数据
            JSONObject ans = new JSONObject();

            // 初始化列表，用于存储月份、采购订单数量和金额
            List<String> monthList = new ArrayList<>();
            List<Integer> purchaseCountList = new ArrayList<>();
            List<String> purchaseMoneyList = new ArrayList<>();

            // 获取当前日期时间
            final DateTime dateNow = DateUtil.date();

            // 循环获取过去六个月的数据
            for (int i = 0; i < 6; i++) {
                // 计算当前月份的开始和结束日期
                DateTime start = DateUtil.beginOfMonth(DateUtil.offsetMonth(dateNow, -i));
                DateTime end = DateUtil.endOfMonth(DateUtil.offsetMonth(dateNow, -i));

                // 添加月份到列表中
                monthList.add(DateUtil.format(start, "MM") + "月");

                // 创建参数映射，用于传递查询条件
                Map<String, String> params = new HashMap<>();
                params.put("enterpriseId", enterpriseId);
                params.put("dbilldateStart", DateUtil.format(start, "yyyy-MM-dd"));
                params.put("dbilldateEnd", DateUtil.format(end, "yyyy-MM-dd"));
                params.put("childrenCompany","10103");

                IPage page = new Page<>(1, 10000); // 调整分页大小

                // 调用服务方法获取数据资产列表
                purchaseOrdersService.getDataAssetList(page, params);
                List<PurchaseOrders> records = page.getRecords();

                params.put("childrenCompany","10170");
                Object dataAssetList2= purchaseOrdersService.getDataAssetList(page, params);
                records.addAll(page.getRecords());

                int total = records.size();
                purchaseCountList.add(total);
                double totalMoney = 0;
                // 遍历记录计算总金额
                for (PurchaseOrders record : records) {
                    totalMoney += record.getNoriginaltaxpricemny().doubleValue();
                }
                purchaseMoneyList.add(String.format("%.3f", totalMoney / 10000));//单位：百万元
            }

            // 将统计数据放入JSON对象中
            ans.put("monthList", monthList);
            ans.put("purchaseCountList", purchaseCountList);
            ans.put("purchaseMoneyList", purchaseMoneyList);

            // 返回成功响应
            return AjaxResult.success(ans);
        } catch (Exception e) {
            // 异常处理
            e.printStackTrace();
            return AjaxResult.error("获取采购订单信息失败：" + e.getMessage());
        }
    }
}
