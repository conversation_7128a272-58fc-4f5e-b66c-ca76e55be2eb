package com.boyo.view.service.impl;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.view.entity.GoviewProject;
import com.boyo.view.mapper.GoviewProjectMapper;
import com.boyo.view.service.IGoviewProjectService;
import java.util.List;

/**
 * 项目表(GoviewProject)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-13 15:15:05
 */
@Service("goviewProjectService")
@AllArgsConstructor
public class GoviewProjectServiceImpl extends ServiceImpl<GoviewProjectMapper, GoviewProject> implements IGoviewProjectService {
    private final GoviewProjectMapper goviewProjectMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<GoviewProject> selectGoviewProjectList(GoviewProject goviewProject) {
        return goviewProjectMapper.selectGoviewProjectList(goviewProject);
    }

}
