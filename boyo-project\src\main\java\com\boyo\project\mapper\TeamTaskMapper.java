package com.boyo.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.project.entity.TeamTask;
import java.util.List;

/**
 * 任务表(TeamTask)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-13 13:05:06
 */
public interface TeamTaskMapper extends BaseMapper<TeamTask>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param teamTask 实例对象
     * @return 对象列表
     */
    List<TeamTask> selectTeamTaskList(TeamTask teamTask);
    List<TeamTask> listMyTask(String assignTo);


}

