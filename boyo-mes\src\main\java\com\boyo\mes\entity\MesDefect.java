package com.boyo.mes.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("t_mes_defect")
public class MesDefect {

    @TableId(type = IdType.AUTO)
    private Long Id;

    @TableField(value = "defect_cause")
    private String defectCause;
}
