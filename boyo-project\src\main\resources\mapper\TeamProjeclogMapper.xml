<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.project.mapper.TeamProjeclogMapper">

    <resultMap type="com.boyo.project.entity.TeamProjeclog" id="TeamProjeclogResult">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="memberCode" column="member_code"/>
        <result property="content" column="content"/>
        <result property="remark" column="remark"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time"/>
        <result property="sourceCode" column="source_code"/>
        <result property="actionType" column="action_type"/>
        <result property="toMemberCode" column="to_member_code"/>
        <result property="isComment" column="is_comment"/>
        <result property="projectCode" column="project_code"/>
        <result property="icon" column="icon"/>
        <result property="isRobot" column="is_robot"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectTeamProjeclogList" parameterType="com.boyo.project.entity.TeamProjeclog"
            resultMap="TeamProjeclogResult">
        select
        id, code, member_code, content, remark, type, create_time, source_code, action_type, to_member_code, is_comment,
        project_code, icon, is_robot
        from team_project_log
        <where>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="memberCode != null and memberCode != ''">
                and member_code = #{memberCode}
            </if>
            <if test="content != null and content != ''">
                and content = #{content}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="sourceCode != null and sourceCode != ''">
                and source_code = #{sourceCode}
            </if>
            <if test="actionType != null and actionType != ''">
                and action_type = #{actionType}
            </if>
            <if test="toMemberCode != null and toMemberCode != ''">
                and to_member_code = #{toMemberCode}
            </if>
            <if test="isComment != null">
                and is_comment = #{isComment}
            </if>
            <if test="projectCode != null and projectCode != ''">
                and project_code = #{projectCode}
            </if>
            <if test="icon != null and icon != ''">
                and icon = #{icon}
            </if>
            <if test="isRobot != null">
                and is_robot = #{isRobot}
            </if>
        </where>
        order by id desc
    </select>
</mapper>

