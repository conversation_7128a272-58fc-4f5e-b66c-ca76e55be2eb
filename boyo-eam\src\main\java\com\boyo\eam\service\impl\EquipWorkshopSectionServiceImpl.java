package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipWorkshopSection;
import com.boyo.eam.mapper.EquipWorkshopSectionMapper;
import com.boyo.eam.service.IEquipWorkshopSectionService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 工段表(EquipWorkshopSection)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-21 15:23:24
 */
@Service("equipWorkshopSectionService")
@AllArgsConstructor
@Tenant
public class EquipWorkshopSectionServiceImpl extends ServiceImpl<EquipWorkshopSectionMapper, EquipWorkshopSection> implements IEquipWorkshopSectionService {
    private final EquipWorkshopSectionMapper equipWorkshopSectionMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipWorkshopSection> selectEquipWorkshopSectionList(EquipWorkshopSection equipWorkshopSection) {
        return equipWorkshopSectionMapper.selectEquipWorkshopSectionList(equipWorkshopSection);
    }

}
