package com.boyo.view.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.view.entity.GoviewProjectData;
import java.util.List;

/**
 * 项目数据关联表(GoviewProjectData)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-12-13 15:15:05
 */
public interface GoviewProjectDataMapper extends BaseMapper<GoviewProjectData>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param goviewProjectData 实例对象
     * @return 对象列表
     */
    List<GoviewProjectData> selectGoviewProjectDataList(GoviewProjectData goviewProjectData);


}

