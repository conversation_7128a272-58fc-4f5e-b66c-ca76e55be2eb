package com.boyo.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.domain.IotEquipmentProp;
import com.boyo.iot.entity.FaultProp;
import com.boyo.iot.vo.IoTAttrVO;

import java.util.List;


/**
 * 设备属性管理Mapper接口
 *
 * <AUTHOR>
 */
@Tenant
public interface IotEquipmentPropMapper extends BaseMapper<IotEquipmentProp> {

    /**
     * 查询设备属性管理列表
     *
     * @param iotEquipmentProp 设备属性管理
     * @return IotEquipmentProp集合
     */
    List<IoTAttrVO> selectIotEquipmentPropList(IotEquipmentProp iotEquipmentProp);

    List<FaultProp> selectEquipmentFaultProp(String code);
}
