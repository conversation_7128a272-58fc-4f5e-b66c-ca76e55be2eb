package com.boyo.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.project.entity.TeamTaskFile;
import java.util.List;

/**
 * (TeamTaskFile)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-17 21:39:42
 */
public interface TeamTaskFileMapper extends BaseMapper<TeamTaskFile>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param teamTaskFile 实例对象
     * @return 对象列表
     */
    List<TeamTaskFile> selectTeamTaskFileList(TeamTaskFile teamTaskFile);


}

