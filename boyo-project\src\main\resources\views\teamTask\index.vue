<template>
  <base-page :config="config">
    <span slot="operation" slot-scope="text, record">
      <a @click="$refs.createForm.handleUpdate($event,record.id)">
      <a-icon type="edit" />{{ $t('app.global.edit') }}
      </a>
      <a-divider type="vertical" />
      <a @click="handleDelete(record.id)"> <a-icon type="delete" />{{ $t('app.global.delete') }}</a>
    </span>
    <!-- 弹窗 -->
    <create-form ref="createForm" :statusOptions="statusOptions" @ok="getList" />
  </base-page>
</template>

<script>
import CreateForm from './modules/CreateForm'
import { delTeamTask, listTeamTask } from '@/api/teamTask'

export default {
  components: {
    CreateForm,
  },
  data() {
    return {
      // 页面加载状态
      loading: false,
      // 数据列表
      list: [],
      // 表格数据总数
      total: 0,
      // 状态数据字典
      statusOptions: [],
    }
  },
  async created() {
    this.getList()
  },
  computed: {
    config() {
      return {
        loading: this.loading,
        query: {
          onQuery: this.getList,
          items: [
                        { label: '编号', name: 'code' },
                        { label: '', name: 'name' },
                        { label: '紧急程度', name: 'pri' },
                        { label: '执行状态', name: 'executeStatus' },
                        { label: '详情', name: 'description' },
                        { label: '创建人', name: 'createBy' },
                        { label: '创建日期', name: 'createTime' },
                        { label: '指派给谁', name: 'assignTo' },
                        { label: '回收站', name: 'deleted' },
                        { label: '任务列表', name: 'stageCode' },
                        { label: '任务标签', name: 'taskTag' },
                        { label: '是否完成', name: 'done' },
                        { label: '开始时间', name: 'beginTime' },
                        { label: '截止时间', name: 'endTime' },
                        { label: '提醒时间', name: 'remindTime' },
                        { label: '父任务id', name: 'pcode' },
                        { label: '排序', name: 'sort' },
                        { label: '点赞数', name: 'like' },
                        { label: '收藏数', name: 'star' },
                        { label: '删除时间', name: 'deletedTime' },
                        { label: '是否隐私模式', name: 'private' },
                        { label: '任务id编号', name: 'idNum' },
                        { label: '上级任务路径', name: 'path' },
                        { label: '进度百分比', name: 'schedule' },
                        { label: '版本id', name: 'versionCode' },
                        { label: '版本库id', name: 'featuresCode' },
                        { label: '预估工时', name: 'workTime' },
                        { label: ''执行状态。0：未开始，1：已完成，2：进行中，3：挂起，4：测试中'', name: 'status' },
                        { label: '', name: 'liked' },
                      ],
        },
        action: {
          add: () => this.$refs.createForm.handleAdd(),
          delete: this.handleDelete,
        },
        table: {
          total: this.total,
          list: this.list,
          columns: [
               { 
                title: '编号', 
                dataIndex: 'code',
                align: 'center',
            },
                { 
                title: '', 
                dataIndex: 'name',
                align: 'center',
            },
                { 
                title: '紧急程度', 
                dataIndex: 'pri',
                align: 'center',
            },
                { 
                title: '执行状态', 
                dataIndex: 'executeStatus',
                align: 'center',
            },
                { 
                title: '详情', 
                dataIndex: 'description',
                align: 'center',
            },
                { 
                title: '创建人', 
                dataIndex: 'createBy',
                align: 'center',
            },
                { 
                title: '创建日期', 
                dataIndex: 'createTime',
                align: 'center',
            },
                { 
                title: '指派给谁', 
                dataIndex: 'assignTo',
                align: 'center',
            },
                { 
                title: '回收站', 
                dataIndex: 'deleted',
                align: 'center',
            },
                { 
                title: '任务列表', 
                dataIndex: 'stageCode',
                align: 'center',
            },
                { 
                title: '任务标签', 
                dataIndex: 'taskTag',
                align: 'center',
            },
                { 
                title: '是否完成', 
                dataIndex: 'done',
                align: 'center',
            },
                { 
                title: '开始时间', 
                dataIndex: 'beginTime',
                align: 'center',
            },
                { 
                title: '截止时间', 
                dataIndex: 'endTime',
                align: 'center',
            },
                { 
                title: '提醒时间', 
                dataIndex: 'remindTime',
                align: 'center',
            },
                { 
                title: '父任务id', 
                dataIndex: 'pcode',
                align: 'center',
            },
                { 
                title: '排序', 
                dataIndex: 'sort',
                align: 'center',
            },
                { 
                title: '点赞数', 
                dataIndex: 'like',
                align: 'center',
            },
                { 
                title: '收藏数', 
                dataIndex: 'star',
                align: 'center',
            },
                { 
                title: '删除时间', 
                dataIndex: 'deletedTime',
                align: 'center',
            },
                { 
                title: '是否隐私模式', 
                dataIndex: 'private',
                align: 'center',
            },
                { 
                title: '任务id编号', 
                dataIndex: 'idNum',
                align: 'center',
            },
                { 
                title: '上级任务路径', 
                dataIndex: 'path',
                align: 'center',
            },
                { 
                title: '进度百分比', 
                dataIndex: 'schedule',
                align: 'center',
            },
                { 
                title: '版本id', 
                dataIndex: 'versionCode',
                align: 'center',
            },
                { 
                title: '版本库id', 
                dataIndex: 'featuresCode',
                align: 'center',
            },
                { 
                title: '预估工时', 
                dataIndex: 'workTime',
                align: 'center',
            },
                { 
                title: ''执行状态。0：未开始，1：已完成，2：进行中，3：挂起，4：测试中'', 
                dataIndex: 'status',
                align: 'center',
            },
                { 
                title: '', 
                dataIndex: 'liked',
                align: 'center',
            },
                        {
              title: this.$t('app.global.operation'),
              dataIndex: 'operation',
              scopedSlots: { customRender: 'operation' },
              align: 'center',
            },
          ],
        },
      }
    },
  },
  methods: {
    /**
     * 查询任务表列表
     */
    async getList(queryParam) {
      this.loading = true
      if (queryParam !== undefined) {
        this.queryParam = queryParam
      }
      this.loading = true
      const response = await listTeamTask(this.queryParam)
      this.list = response.rows
      this.total = response.total
      this.loading = false
    },
    /**
     * 删除按钮操作
     */
    handleDelete(id) {
      
      const ids = id || this.ids
      this.$alert.confirm({
        content: this.$t('app.global.delete.content'),
        onOk: async () => {
        await delTeamTask(ids)
        this.getList()
        this.$alert.success(this.$t('app.global.delete.success'))
        },
      })
    },
  },
}
</script>
}
