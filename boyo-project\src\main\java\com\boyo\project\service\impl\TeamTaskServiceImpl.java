package com.boyo.project.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.project.entity.TeamProjeclog;
import com.boyo.project.entity.TeamProject;
import com.boyo.project.mapper.TeamProjeclogMapper;
import com.boyo.project.mapper.TeamProjectMapper;
import com.boyo.project.util.ExecuteStatus;
import com.boyo.project.util.TaskPriority;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.project.entity.TeamTask;
import com.boyo.project.mapper.TeamTaskMapper;
import com.boyo.project.service.ITeamTaskService;

import java.util.Date;
import java.util.List;

/**
 * 任务表(TeamTask)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-13 13:05:06
 */
@Service("teamTaskService")
@AllArgsConstructor
@Tenant
public class TeamTaskServiceImpl extends ServiceImpl<TeamTaskMapper, TeamTask> implements ITeamTaskService {
    private final TeamTaskMapper teamTaskMapper;
    private final TeamProjeclogMapper teamProjeclogMapper;
    private final TeamProjectMapper projectMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<TeamTask> selectTeamTaskList(TeamTask teamTask) {
        return teamTaskMapper.selectTeamTaskList(teamTask);
    }

    @Override
    public List<TeamTask> listMyTask() {
//        QueryWrapper<TeamTask> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("assign_to",SecurityUtils.getUserOpenid()).eq("done","0").isNotNull("begin_time").orderByDesc("id");
        return teamTaskMapper.listMyTask(SecurityUtils.getUserOpenid());
    }

    @Override
    public boolean save(TeamTask entity) {
        QueryWrapper<TeamTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_code", entity.getProjectCode()).eq("stage_code", entity.getStageCode());
        entity.setSort(teamTaskMapper.selectCount(queryWrapper));
        entity.setCode(IdUtil.fastSimpleUUID());
        QueryWrapper<TeamProject> projectQueryWrapper = new QueryWrapper<>();
        projectQueryWrapper.eq("code",entity.getProjectCode());
        TeamProject project = projectMapper.selectOne(projectQueryWrapper);
        if(ObjectUtil.isNotNull(project)){
            entity.setAssignTo(project.getWhiteList());
        }
        TeamProjeclog log = new TeamProjeclog();
        log.setCreateTime(new Date());
        log.setRemark("创建任务");
        log.setCode(IdUtil.fastSimpleUUID());
        log.setMemberCode(SecurityUtils.getUserOpenid());
        log.setActionType("task");
        log.setSourceCode(entity.getCode());
        log.setProjectCode(entity.getProjectCode());
        teamProjeclogMapper.insert(log);
        return super.save(entity);
    }

    @Override
    public boolean updateById(TeamTask entity) {
        TeamTask task = super.getById(entity.getId());
        if (!task.getAssignTo().equals(entity.getAssignTo())) {
//            变更负责人
            changeAssignTo(task, entity);
        }
        if (!task.getName().equals(entity.getName())) {
//            变更任务名称
            changeName(task, entity);
        }
        if (!task.getPri().equals(entity.getPri())) {
//            变更优先级
            changePriotiry(task,entity);
        }
//        if (!task.getDone().equals(entity.getDone())) {
////            变更完成状态
//            changeDone(task,entity);
//        }
        if (!task.getExecuteStatus().equals(entity.getExecuteStatus())) {
//            变更执行状态
            changeExecuteStatus(task,entity);
        }
        return super.updateById(entity);
    }

    private void changeAssignTo(TeamTask old, TeamTask now) {
        TeamProjeclog log = new TeamProjeclog();
        log.setCreateTime(new Date());
        log.setRemark("变更负责人");
        log.setCode(IdUtil.fastSimpleUUID());
        log.setMemberCode(SecurityUtils.getUserOpenid());
        log.setActionType("task");
        log.setProjectCode(now.getProjectCode());
        log.setToMemberCode(now.getAssignTo());
        log.setSourceCode(now.getCode());
        teamProjeclogMapper.insert(log);
    }
    private void changePriotiry(TeamTask old, TeamTask now) {
        TeamProjeclog log = new TeamProjeclog();
        log.setCreateTime(new Date());
        log.setRemark("变更项目优先级:由【"+ TaskPriority.getText(old.getPri()) +"】变更为【"+TaskPriority.getText(now.getPri())+"】");
        log.setCode(IdUtil.fastSimpleUUID());
        log.setMemberCode(SecurityUtils.getUserOpenid());
        log.setActionType("task");
        log.setProjectCode(now.getProjectCode());
        log.setToMemberCode(now.getAssignTo());
        log.setSourceCode(now.getCode());
        teamProjeclogMapper.insert(log);
    }
    private void changeName(TeamTask old, TeamTask now) {
        TeamProjeclog log = new TeamProjeclog();
        log.setCreateTime(new Date());
        log.setRemark("变更任务名称:由【" + old.getName() + "】变更为【" + now.getName() + "】");
        log.setCode(IdUtil.fastSimpleUUID());
        log.setMemberCode(SecurityUtils.getUserOpenid());
        log.setActionType("task");
        log.setProjectCode(now.getProjectCode());
        log.setSourceCode(now.getCode());
        teamProjeclogMapper.insert(log);
    }

    private void changeDone(TeamTask old, TeamTask now) {
        TeamProjeclog log = new TeamProjeclog();
        log.setCreateTime(new Date());
        log.setRemark("任务执行状态:由【" + (old.getExecuteStatus().equals("0")?"未完成":"已完成") + "】变更为【" + (now.getExecuteStatus().equals("0")?"未完成":"已完成") + "】");
        log.setCode(IdUtil.fastSimpleUUID());
        log.setMemberCode(SecurityUtils.getUserOpenid());
        log.setActionType("task");
        log.setProjectCode(now.getProjectCode());
        log.setSourceCode(now.getCode());
        teamProjeclogMapper.insert(log);
    }

    private void changeExecuteStatus(TeamTask old, TeamTask now) {
        TeamProjeclog log = new TeamProjeclog();
        log.setCreateTime(new Date());
        log.setRemark("任务执行状态:由【" + ExecuteStatus.getText(old.getExecuteStatus()) + "】变更为【" + ExecuteStatus.getText(now.getExecuteStatus()) + "】");
        log.setCode(IdUtil.fastSimpleUUID());
        log.setMemberCode(SecurityUtils.getUserOpenid());
        log.setActionType("task");
        log.setProjectCode(now.getProjectCode());
        log.setSourceCode(now.getCode());
        teamProjeclogMapper.insert(log);
    }

}
