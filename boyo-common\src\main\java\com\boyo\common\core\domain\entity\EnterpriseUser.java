package com.boyo.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 企业用户管理
 * 表名 t_enterprise_user
 *
 * <AUTHOR>
 */
@ApiModel("企业用户表")
@Data
@TableName("t_enterprise_user")
public class EnterpriseUser extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    @TableField(value = "enterprise_openid")
    private String enterpriseOpenid;
    /**
     * 部门id
     */
    @ApiModelProperty("部门id")
    @TableField(value = "department_openid")
    private String departmentOpenid;
    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    @TableField(value = "user_openid")
    private String userOpenid;
    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    @TableField(value = "user_name")
    private String userName;
    /**
     * 密码
     */
    @ApiModelProperty("密码")
    @TableField(value = "user_password")
    private String userPassword;
    /**
     * 用户头像
     */
    @ApiModelProperty("用户头像")
    @TableField(value = "user_img")
    private String userImg;
    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    @TableField(value = "user_phone")
    private String userPhone;
    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    @TableField(value = "user_email")
    private String userEmail;
    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    @TableField(value = "user_full_name")
    private String userFullName;
    /**
     * 工号
     */
    @ApiModelProperty("工号")
    @TableField(value = "user_emp_code")
    private String userEmpCode;
    /**
     * 是否为管理员 0 否 1 是
     */
    @ApiModelProperty("是否为管理员 0 否 1 是")
    @TableField(value = "user_admin")
    private Long userAdmin;
    /**
     * 微信号
     */
    @ApiModelProperty("微信号")
    @TableField(value = "user_wechat")
    private String userWechat;
    /**
     * 支付宝
     */
    @ApiModelProperty("支付宝")
    @TableField(value = "user_alipay")
    private String userAlipay;
    /**
     * 钉钉
     */
    @ApiModelProperty("钉钉")
    @TableField(value = "user_dingding")
    private String userDingding;
    /**
     * 状态  1启用 0停用
     */
    @ApiModelProperty("状态  1启用 0停用")
    @TableField(value = "user_status")
    private String userStatus;
    /**
     * 是否需要修改密码 1是0否
     */
    @ApiModelProperty("是否需要修改密码 1是0否")
    @TableField(value = "need_modify_password")
    private String needModifyPassword;
    /**
     * 最后登录时间
     */
    @ApiModelProperty("最后登录时间")
    @TableField(value = "last_login_time")
    private Date lastLoginTime;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(value = "update_time")
    private Date updateTime;

    @ApiModelProperty("用户角色列表")
    @TableField(exist = false)
    private List<String> roleList;
}
