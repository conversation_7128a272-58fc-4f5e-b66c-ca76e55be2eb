package com.boyo.crm.controller;

import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.crm.entity.CrmReceivables;
import com.boyo.crm.service.ICrmReceivablesService;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

/**
 * 回款表(CrmReceivables)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-28 10:22:52
 */
@Api("回款表")
@RestController
@RequestMapping("/crm/crmReceivables")
@AllArgsConstructor
public class CrmReceivablesController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmReceivablesService crmReceivablesService;
    private final IEnterpriseUserService enterpriseUserService;

    /**
     * 查询回款表列表
     *
     */
    @ApiOperation("查询回款表列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmReceivables crmReceivables) {
        startPage();
        List<CrmReceivables> list = crmReceivablesService.selectCrmReceivablesList(crmReceivables);
        if(list != null && list.size() > 0){
            List<Long> ids = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                ids.add(list.get(i).getOwnerUserId());
            }
            List<EnterpriseUser> userList = enterpriseUserService.selectByIds(ids);
            if(userList != null && userList.size() > 0){
                for (int i = 0; i < list.size(); i++) {
                    for (int j = 0; j < userList.size(); j++) {
                        if(list.get(i).getOwnerUserId().equals(userList.get(j).getId())){
                            list.get(i).setOwnerUserName(userList.get(j).getUserFullName());
                            break;
                        }
                    }
                }
            }
        }
        return getDataTable(list);
    }
    
    /**
     * 获取回款表详情
     */
    @ApiOperation("获取回款表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(crmReceivablesService.getById(id));
    }

    /**
     * 新增回款表
     */
    @ApiOperation("新增回款表")
    @PostMapping
    public AjaxResult add(@RequestBody CrmReceivables crmReceivables) {
        return toBooleanAjax(crmReceivablesService.save(crmReceivables));
    }

    /**
     * 修改回款表
     */
    @ApiOperation("修改回款表")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmReceivables crmReceivables) {
        return toBooleanAjax(crmReceivablesService.updateById(crmReceivables));
    }

    /**
     * 删除回款表
     */
    @ApiOperation("删除回款表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmReceivablesService.removeByIds(Arrays.asList(ids)));
    }

}
