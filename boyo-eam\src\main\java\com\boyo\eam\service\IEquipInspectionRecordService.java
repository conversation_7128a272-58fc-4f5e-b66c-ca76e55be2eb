package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipInspectionRecord;

import java.util.List;

/**
 * (EquipInspectionRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-28 19:30:53
 */
public interface IEquipInspectionRecordService extends IService<EquipInspectionRecord> {

    /**
     * 查询多条数据
     *
     * @param equipInspectionRecord 对象信息
     * @return 对象列表
     */
    List<EquipInspectionRecord> selectEquipInspectionRecordList(EquipInspectionRecord equipInspectionRecord);


    /**
     * 通过点检openid插入记录
     * @param openid
     * @return
     */
    Boolean insertBySpotOpenid(String openid);

}
