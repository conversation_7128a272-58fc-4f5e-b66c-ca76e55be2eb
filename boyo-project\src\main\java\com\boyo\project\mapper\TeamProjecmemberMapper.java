package com.boyo.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.project.entity.TeamProjecmember;
import java.util.List;

/**
 * 项目-成员表(TeamProjecmember)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-10 16:43:19
 */
public interface TeamProjecmemberMapper extends BaseMapper<TeamProjecmember>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param teamProjecmember 实例对象
     * @return 对象列表
     */
    List<TeamProjecmember> selectTeamProjecmemberList(TeamProjecmember teamProjecmember);


}

