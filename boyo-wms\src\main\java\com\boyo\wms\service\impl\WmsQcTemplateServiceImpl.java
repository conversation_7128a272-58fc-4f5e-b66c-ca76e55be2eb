package com.boyo.wms.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.wms.entity.WmsQcTemplate;
import com.boyo.wms.mapper.WmsQcTemplateMapper;
import com.boyo.wms.service.IWmsQcTemplateService;
import java.util.List;

/**
 * Wms质检模板(WmsQcTemplate)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-08 15:21:02
 */
@Service("wmsQcTemplateService")
@AllArgsConstructor
@Tenant
public class WmsQcTemplateServiceImpl extends ServiceImpl<WmsQcTemplateMapper, WmsQcTemplate> implements IWmsQcTemplateService {
    private final WmsQcTemplateMapper wmsQcTemplateMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<WmsQcTemplate> selectWmsQcTemplateList(WmsQcTemplate wmsQcTemplate) {
        return wmsQcTemplateMapper.selectWmsQcTemplateList(wmsQcTemplate);
    }

}
