package com.boyo.master.service;

import java.util.List;

import com.boyo.master.domain.TMaterial;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.master.vo.MaterialVO;

/**
 * 主数据-物料Service接口
 *
 * <AUTHOR>
 */
public interface ITMaterialService extends IService<TMaterial> {
    /**
     * 根据条件查询查询主数据-物料列表
     *
     * @param tMaterial 主数据-物料
     * @return 主数据-物料集合
     */
    List<MaterialVO> selectTMaterialList(TMaterial tMaterial);

    boolean checkExist(TMaterial tMaterial);
}
