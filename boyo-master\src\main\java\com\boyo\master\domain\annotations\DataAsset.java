package com.boyo.master.domain.annotations;

import java.lang.annotation.*;

/**
 * 数据资产信息标记
 * 具体的分组交给前端去拆分，这里只提供中英文映射关系
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface DataAsset {
    /**
     * 数据资产中文名称
     * @return
     */
    String value() default "";

    /**
     * 数据资产类型编码，可作为URL路径使用
     * @return
     */
    String type() default "";

    /**
     * 是否使用默认导出Excel todo 这个什么作用来着？
     * @return
     */
    boolean defaultExportExcelMethod() default true;

    /**
     * 导出Excel对应的类
     * @return
     */
    Class<?> entityClass() default Object.class;

    /**
     * 是否使用同一个定时任务
     * @return
     */
    boolean commonScheduledTask() default true;

    /**
     * 定时任务是否需要在其他数据资产的定时任务执行后执行
     * @return
     */
    Class<?> dependsOn() default Object.class;
}
