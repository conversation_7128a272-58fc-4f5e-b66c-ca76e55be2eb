package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipType;

import java.util.List;

/**
 * 设备类型表(EquipType)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
public interface EquipTypeMapper extends BaseMapper<EquipType>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipType 实例对象
     * @return 对象列表
     */
    List<EquipType> selectEquipTypeList(EquipType equipType);


}

