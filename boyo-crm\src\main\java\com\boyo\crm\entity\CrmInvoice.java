package com.boyo.crm.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import com.boyo.framework.annotation.PropertyMsg;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 发票表(CrmInvoice)实体类
 *
 * <AUTHOR>
 * @since 2022-03-28 13:51:42
 */
@Data
@TableName(value = "t_crm_invoice")
public class CrmInvoice extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = -79765698820031327L;
        /**
    * 发票id
    */    
    @TableId
    private Integer id;
    
    /**
    * 发票申请编号
    */
    @TableField(value="invoice_apply_number")
    @PropertyMsg(value="发票申请编号")
    private String invoiceApplyNumber;
    /**
    * 客户id
    */
    @TableField(value="customer_id")
    @PropertyMsg(value="客户",type = "customer")
    private Integer customerId;
    /**
    * 合同id
    */
    @TableField(value="contract_id")
    @PropertyMsg(value="合同",type="contract")
    private Integer contractId;
    /**
    * 开票金额
    */
    @TableField(value="invoice_money")
    @PropertyMsg(value="开票金额")
    private Double invoiceMoney;
    /**
    * 开票日期
    */
    @TableField(value="invoice_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @PropertyMsg(value="开票日期")
    private Date invoiceDate;
    /**
    * 开票类型
    */
    @TableField(value="invoice_type")
    @PropertyMsg(value="开票类型",type = "base")
    private Integer invoiceType;
    /**
    * 备注
    */
    @TableField(value="remark")
    private String remark;
    /**
    * 抬头类型 1单位 2个人
    */
    @TableField(value="title_type")
    private Integer titleType;
    /**
    * 开票抬头
    */
    @TableField(value="invoice_title")
    @PropertyMsg(value="开票抬头")
    private String invoiceTitle;
    /**
    * 纳税识别号
    */
    @TableField(value="tax_number")
    @PropertyMsg(value="纳税识别号")
    private String taxNumber;
    /**
    * 开户行
    */
    @TableField(value="deposit_bank")
    @PropertyMsg(value="开户行")
    private String depositBank;
    /**
    * 开户账户
    */
    @TableField(value="deposit_account")
    @PropertyMsg(value="开户账户")
    private String depositAccount;
    /**
    * 开票地址
    */
    @TableField(value="deposit_address")
    @PropertyMsg(value="开票地址")
    private String depositAddress;
    /**
    * 电话
    */
    @TableField(value="telephone")
    @PropertyMsg(value="电话")
    private String telephone;
    /**
    * 联系人名称
    */
    @TableField(value="contacts_name")
    @PropertyMsg(value="联系人名称")
    private String contactsName;
    /**
    * 联系方式
    */
    @TableField(value="contacts_mobile")
    @PropertyMsg(value="联系方式")
    private String contactsMobile;
    /**
    * 邮寄地址
    */
    @TableField(value="contacts_address")
    @PropertyMsg(value="邮寄地址")
    private String contactsAddress;
    /**
    * 审批记录id
    */
    @TableField(value="examine_record_id")
    private Integer examineRecordId;
    /**
    * 审核状态 0待审核、1通过、2拒绝、3审核中、4撤回
    */
    @TableField(value="check_status")
    private Integer checkStatus;
    /**
    * 负责人id
    */
    @TableField(value="owner_user_id")
    private Long ownerUserId;
    /**
    * 发票号码
    */
    @TableField(value="invoice_number")
    @PropertyMsg(value="发票号码")
    private String invoiceNumber;
    /**
    * 实际开票日期
    */
    @TableField(value="real_invoice_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @PropertyMsg(value="实际开票日期")
    private Date realInvoiceDate;
    /**
    * 物流单号
    */
    @TableField(value="logistics_number")
    @PropertyMsg(value="物流单号")
    private String logisticsNumber;
    /**
    * 开票状态 0 未开票， 1 已开票
    */
    @TableField(value="invoice_status")
    private Integer invoiceStatus;
    /**
    * 创建人id
    */
    @TableField(value="create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 创建时间
    */
    @TableField(value="create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 更新时间
    */
    @TableField(value="update_time",fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
    * 批次id
    */
    @TableField(value="batch_id")
    private String batchId;

    /**
     * 客户名称
     */
    @TableField(exist = false)
    private String customerName;

    /**
     * 合同名称
     */
    @TableField(exist = false)
    private String contractName;

    /**
     * 合同编码
     */
    @TableField(exist = false)
    private String contractCode;

    /**
     * 开票类型
     */
    @TableField(exist = false)
    private String invoiceTypeName;
}
