package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 维保记录表(EquipMaintRecord)实体类
 *
 * <AUTHOR>
 * @since 2021-11-19 14:56:15
 */
@Data
@TableName(value = "equip_maint_record")
public class EquipMaintRecord implements Serializable {
    private static final long serialVersionUID = 917932014682939423L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 对应equip_maint_task表的openid
    */
    @TableField(value="equip_maint_task_openid")
    private String equipMaintTaskOpenid;
    /**
    * 对应equip_maint_task_item表的openid
    */
    @TableField(value="equip_maint_task_item_openid")
    private String equipMaintTaskItemOpenid;
    /**
    * 对应equip_maint_task_item_detail表的openid
    */
    @TableField(value="equip_maint_task_item_detail_openid")
    private String equipMaintTaskItemDetailOpenid;
    /**
    * 对应equip_maint_fix表里的openid
    */
    @TableField(value="equip_maint_fix_openid")
    private String equipMaintFixOpenid;
    /**
    * 维保开始时间
    */
    @TableField(value="begin_date")
    private Date beginDate;
    /**
    * 维保结束时间
    */
    @TableField(value="end_date")
    private Date endDate;
    /**
    * 处理时长(分钟)
    */
    @TableField(value="handle_minute")
    private Integer handleMinute;
    /**
    * 判定结果：0不通过，1通过
    */
    @TableField(value="pass")
    private String pass;
    /**
    * 维保说明
    */
    @TableField(value="maint_remark")
    private String maintRemark;
    /**
    * 响应时长(分钟)
    */
    @TableField(value="response_minute")
    private Integer responseMinute;
    /**
     * 实际值
     */
    @TableField(value="actual_value")
    private Double actualValue;
    /**
    * 任务类型：0维保计划，1设备报修
    */
    @TableField(value="type")
    private String type;
    /**
     * 照片：关联t_media表的id，多个用逗号隔开
     */
    @TableField(value="media_id")
    private String mediaId;


    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

    /** 额外字段 */
    //维保主表
    @TableField(exist = false)
    private String planCode;//计划单号
    @TableField(exist = false)
    private String taskCode;//派工单号
    @TableField(exist = false)
    private String equipTypeName;//设备类型名
    @TableField(exist = false)
    private String equipCode;//设备编号
    @TableField(exist = false)
    private String equipName;//设备名称
    @TableField(exist = false)
    private String taskState;//任务状态
    @TableField(exist = false)
    private String staffName;//保养人员

    //维修
    @TableField(exist = false)
    private String fixCreateBy;//上报人
    @TableField(exist = false)
    private String fixCreateTime;//上报时间
    @TableField(exist = false)
    private String fixRemark;//上报说明
    @TableField(exist = false)
    private String fixLocation;//位置

    //专用于查询
    @TableField(exist = false)
    private Date upSTime; //上报开始时间
    @TableField(exist = false)
    private Date upETime; //上报结束时间
    @TableField(exist = false)
    private Date maintSTime; //维保开始时间
    @TableField(exist = false)
    private Date maintETime; //维保结束时间

    /** 额外字段 */
}
