<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.NoticeMapper">

    <resultMap type="com.boyo.master.entity.Notice" id="NoticeResult">
        <result property="id" column="id"/>
        <result property="noticeTitle" column="notice_title"/>
        <result property="noticeContent" column="notice_content"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectNoticeList" parameterType="com.boyo.master.entity.Notice" resultMap="NoticeResult">
        select
        id, notice_title, notice_content, create_by, create_time
        from t_notice
        <where>
            <if test="noticeTitle != null and noticeTitle != ''">
                and notice_title = #{noticeTitle}
            </if>
            <if test="noticeContent != null and noticeContent != ''">
                and notice_content = #{noticeContent}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
        order by id desc
    </select>
</mapper>

