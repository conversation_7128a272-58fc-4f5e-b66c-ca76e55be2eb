<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.ScreenWorkshopMapper">

    <resultMap type="com.boyo.master.domain.ScreenWorkshop" id="ScreenWorkshopResult">
        <result property="workshopId"    column="workshop_id"    />
        <result property="workshopName"    column="workshop_name"    />
        <result property="workshopCode"    column="workshop_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectScreenWorkshopVo">
        select workshop_id, workshop_name, workshop_code, create_by, create_time, update_by, update_time from screen_workshop
    </sql>

    <select id="selectScreenWorkshopList" parameterType="com.boyo.master.domain.ScreenWorkshop" resultMap="ScreenWorkshopResult">
        <include refid="selectScreenWorkshopVo"/>
        <where>
            <if test="workshopName != null  and workshopName != ''"> and workshop_name like concat('%', #{workshopName}, '%')</if>
            <if test="workshopCode != null  and workshopCode != ''"> and workshop_code = #{workshopCode}</if>
        </where>
    </select>

    <select id="selectScreenWorkshopByWorkshopId" parameterType="Long" resultMap="ScreenWorkshopResult">
        <include refid="selectScreenWorkshopVo"/>
        where workshop_id = #{workshopId}
    </select>

    <insert id="insertScreenWorkshop" parameterType="com.boyo.master.domain.ScreenWorkshop" useGeneratedKeys="true" keyProperty="workshopId">
        insert into screen_workshop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workshopName != null">workshop_name,</if>
            <if test="workshopCode != null">workshop_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workshopName != null">#{workshopName},</if>
            <if test="workshopCode != null">#{workshopCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateScreenWorkshop" parameterType="com.boyo.master.domain.ScreenWorkshop">
        update screen_workshop
        <trim prefix="SET" suffixOverrides=",">
            <if test="workshopName != null">workshop_name = #{workshopName},</if>
            <if test="workshopCode != null">workshop_code = #{workshopCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where workshop_id = #{workshopId}
    </update>

    <delete id="deleteScreenWorkshopByWorkshopId" parameterType="Long">
        delete from screen_workshop where workshop_id = #{workshopId}
    </delete>

    <delete id="deleteScreenWorkshopByWorkshopIds" parameterType="String">
        delete from screen_workshop where workshop_id in
        <foreach item="workshopId" collection="array" open="(" separator="," close=")">
            #{workshopId}
        </foreach>
    </delete>
</mapper>