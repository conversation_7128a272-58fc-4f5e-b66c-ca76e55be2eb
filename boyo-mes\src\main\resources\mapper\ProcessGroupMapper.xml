<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.ProcessGroupMapper">

    <resultMap type="com.boyo.mes.entity.ProcessGroup" id="ProcessGroupResult">
        <result property="id" column="id" />
        <result property="groupName" column="group_name" />
        <result property="groupStatus" column="group_status" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectProcessGroupList" parameterType="com.boyo.mes.entity.ProcessGroup" resultMap="ProcessGroupResult">
        select
          id, group_name, group_status
        from t_process_group
        <where>
            <if test="groupName != null and groupName != ''">
                and group_name like concat('%', #{groupName}, '%')
            </if>
            <if test="groupStatus != null and groupStatus != ''">
                and group_status = #{groupStatus}
            </if>
        </where>
    </select>
</mapper>

