package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmContract;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * 合同表(CrmContract)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-27 17:04:54
 */
@Tenant
public interface CrmContractMapper extends BaseMapper<CrmContract>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmContract 实例对象
     * @return 对象列表
     */
    List<CrmContract> selectCrmContractList(CrmContract crmContract);


}

