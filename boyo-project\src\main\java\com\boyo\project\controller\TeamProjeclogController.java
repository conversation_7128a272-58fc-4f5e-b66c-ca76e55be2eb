package com.boyo.project.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.project.entity.TeamProjeclog;
import com.boyo.project.service.ITeamProjeclogService;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 项目日志表(TeamProjeclog)表控制层
 *
 * <AUTHOR>
 * @since 2022-02-17 19:34:44
 */
@Api("项目日志表")
@RestController
@RequestMapping("/project/teamProjeclog")
@AllArgsConstructor
public class TeamProjeclogController extends BaseController{
    /**
     * 服务对象
     */
    private final ITeamProjeclogService teamProjeclogService;

    private final IEnterpriseUserService enterpriseUserService;


    /**
     * 查询项目日志表列表
     *
     */
    @ApiOperation("查询项目日志表列表")
    @GetMapping("/list")
    public AjaxResult list(TeamProjeclog teamProjeclog) {
        List<TeamProjeclog> list = teamProjeclogService.selectTeamProjeclogList(teamProjeclog);
        List<String> codes = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            codes.add(list.get(i).getMemberCode());
        }
        codes = codes.stream().distinct().collect(Collectors.toList());
        if(codes.size() > 0){
            QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("user_openid",codes);
            List<EnterpriseUser> userList = enterpriseUserService.list(queryWrapper);
            for (int i = 0; i < list.size(); i++) {
                for (int j = 0; j < userList.size(); j++) {
                    if(list.get(i).getMemberCode().equals(userList.get(j).getUserOpenid())){
                        list.get(i).setMemberName(userList.get(j).getUserFullName());
                        continue;
                    }
                }
            }
        }
        return AjaxResult.success(list);
    }
    
    /**
     * 获取项目日志表详情
     */
    @ApiOperation("获取项目日志表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(teamProjeclogService.getById(id));
    }

    /**
     * 新增项目日志表
     */
    @ApiOperation("新增项目日志表")
    @PostMapping
    public AjaxResult add(@RequestBody TeamProjeclog teamProjeclog) {
        return toBooleanAjax(teamProjeclogService.save(teamProjeclog));
    }

    /**
     * 修改项目日志表
     */
    @ApiOperation("修改项目日志表")
    @PutMapping
    public AjaxResult edit(@RequestBody TeamProjeclog teamProjeclog) {
        return toBooleanAjax(teamProjeclogService.updateById(teamProjeclog));
    }

    /**
     * 删除项目日志表
     */
    @ApiOperation("删除项目日志表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(teamProjeclogService.removeByIds(Arrays.asList(ids)));
    }

}
