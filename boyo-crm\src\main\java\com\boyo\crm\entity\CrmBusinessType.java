package com.boyo.crm.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 商机状态组类别(CrmBusinessType)实体类
 *
 * <AUTHOR>
 * @since 2022-03-25 14:53:11
 */
@Data
@TableName(value = "t_crm_business_type")
public class CrmBusinessType extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = 237541399906479987L;
            
    @TableId
    private Integer id;
    
    /**
    * 状态组名称
    */
    @TableField(value="name")
    private String name;
    /**
    * 创建人ID
    */
    @TableField(value="create_user_id", fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 创建时间
    */
    @TableField(value="create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 更新时间
    */
    @TableField(value="update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
    * 1启用0禁用
    */
    @TableField(value="status")
    private Integer status;

    @TableField(exist = false)
    private String createUserName;

}
