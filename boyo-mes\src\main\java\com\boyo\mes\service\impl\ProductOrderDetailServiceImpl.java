package com.boyo.mes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.mes.entity.WorkReport;
import com.boyo.mes.mapper.WorkReportMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.ProductOrderDetail;
import com.boyo.mes.mapper.ProductOrderDetailMapper;
import com.boyo.mes.service.IProductOrderDetailService;

import java.util.Date;
import java.util.List;

/**
 * 生产工单执行(ProductOrderDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Service("productOrderDetailService")
@AllArgsConstructor
@Tenant
public class ProductOrderDetailServiceImpl extends ServiceImpl<ProductOrderDetailMapper, ProductOrderDetail> implements IProductOrderDetailService {
    private final ProductOrderDetailMapper productOrderDetailMapper;
    private final WorkReportMapper workReportMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<ProductOrderDetail> selectProductOrderDetailList(ProductOrderDetail productOrderDetail) {
        return productOrderDetailMapper.selectProductOrderDetailList(productOrderDetail);
    }

    @Override
    public List<ProductOrderDetail> listCurrentOrder() {
        return productOrderDetailMapper.listCurrentOrder(SecurityUtils.getUserId());
    }

    @Override
    public void completeOrder(ProductOrderDetail productOrderDetail) {
        ProductOrderDetail temp = productOrderDetailMapper.selectById(productOrderDetail.getId());
        temp.setEndTime(new Date());
        temp.setStatus(2);
        productOrderDetailMapper.updateById(temp);
        WorkReport report = new WorkReport();
        BeanUtil.copyProperties(temp,report,"id");
        report.setReportNum(productOrderDetail.getReportNum());
        report.setWasteNum(productOrderDetail.getWasteNum());
        report.setReportTime(new Date());
        report.setQualityInspector(SecurityUtils.getUsername());
        report.setTaskId(productOrderDetail.getTaskId());
        report.setAttachments(productOrderDetail.getAttachments());
        report.setDefectCause(productOrderDetail.getDefectCause());
        workReportMapper.insert(report);
    }

    @Override
    public boolean save(ProductOrderDetail entity) {
        QueryWrapper<ProductOrderDetail> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("equipment_id", entity.getEquipmentId())
                .eq("user_id", SecurityUtils.getUserId()).eq("process_id", entity.getProcessId()).isNull("end_time").isNull("stop_time");
        if (productOrderDetailMapper.selectCount(queryWrapper1) > 0) {
            throw new CustomException("该设备正在执行任务");
        }
        QueryWrapper<ProductOrderDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_id", entity.getOrderId()).eq("equipment_id", entity.getEquipmentId())
                .eq("user_id", SecurityUtils.getUserId()).eq("process_id", entity.getProcessId()).isNull("end_time").isNotNull("stop_time").ne("status",1);
        if (productOrderDetailMapper.selectCount(queryWrapper) > 0) {
            throw new CustomException("您正在执行该工单");
        }
        entity.setStartTime(new Date());
        entity.setUserId(SecurityUtils.getUserId());
        entity.setUserName(SecurityUtils.getUsername());
        return super.save(entity);
    }
}
