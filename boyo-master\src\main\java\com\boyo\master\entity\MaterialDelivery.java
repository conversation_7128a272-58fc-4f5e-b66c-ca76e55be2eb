package com.boyo.master.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.thoughtworks.xstream.converters.time.LocalDateConverter;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 材料出库单
 * @TableName bu_enterprise_data_asset_material_delivery
 */
@TableName(value ="bu_enterprise_data_asset_material_delivery")
@Data
public class MaterialDelivery extends DataAssetBase implements Serializable {
    /**
     * 库存组织编号
     */
    @JSONField(name = "BODYCODE")
    @TableField(value = "bodycode")
    private String bodycode;

    /**
     * 库存组织名称
     */
    @JSONField(name = "BODYNAME")
    @TableField(value = "bodyname")
    private String bodyname;

    /**
     * 单据类型名称
     */
    @JSONField(name = "VBILLTYPENAME")
    @TableField(value = "vbilltypename")
    private String vbilltypename;

    /**
     * 存货编码
     */
    @JSONField(name = "INVCODE")
    @TableField(value = "invcode")
    private String invcode;

    /**
     * 存货名称
     */
    @JSONField(name = "INVNAME")
    @TableField(value = "invname")
    private String invname;

    /**
     * 规格
     */
    @JSONField(name = "INVSPEC")
    @TableField(value = "invspec")
    private String invspec;

    /**
     * 型号
     */
    @JSONField(name = "INVTYPE")
    @TableField(value = "invtype")
    private String invtype;

    /**
     * 实出数量
     */
    @JSONField(name = "NOUTNUM")
    @TableField(value = "noutnum")
    private BigDecimal noutnum;

    /**
     * 单据日期
     */
    @JSONField(name = "DBILLDATE",format="yyyy-MM-dd")
    @TableField(value = "dbilldate")
    private Date dbilldate;

    /**
     * 单价
     */
    @JSONField(name = "NPRICE")
    @TableField(value = "nprice")
    private BigDecimal nprice;

    /**
     * 金额
     */
    @JSONField(name = "NMNY")
    @TableField(value = "nmny")
    private BigDecimal nmny;

    /**
     * 成本对象ID
     */
    @JSONField(name = "CCOSTOBJECT")
    @TableField(value = "ccostobject")
    private String ccostobject;

    /**
     * 项目ID
     */
    @JSONField(name = "CPROJECTID")
    @TableField(value = "cprojectid")
    private String cprojectid;

    /**
     * 单据编码
     */
    @JSONField(name = "VBILLCODE")
    @TableField(value = "vbillcode")
    private String vbillcode;

    /**
     * 源头单据编码
     */
    @JSONField(name = "VFIRSTBILLCODE")
    @TableField(value = "vfirstbillcode")
    private String vfirstbillcode;

    @TableField(value = "children_company")
    private String childrenCompany;

    @JSONField(name = "DACCOUNTDATE",format="yyyy-MM-dd")
    @TableField(value = "daccountdate")
    private LocalDate daccountdate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
