package com.boyo.iot.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * IoT实时数据(IorealData)实体类
 *
 * <AUTHOR>
 * @since 2022-03-31 17:07:23
 */
@Data
@TableName(value = "iot_real_data")
public class IorealData implements Serializable {
    private static final long serialVersionUID = 648706935838713204L;
            
    @TableId(type = IdType.INPUT)
    private String key;
    
    /**
    * 设备编码
    */
    @TableField
    private String deviceCode;
    /**
    * 属性编码
    */
    @TableField
    private String tag;

    @TableField(value="val")
    private String val;
    /**
    * 更新时间
    */
    @TableField(value="update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
