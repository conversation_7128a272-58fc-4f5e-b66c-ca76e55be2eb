package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipLedger;
import com.boyo.eam.domain.EquipMaintTempl;
import com.boyo.eam.mapper.EquipLedgerMapper;
import com.boyo.eam.mapper.EquipMaintTemplMapper;
import com.boyo.eam.service.IEquipMaintTemplService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备-维保模板(EquipMaintTempl)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:30
 */
@Service("equipMaintTemplService")
@AllArgsConstructor
@Tenant
public class EquipMaintTemplServiceImpl extends ServiceImpl<EquipMaintTemplMapper, EquipMaintTempl> implements IEquipMaintTemplService {
    private final EquipMaintTemplMapper equipMaintTemplMapper;
    private final EquipLedgerMapper equipLedgerMapper;
    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipMaintTempl> selectEquipMaintTemplList(EquipMaintTempl equipMaintTempl) {
        List<EquipMaintTempl> templList = equipMaintTemplMapper.selectEquipMaintTemplList(equipMaintTempl);
        // 查询出所有设备名称，用逗号隔开
        for (EquipMaintTempl emt:templList){
            String equipOpenid = emt.getEquipOpenid();
            if (equipOpenid!=null&&!"".equals(equipOpenid)){
                String[] equipOpenids = equipOpenid.split(",");
                List<EquipLedger> equipLedgers = equipLedgerMapper.selectList(
                        Wrappers.<EquipLedger>lambdaQuery()
                                .in(EquipLedger::getOpenid, equipOpenids)
                );
                StringBuffer buffer = new StringBuffer();
                for (EquipLedger el:equipLedgers){
                    buffer.append(el.getName()).append(",");
                }
                buffer.deleteCharAt(buffer.length()-1);
                emt.setEquipNames(buffer.toString());
            }
        }
        return templList;
    }

}
