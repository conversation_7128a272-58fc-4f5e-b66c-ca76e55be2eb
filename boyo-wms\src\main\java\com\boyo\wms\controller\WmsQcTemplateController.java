package com.boyo.wms.controller;

import com.boyo.wms.entity.WmsQcTemplate;
import com.boyo.wms.service.IWmsQcTemplateService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * Wms质检模板(WmsQcTemplate)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-08 15:21:03
 */
@Api("Wms质检模板")
@RestController
@RequestMapping("/wms/wmsQcTemplate")
@AllArgsConstructor
public class WmsQcTemplateController extends BaseController{
    /**
     * 服务对象
     */
    private final IWmsQcTemplateService wmsQcTemplateService;

    /**
     * 查询Wms质检模板列表
     *
     */
    @ApiOperation("查询Wms质检模板列表")
    @GetMapping("/list")
    public TableDataInfo list(WmsQcTemplate wmsQcTemplate) {
        startPage();
        List<WmsQcTemplate> list = wmsQcTemplateService.selectWmsQcTemplateList(wmsQcTemplate);
        return getDataTable(list);
    }
    
    /**
     * 获取Wms质检模板详情
     */
    @ApiOperation("获取Wms质检模板详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(wmsQcTemplateService.getById(id));
    }

    /**
     * 新增Wms质检模板
     */
    @ApiOperation("新增Wms质检模板")
    @PostMapping
    public AjaxResult add(@RequestBody WmsQcTemplate wmsQcTemplate) {
        return toBooleanAjax(wmsQcTemplateService.save(wmsQcTemplate));
    }

    /**
     * 修改Wms质检模板
     */
    @ApiOperation("修改Wms质检模板")
    @PutMapping
    public AjaxResult edit(@RequestBody WmsQcTemplate wmsQcTemplate) {
        return toBooleanAjax(wmsQcTemplateService.updateById(wmsQcTemplate));
    }

    /**
     * 删除Wms质检模板
     */
    @ApiOperation("删除Wms质检模板")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(wmsQcTemplateService.removeByIds(Arrays.asList(ids)));
    }

}
