package com.boyo.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.common.core.domain.entity.EnterpriseUser;

/**
 * 企业用户管理Service接口
 *
 * <AUTHOR>
 */
public interface IEnterpriseUserService extends IService<EnterpriseUser> {
    /**
     * 根据条件查询查询企业用户管理列表
     *
     * @param enterpriseUser 企业用户管理
     * @return 企业用户管理集合
     */
    List<EnterpriseUser> selectEnterpriseUserList(EnterpriseUser enterpriseUser);

    EnterpriseUser selectUserByOpenid(String openid);

    List<EnterpriseUser> selectByIds(List<Long> ids);

    List<EnterpriseUser> selectByOpenIds(List<String> openids);

    EnterpriseUser selectByOpenId(String openid);

    boolean updateOwnUser(EnterpriseUser enterpriseUser);

    boolean updateWechat(String openid,String wechat);

    EnterpriseUser selectUserByOpenidAndUsername(String openid, String name);

    int saveEnterpriseUser(EnterpriseUser enterpriseUser);

    void updateEnterpriseUserById(EnterpriseUser enterpriseUser);
}
