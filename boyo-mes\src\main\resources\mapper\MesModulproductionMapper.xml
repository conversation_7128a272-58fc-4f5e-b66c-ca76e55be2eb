<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.MesModulproductionMapper">

    <resultMap type="com.boyo.mes.entity.MesModulproduction" id="MesModulproductionResult">
        <result property="id" column="id" />
        <result property="modulId" column="modul_id" />
        <result property="productionId" column="production_id" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectMesModulproductionList" parameterType="com.boyo.mes.entity.MesModulproduction" resultMap="MesModulproductionResult">
        select
          id, modul_id, production_id
        from t_mes_modulproduction
        <where>
            <if test="modulId != null">
                and modul_id = #{modulId}
            </if>
            <if test="productionId != null">
                and production_id = #{productionId}
            </if>
        </where>
    </select>
</mapper>

