package com.boyo.mes.controller;

import com.alibaba.fastjson.JSONObject;
import com.boyo.mes.entity.ProcessGroupDetail;
import com.boyo.mes.service.IProcessGroupDetailService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 工序组详情(ProcessGroupDetail)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Api("工序组详情")
@RestController
@RequestMapping("/mes/processGroupDetail")
@AllArgsConstructor
public class ProcessGroupDetailController extends BaseController{
    /**
     * 服务对象
     */
    private final IProcessGroupDetailService processGroupDetailService;

    /**
     * 查询工序组详情列表
     *
     */
    @ApiOperation("查询工序组详情列表")
    @GetMapping("/list")
    public TableDataInfo list(ProcessGroupDetail processGroupDetail) {
        startPage();
        List<ProcessGroupDetail> list = processGroupDetailService.selectProcessGroupDetailList(processGroupDetail);
        return getDataTable(list);
    }
    
    /**
     * 获取工序组详情详情
     */
    @ApiOperation("获取工序组详情详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(processGroupDetailService.getById(id));
    }

    /**
     * 新增工序组详情
     */
    @ApiOperation("新增工序组详情")
    @PostMapping
    public AjaxResult add(@RequestBody JSONObject object) {
        processGroupDetailService.addProcessGroupDetail(object);
        return AjaxResult.success();
    }

    /**
     * 修改工序组详情
     */
    @ApiOperation("修改工序组详情")
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessGroupDetail processGroupDetail) {
        return toBooleanAjax(processGroupDetailService.updateById(processGroupDetail));
    }

    /**
     * 删除工序组详情
     */
    @ApiOperation("删除工序组详情")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(processGroupDetailService.removeByIds(Arrays.asList(ids)));
    }

}
