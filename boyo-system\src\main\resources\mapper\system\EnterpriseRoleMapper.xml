<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.system.mapper.EnterpriseRoleMapper">

    <resultMap type="com.boyo.system.domain.EnterpriseRole" id="EnterpriseRoleResult">
        <result property="id" column="id"/>
        <result property="roleOpenid" column="role_openid"/>
        <result property="enterpriseOpenid" column="enterprise_openid"/>
        <result property="roleName" column="role_name"/>
        <result property="dataScope" column="data_scope"/>
        <result property="roleCode" column="role_code"/>
        <result property="roleIcon" column="role_icon"/>
        <result property="roleDesc" column="role_desc"/>
        <result property="roleStatus" column="role_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectEnterpriseRoleVo">
        select id,
               role_openid,
               enterprise_openid,
               role_name,
               data_scope,
               role_code,
               role_icon,
               role_desc,
               role_status,
               create_time,
               update_time
        from t_enterprise_role
    </sql>

    <select id="selectEnterpriseRoleList" parameterType="com.boyo.system.domain.EnterpriseRole"
            resultMap="EnterpriseRoleResult">
        <include refid="selectEnterpriseRoleVo"/>
        <where>
            <if test="roleOpenid != null  and roleOpenid != ''">
                and role_openid = #{roleOpenid}
            </if>
            <if test="enterpriseOpenid != null  and enterpriseOpenid != ''">
                and enterprise_openid = #{enterpriseOpenid}
            </if>
            <if test="roleName != null  and roleName != ''">
                and role_name like concat('%', #{roleName}, '%')
            </if>
            <if test="roleCode != null  and roleCode != ''">
                and role_code = #{roleCode}
            </if>
            <if test="roleIcon != null  and roleIcon != ''">
                and role_icon = #{roleIcon}
            </if>
            <if test="roleDesc != null  and roleDesc != ''">
                and role_desc = #{roleDesc}
            </if>
            <if test="roleStatus != null  and roleStatus != ''">
                and role_status = #{roleStatus}
            </if>
        </where>
    </select>

    <insert id="saveRoleAdmin" parameterType="com.boyo.system.domain.EnterpriseRole">
        insert into t_enterprise_role (enterprise_openid,role_openid,role_name,data_scope,role_desc,role_status,create_time)
        values(#{enterpriseOpenid},#{roleOpenid},#{roleName},#{dataScope},#{roleDesc},'1',now())
    </insert>
</mapper>
