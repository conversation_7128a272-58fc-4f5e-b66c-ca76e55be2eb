package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipMaintTemplItem;
import com.boyo.eam.mapper.EquipMaintTemplItemMapper;
import com.boyo.eam.service.IEquipMaintTemplItemService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备-维保模板-维保项目(EquipMaintTemplItem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:31
 */
@Service("equipMaintTemplItemService")
@AllArgsConstructor
@Tenant
public class EquipMaintTemplItemServiceImpl extends ServiceImpl<EquipMaintTemplItemMapper, EquipMaintTemplItem> implements IEquipMaintTemplItemService {
    private final EquipMaintTemplItemMapper equipMaintTemplItemMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipMaintTemplItem> selectEquipMaintTemplItemList(EquipMaintTemplItem equipMaintTemplItem) {
        return equipMaintTemplItemMapper.selectEquipMaintTemplItemList(equipMaintTemplItem);
    }

}
