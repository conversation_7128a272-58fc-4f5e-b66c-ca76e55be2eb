<template>
  <base-page :config="config">
    <span slot="operation" slot-scope="text, record">
      <a @click="$refs.createForm.handleUpdate($event,record.id)">
      <a-icon type="edit" />{{ $t('app.global.edit') }}
      </a>
      <a-divider type="vertical" />
      <a @click="handleDelete(record.id)"> <a-icon type="delete" />{{ $t('app.global.delete') }}</a>
    </span>
    <!-- 弹窗 -->
    <create-form ref="createForm" :statusOptions="statusOptions" @ok="getList" />
  </base-page>
</template>

<script>
import CreateForm from './modules/CreateForm'
import { delTeamTaskWorkTime, listTeamTaskWorkTime } from '@/api/teamTaskWorkTime'

export default {
  components: {
    CreateForm,
  },
  data() {
    return {
      // 页面加载状态
      loading: false,
      // 数据列表
      list: [],
      // 表格数据总数
      total: 0,
      // 状态数据字典
      statusOptions: [],
    }
  },
  async created() {
    this.getList()
  },
  computed: {
    config() {
      return {
        loading: this.loading,
        query: {
          onQuery: this.getList,
          items: [
                        { label: '任务ID', name: 'taskCode' },
                        { label: '成员id', name: 'memberCode' },
                        { label: '', name: 'createTime' },
                        { label: '描述', name: 'content' },
                        { label: '开始时间', name: 'beginTime' },
                        { label: '工时', name: 'num' },
                        { label: 'id', name: 'code' },
                      ],
        },
        action: {
          add: () => this.$refs.createForm.handleAdd(),
          delete: this.handleDelete,
        },
        table: {
          total: this.total,
          list: this.list,
          columns: [
               { 
                title: '任务ID', 
                dataIndex: 'taskCode',
                align: 'center',
            },
                { 
                title: '成员id', 
                dataIndex: 'memberCode',
                align: 'center',
            },
                { 
                title: '', 
                dataIndex: 'createTime',
                align: 'center',
            },
                { 
                title: '描述', 
                dataIndex: 'content',
                align: 'center',
            },
                { 
                title: '开始时间', 
                dataIndex: 'beginTime',
                align: 'center',
            },
                { 
                title: '工时', 
                dataIndex: 'num',
                align: 'center',
            },
                { 
                title: 'id', 
                dataIndex: 'code',
                align: 'center',
            },
                        {
              title: this.$t('app.global.operation'),
              dataIndex: 'operation',
              scopedSlots: { customRender: 'operation' },
              align: 'center',
            },
          ],
        },
      }
    },
  },
  methods: {
    /**
     * 查询任务工时表列表
     */
    async getList(queryParam) {
      this.loading = true
      if (queryParam !== undefined) {
        this.queryParam = queryParam
      }
      this.loading = true
      const response = await listTeamTaskWorkTime(this.queryParam)
      this.list = response.rows
      this.total = response.total
      this.loading = false
    },
    /**
     * 删除按钮操作
     */
    handleDelete(id) {
      
      const ids = id || this.ids
      this.$alert.confirm({
        content: this.$t('app.global.delete.content'),
        onOk: async () => {
        await delTeamTaskWorkTime(ids)
        this.getList()
        this.$alert.success(this.$t('app.global.delete.success'))
        },
      })
    },
  },
}
</script>
}
