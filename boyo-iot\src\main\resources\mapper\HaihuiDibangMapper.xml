<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.iot.mapper.HaihuiDibangMapper">

    <resultMap id="BaseResultMap" type="com.boyo.iot.entity.HaihuiDibang">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="ticket_number" property="ticket_number" jdbcType="VARCHAR"/>
        <result column="car_number" property="car_number" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goods_name" jdbcType="VARCHAR"/>
        <result column="receiver" property="receiver" jdbcType="VARCHAR"/>
        <result column="sender" property="sender" jdbcType="VARCHAR"/>
        <result column="transporter" property="transporter" jdbcType="VARCHAR"/>
        <result column="weighing_fee" property="weighing_fee" jdbcType="DECIMAL"/>
        <result column="unit_price" property="unit_price" jdbcType="DECIMAL"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="conversion_factor" property="conversion_factor" jdbcType="VARCHAR"/>
        <result column="volume" property="volume" jdbcType="VARCHAR"/>
        <result column="gross_weight" property="gross_weight" jdbcType="DOUBLE"/>
        <result column="tare_weight" property="tare_weight" jdbcType="DOUBLE"/>
        <result column="net_weight" property="net_weight" jdbcType="DOUBLE"/>
        <result column="deduction_rate" property="deduction_rate" jdbcType="DOUBLE"/>
        <result column="deduction" property="deduction" jdbcType="DOUBLE"/>
        <result column="actual_weight" property="actual_weight" jdbcType="DOUBLE"/>
        <result column="gross_time" property="gross_time" jdbcType="TIMESTAMP"/>
        <result column="tare_time" property="tare_time" jdbcType="TIMESTAMP"/>
        <result column="DeviceId" property="deviceId" jdbcType="VARCHAR"/>
        <result column="Module" property="module" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="selectHaihuiDibangList" resultMap="BaseResultMap" parameterType="com.boyo.iot.entity.HaihuiDibang">
        SELECT * FROM haihui_dibang
        <where>
            <if test="ticket_number != null and ticket_number != ''">
                AND ticket_number LIKE CONCAT('%', #{ticket_number}, '%')
            </if>
            <if test="car_number != null and car_number != ''">
                AND car_number LIKE CONCAT('%', #{car_number}, '%')
            </if>
            <if test="Module != null and Module != ''">
                AND module = #{Module}
            </if>
            <!-- 其他条件 -->
        </where>
        ORDER BY gross_time DESC
    </select>

    <insert id="insertHaihuiDibang" parameterType="com.boyo.iot.entity.HaihuiDibang">
        INSERT INTO haihui_dibang (ticket_number, car_number, goods_name, receiver, sender, transporter,
                                   weighing_fee, unit_price, amount, conversion_factor, volume,
                                   gross_weight, tare_weight, net_weight, deduction_rate, deduction,
                                   actual_weight, gross_time, tare_time, DeviceId, module, create_time)
        VALUES (#{ticket_number}, #{car_number}, #{goods_name}, #{receiver}, #{sender}, #{transporter},
                #{weighing_fee}, #{unit_price}, #{amount}, #{conversion_factor}, #{volume},
                #{gross_weight}, #{tare_weight}, #{net_weight}, #{deduction_rate}, #{deduction},
                #{actual_weight}, #{gross_time}, #{tare_time}, #{deviceId}, #{module}, NOW())
    </insert>

    <update id="updateHaihuiDibang" parameterType="com.boyo.iot.entity.HaihuiDibang">
        UPDATE haihui_dibang
        <set>
            <if test="ticket_number != null">ticket_number = #{ticket_number},</if>
            <if test="car_number != null">car_number = #{car_number},</if>
            <if test="goods_name != null">goods_name = #{goods_name},</if>
            <if test="receiver != null">receiver = #{receiver},</if>
            <if test="sender != null">sender = #{sender},</if>
            <if test="transporter != null">transporter = #{transporter},</if>
            <if test="weighing_fee != null">weighing_fee = #{weighing_fee},</if>
            <if test="unit_price != null">unit_price = #{unit_price},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="conversion_factor != null">conversion_factor = #{conversion_factor},</if>
            <if test="volume != null">volume = #{volume},</if>
            <if test="gross_weight != null">gross_weight = #{gross_weight},</if>
            <if test="tare_weight != null">tare_weight = #{tare_weight},</if>
            <if test="net_weight != null">net_weight = #{net_weight},</if>
            <if test="deduction_rate != null">deduction_rate = #{deduction_rate},</if>
            <if test="deduction != null">deduction = #{deduction},</if>
            <if test="actual_weight != null">actual_weight = #{actual_weight},</if>
            <if test="gross_time != null">gross_time = #{gross_time},</if>
            <if test="tare_time != null">tare_time = #{tare_time},</if>
            <if test="deviceId != null">DeviceId = #{deviceId},</if>
            <if test="module != null">module = #{module}</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteHaihuiDibangByIds">
        DELETE FROM haihui_dibang WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectHaihuiDibangById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT *
        FROM haihui_dibang
        WHERE id = #{id}
    </select>
    <select id="selectHaihuiDibangByticketNumber" resultMap="BaseResultMap" parameterType="String">
        SELECT *
        FROM haihui_dibang
        WHERE ticket_number = #{ticketNumber}
    </select>
</mapper>
