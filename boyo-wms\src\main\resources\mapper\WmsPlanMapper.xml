<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.wms.mapper.WmsPlanMapper">

    <resultMap type="com.boyo.wms.vo.WmsPlanVO" id="WmsPlanResult">
        <result property="id" column="id"/>
        <result property="planOpenid" column="plan_openid"/>
        <result property="planType" column="plan_type"/>
        <result property="planCategory" column="plan_category"/>
        <result property="planOrder" column="plan_order"/>
        <result property="planRemarks" column="plan_remarks"/>
        <result property="planState" column="plan_state"/>
        <result property="createdAt" column="created_at"/>
        <result property="createdUser" column="created_user"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="updatedUser" column="updated_user"/>
        <result property="planStarttime" column="plan_starttime"/>
        <result property="planEndtime" column="plan_endtime"/>
        <result property="planEndreason" column="plan_endreason"/>
        <result property="categoryName" column="category_name"/>
    </resultMap>

    <sql id="selectWmsPlanVo">
        select id,
               plan_openid,
               plan_type,
               plan_category,
               plan_order,
               plan_remarks,
               plan_state,
               created_at,
               created_user,
               updated_at,
               updated_user,
               plan_starttime,
               plan_endtime,
               plan_endreason
        from t_wms_plan
    </sql>

    <select id="selectWmsPlanList" parameterType="com.boyo.wms.entity.WmsPlan" resultMap="WmsPlanResult">
        select t1.*,t2.base_desc as category_name from t_wms_plan t1
        left join t_base_dict t2 on t1.plan_category = t2.openid
        <where>
                t1.plan_state &lt;&gt; 99
            <if test="planOpenid != null  and planOpenid != ''">
                and t1.plan_openid = #{planOpenid}
            </if>
            <if test="planType != null  and planType != ''">
                and t1.plan_type = #{planType}
            </if>
            <if test="planCategory != null  and planCategory != ''">
                and t1.plan_category = #{planCategory}
            </if>
            <if test="planOrder != null  and planOrder != ''">
                and t1.plan_order = #{planOrder}
            </if>
            <if test="planRemarks != null  and planRemarks != ''">
                and t1.plan_remarks = #{planRemarks}
            </if>
            <if test="planState != null  and planState != ''">
                <choose>
                    <when test="planState == 2">
                        and (t1.plan_state = '2' or t1.plan_state = '3')
                    </when>
                    <otherwise>
                        and t1.plan_state = #{planState}
                    </otherwise>
                </choose>
            </if>
            <if test="createdAt != null ">
                and t1.created_at = #{createdAt}
            </if>
            <if test="createdUser != null  and createdUser != ''">
                and t1.created_user = #{createdUser}
            </if>
            <if test="updatedAt != null ">
                and t1.updated_at = #{updatedAt}
            </if>
            <if test="updatedUser != null  and updatedUser != ''">
                and t1.updated_user = #{updatedUser}
            </if>
            <if test="planStarttime != null ">
                and t1.plan_starttime = #{planStarttime}
            </if>
            <if test="planEndtime != null ">
                and t1.plan_endtime = #{planEndtime}
            </if>
            <if test="planEndreason != null  and planEndreason != ''">
                and t1.plan_endreason = #{planEndreason}
            </if>
        </where>
order by t1.plan_state asc,t1.id desc
    </select>
</mapper>
