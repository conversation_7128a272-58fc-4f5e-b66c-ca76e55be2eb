<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipInspectionSpotMapper">

    <resultMap type="com.boyo.eam.domain.EquipInspectionSpot" id="EquipInspectionSpotResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="code" column="code" />
        <result property="equipLedgerOpenid" column="equip_ledger_openid" />
        <result property="sys_user_id" column="equip_ledger_openid" />
        <result property="reviewer" column="reviewer" />
        <result property="cycle" column="cycle" />
        <result property="cycleUnit" column="cycle_unit" />
        <result property="cycleBeginDate" column="cycle_begin_date" />
        <result property="cycleEndDate" column="cycle_end_date" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipInspectionSpotList" parameterType="com.boyo.eam.domain.EquipInspectionSpot" resultType="EquipInspectionSpot">
        select
          EIS.*,EL.code as equipCode,EL.name as equipName,TML.line_name as lineName
        from
        equip_inspection_spot EIS left join
        equip_ledger EL on EIS.equip_ledger_openid=EL.openid left join
        t_model_line TML on EIS.line_openid=TML.line_openid

        <where>
            <if test="openid != null and openid != ''">
                and EIS.openid = #{openid}
            </if>
            <if test="code != null and code != ''">
                and EIS.code = #{code}
            </if>
            <if test="equipLedgerOpenid != null and equipLedgerOpenid != ''">
                and EIS.equip_ledger_openid = #{equipLedgerOpenid}
            </if>
            <if test="sysUserId != null and sysUserId != ''">
                and EIS.sys_user_id = #{sysUserId}
            </if>
            <if test="type != null and type != ''">
                and EIS.type = #{type}
            </if>
            <if test="spotType != null and spotType != ''">
                and EIS.spot_type = #{spotType}
            </if>
            <if test="lineOpenid != null and lineOpenid != ''">
                and EIS.line_openid = #{lineOpenid}
            </if>
            <if test="state != null and state != ''">
                and EIS.state = #{state}
            </if>
            <if test="cycle != null">
                and EIS.cycle = #{cycle}
            </if>
            <if test="cycleUnit != null and cycleUnit != ''">
                and EIS.cycle_unit = #{cycleUnit}
            </if>
            <if test="cycleBeginDate != null">
                and EIS.cycle_begin_date = #{cycleBeginDate}
            </if>
            <if test="cycleEndDate != null">
                and EIS.cycle_end_date = #{cycleEndDate}
            </if>
            <if test="createBy != null and createBy != ''">
                and EIS.create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and EIS.create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and EIS.update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and EIS.update_time = #{updateTime}
            </if>
            <if test="beginDate!=null and endDate!=null">
                and #{beginDate} &lt;= EIS.create_time
                and EIS.create_time &lt;= #{endDate}
            </if>
            <if test="spotContent!=null and spotContent!=''">
                and CONCAT(EL.name,EL.code,EIS.code) like CONCAT('%',#{spotContent},'%')
            </if>
        </where>
    </select>
</mapper>

