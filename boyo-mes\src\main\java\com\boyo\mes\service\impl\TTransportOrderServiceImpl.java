package com.boyo.mes.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.framework.annotation.Tenant;
import com.boyo.mes.mapper.TTransportOrderMapper;
import com.boyo.mes.entity.TTransportOrder;
import com.boyo.mes.service.TTransportOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * (TTransportOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-12 09:30:43
 */
@Service("tTransportOrderService")
@Tenant
public class TTransportOrderServiceImpl extends ServiceImpl<TTransportOrderMapper, TTransportOrder> implements TTransportOrderService {
    @Autowired
    private TTransportOrderMapper tTransportOrderMapper;

    @Override
    public List<TTransportOrder> selectTransportOrderList(TTransportOrder tTransportOrder) {
        return tTransportOrderMapper.selectTransportOrderList(tTransportOrder);
    }

    public boolean save(TTransportOrder tTransportOrder){
        tTransportOrder.setCreateTime(new Date());
        if(tTransportOrder.getInvoiceNumber()==null){
            tTransportOrder.setShippingStatus(0);
        }else if(tTransportOrder.getInvoiceNumber()!=null && tTransportOrder.getReceiveTime() == null){
            tTransportOrder.setShippingStatus(1);
            tTransportOrder.setDeliveryTime(new Date());
        }else {
            tTransportOrder.setShippingStatus(2);
        }
        return super.save(tTransportOrder);
    }
    public boolean updateById(TTransportOrder tTransportOrder){
        if(tTransportOrder.getInvoiceNumber()==null){
            tTransportOrder.setShippingStatus(0);
        }else if(tTransportOrder.getInvoiceNumber()!=null && tTransportOrder.getReceiveTime() == null){
            tTransportOrder.setShippingStatus(1);
            tTransportOrder.setDeliveryTime(new Date());
        }else {
            tTransportOrder.setShippingStatus(2);
        }
        return super.updateById(tTransportOrder);
    }
}

