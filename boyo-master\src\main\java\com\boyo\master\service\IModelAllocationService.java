package com.boyo.master.service;

import java.util.List;

import com.boyo.master.domain.ModelAllocation;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.master.vo.ModelAllocatonVO;

/**
 * 主数据-货位管理Service接口
 *
 * <AUTHOR>
 */
public interface IModelAllocationService extends IService<ModelAllocation> {
    /**
     * 根据条件查询查询主数据-货位管理列表
     *
     * @param modelAllocation 主数据-货位管理
     * @return 主数据-货位管理集合
     */
    List<ModelAllocatonVO> selectModelAllocationList(ModelAllocation modelAllocation);
}
