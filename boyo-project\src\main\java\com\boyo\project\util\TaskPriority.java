package com.boyo.project.util;

public enum TaskPriority {
    WAIT("0", "普通"),
    DOING("1", "紧急"),
    DONE("2", "非常紧急");

    private String code;
    private String text;

    TaskPriority(String code, String text){
        this.code = code;
        this.text = text;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
    public static String getText(String code) {
        for (TaskPriority ele : values()) {
            if(ele.getCode().equals(code)) return ele.getText();
        }
        return null;
    }
}
