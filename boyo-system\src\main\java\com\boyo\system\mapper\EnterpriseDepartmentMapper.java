package com.boyo.system.mapper;

import java.util.List;

import com.boyo.system.domain.EnterpriseDepartment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;


/**
 * 企业部门管理Mapper接口
 *
 * <AUTHOR>
 */
public interface EnterpriseDepartmentMapper extends BaseMapper<EnterpriseDepartment> {

    /**
     * 查询企业部门管理列表
     *
     * @param enterpriseDepartment 企业部门管理
     * @return EnterpriseDepartment集合
     */
    List<EnterpriseDepartment> selectEnterpriseDepartmentList(EnterpriseDepartment enterpriseDepartment);

    public List<String> getDeptAndChildren(String openid);

}
