package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipType;
import com.boyo.eam.mapper.EquipTypeMapper;
import com.boyo.eam.service.IEquipTypeService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备类型表(EquipType)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Service("equipTypeService")
@AllArgsConstructor
@Tenant
public class EquipTypeServiceImpl extends ServiceImpl<EquipTypeMapper, EquipType> implements IEquipTypeService {
    private final EquipTypeMapper equipTypeMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipType> selectEquipTypeList(EquipType equipType) {
        return equipTypeMapper.selectEquipTypeList(equipType);
    }

}
