package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmPool;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * 公海表(CrmPool)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-30 14:33:58
 */
@Tenant
public interface CrmPoolMapper extends BaseMapper<CrmPool>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmPool 实例对象
     * @return 对象列表
     */
    List<CrmPool> selectCrmPoolList(CrmPool crmPool);


}

