package com.boyo.system.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 企业权限管理
 * 表名 t_sys_enterprise_authority
 *
 * <AUTHOR>
 */
@ApiModel("企业权限表")
@Data
@TableName("t_sys_enterprise_authority")
public class SysEnterpriseAuthority extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    @TableField(value = "enterprise_openid")
    private String enterpriseOpenid;
    /**
     * 系统id
     */
    @ApiModelProperty("系统id")
    @TableField(value = "system_openid")
    private String systemOpenid;
    /**
     * 系统有效期
     */
    @ApiModelProperty("系统有效期")
    @TableField(value = "system_validity")
    private Date systemValidity;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(value = "update_time")
    private Date updateTime;
}
