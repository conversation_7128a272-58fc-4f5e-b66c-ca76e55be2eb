package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipState;

import java.util.List;

/**
 * 设备状态表(EquipState)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
public interface EquipStateMapper extends BaseMapper<EquipState>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipState 实例对象
     * @return 对象列表
     */
    List<EquipState> selectEquipStateList(EquipState equipState);


}

