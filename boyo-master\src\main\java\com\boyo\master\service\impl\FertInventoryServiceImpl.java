package com.boyo.master.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.master.domain.EnterpriseConstant;
import com.boyo.master.domain.annotations.DataAsset;
import com.boyo.master.domain.annotations.Enterprise;
import com.boyo.master.entity.FertInventory;
import com.boyo.master.mapper.FertInventoryMapper;
import com.boyo.master.service.DataAssetService;
import com.boyo.master.service.FertInventoryService;
import com.boyo.master.utils.HttpUtil;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

import static com.boyo.master.register.DataAssetRegister.getDataAssetType;
import static com.boyo.master.utils.DateUtil2.getDateLate;

/**
* <AUTHOR>
* @description 针对表【bu_enterprise_data_asset_fert_inventory】的数据库操作Service实现
* @createDate 2024-11-16 14:16:13
*/
@Slf4j
@DataAsset("产成品入库")
@Service
public class FertInventoryServiceImpl extends ServiceImpl<FertInventoryMapper, FertInventory>
    implements FertInventoryService, DataAssetService {

    private String ncUrl = EnterpriseConstant.NCURL;


    @Override
    public Object getDataAssetList(IPage page, Map<String, String> params) {
       PageHelper.clearPage();
        return this.page(page, new LambdaQueryWrapper<FertInventory>()
                .eq(FertInventory::getEnterpriseId, params.get(EnterpriseConstant.ENTERPRISE_ID))
                .eq(StrUtil.isNotEmpty(params.get("childrenCompany")), FertInventory::getChildrenCompany, params.get("childrenCompany"))
                .like(StrUtil.isNotEmpty(params.get("bodyname")), FertInventory::getBodyname, params.get("bodyname"))
                .eq(StrUtil.isNotEmpty(params.get("vbillcode")), FertInventory::getVbillcode, params.get("vbillcode"))
                .like(StrUtil.isNotEmpty(params.get("invname")), FertInventory::getInvname, params.get("invname"))
                .between(StrUtil.isNotEmpty(params.get("dbilldateStart")) && StrUtil.isNotEmpty(params.get("dbilldateEnd")), FertInventory::getDbilldate, params.get("dbilldateStart"), params.get("dbilldateEnd"))
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncDataAssetHaihui() throws ExecutionException {
        Enterprise annotation = getCurrentMethodAnnotation(Enterprise.class);
        Long enterpriseId = Convert.toLong(annotation.value());
        Map<String, String> company = (Map<String, String>) getSelectorList(getDataAssetType(this), enterpriseId)
                .getOrDefault("company", new HashMap<>());
        for (String companyItem : company.keySet()) {
            // 1. 拼装查询参数
            Map<String, Object> params = new HashMap<>();
            params.put("begindate", getDateLate(100));
            params.put("enddate", getDateLate(1));
            params.put("url", "api/nc57/index/fertInventory");
            params.put("unitname", companyItem);
            log.info("同步数据开始，查询参数【{}】", params.toString());
            // 2. 发送请求获取数据
            final ResponseEntity<?> responseEntity = HttpUtil.postData(params);
            String post = responseEntity.getBody().toString();
            // 3. 解析响应数据
            JSONObject response = JSON.parseObject(post);
            String data = response.getString("data");
            if (StrUtil.isEmpty(data)) {
                continue;
            }
            FertInventory[] dataArr = JSON.parseObject(data).getObject("data", FertInventory[].class);
            // 4. 循环处理数据
            List<String> remoteIdList = new ArrayList<>();
            List<FertInventory> dataList = new ArrayList<>();
            for (FertInventory item : dataArr) {
                item.setChildrenCompany(companyItem);
                item.setEnterpriseId(enterpriseId);
                item.setRemoteId(item.getVbillcode());
                remoteIdList.add(item.getRemoteId());
                dataList.add(item);
            }
            // 5. 更新数据
            this.remove(new LambdaQueryWrapper<FertInventory>()
                    .eq(FertInventory::getEnterpriseId, enterpriseId)
                    .in(FertInventory::getRemoteId, remoteIdList)
            );
            if (!this.saveBatch(dataList)) {
                log.error("同步数据失败，查询参数【{}】", params.toString());
            }
        }
    }
}




