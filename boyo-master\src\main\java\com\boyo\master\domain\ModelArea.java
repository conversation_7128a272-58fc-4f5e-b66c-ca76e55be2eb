package com.boyo.master.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 主数据-区域管理
 * 表名 t_model_area
 *
 * <AUTHOR>
 */
@ApiModel("主数据-区域")
@Data
@TableName("t_model_area")
public class ModelArea extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "area_openid")
    private String areaOpenid;
    /**
     * 区域名称
     */
    @ApiModelProperty("区域名称")
    @TableField(value = "area_name")
    private String areaName;
    /**
     * 区域编码
     */
    @ApiModelProperty("区域编码")
    @TableField(value = "area_code")
    private String areaCode;

    @ApiModelProperty("所属工厂ID")
    @TableField(value = "area_factory")
    private String areaFactory;
    /**
     * 所属仓库ID
     */
    @ApiModelProperty("所属仓库ID")
    @TableField(value = "area_warehouse_id")
    private String areaWarehouseId;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "created_at")
    private Date createdAt;
    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    @TableField(value = "created_user")
    private String createdUser;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(value = "updated_at")
    private Date updatedAt;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "updated_user")
    private String updatedUser;

}
