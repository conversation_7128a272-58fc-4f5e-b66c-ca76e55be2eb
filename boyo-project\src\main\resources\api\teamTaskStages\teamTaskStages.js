import request from '@/utils/request'

const prefix = ''

// 查询任务列表表(TeamTaskStages)列表
export function listTeamTaskStages(query) {
  return request({
    url: prefix + '/teamTaskStages/list',
    method: 'get',
    params: query,
  })
}

// 查询任务列表表(TeamTaskStages)详细
export function getTeamTaskStages(id) {
  return request({
    url: prefix + '/teamTaskStages/' + id,
    method: 'get',
  })
}

// 新增任务列表表(TeamTaskStages)
export function addTeamTaskStages(data) {
  return request({
    url: prefix + '/teamTaskStages',
    method: 'post',
    data: data,
  })
}

// 修改任务列表表(TeamTaskStages)
export function updateTeamTaskStages(data) {
  return request({
    url: prefix + '/teamTaskStages',
    method: 'put',
    data: data,
  })
}

// 删除任务列表表(TeamTaskStages)
export function delTeamTaskStages(id) {
  return request({
    url: prefix + '/teamTaskStages/' + id,
    method: 'delete',
  })
}
