import request from '@/utils/request'

const prefix = '/${module}'

// 查询任务工时表(TeamTaskWorkTime)列表
export function listTeamTaskWorkTime(query) {
  return request({
    url: prefix + '/teamTaskWorkTime/list',
    method: 'get',
    params: query,
  })
}

// 查询任务工时表(TeamTaskWorkTime)详细
export function getTeamTaskWorkTime(id) {
  return request({
    url: prefix + '/teamTaskWorkTime/' + id,
    method: 'get',
  })
}

// 新增任务工时表(TeamTaskWorkTime)
export function addTeamTaskWorkTime(data) {
  return request({
    url: prefix + '/teamTaskWorkTime',
    method: 'post',
    data: data,
  })
}

// 修改任务工时表(TeamTaskWorkTime)
export function updateTeamTaskWorkTime(data) {
  return request({
    url: prefix + '/teamTaskWorkTime',
    method: 'put',
    data: data,
  })
}

// 删除任务工时表(TeamTaskWorkTime)
export function delTeamTaskWorkTime(id) {
  return request({
    url: prefix + '/teamTaskWorkTime/' + id,
    method: 'delete',
  })
}
