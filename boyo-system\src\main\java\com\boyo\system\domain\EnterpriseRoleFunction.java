package com.boyo.system.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 企业角色权限管理
 * 表名 t_enterprise_role_function
 *
 * <AUTHOR>
 */
@ApiModel("客户端权限表（角色功能关联表）")
@Data
@TableName("t_enterprise_role_function")
public class EnterpriseRoleFunction extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 角色ID
     */
    @ApiModelProperty("角色ID")
    @TableField(value = "role_openid")
    private String roleOpenid;
    /**
     * 功能ID
     */
    @ApiModelProperty("功能ID")
    @TableField(value = "function_openid")
    private String functionOpenid;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(value = "update_time")
    private Date updateTime;
}
