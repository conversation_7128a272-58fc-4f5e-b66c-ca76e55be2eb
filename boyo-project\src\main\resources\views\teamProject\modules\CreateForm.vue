<template>
  <a-modal width="30%" :maskClosable="false" :visible="open" @cancel="cancel">
    <template #title>
      <a-icon type="security-scan" />
      {{ formTitle }}
    </template>
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      layout="horizontal"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
           <a-form-model-item label="封面">
        <a-input
          :size="formSize"
          v-model="form.cover"
          :placeholder="$t('app.global.please.input') + '封面'"
        />
      </a-form-model-item>
           <a-form-model-item label="名称">
        <a-input
          :size="formSize"
          v-model="form.name"
          :placeholder="$t('app.global.please.input') + '名称'"
        />
      </a-form-model-item>
           <a-form-model-item label="编号">
        <a-input
          :size="formSize"
          v-model="form.code"
          :placeholder="$t('app.global.please.input') + '编号'"
        />
      </a-form-model-item>
           <a-form-model-item label="描述">
        <a-input
          :size="formSize"
          v-model="form.description"
          :placeholder="$t('app.global.please.input') + '描述'"
        />
      </a-form-model-item>
           <a-form-model-item label="访问控制l类型">
        <a-input
          :size="formSize"
          v-model="form.accessControlType"
          :placeholder="$t('app.global.please.input') + '访问控制l类型'"
        />
      </a-form-model-item>
           <a-form-model-item label="可以访问项目的权限组（白名单）">
        <a-input
          :size="formSize"
          v-model="form.whiteList"
          :placeholder="$t('app.global.please.input') + '可以访问项目的权限组（白名单）'"
        />
      </a-form-model-item>
           <a-form-model-item label="排序">
        <a-input
          :size="formSize"
          v-model="form.order"
          :placeholder="$t('app.global.please.input') + '排序'"
        />
      </a-form-model-item>
           <a-form-model-item label="删除标记">
        <a-input
          :size="formSize"
          v-model="form.deleted"
          :placeholder="$t('app.global.please.input') + '删除标记'"
        />
      </a-form-model-item>
           <a-form-model-item label="项目类型">
        <a-input
          :size="formSize"
          v-model="form.templateCode"
          :placeholder="$t('app.global.please.input') + '项目类型'"
        />
      </a-form-model-item>
           <a-form-model-item label="进度">
        <a-input
          :size="formSize"
          v-model="form.schedule"
          :placeholder="$t('app.global.please.input') + '进度'"
        />
      </a-form-model-item>
           <a-form-model-item label="创建时间">
        <a-input
          :size="formSize"
          v-model="form.createTime"
          :placeholder="$t('app.global.please.input') + '创建时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="组织id">
        <a-input
          :size="formSize"
          v-model="form.organizationCode"
          :placeholder="$t('app.global.please.input') + '组织id'"
        />
      </a-form-model-item>
           <a-form-model-item label="删除时间">
        <a-input
          :size="formSize"
          v-model="form.deletedTime"
          :placeholder="$t('app.global.please.input') + '删除时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="是否私有">
        <a-input
          :size="formSize"
          v-model="form.private"
          :placeholder="$t('app.global.please.input') + '是否私有'"
        />
      </a-form-model-item>
           <a-form-model-item label="项目前缀">
        <a-input
          :size="formSize"
          v-model="form.prefix"
          :placeholder="$t('app.global.please.input') + '项目前缀'"
        />
      </a-form-model-item>
           <a-form-model-item label="是否开启项目前缀">
        <a-input
          :size="formSize"
          v-model="form.openPrefix"
          :placeholder="$t('app.global.please.input') + '是否开启项目前缀'"
        />
      </a-form-model-item>
           <a-form-model-item label="是否归档">
        <a-input
          :size="formSize"
          v-model="form.archive"
          :placeholder="$t('app.global.please.input') + '是否归档'"
        />
      </a-form-model-item>
           <a-form-model-item label="归档时间">
        <a-input
          :size="formSize"
          v-model="form.archiveTime"
          :placeholder="$t('app.global.please.input') + '归档时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="是否开启任务开始时间">
        <a-input
          :size="formSize"
          v-model="form.openBeginTime"
          :placeholder="$t('app.global.please.input') + '是否开启任务开始时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="是否开启新任务默认开启隐私模式">
        <a-input
          :size="formSize"
          v-model="form.openTaskPrivate"
          :placeholder="$t('app.global.please.input') + '是否开启新任务默认开启隐私模式'"
        />
      </a-form-model-item>
           <a-form-model-item label="看板风格">
        <a-input
          :size="formSize"
          v-model="form.taskBoardTheme"
          :placeholder="$t('app.global.please.input') + '看板风格'"
        />
      </a-form-model-item>
           <a-form-model-item label="项目开始日期">
        <a-input
          :size="formSize"
          v-model="form.beginTime"
          :placeholder="$t('app.global.please.input') + '项目开始日期'"
        />
      </a-form-model-item>
           <a-form-model-item label="项目截止日期">
        <a-input
          :size="formSize"
          v-model="form.endTime"
          :placeholder="$t('app.global.please.input') + '项目截止日期'"
        />
      </a-form-model-item>
           <a-form-model-item label="自动更新项目进度">
        <a-input
          :size="formSize"
          v-model="form.autoUpdateSchedule"
          :placeholder="$t('app.global.please.input') + '自动更新项目进度'"
        />
      </a-form-model-item>
        </a-form-model>
    <template #footer>
      <a-space>
        <a-button :size="formSize" icon="close" type="danger" @click="cancel">
          {{ $t('app.global.close') }}
        </a-button>
        <a-button :size="formSize" icon="save" type="primary" @click="submitForm">
          {{ $t('app.global.save') }}
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script>
import { addTeamProject, updateTeamProject, getTeamProject } from '@/api/teamProject'
export default {
  data() {
    return {
      //新增或修改
      updateState: false,
      formTitle: '项目表',
      // 表单参数
      form: {
           id: '',
           cover: '',
           name: '',
           code: '',
           description: '',
           accessControlType: '',
           whiteList: '',
           order: '',
           deleted: '',
           templateCode: '',
           schedule: '',
           createTime: '',
           organizationCode: '',
           deletedTime: '',
           private: '',
           prefix: '',
           openPrefix: '',
           archive: '',
           archiveTime: '',
           openBeginTime: '',
           openTaskPrivate: '',
           taskBoardTheme: '',
           beginTime: '',
           endTime: '',
           autoUpdateSchedule: '',
             },
      open: false,
      rules: {},
    }
  },
  created() {
    this.rules = {
    }
  },
  methods: {
    /**
     * 新增按钮操作
     * */
    handleAdd() {
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.add') + '项目表'
      this.form = {}
      this.updateState = false
    },
    /**
     * 修改按钮操作
     * */
    async handleUpdate($event, id) {
      $event.stopPropagation()
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.edit') + '项目表'
      const response = await getTeamProject(id)
      this.form = response.data
    },
    /**
     * 提交按钮
     * */
    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
            if (this.form.id) {
                await updateTeamProject(this.form)
                this.$alert.success(this.$t('app.global.edit.success'))
                this.open = false
                this.$emit('ok')
            } else {
                await addTeamProject(this.form)
                this.$alert.success(this.$t('app.global.add.success'))
                this.open = false
                this.$emit('ok')
            }
        } else {
            return false
        }
        })
    },
    /**
     * 取消按钮
     * */
    cancel() {
      this.open = false
      this.reset()
    },
    /**
     * 表单重置
     * */
    reset() {
      this.form = {}
      if (this.$refs.form) {
        this.$refs.form.resetFields()}
      }
    },
}
</script>

