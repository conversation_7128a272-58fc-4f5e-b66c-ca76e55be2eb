package com.boyo.mes.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.master.domain.TMaterial;
import com.boyo.master.domain.TSupplier;
import com.boyo.master.service.ITMaterialService;
import com.boyo.master.service.ITSupplierService;
import com.boyo.mes.entity.*;
import com.boyo.mes.service.IProcessGroupDetailService;
import com.boyo.mes.service.IProductOrderService;
import com.boyo.mes.service.IWorkReportService;
import com.boyo.mes.service.TFactoryOrderService;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 生产订单(ProductOrder)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Api("生产订单")
@RestController
@RequestMapping("/mes/productOrder")
@AllArgsConstructor
public class ProductOrderController extends BaseController {
    /**
     * 服务对象
     */
    private final IProductOrderService productOrderService;

    private final ITMaterialService materialService;

    private final IProcessGroupDetailService groupDetailService;

    private final IWorkReportService workReportService;

    private final ITSupplierService supplierService;

    /**
     * 查询生产订单列表
     */
    @ApiOperation("查询生产订单列表")
    @GetMapping("/list")
    public TableDataInfo list(ProductOrder productOrder) {
        startPage();
        List<ProductOrder> list = productOrderService.selectProductOrderList(productOrder);
        return getDataTable(list);
    }

    /**
     * 获取生产订单详情
     */
    @ApiOperation("获取生产订单详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(productOrderService.getById(id));
    }

    /**
     * 新增生产订单
     */
    @ApiOperation("新增生产订单")
    @PostMapping
    public AjaxResult add(@RequestBody ProductOrder productOrder) {
        if(StrUtil.isEmpty(productOrder.getOrderNum())){
            Snowflake snowflake = IdUtil.getSnowflake(1, 1);
            long id = snowflake.nextId();
            productOrder.setOrderNum(Convert.toStr(id).toUpperCase());
        }
        return toBooleanAjax(productOrderService.save(productOrder));
    }

    /**
     * 修改生产订单
     */
    @ApiOperation("修改生产任务")
    @PutMapping
    public AjaxResult edit(@RequestBody ProductOrder productOrder) {
        productOrder.setCompleteTime(new Date());
        return toBooleanAjax(productOrderService.updateById(productOrder));
    }

    /**
     * 删除生产订单
     */
    @ApiOperation("删除生产任务")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(productOrderService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 获取当前设备可以执行的工单
     *
     * @param equipmentId
     * @return
     */
    @GetMapping("/listExecuteOrder")
    public AjaxResult listExecuteOrder(Long equipmentId) {
        return AjaxResult.success(productOrderService.listExecuteOrder(equipmentId));
    }

    @ApiOperation("工单日产能报表")
    @GetMapping("/dailyCapacity")
    public TableDataInfo dailyCapacity(ProductOrder productOrder) {
        startPage();
        String day = "00:00";
        String night="23:59";
        String rq = DateUtil.formatDate(new Date());
        String start = rq + " " + day + ":00";
        String end = rq + " " + night + ":00";
        List<ProductOrder> list = productOrderService.selectProductOrderList(productOrder);
        for (int i =0 ;i< list.size(); i++) {
            ProductOrder order =list.get(i);
            QueryWrapper<ProcessGroupDetail> groupDetailQueryWrapper = new QueryWrapper<>();
            groupDetailQueryWrapper.eq("group_id", order.getProcessGroupId());// 查找工单中的工序组id
            List<ProcessGroupDetail> groupList = groupDetailService.list(groupDetailQueryWrapper);
            int processId = 0;
            int sort = 0;
            for (ProcessGroupDetail processGroupDetail : groupList) {
                int temp;
                temp = processGroupDetail.getSortNum();
                if (temp > processId) {
                    sort = temp;
                }
                QueryWrapper<ProcessGroupDetail> groupDetailQueryWrapper1 = new QueryWrapper<>();
                groupDetailQueryWrapper1.eq("sort_num", sort);
                List<ProcessGroupDetail> groupList1 = groupDetailService.list(groupDetailQueryWrapper);//查找出最后一道工序的processid
                for (ProcessGroupDetail groupDetail : groupList1) {
                    processId = groupDetail.getProcessId();//将工序id赋值给process_id
                }
                QueryWrapper<WorkReport> workReportQueryWrapper = new QueryWrapper<>();
                workReportQueryWrapper.eq("process_id", processGroupDetail.getProcessId())
                        .eq("order_id", order.getId())
                        .between("report_time",start,end).select("sum(report_num) as total");
                Map<String, Object> map = workReportService.getMap(workReportQueryWrapper);
                QueryWrapper<WorkReport> workReportQueryWrapper1 = new QueryWrapper<>();
                workReportQueryWrapper1.eq("process_id", processGroupDetail.getProcessId())
                        .eq("order_id", order.getId()).select("sum(waste_num) as waste");
                Map<String, Object> map1 = workReportService.getMap(workReportQueryWrapper1);
                Integer total = null;
                if(map!=null){
                    total = Integer.parseInt(map.get("total").toString()) ;
                }else {
                    total = 0;
                }
                Integer totalwaste = null;
                if(map1!=null){
                    totalwaste = Integer.parseInt(map1.get("waste").toString()) ;
                }else {
                    totalwaste = 0;
                }
                if (order.getOrderFinishNum()==null){
                    order.setOrderFinishNum(0);
                }
                order.setWasteNum(totalwaste);
                order.setReportNum(total);
            }
        }
        return getDataTable(list);
    }
    @ApiOperation("获取生产订单产品详情")
    @GetMapping("/getProductionList")
    public  AjaxResult getProductionList(Integer orderId) {
            QueryWrapper<ProductOrder> productOrderQueryWrapper = new QueryWrapper<>();
            productOrderQueryWrapper.eq("task_id", orderId);
            List<ProductOrder> list = productOrderService.list(productOrderQueryWrapper);
            List<TMaterial> materialVos = new ArrayList<>();
            if (list.size() > 0) {
                for (ProductOrder order : list) {
                    TMaterial materialVo = new TMaterial();
                    QueryWrapper<TMaterial> materialQueryWrapper = new QueryWrapper<>();
                    materialQueryWrapper.eq("id", order.getProductionId());
                    List<TMaterial> materialList = materialService.list(materialQueryWrapper);
                    if (materialList.size() > 0) {
                        for (TMaterial tMaterial : materialList) {
                            materialVo.setMaterielName(tMaterial.getMaterielName());
                            materialVo.setMaxTime(order.getProductionNum());
                            materialVo.setMaterielSunit(order.getUnit());
                            materialVo.setMaterielNorms(tMaterial.getMaterielNorms());
                            materialVos.add(materialVo);
                        }
                    }
                }
            }
        return AjaxResult.success(materialVos);
    }
}