package com.boyo.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.iot.domain.IotEquipmentProp;
import com.boyo.iot.entity.FaultProp;
import com.boyo.iot.entity.IorealData;
import com.boyo.iot.vo.IoTAttrVO;

import java.util.List;

/**
 * 设备属性管理Service接口
 *
 * <AUTHOR>
 */
public interface IIotEquipmentPropService extends IService<IotEquipmentProp> {
    /**
     * 根据条件查询查询设备属性管理列表
     *
     * @param iotEquipmentProp 设备属性管理
     * @return 设备属性管理集合
     */
    List<IoTAttrVO> selectIotEquipmentPropList(IotEquipmentProp iotEquipmentProp);

    void executeEquipmentFaultProp(String code,List<IorealData> realList);
}
