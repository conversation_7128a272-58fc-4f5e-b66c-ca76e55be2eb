package com.boyo.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.exception.CustomException;
import com.boyo.framework.annotation.Tenant;
import com.boyo.mes.mapper.TFactoryOrderMapper;
import com.boyo.mes.entity.TFactoryOrder;
import com.boyo.mes.service.TFactoryOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

/**
 * (TFactoryOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-06 16:18:20
 */
@Service("tFactoryOrderService")
@Tenant
public class TFactoryOrderServiceImpl extends ServiceImpl<TFactoryOrderMapper, TFactoryOrder> implements TFactoryOrderService {

    @Autowired
    private TFactoryOrderMapper orderMapper;

    @Override
    public List<TFactoryOrder> selectList(TFactoryOrder factoryOrder) {
        return orderMapper.selectFactoryOrderList(factoryOrder);
    }

    @Override
    public boolean save(TFactoryOrder workReport){
        QueryWrapper<TFactoryOrder > factoryOrderQueryWrapper = new QueryWrapper<>();
        factoryOrderQueryWrapper.eq("order_name",workReport.getOrderName());
        List<TFactoryOrder> list = orderMapper.selectList(factoryOrderQueryWrapper);
        if(list.size() > 0){
            throw  new CustomException("订单名称重复");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
        QueryWrapper<TFactoryOrder> factoryOrderQueryWrapper1 = new QueryWrapper<>();
        factoryOrderQueryWrapper1.like("order_num",simpleDateFormat.format(new Date())).orderByDesc("create_time");
        List<TFactoryOrder> list1 = orderMapper.selectList(factoryOrderQueryWrapper1);
        if(list1.size()>0){
            String[] s = list1.get(0).getOrderNum().split("_");
            DecimalFormat decimalFormat = new DecimalFormat("0000");
            String maxSerialNumber = decimalFormat.format( Integer.parseInt(s[1])+ 1);
            System.out.println(maxSerialNumber);
            String id = s[0]+"_"+maxSerialNumber;
            workReport.setOrderNum(id);
        }else {
            workReport.setOrderNum("DD"+simpleDateFormat.format(new Date())+"_"+"0001");
        }
        workReport.setOrderStatus(0);
        workReport.setCreateTime(new Date());
        LocalDate date = LocalDate.parse(simpleDateFormat1.format(workReport.getDeliveryTime()));
        LocalDate date1 =LocalDate.parse(simpleDateFormat1.format(workReport.getCreateTime()));
        int days = (int) ChronoUnit.DAYS.between(date1, date);
        workReport.setDeliveryCycle(days);
        return super.save(workReport);
    }

    @Override
    public boolean updateById(TFactoryOrder workReport){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        QueryWrapper<TFactoryOrder> tFactoryOrderQueryWrapper = new QueryWrapper<>();
        tFactoryOrderQueryWrapper.eq("id",workReport.getId());
        List<TFactoryOrder> list = orderMapper.selectList(tFactoryOrderQueryWrapper);
        if(list.size()>0){
            for (TFactoryOrder tFactoryOrder : list) {
                LocalDate date = LocalDate.parse(simpleDateFormat.format(tFactoryOrder.getDeliveryTime()));
                LocalDate date1 =LocalDate.parse(simpleDateFormat.format(tFactoryOrder.getCreateTime()));
                int days = (int) ChronoUnit.DAYS.between(date1, date);
                workReport.setDeliveryCycle(days);
            }
        }
        try {
            workReport.setUpdateTime(simpleDateFormat.parse(simpleDateFormat1.format(new Date())));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return super.updateById(workReport);
    }
}

