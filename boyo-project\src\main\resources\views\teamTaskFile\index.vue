<template>
  <base-page :config="config">
    <span slot="operation" slot-scope="text, record">
      <a @click="$refs.createForm.handleUpdate($event,record.id)">
      <a-icon type="edit"/>{{ $t('app.global.edit') }}
      </a>
      <a-divider type="vertical"/>
      <a @click="handleDelete(record.id)"> <a-icon type="delete"/>{{ $t('app.global.delete') }}</a>
    </span>
    <!-- 弹窗 -->
    <create-form ref="createForm" :statusOptions="statusOptions" @ok="getList"/>
  </base-page>
</template>

<script>
import CreateForm from './modules/CreateForm'
import {delTeamTaskFile, listTeamTaskFile} from '@/api/teamTaskFile'

export default {
  components: {
    CreateForm,
  },
  data() {
    return {
      // 页面加载状态
      loading: false,
      // 数据列表
      list: [],
      // 表格数据总数
      total: 0,
      // 状态数据字典
      statusOptions: [],
    }
  },
  async created() {
    this.getList()
  },
  computed: {
    config() {
      return {
        loading: this.loading,
        query: {
          onQuery: this.getList,
          items: [
            {label: '项目code', name: 'projectCode'},
            {label: '任务code', name: 'taskCode'},
            {label: '文件名', name: 'fileName'},
            {label: '文件路径', name: 'fileUrl'},
            {label: '上传时间', name: 'createTime'},
            {label: '创建人', name: 'createUser'},
          ],
        },
        action: {
          add: () => this.$refs.createForm.handleAdd(),
          delete: this.handleDelete,
        },
        table: {
          total: this.total,
          list: this.list,
          columns: [
            {
              title: '项目code',
              dataIndex: 'projectCode',
              align: 'center',
            },
            {
              title: '任务code',
              dataIndex: 'taskCode',
              align: 'center',
            },
            {
              title: '文件名',
              dataIndex: 'fileName',
              align: 'center',
            },
            {
              title: '文件路径',
              dataIndex: 'fileUrl',
              align: 'center',
            },
            {
              title: '上传时间',
              dataIndex: 'createTime',
              align: 'center',
            },
            {
              title: '创建人',
              dataIndex: 'createUser',
              align: 'center',
            },
            {
              title: this.$t('app.global.operation'),
              dataIndex: 'operation',
              scopedSlots: {customRender: 'operation'},
              align: 'center',
            },
          ],
        },
      }
    },
  },
  methods: {
    /**
     * 查询${tableInfo.comment}列表
     */
    async getList(queryParam) {
      this.loading = true
      if (queryParam !== undefined) {
        this.queryParam = queryParam
      }
      this.loading = true
      const response = await listTeamTaskFile(this.queryParam)
      this.list = response.rows
      this.total = response.total
      this.loading = false
    },
    /**
     * 删除按钮操作
     */
    handleDelete(id) {

      const ids = id || this.ids
      this.$alert.confirm({
        content: this.$t('app.global.delete.content'),
        onOk: async () => {
          await delTeamTaskFile(ids)
          this.getList()
          this.$alert.success(this.$t('app.global.delete.success'))
        },
      })
    },
  },
}
</script>
}
