package com.boyo.web.controller.haihui;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.processor.TenantProcessor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * HaiHuiScreenController 测试类
 * 主要测试定时任务中的租户数据源切换功能
 */
public class HaiHuiScreenControllerTest {

    @InjectMocks
    private HaiHuiScreenController haiHuiScreenController;

    @Mock
    private TenantProcessor tenantProcessor;

    @Mock
    private IIotEquipmentService iotEquipmentService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 设置私有字段
        ReflectionTestUtils.setField(haiHuiScreenController, "tenantProcessor", tenantProcessor);
        ReflectionTestUtils.setField(haiHuiScreenController, "iotEquipmentService", iotEquipmentService);
    }

    @Test
    void testSendPowerData_SuccessfulTenantSwitch() {
        // 模拟租户数据源检查成功
        when(tenantProcessor.checkDataSource(anyString())).thenReturn(true);

        // 模拟设备服务返回null（避免实际数据库调用）
        when(iotEquipmentService.getEquipmentDetail(250)).thenReturn(null);
        when(iotEquipmentService.getEquipmentDetail(251)).thenReturn(null);
        when(iotEquipmentService.getEquipmentByCode("250")).thenReturn(null);
        when(iotEquipmentService.getEquipmentByCode("251")).thenReturn(null);

        // 执行定时任务（这个测试主要验证不会抛出异常）
        try {
            haiHuiScreenController.sendPowerData();
        } catch (Exception e) {
            // 如果有异常，打印出来以便调试
            e.printStackTrace();
            throw e;
        }

        // 验证租户数据源检查被调用
        verify(tenantProcessor, times(1)).checkDataSource("2db4bfe2cbe14269b410e4747073ee47");
    }

    @Test
    void testSendPowerData_TenantSwitchFailure() {
        // 模拟租户数据源检查失败
        when(tenantProcessor.checkDataSource(anyString())).thenReturn(false);

        // 执行定时任务
        haiHuiScreenController.sendPowerData();

        // 验证租户数据源检查被调用
        verify(tenantProcessor, times(1)).checkDataSource("2db4bfe2cbe14269b410e4747073ee47");
        
        // 验证设备服务没有被调用（因为租户切换失败，方法应该提前返回）
        verify(iotEquipmentService, never()).getEquipmentDetail(anyInt());
        verify(iotEquipmentService, never()).getEquipmentByCode(anyString());
    }

    @Test
    void testSendPowerData_TenantSwitchException() {
        // 模拟租户数据源检查抛出异常
        when(tenantProcessor.checkDataSource(anyString())).thenThrow(new RuntimeException("数据源连接失败"));

        // 执行定时任务
        haiHuiScreenController.sendPowerData();

        // 验证租户数据源检查被调用
        verify(tenantProcessor, times(1)).checkDataSource("2db4bfe2cbe14269b410e4747073ee47");
        
        // 验证设备服务没有被调用（因为异常，方法应该提前返回）
        verify(iotEquipmentService, never()).getEquipmentDetail(anyInt());
        verify(iotEquipmentService, never()).getEquipmentByCode(anyString());
    }
}
        // 验证设备服务没有被调用（因为异常，方法应该提前返回）
        verify(iotEquipmentService, never()).getEquipmentDetail(anyInt());
        verify(iotEquipmentService, never()).getEquipmentByCode(anyString());
    }
}
