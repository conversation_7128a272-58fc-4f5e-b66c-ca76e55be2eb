package com.boyo.project.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.project.entity.*;
import com.boyo.project.mapper.*;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.project.service.ITeamProjectService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 项目表(TeamProject)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-09 11:07:52
 */
@Service("teamProjectService")
@AllArgsConstructor
@Tenant
public class TeamProjectServiceImpl extends ServiceImpl<TeamProjectMapper, TeamProject> implements ITeamProjectService {
    private final TeamProjectMapper teamProjectMapper;
    private final TeamTaskStagesTemplateMapper stagesTemplateMapper;
    private final TeamTaskStagesMapper taskStagesMapper;
    private final TeamProjecmemberMapper memberMapper;
    private final TeamTaskMapper taskMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<TeamProject> selectTeamProjectList(TeamProject teamProject) {
        teamProject.setCurrentUser(SecurityUtils.getUserOpenid());
        List<TeamProject> list = teamProjectMapper.selectTeamProjectList(teamProject);
        for (int i = 0; i < list.size(); i++) {
            QueryWrapper<TeamTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("project_code", list.get(i).getCode());
            list.get(i).setTaskCount(taskMapper.selectCount(queryWrapper));
            QueryWrapper<TeamTask> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("project_code", list.get(i).getCode()).eq("execute_status", "done");
            list.get(i).setCompleteCount(taskMapper.selectCount(queryWrapper1));
        }
        return list;
    }

    @Override
    public List<TeamProject> selectAllProjectList(TeamProject teamProject) {
        List<TeamProject> list = teamProjectMapper.selectAllProjectList(teamProject);
        for (int i = 0; i < list.size(); i++) {
            QueryWrapper<TeamTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("project_code", list.get(i).getCode());
            list.get(i).setTaskCount(taskMapper.selectCount(queryWrapper));
            QueryWrapper<TeamTask> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("project_code", list.get(i).getCode()).eq("execute_status", "done");
            list.get(i).setCompleteCount(taskMapper.selectCount(queryWrapper1));
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, propagation = Propagation.REQUIRED)
    public boolean save(TeamProject entity) {
//        复制项目来源
        String conver = entity.getCover();
//        项目基本信息
        entity.setCover("");
        entity.setCreateTime(DateUtil.formatTime(new Date()));
        entity.setCode(IdUtil.fastSimpleUUID());
        super.save(entity);
        if (StrUtil.isEmpty(conver)) {
            //        项目阶段模板信息
            QueryWrapper<TeamTaskStagesTemplate> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("project_template_code", entity.getTemplateCode());
            List<TeamTaskStagesTemplate> list = stagesTemplateMapper.selectList(queryWrapper);
            if (list != null && list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    TeamTaskStages stages = new TeamTaskStages();
                    stages.setProjectCode(entity.getCode());
                    stages.setCreateTime(DateUtil.formatDateTime(new Date()));
                    stages.setName(list.get(i).getName());
                    stages.setCode(IdUtil.fastSimpleUUID());
                    stages.setSort(list.get(i).getSort());
                    taskStagesMapper.insert(stages);
                }
            }
        } else {
            TeamProject clone = super.getById(conver);
            QueryWrapper<TeamTaskStages> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("project_code", clone.getCode());
            List<TeamTaskStages> stagesList = taskStagesMapper.selectList(queryWrapper);
            System.out.println(JSONObject.toJSONString(stagesList));

            QueryWrapper<TeamTask> taskQueryWrapper = new QueryWrapper<>();
            taskQueryWrapper.eq("project_code", clone.getCode());
            List<TeamTask> taskList = taskMapper.selectList(taskQueryWrapper);
            System.out.println(JSONObject.toJSONString(taskList));
            if (stagesList != null && stagesList.size() > 0) {
                for (int i = 0; i < stagesList.size(); i++) {
                    String code = ObjectUtil.clone(stagesList.get(i).getCode());
                    stagesList.get(i).setProjectCode(entity.getCode());
                    stagesList.get(i).setCode(IdUtil.fastSimpleUUID());
                    stagesList.get(i).setCreateTime(DateUtil.formatDateTime(new Date()));
                    taskStagesMapper.insert(stagesList.get(i));
                    if (taskList != null && taskList.size() > 0) {
                        for (int j = 0; j < taskList.size(); j++) {
                            TeamTask task = taskList.get(j);
                            if (task.getStageCode().equals(code)) {
                                task.setProjectCode(entity.getCode());
                                task.setCode(IdUtil.fastSimpleUUID());
                                task.setExecuteStatus("wait");
                                task.setCreateBy(SecurityUtils.getUserOpenid());
                                task.setCreateTime(DateUtil.formatDateTime(new Date()));
                                task.setAssignTo(entity.getWhiteList());
                                task.setStageCode(stagesList.get(i).getCode());
                                task.setDone("0");
                                task.setBeginTime("");
                                task.setEndTime("");
                                taskMapper.insert(task);
                            } else {
                                continue;
                            }
                        }
                    }
                }
            }
        }

//        项目归属人
        {
            TeamProjecmember member = new TeamProjecmember();
            member.setMemberCode(SecurityUtils.getUserOpenid());
            member.setProjectCode(entity.getCode());
            member.setIsOwner(1);
            member.setJoinTime(DateUtil.formatDateTime(new Date()));
            memberMapper.insert(member);
        }

//        项目负责人
        {
            if (!entity.getWhiteList().equals(SecurityUtils.getUserOpenid())) {
                TeamProjecmember member = new TeamProjecmember();
                member.setMemberCode(entity.getWhiteList());
                member.setProjectCode(entity.getCode());
                member.setIsOwner(0);
                member.setJoinTime(DateUtil.formatDateTime(new Date()));
                memberMapper.insert(member);
            }
        }
        return true;
    }

    @Override
    public boolean updateById(TeamProject entity) {
        TeamProject old = super.getById(entity.getId());
        if (ObjectUtil.isNull(old.getWhiteList()) || !old.getWhiteList().equals(entity.getWhiteList())) {
            QueryWrapper<TeamProjecmember> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("project_code", old.getCode()).eq("member_code", entity.getWhiteList());
            if (memberMapper.selectCount(queryWrapper) == 0) {
                TeamProjecmember member = new TeamProjecmember();
                member.setMemberCode(entity.getWhiteList());
                member.setProjectCode(entity.getCode());
                member.setIsOwner(0);
                member.setJoinTime(DateUtil.formatDateTime(new Date()));
                memberMapper.insert(member);
            }
        }
        return super.updateById(entity);
    }
}
