package com.boyo.master.domain;

import cn.hutool.core.date.DateTime;
import com.boyo.common.annotation.Excel;
import com.boyo.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.time.LocalDate;
import java.util.Date;

/**
 * 【请填写功能名称】对象 haihui_sale
 *
 * <AUTHOR>
 * @date 2024-10-24
 */
public class HaihuiSale extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 合同编号 */
    private String htNo;

    /** 付款条件 */
    @Excel(name = "付款条件")
    private String payment;

    /** 合同履行状态，0:未生产|1:生产中|2:生产完成|3:发货中|5:发货完成|6:开始安装|4:安装中|7:安装完成|9:正在验收|8:验收完成|10:收款完成|11:作废/终止 */
    @Excel(name = "合同履行状态，0:未生产|1:生产中|2:生产完成|3:发货中|5:发货完成|6:开始安装|4:安装中|7:安装完成|9:正在验收|8:验收完成|10:收款完成|11:作废/终止")
    private String htlxzt;

    /** 状态,0:执行中|1:完成|2:终止|3:已作废 */
    @Excel(name = "状态,0:执行中|1:完成|2:终止|3:已作废")
    private String state;

    /** 我方单位,16093aab-8051-4567-b3cb-041454b4fe59:海汇环保设备有限公司|*************-4c40-b0eb-f8d193a88745:海汇环保设备股份有限公司 */
    @Excel(name = "我方单位,16093aab-8051-4567-b3cb-041454b4fe59:海汇环保设备有限公司|*************-4c40-b0eb-f8d193a88745:海汇环保设备股份有限公司")
    private String signatory;

    /** 金额 */
    @Excel(name = "金额")
    private Double amount;

    /** 回款金额 */
    @Excel(name = "回款金额")
    private  Double payament;

    /** 未回款金额 */
    @Excel(name = "未回款金额")
    private Double nopayment;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String custName;

    /** 开始日期 */
    private String startdate;

    /** 结束日期 */
    private String enddate;

    private Date saveTime;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String ex1;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String ex2;

    public void setHtNo(String htNo)
    {
        this.htNo = htNo;
    }

    public String getHtNo()
    {
        return htNo;
    }
    public void setPayment(String payment)
    {
        this.payment = payment;
    }

    public String getPayment()
    {
        return payment;
    }
    public void setHtlxzt(String htlxzt)
    {
        this.htlxzt = htlxzt;
    }

    public String getHtlxzt()
    {
        return htlxzt;
    }
    public void setState(String state)
    {
        this.state = state;
    }

    public String getState()
    {
        return state;
    }
    public void setSignatory(String signatory)
    {
        this.signatory = signatory;
    }

    public String getSignatory()
    {
        return signatory;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getPayament() {
        return payament;
    }

    public void setPayament(Double payament) {
        this.payament = payament;
    }

    public Double getNopayment() {
        return nopayment;
    }

    public void setNopayment(Double nopayment) {
        this.nopayment = nopayment;
    }

    public void setCustName(String custName)
    {
        this.custName = custName;
    }

    public String getCustName()
    {
        return custName;
    }

    public String getStartdate() {
        return startdate;
    }

    public void setStartdate(String startdate) {
        this.startdate = startdate;
    }

    public String getEnddate() {
        return enddate;
    }

    public void setEnddate(String enddate) {
        this.enddate = enddate;
    }

    public Date getSaveTime() {
        return saveTime;
    }

    public void setSaveTime(Date saveTime) {
        this.saveTime = saveTime;
    }

    public void setEx1(String ex1)
    {
        this.ex1 = ex1;
    }

    public String getEx1()
    {
        return ex1;
    }
    public void setEx2(String ex2)
    {
        this.ex2 = ex2;
    }

    public String getEx2()
    {
        return ex2;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("htNo", getHtNo())
                .append("payment", getPayment())
                .append("htlxzt", getHtlxzt())
                .append("state", getState())
                .append("signatory", getSignatory())
                .append("amount", getAmount())
                .append("payament", getPayament())
                .append("nopayment", getNopayment())
                .append("custName", getCustName())
                .append("startdate", getStartdate())
                .append("enddate", getEnddate())
                .append("remark", getRemark())
                .append("ex1", getEx1())
                .append("ex2", getEx2())
                .toString();
    }
}