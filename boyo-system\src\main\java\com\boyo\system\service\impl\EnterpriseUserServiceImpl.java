package com.boyo.system.service.impl;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.system.domain.EnterpriseUserRole;
import com.boyo.system.mapper.EnterpriseUserRoleMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.system.mapper.EnterpriseUserMapper;
import com.boyo.system.service.IEnterpriseUserService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 企业用户管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class EnterpriseUserServiceImpl extends ServiceImpl<EnterpriseUserMapper, EnterpriseUser> implements IEnterpriseUserService {
    private final EnterpriseUserMapper enterpriseUserMapper;

    private final EnterpriseUserRoleMapper userRoleMapper;


    /**
     * 查询企业用户管理列表
     *
     * @param enterpriseUser 企业用户管理
     * @return enterpriseUser 列表
     */
    @Override
    public List<EnterpriseUser> selectEnterpriseUserList(EnterpriseUser enterpriseUser) {
        return enterpriseUserMapper.selectEnterpriseUserList(enterpriseUser);
    }

    @Override
    public EnterpriseUser selectUserByOpenid(String openid) {
        QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_wechat",openid);
        return enterpriseUserMapper.selectOne(queryWrapper);
    }

    @Override
    public List<EnterpriseUser> selectByIds(List<Long> ids) {
        return enterpriseUserMapper.selectBatchIds(ids);
    }

    @Override
    public List<EnterpriseUser> selectByOpenIds(List<String> openids) {
        QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("user_openid",openids);
        return enterpriseUserMapper.selectList(queryWrapper);
    }

    @Override
    public EnterpriseUser selectByOpenId(String openid) {
        QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_openid",openid);
        return enterpriseUserMapper.selectOne(queryWrapper);
    }

    @Override
    public boolean updateOwnUser(EnterpriseUser enterpriseUser) {
        if(!SecurityUtils.getLoginUser().getEnterpriseUser().getUserOpenid().equals(enterpriseUser.getUserOpenid())){
            throw new CustomException("只能修改自己的信息");
        }
        enterpriseUserMapper.updateById(enterpriseUser);
        return true;
    }

    @Override
    public boolean updateWechat(String openid, String wechat) {
        QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_openid",openid);
        EnterpriseUser user = enterpriseUserMapper.selectOne(queryWrapper);
        user.setUserWechat(wechat);
        enterpriseUserMapper.updateById(user);
        return true;
    }

    @Override
    public EnterpriseUser selectUserByOpenidAndUsername(String openid, String name) {
        QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_openid",openid);
        queryWrapper.eq("user_name",name);
        EnterpriseUser enterpriseUser = enterpriseUserMapper.selectOne(queryWrapper);
        return enterpriseUser;
    }

    @Override
    public int saveEnterpriseUser(EnterpriseUser enterpriseUser) {
        return enterpriseUserMapper.saveEnterpriseUser(enterpriseUser);
    }

    @Override
    public void updateEnterpriseUserById(EnterpriseUser enterpriseUser) {
        List<String> roleList = enterpriseUser.getRoleList();
        if(roleList != null && roleList.size() > 0){
            for (int i = 0; i < roleList.size(); i++) {
                EnterpriseUserRole userRole = new EnterpriseUserRole();
                userRole.setUserOpenid(enterpriseUser.getUserOpenid());
                userRole.setCreateTime(new Date());
                userRole.setUpdateTime(new Date());
                userRole.setRoleOpenid(roleList.get(i));
                userRoleMapper.insert(userRole);
            }
        }
        enterpriseUserMapper.updateEnterpriseUserById(enterpriseUser);
    }

    /**
     * 新增用户
     * @param entity
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, propagation = Propagation.REQUIRED)
    public boolean save(EnterpriseUser entity) {
        QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_name",entity.getUserName()).eq("enterprise_openid", SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid());
        if(enterpriseUserMapper.selectCount(queryWrapper) > 0){
            throw new CustomException("用户名已存在!");
        }
        queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_phone",entity.getUserPhone()).eq("enterprise_openid", SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid());
        if(enterpriseUserMapper.selectCount(queryWrapper) > 0){
            throw new CustomException("手机号已存在!");
        }
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setUserOpenid(IdUtil.fastSimpleUUID());
        entity.setUserPassword(SecurityUtils.encryptPassword("123456"));
        entity.setEnterpriseOpenid(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid());
        List<String> roleList = entity.getRoleList();
        if(roleList != null && roleList.size() > 0){
            for (int i = 0; i < roleList.size(); i++) {
                EnterpriseUserRole userRole = new EnterpriseUserRole();
                userRole.setUserOpenid(entity.getUserOpenid());
                userRole.setCreateTime(new Date());
                userRole.setUpdateTime(new Date());
                userRole.setRoleOpenid(roleList.get(i));
                userRoleMapper.insert(userRole);
            }
        }
        entity.setUserAdmin(0L);
        return super.save(entity);
    }

    @Override
    public EnterpriseUser getById(Serializable id) {
        QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id",id).eq("enterprise_openid",SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid());
        EnterpriseUser user = enterpriseUserMapper.selectOne(queryWrapper);
        if(ObjectUtil.isNull(user)){
            throw new CustomException("用户不存在！");
        }
        QueryWrapper<EnterpriseUserRole> roleQueryWrapper = new QueryWrapper<>();
        roleQueryWrapper.eq("user_openid",user.getUserOpenid());
        List<EnterpriseUserRole> roleList = userRoleMapper.selectList(roleQueryWrapper);
        List<String> roles = new ArrayList<>();
        if(roleList != null && roleList.size() > 0){
            for (int i = 0; i < roleList.size(); i++) {
                roles.add(roleList.get(i).getRoleOpenid());
            }
            user.setRoleList(roles);
        }
        return user;
    }

    @Override
    public boolean updateById(EnterpriseUser entity) {
        QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id",entity.getId()).eq("enterprise_openid",SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid());
        EnterpriseUser user = enterpriseUserMapper.selectOne(queryWrapper);
        if(ObjectUtil.isNull(user)){
            throw new CustomException("用户不存在！");
        }
        QueryWrapper<EnterpriseUserRole> roleQueryWrapper = new QueryWrapper<>();
        roleQueryWrapper.eq("user_openid",user.getUserOpenid());
        userRoleMapper.delete(roleQueryWrapper);
        List<String> roleList = entity.getRoleList();
        if(roleList != null && roleList.size() > 0){
            for (int i = 0; i < roleList.size(); i++) {
                EnterpriseUserRole userRole = new EnterpriseUserRole();
                userRole.setUserOpenid(entity.getUserOpenid());
                userRole.setCreateTime(new Date());
                userRole.setUpdateTime(new Date());
                userRole.setRoleOpenid(roleList.get(i));
                userRoleMapper.insert(userRole);
            }
        }
        entity.setUpdateTime(new Date());
        return super.updateById(entity);
    }
}
