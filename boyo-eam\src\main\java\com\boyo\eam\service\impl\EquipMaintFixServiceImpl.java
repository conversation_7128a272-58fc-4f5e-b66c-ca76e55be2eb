package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipMaintFix;
import com.boyo.eam.mapper.EquipLedgerMapper;
import com.boyo.eam.mapper.EquipMaintFixMapper;
import com.boyo.eam.service.IEquipMaintFixService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 维修任务管理(EquipMaintFix)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-17 16:13:53
 */
@Service("equipMaintFixService")
@AllArgsConstructor
@Tenant
public class EquipMaintFixServiceImpl extends ServiceImpl<EquipMaintFixMapper, EquipMaintFix> implements IEquipMaintFixService {
    private final EquipMaintFixMapper equipMaintFixMapper;
    private final EquipLedgerMapper equipLedgerMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipMaintFix> selectEquipMaintFixList(EquipMaintFix equipMaintFix) {
        List<EquipMaintFix> equipMaintFixeList = equipMaintFixMapper.selectEquipMaintFixList(equipMaintFix);
        return equipMaintFixeList;
    }

}
