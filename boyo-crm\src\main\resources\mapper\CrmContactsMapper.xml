<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmContactsMapper">

    <resultMap type="com.boyo.crm.entity.CrmContacts" id="CrmContactsResult">
        <result property="contactsId" column="contacts_id" />
        <result property="name" column="name" />
        <result property="nextTime" column="next_time" />
        <result property="mobile" column="mobile" />
        <result property="telephone" column="telephone" />
        <result property="email" column="email" />
        <result property="post" column="post" />
        <result property="customerId" column="customer_id" />
        <result property="address" column="address" />
        <result property="remark" column="remark" />
        <result property="createUserId" column="create_user_id" />
        <result property="ownerUserId" column="owner_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmContactsList" parameterType="com.boyo.crm.entity.CrmContacts" resultMap="CrmContactsResult">
        select
          contacts_id, name, next_time, mobile, telephone, email, post, customer_id, address, remark, create_user_id, owner_user_id, create_time, update_time
        from t_crm_contacts
        <where>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="nextTime != null">
                and next_time = #{nextTime}
            </if>
            <if test="mobile != null and mobile != ''">
                and mobile = #{mobile}
            </if>
            <if test="telephone != null and telephone != ''">
                and telephone = #{telephone}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="post != null and post != ''">
                and post = #{post}
            </if>
            <if test="customerId != null">
                and customer_id = #{customerId}
            </if>
            <if test="address != null and address != ''">
                and address = #{address}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="ownerUserId != null">
                and owner_user_id = #{ownerUserId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            ${params.dataScope}
        </where>
    </select>
</mapper>

