package com.boyo.master.domain;

import com.boyo.common.annotation.Excel;
import com.boyo.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 生产计划对象 mes_production_plan
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
public class MesProductionPlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 计划号 */
    @Excel(name = "计划号")
    private String planCode;

    /** 项目编码 */
    @Excel(name = "项目编码")
    private String projectNumber;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 计划数量 */
    @Excel(name = "计划数量")
    private Long quantity;

    /** 已拆分数量 */
    @Excel(name = "已拆分数量")
    private Long splitQuantity;

    /** 未拆分数量 */
    @Excel(name = "未拆分数量")
    private Long notSplitQuantity;

    /** 预开工日 */
    @Excel(name = "预开工日")
    private String preOpeningDays;

    /** 预完成日 */
    @Excel(name = "预完成日")
    private String preCompletionDays;

    /** 生产类型（是否委外） 0是 1否 */
    @Excel(name = "生产类型", readConverterExp = "是=否委外")
    private String agentType;

    /** 派工状况 0未派工 1已派工 2报工 3报工完成 */
    @Excel(name = "派工状况 0未派工 1已派工 2报工 3报工完成")
    private String status;

    /** 派工单号 */
    @Excel(name = "派工单号")
    private String noticeNo;

    /** 派工数量 */
    @Excel(name = "派工数量")
    private Long dispatchQuantity;

    /** 完工数量 */
    @Excel(name = "完工数量")
    private Long workReportsQuantity;

    /** 未完工数量 */
    @Excel(name = "未完工数量")
    private Long workNotReportsQuantity;

    /** 合格数 */
    @Excel(name = "合格数")
    private Long qualifiedQuantity;

    /** 成品编号 */
    @Excel(name = "成品编号")
    private String productCode;

    /** 成品名称 */
    @Excel(name = "成品名称")
    private String productName;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }
    public void setPlanCode(String planCode) 
    {
        this.planCode = planCode;
    }

    public String getPlanCode() 
    {
        return planCode;
    }
    public void setProjectNumber(String projectNumber) 
    {
        this.projectNumber = projectNumber;
    }

    public String getProjectNumber() 
    {
        return projectNumber;
    }
    public void setProjectName(String projectName) 
    {
        this.projectName = projectName;
    }

    public String getProjectName() 
    {
        return projectName;
    }
    public void setQuantity(Long quantity) 
    {
        this.quantity = quantity;
    }

    public Long getQuantity() 
    {
        return quantity;
    }
    public void setSplitQuantity(Long splitQuantity) 
    {
        this.splitQuantity = splitQuantity;
    }

    public Long getSplitQuantity() 
    {
        return splitQuantity;
    }
    public void setNotSplitQuantity(Long notSplitQuantity) 
    {
        this.notSplitQuantity = notSplitQuantity;
    }

    public Long getNotSplitQuantity() 
    {
        return notSplitQuantity;
    }
    public void setPreOpeningDays(String preOpeningDays) 
    {
        this.preOpeningDays = preOpeningDays;
    }

    public String getPreOpeningDays() 
    {
        return preOpeningDays;
    }
    public void setPreCompletionDays(String preCompletionDays) 
    {
        this.preCompletionDays = preCompletionDays;
    }

    public String getPreCompletionDays() 
    {
        return preCompletionDays;
    }
    public void setAgentType(String agentType) 
    {
        this.agentType = agentType;
    }

    public String getAgentType() 
    {
        return agentType;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setNoticeNo(String noticeNo) 
    {
        this.noticeNo = noticeNo;
    }

    public String getNoticeNo() 
    {
        return noticeNo;
    }
    public void setDispatchQuantity(Long dispatchQuantity) 
    {
        this.dispatchQuantity = dispatchQuantity;
    }

    public Long getDispatchQuantity() 
    {
        return dispatchQuantity;
    }
    public void setWorkReportsQuantity(Long workReportsQuantity) 
    {
        this.workReportsQuantity = workReportsQuantity;
    }

    public Long getWorkReportsQuantity() 
    {
        return workReportsQuantity;
    }
    public void setWorkNotReportsQuantity(Long workNotReportsQuantity) 
    {
        this.workNotReportsQuantity = workNotReportsQuantity;
    }

    public Long getWorkNotReportsQuantity() 
    {
        return workNotReportsQuantity;
    }
    public void setQualifiedQuantity(Long qualifiedQuantity) 
    {
        this.qualifiedQuantity = qualifiedQuantity;
    }

    public Long getQualifiedQuantity() 
    {
        return qualifiedQuantity;
    }
    public void setProductCode(String productCode) 
    {
        this.productCode = productCode;
    }

    public String getProductCode() 
    {
        return productCode;
    }
    public void setProductName(String productName) 
    {
        this.productName = productName;
    }

    public String getProductName() 
    {
        return productName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("planCode", getPlanCode())
            .append("projectNumber", getProjectNumber())
            .append("projectName", getProjectName())
            .append("quantity", getQuantity())
            .append("splitQuantity", getSplitQuantity())
            .append("notSplitQuantity", getNotSplitQuantity())
            .append("preOpeningDays", getPreOpeningDays())
            .append("preCompletionDays", getPreCompletionDays())
            .append("agentType", getAgentType())
            .append("status", getStatus())
            .append("noticeNo", getNoticeNo())
            .append("dispatchQuantity", getDispatchQuantity())
            .append("workReportsQuantity", getWorkReportsQuantity())
            .append("workNotReportsQuantity", getWorkNotReportsQuantity())
            .append("qualifiedQuantity", getQualifiedQuantity())
            .append("productCode", getProductCode())
            .append("productName", getProductName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
