package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipInspectionSpotItem;

import java.util.List;

/**
 * 点检-项目(EquipInspectionSpotItem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-15 16:16:30
 */
public interface IEquipInspectionSpotItemService extends IService<EquipInspectionSpotItem> {

    /**
     * 查询多条数据
     *
     * @param equipInspectionSpotItem 对象信息
     * @return 对象列表
     */
    List<EquipInspectionSpotItem> selectEquipInspectionSpotItemList(EquipInspectionSpotItem equipInspectionSpotItem);

    /**
     * 获取详情和记录
     * @param id
     * @return
     */
    EquipInspectionSpotItem getItemAndRecord(Integer id);
}
