package com.boyo.system.service.impl;

import java.io.StringReader;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.system.domain.TSysEnterprise;
import com.boyo.system.mapper.TSysEnterpriseMapper;
import lombok.AllArgsConstructor;
import org.apache.ibatis.jdbc.ScriptRunner;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.system.mapper.DatabaseLogMapper;
import com.boyo.system.domain.DatabaseLog;
import com.boyo.system.service.IDatabaseLogService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据库变更管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class DatabaseLogServiceImpl extends ServiceImpl<DatabaseLogMapper, DatabaseLog> implements IDatabaseLogService {
    private final DatabaseLogMapper databaseLogMapper;
    private final TSysEnterpriseMapper tSysEnterpriseMapper;


    /**
     * 查询数据库变更管理列表
     *
     * @param databaseLog 数据库变更管理
     * @return databaseLog 列表
     */
    @Override
    public List<DatabaseLog> selectDatabaseLogList(DatabaseLog databaseLog) {
        return databaseLogMapper.selectDatabaseLogList(databaseLog);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, propagation = Propagation.REQUIRED)
    public void executeSql(Integer id) {
        QueryWrapper<TSysEnterprise> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enterprise_init","1").lt("enterprise_database_version",id);
        List<TSysEnterprise> enterpriseList = tSysEnterpriseMapper.selectList(queryWrapper);
        if(enterpriseList != null && enterpriseList.size() > 0){
            for (int i = 0; i < enterpriseList.size(); i++) {
                TSysEnterprise enterprise = enterpriseList.get(i);
                QueryWrapper<DatabaseLog> databaseQueryWrapper = new QueryWrapper<>();
                databaseQueryWrapper.le("id",id).gt("id",enterprise.getEnterpriseDatabaseVersion()).orderByAsc("id");
                List<DatabaseLog> logList = databaseLogMapper.selectList(databaseQueryWrapper);
                if(logList != null && logList.size() > 0){
                    Connection conn = null;
                    try {
                        conn = DriverManager.getConnection(enterprise.getEnterpriseDatabaseUrl(), enterprise.getEnterpriseDatabaseUsername(), enterprise.getEnterpriseDatabasePassword());
                        ScriptRunner runner = new ScriptRunner(conn);
                        runner.setAutoCommit(false);
                        runner.setErrorLogWriter(null);
                        runner.setLogWriter(null);
                        for (int j = 0; j < logList.size(); j++) {
                            runner.runScript(new StringReader(logList.get(j).getDatabaseChangeSql()));
                            enterprise.setEnterpriseDatabaseVersion(logList.get(j).getId());
                        }
                        tSysEnterpriseMapper.updateById(enterprise);
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }finally {
                        if (conn != null) {
                            try {
                                conn.close();
                            } catch (Exception e) {
                            }
                        }
                    }
                }
            }
        }
    }
}
