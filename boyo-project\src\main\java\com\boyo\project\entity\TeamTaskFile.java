package com.boyo.project.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (TeamTaskFile)实体类
 *
 * <AUTHOR>
 * @since 2022-02-17 21:39:42
 */
@Data
@TableName(value = "team_task_file")
public class TeamTaskFile implements Serializable {
    private static final long serialVersionUID = -73206733932855333L;
        /**
    * 主键
    */    
    @TableId
    private Integer id;
    
    /**
    * 项目code
    */
    @TableField(value="project_code")
    private String projectCode;
    /**
    * 任务code
    */
    @TableField(value="task_code")
    private String taskCode;
    /**
    * 文件名
    */
    @TableField(value="file_name")
    private String fileName;
    /**
    * 文件路径
    */
    @TableField(value="file_url")
    private String fileUrl;
    /**
    * 上传时间
    */
    @TableField(value="create_time")
    private Date createTime;
    /**
    * 创建人
    */
    @TableField(value="create_user")
    private String createUser;
    /**
    * 删除标记
    */
    @TableField(value="del_flag")
    private String delFlag;
    /**
    * 删除时间
    */
    @TableField(value="del_time")
    private Date delTime;

}
