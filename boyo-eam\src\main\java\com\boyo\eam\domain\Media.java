package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (Media)实体类
 *
 * <AUTHOR>
 * @since 2021-11-05 10:30:23
 */
@Data
@TableName(value = "t_media")
public class Media implements Serializable {
    private static final long serialVersionUID = -33770532049177730L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * 文件名
    */
    @TableField(value="file_name")
    private String fileName;
    /**
    * 旧文件名
    */
    @TableField(value="name")
    private String name;
    /**
    * 地址
    */
    @TableField(value="url")
    private String url;
    /**
    * 大小（kb）
    */
    @TableField(value="size")
    private Integer size;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private Date updateBy;

    @TableField(value="update_time")
    private Date updateTime;

}
