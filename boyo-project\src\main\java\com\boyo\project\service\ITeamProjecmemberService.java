package com.boyo.project.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.project.entity.TeamProjecmember;
import java.util.List;

/**
 * 项目-成员表(TeamProjecmember)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-10 16:43:19
 */
public interface ITeamProjecmemberService extends IService<TeamProjecmember> {

    /**
     * 查询多条数据
     *
     * @param teamProjecmember 对象信息
     * @return 对象列表
     */
    List<TeamProjecmember> selectTeamProjecmemberList(TeamProjecmember teamProjecmember);


}
