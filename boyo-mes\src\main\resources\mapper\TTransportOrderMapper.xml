<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.TTransportOrderMapper">

    <resultMap type="com.boyo.mes.entity.TTransportOrder" id="TTransportOrderMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orderNum" column="order_num" jdbcType="VARCHAR"/>
        <result property="orderName" column="order_name" jdbcType="VARCHAR"/>
        <result property="customerId" column="customer_id" jdbcType="INTEGER"/>
        <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
        <result property="number" column="number" jdbcType="INTEGER"/>
        <result property="unit" column="unit" jdbcType="VARCHAR"/>
        <result property="deliveryWay" column="delivery_way" jdbcType="VARCHAR"/>
        <result property="deliveryNum" column="delivery_num" jdbcType="INTEGER"/>
        <result property="invoiceNumber" column="invoice_number" jdbcType="VARCHAR"/>
        <result property="shippingStatus" column="shipping_status" jdbcType="INTEGER"/>
        <result property="consigner" column="consigner" jdbcType="VARCHAR"/>
        <result property="deliveryReport" column="delivery_report" jdbcType="VARCHAR"/>
        <result property="deliveryTime" column="delivery_time" jdbcType="TIMESTAMP"/>
        <result property="receiveTime" column="receive_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="selectTransportOrderList" resultType="com.boyo.mes.entity.TTransportOrder">
        select t1.*,t3.supplier_name as customer_name from
           (select
                * from t_transport_order
                <where>
                    <if test="orderNum != null and orderNum != ''">
                        and order_num like concat('%', #{orderNum}, '%')
                    </if>
                    <if test="orderName != null and orderName != ''">
                        and order_name concat('%', #{orderName}, '%')
                    </if>
                    <if test="startTime != null ">
                        and create_time between #{startTime} and #{endTime}
                    </if>
                    <if test="shippingStatus != null and shippingStatus != ''">
                        and shipping_status = #{shippingStatus}
                    </if>
                </where>
           ) t1  left join t_supplier t3 on t1.customer_Id = t3.id
        order by t1.create_time desc
    </select>

</mapper>

