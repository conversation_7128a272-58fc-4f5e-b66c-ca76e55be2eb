package com.boyo.system.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.constant.UserConstants;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.core.domain.entity.SysMenu;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.StringUtils;
import com.boyo.system.domain.TSysSystem;
import com.boyo.system.domain.vo.MetaVo;
import com.boyo.system.domain.vo.RouterVo;
import com.boyo.system.mapper.EnterpriseUserMapper;
import com.boyo.system.mapper.TSysSystemMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.system.mapper.BoyoMenuMapper;
import com.boyo.system.domain.BoyoMenu;
import com.boyo.system.service.IBoyoMenuService;

/**
 * 菜单管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class BoyoMenuServiceImpl extends ServiceImpl<BoyoMenuMapper, BoyoMenu> implements IBoyoMenuService {
    private final BoyoMenuMapper boyoMenuMapper;

    private final EnterpriseUserMapper userMapper;

    private final TSysSystemMapper sysSystemMapper;


    /**
     * 查询菜单管理列表
     *
     * @param boyoMenu 菜单管理
     * @return boyoMenu 列表
     */
    @Override
    public List<BoyoMenu> selectBoyoMenuList(BoyoMenu boyoMenu) {
        return boyoMenuMapper.selectBoyoMenuList(boyoMenu);
    }

    @Override
    public List<BoyoMenu> selectMenuTreeByUserId(String systemOpenid, String userOpenid) {
        QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_openid", userOpenid);
        EnterpriseUser enterpriseUser = userMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNull(enterpriseUser)) {
            throw new CustomException("用户不存在");
        }
        List<BoyoMenu> menus = new ArrayList<>();
        if (enterpriseUser.getUserAdmin().equals(1L)) {
            menus = boyoMenuMapper.selectMenuTreeByUserId(systemOpenid, enterpriseUser.getEnterpriseOpenid());
        } else {
            menus = boyoMenuMapper.selectMenuByUserId(userOpenid);
            List<Long> ids = new ArrayList<>();
            for (BoyoMenu menu : menus) {
                ids.add(menu.getMenuId());
            }
            int c = 0;
            while (c < 5){
                boolean checked = true;
                List<Long> wait = new ArrayList<>();
                for (BoyoMenu menu : menus) {
                    if(!menu.getParentId().equals(0L) && !ids.contains(menu.getParentId())){
                        wait.add(menu.getParentId());
                        checked = false;
                    }
                }
                if(wait != null && wait.size() > 0){
                    menus.addAll(boyoMenuMapper.selectBatchIds(wait));
                }
                if(checked){
                    break;
                }
                c ++;
            }

        }
//
        menus = menus.stream().distinct().collect(Collectors.toList());
        menus.sort((BoyoMenu o1, BoyoMenu o2)->o1.getOrderNum().compareTo(o2.getOrderNum()));
        menus = getChildPerms(menus, 0);

        return menus;
    }

    /**
     * 递归获取父级
     */
    private List<BoyoMenu> getParentTaxCompanyIds(List<BoyoMenu> menuList, Long id, List<BoyoMenu> pMenuList) {
        for (BoyoMenu menu : menuList) {
            //判断是否有父节点
            if (id.equals(menu.getMenuId())) {
                pMenuList.add(menu);
                getParentTaxCompanyIds(menuList, menu.getParentId(), pMenuList);
            }
        }
        return pMenuList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<RouterVo> buildMenus(List<BoyoMenu> menus) {
        List<RouterVo> routers = new LinkedList<RouterVo>();
        for (BoyoMenu menu : menus) {
            RouterVo router = new RouterVo();
            router.setHidden("1".equals(menu.getVisible()));
            router.setName(getRouteName(menu));
            router.setPath(getRouterPath(menu));
            router.setComponent(getComponent(menu));
            router.setSystemOpenid(menu.getSystemOpenid());
            router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache().toString())));
            List<BoyoMenu> cMenus = menu.getChildren();
            if (!cMenus.isEmpty() && cMenus.size() > 0 && UserConstants.TYPE_DIR.equals(menu.getMenuType())) {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            } else if (isMeunFrame(menu)) {
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache().toString())));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    @Override
    public List<RouterVo> addSystem(List<RouterVo> routers) {
        List<RouterVo> result = new ArrayList<>();
        List<String> systemList = new ArrayList<>();
        for (int i = 0; i < routers.size(); i++) {
            systemList.add(routers.get(i).getSystemOpenid());
        }
        if(systemList == null || systemList.size() == 0){
            return result;
        }
        QueryWrapper<TSysSystem> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("system_openid", systemList).orderByAsc("system_order");
        List<TSysSystem> sysSystemList = sysSystemMapper.selectList(queryWrapper);
        for (int i = 0; i < sysSystemList.size(); i++) {
            RouterVo obj = new RouterVo();
            obj.setPath("/" + sysSystemList.get(i).getSystemOpenid());
            obj.setMeta(new MetaVo(sysSystemList.get(i).getSystemName(), sysSystemList.get(i).getSystemIcon(), true));
            obj.setName(sysSystemList.get(i).getSystemName());
            obj.setAlwaysShow(true);
            obj.setPcShow(sysSystemList.get(i).getPcShow());
            obj.setComponent(UserConstants.PARENT_VIEW);
            obj.setRedirect("noRedirect");
            for (int j = 0; j < routers.size(); j++) {
                if (sysSystemList.get(i).getSystemOpenid().equals(routers.get(j).getSystemOpenid())) {
                    if (ObjectUtil.isNull(obj.getChildren())) {
                        obj.setChildren(new ArrayList<>());
                    }
                    obj.getChildren().add(routers.get(j));
                }
            }
            result.add(obj);
        }
        return result;
    }

    /**
     * 获取路由名称
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(BoyoMenu menu) {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 非外链并且是一级目录（类型为目录）
        if (isMeunFrame(menu)) {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 获取路由地址
     *
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(BoyoMenu menu) {
        String routerPath = menu.getPath();
        // 非外链并且是一级目录（类型为目录）
        if (0 == menu.getParentId().intValue() && UserConstants.TYPE_DIR.equals(menu.getMenuType())
                && UserConstants.NO_FRAME.equals(menu.getIsFrame())) {
            routerPath = "/" + menu.getPath();
        }
        // 非外链并且是一级目录（类型为菜单）
        else if (isMeunFrame(menu)) {
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     *
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(BoyoMenu menu) {
        String component = UserConstants.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMeunFrame(menu)) {
            component = menu.getComponent();
        } else if (StringUtils.isEmpty(menu.getComponent()) && isParentView(menu)) {
            component = UserConstants.PARENT_VIEW;
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMeunFrame(BoyoMenu menu) {
        return menu.getParentId().intValue() == 0 && UserConstants.TYPE_MENU.equals(menu.getMenuType())
                && menu.getIsFrame().equals(UserConstants.NO_FRAME);
    }

    /**
     * 是否为parent_view组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isParentView(BoyoMenu menu) {
        return menu.getParentId().intValue() != 0 && UserConstants.TYPE_DIR.equals(menu.getMenuType());
    }


    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list     分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    public List<BoyoMenu> getChildPerms(List<BoyoMenu> list, int parentId) {
        List<BoyoMenu> returnList = new ArrayList<>();
        for (Iterator<BoyoMenu> iterator = list.iterator(); iterator.hasNext(); ) {
            BoyoMenu t = (BoyoMenu) iterator.next();
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getParentId() == parentId) {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param list
     * @param t
     */
    private void recursionFn(List<BoyoMenu> list, BoyoMenu t) {
        // 得到子节点列表
        List<BoyoMenu> childList = getChildList(list, t);
        t.setChildren(childList);
        for (BoyoMenu tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<BoyoMenu> getChildList(List<BoyoMenu> list, BoyoMenu t) {
        List<BoyoMenu> tlist = new ArrayList<BoyoMenu>();
        Iterator<BoyoMenu> it = list.iterator();
        while (it.hasNext()) {
            BoyoMenu n = (BoyoMenu) it.next();
            if (n.getParentId().longValue() == t.getMenuId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<BoyoMenu> list, BoyoMenu t) {
        return getChildList(list, t).size() > 0 ? true : false;
    }
}
