package com.boyo.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.mes.entity.ProductOrderDetail;
import java.util.List;

/**
 * 生产工单执行(ProductOrderDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
public interface ProductOrderDetailMapper extends BaseMapper<ProductOrderDetail>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param productOrderDetail 实例对象
     * @return 对象列表
     */
    List<ProductOrderDetail> selectProductOrderDetailList(ProductOrderDetail productOrderDetail);

    List<ProductOrderDetail> listCurrentOrder(Long userId);


}

