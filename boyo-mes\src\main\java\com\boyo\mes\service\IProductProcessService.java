package com.boyo.mes.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.mes.entity.ProductProcess;
import java.util.List;

/**
 * 工序管理(ProductProcess)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
public interface IProductProcessService extends IService<ProductProcess> {

    /**
     * 查询多条数据
     *
     * @param productProcess 对象信息
     * @return 对象列表
     */
    List<ProductProcess> selectProductProcessList(ProductProcess productProcess);


}
