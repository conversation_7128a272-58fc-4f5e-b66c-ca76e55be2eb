<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.system.mapper.DatabaseLogMapper">

    <resultMap type="com.boyo.system.domain.DatabaseLog" id="DatabaseLogResult">
        <result property="id" column="id"/>
        <result property="databaseOpenid" column="database_openid"/>
        <result property="databaseVersionCode" column="database_version_code"/>
        <result property="databaseVersionName" column="database_version_name"/>
        <result property="databaseVersionTime" column="database_version_time"/>
        <result property="databaseChangeSql" column="database_change_sql"/>
        <result property="databaseFullSql" column="database_full_sql"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDatabaseLogVo">
        select id,
               database_openid,
               database_version_code,
               database_version_name,
               database_version_time,
               database_change_sql,
               database_full_sql,
               create_time,
               update_time
        from t_database_log
    </sql>

    <select id="selectDatabaseLogList" parameterType="com.boyo.system.domain.DatabaseLog" resultMap="DatabaseLogResult">
        <include refid="selectDatabaseLogVo"/>
        <where>
            <if test="databaseOpenid != null  and databaseOpenid != ''">
                and database_openid = #{databaseOpenid}
            </if>
            <if test="databaseVersionCode != null ">
                and database_version_code = #{databaseVersionCode}
            </if>
            <if test="databaseVersionName != null  and databaseVersionName != ''">
                and database_version_name like concat('%', #{databaseVersionName}, '%')
            </if>
            <if test="databaseVersionTime != null ">
                and database_version_time = #{databaseVersionTime}
            </if>
            <if test="databaseChangeSql != null  and databaseChangeSql != ''">
                and database_change_sql = #{databaseChangeSql}
            </if>
            <if test="databaseFullSql != null  and databaseFullSql != ''">
                and database_full_sql = #{databaseFullSql}
            </if>
        </where>
    </select>
</mapper>
