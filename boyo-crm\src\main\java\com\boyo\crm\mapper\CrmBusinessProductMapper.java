package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmBusinessProduct;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * 商机产品关系表(CrmBusinessProduct)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-26 09:56:24
 */
@Tenant
public interface CrmBusinessProductMapper extends BaseMapper<CrmBusinessProduct>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmBusinessProduct 实例对象
     * @return 对象列表
     */
    List<CrmBusinessProduct> selectCrmBusinessProductList(CrmBusinessProduct crmBusinessProduct);


}

