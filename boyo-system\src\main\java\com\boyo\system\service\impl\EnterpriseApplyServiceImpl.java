package com.boyo.system.service.impl;

import java.util.List;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.system.mapper.EnterpriseApplyMapper;
import com.boyo.system.domain.EnterpriseApply;
import com.boyo.system.service.IEnterpriseApplyService;

/**
 * 申请试用Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class EnterpriseApplyServiceImpl extends ServiceImpl<EnterpriseApplyMapper, EnterpriseApply> implements IEnterpriseApplyService {
    private final EnterpriseApplyMapper enterpriseApplyMapper;

    /**
     * 查询申请试用列表
     *
     * @param enterpriseApply 申请试用
     * @return enterpriseApply 列表
     */
    @Override
    public List<EnterpriseApply> selectEnterpriseApplyList(EnterpriseApply enterpriseApply) {
        return enterpriseApplyMapper.selectEnterpriseApplyList(enterpriseApply);
    }
}
