<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.iot.mapper.IotTslMapper">

    <resultMap type="com.boyo.iot.domain.IotTsl" id="IotTslResult">
        <result property="id" column="id"/>
        <result property="tslName" column="tsl_name"/>
        <result property="tslCode" column="tsl_code"/>
        <result property="tslDesc" column="tsl_desc"/>
        <result property="tslImg" column="tsl_img"/>
        <result property="tslSort" column="tsl_sort"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="tslCount" column="tsl_count"/>
    </resultMap>

    <!--    <select id="selectIotTslList" parameterType="com.boyo.iot.domain.IotTsl" resultMap="IotTslResult">-->
    <!--        select t1.*,t2.tsl_count from (select *-->
    <!--        from iot_tsl-->
    <!--        <where>-->
    <!--            <if test="tslName != null  and tslName != ''">-->
    <!--                and tsl_name like concat('%', #{tslName}, '%')-->
    <!--            </if>-->
    <!--            <if test="tslCode != null  and tslCode != ''">-->
    <!--                and tsl_code = #{tslCode}-->
    <!--            </if>-->
    <!--            ${params.dataScope}-->
    <!--        </where>-->
    <!--        ) t1 left join (select count(*) as tsl_count,tsl_id from iot_equipment group by tsl_id) t2-->
    <!--        on t1.id = t2.tsl_id-->
    <!--        order by t1.tsl_sort IS NULL,t1.tsl_sort-->
    <!--    </select>-->
    <select id="selectIotTslList" parameterType="com.boyo.iot.domain.IotTsl" resultMap="IotTslResult">
        select t1.*,t2.tsl_count from
        (select * from iot_tsl
        <where>
            <if test="tslName != null  and tslName != ''">
                and tsl_name like concat('%', #{tslName}, '%')
            </if>
            <if test="tslCode != null  and tslCode != ''">
                and tsl_code = #{tslCode}
            </if>
        </where>
        ) t1 left join (select count(*) as tsl_count,tsl_id from iot_equipment
        <where>
            ${params.dataScope}
        </where>
        group by tsl_id) t2
        on t1.id = t2.tsl_id
        order by t1.tsl_sort IS NULL,t1.tsl_sort
    </select>

</mapper>
