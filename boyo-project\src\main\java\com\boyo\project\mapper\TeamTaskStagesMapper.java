package com.boyo.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.project.entity.TeamTaskStages;
import java.util.List;

/**
 * 任务列表表(TeamTaskStages)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-10 11:03:15
 */
public interface TeamTaskStagesMapper extends BaseMapper<TeamTaskStages>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param teamTaskStages 实例对象
     * @return 对象列表
     */
    List<TeamTaskStages> selectTeamTaskStagesList(TeamTaskStages teamTaskStages);


}

