package com.boyo.web.controller.haihui;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.master.domain.MesPersonWage;
import com.boyo.master.domain.MesProductionPlan;
import com.boyo.master.service.IMesPersonWageService;
import com.boyo.master.service.MesProductionPlanService;
import com.boyo.web.lysso.controller.HttpRequest;
import com.boyo.web.lysso.controller.HttpResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 生产计划Controller
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@RestController
@RequestMapping("/haihui/haihui/mes")
public class MesProductionPlanController extends BaseController {


    private static final String MES_BASEURL = "http://192.168.222.33:9114/api/outApi/listMesProductionSchedule?pageSize=10000&pageNum=1";
    private static final String WAGE_BASEURL = "http://192.168.222.33:9114/api/outApi/listMesPerformance?pageSize=10000&pageNum=1";

    @Autowired
    private MesProductionPlanService mesProductionPlanService;
    @Autowired
    private IMesPersonWageService wageDataService;

    /**
     * 查询生产计划列表
     */
    @GetMapping("/getPlanData/list")
    public TableDataInfo list(MesProductionPlan mesProductionPlan) {
        startPage();
        List<MesProductionPlan> list = mesProductionPlanService.selectMesProductionPlanList(mesProductionPlan);
        return getDataTable(list);
    }

    /**
     * 查询生产计划列表
     */
    @GetMapping("/getWageData/list")
    public TableDataInfo wageList(MesPersonWage mesPersonWage) {
        startPage();
        List<MesPersonWage> list = wageDataService.selectMesPersonWageList(mesPersonWage);
        return getDataTable(list);
    }

    @GetMapping("/getPlanData")
    public AjaxResult getPlanData(MesProductionPlan mesProductionPlan) {
        return getPlanData();
    }

    @GetMapping("/getWageData")
    public AjaxResult getWageData(MesProductionPlan mesProductionPlan) {
        return getWageData();
    }

    /**
     * 导出生产计划列表
     */
//    @PreAuthorize("@ss.hasPermi('haihui:haihui:export')")
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, MesProductionPlan mesProductionPlan)
//    {
//        List<MesProductionPlan> list = mesProductionPlanService.selectMesProductionPlanList(mesProductionPlan);
//        ExcelUtil<MesProductionPlan> util = new ExcelUtil<MesProductionPlan>(MesProductionPlan.class);
//        util.exportExcel(response, list, "生产计划数据");
//    }

    /**
     * 获取生产计划详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(mesProductionPlanService.selectMesProductionPlanById(id));
    }

    /**
     * 新增生产计划
     */
//    @PostMapping
//    public AjaxResult add(@RequestBody MesProductionPlan mesProductionPlan) {
//        return toAjax(mesProductionPlanService.insertMesProductionPlan(mesProductionPlan));
//    }

    /**
     * 修改生产计划
     */
//    @PutMapping
//    public AjaxResult edit(@RequestBody MesProductionPlan mesProductionPlan) {
//        return toAjax(mesProductionPlanService.updateMesProductionPlan(mesProductionPlan));
//    }
    @Scheduled(cron = "0 20 12,00 * * ?")//每天12：20和00：20执行各一次
    public AjaxResult getPlanData() {
        String url = MES_BASEURL;
        try {
            HttpRequest requestPlanData = HttpRequest.builder()
                    .setUrl(url)
                    .setMethod(HttpRequest.Method.GET)
                    .addHeader("Content-Type", "application/json; charset=UTF-8")
                    .addHeader("Accept", "*/*")
                    .addHeader("Connection", "keep-alive");
            HttpResponse response = requestPlanData.get();
            JSONObject jsonObject = JSONObject.parseObject(response.getText());
            savePlanData(jsonObject);
            return AjaxResult.success(jsonObject);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;


    }


    private int savePlanData(JSONObject jsonObject) {
        if (!jsonObject.containsKey("rows")) {
            return 0;
        }
        final JSONArray rows = (JSONArray) jsonObject.get("rows");
        int successCount = 0;
        if (rows == null) {
            return 0;
        }
        for (int i = 0; i < rows.size(); i++) {
            try {
                JSONObject oneData = rows.getJSONObject(i);
                MesProductionPlan productionPlan = new MesProductionPlan();
                // 设置销售数据对象的各个字段
                productionPlan.setPlanCode(oneData.getString("planCode"));
                productionPlan.setProjectNumber(oneData.getString("projectNumber"));
                productionPlan.setProjectName(oneData.getString("projectName"));
                productionPlan.setQuantity(oneData.getLong("quantity"));
                productionPlan.setSplitQuantity(oneData.getLong("splitQuantity"));
                productionPlan.setNotSplitQuantity(oneData.getLong("notSplitQuantity"));
                productionPlan.setPreOpeningDays(oneData.getString("preOpeningDays"));
                productionPlan.setPreCompletionDays(oneData.getString("preCompletionDays"));
                productionPlan.setAgentType(oneData.getString("agentType"));
                productionPlan.setStatus(oneData.getString("status"));
                productionPlan.setNoticeNo(oneData.getString("noticeNo"));
                productionPlan.setDispatchQuantity(oneData.getLong("dispatchQuantity"));
                productionPlan.setWorkReportsQuantity(oneData.getLong("workReportsQuantity"));
                productionPlan.setWorkNotReportsQuantity(oneData.getLong("workNotReportsQuantity"));
                productionPlan.setQualifiedQuantity(oneData.getLong("qualifiedQuantity"));
                productionPlan.setProductCode(oneData.getString("productCode"));
                productionPlan.setProductName(oneData.getString("productName"));
                productionPlan.setCreateTime(oneData.getDate("createTime"));
                productionPlan.setUpdateTime(new Date());
                productionPlan.setCreateBy(oneData.getString("createBy"));
                productionPlan.setId(UUID.randomUUID().toString());
                productionPlan.setId(oneData.get("id").toString());
                final MesProductionPlan mesProductionPlan = mesProductionPlanService.selectMesProductionPlanById(productionPlan.getId());
                if (mesProductionPlan == null) {
                    successCount += mesProductionPlanService.insertMesProductionPlan(productionPlan);
                } else {
                    successCount += mesProductionPlanService.updateMesProductionPlan(productionPlan);
                }
            } catch (Exception e) {
                logger.error("mes savePlanData保存数据失败");
                e.printStackTrace();
            }
        }
        return successCount;
    }

    @Scheduled(cron = "0 28 12,00 * * ?")//每天12：28和00：28执行各一次
    public AjaxResult getWageData() {
        String url = WAGE_BASEURL;
        try {
            HttpRequest request1 = HttpRequest.builder()
                    .setUrl(url)
                    .setMethod(HttpRequest.Method.GET)
                    .addHeader("Content-Type", "application/json; charset=UTF-8")
                    .addHeader("Accept", "*/*")
                    .addHeader("Connection", "keep-alive");
            HttpResponse response = request1.get();
            JSONObject jsonObject = JSONObject.parseObject(response.getText());
            saveWageData(jsonObject);
            return AjaxResult.success(jsonObject);

        } catch (Exception e) {
            AjaxResult.error(e.getMessage());
        }
        return null;

    }


    private int saveWageData(JSONObject jsonObject) {
        // 非空校验
        if (jsonObject == null || !jsonObject.containsKey("rows")) {
            logger.error("mes      输入的 JSON 数据为空或缺少 rows 字段");
            return 0;
        }

        final JSONArray rows = jsonObject.getJSONArray("rows");
        if (rows == null || rows.isEmpty()) {
            logger.warn("mes      rows 数组为空，无需保存工资数据");
            return 0;
        }

        int successCount = 0;

        for (int i = 0; i < rows.size(); i++) {
            try {
                JSONObject oneData = rows.getJSONObject(i);

                // 校验并构造 MesPersonWage 对象
                MesPersonWage personWage = buildPersonWage(oneData);
                if (personWage == null) {
                    logger.error("mes      构造工资数据失败，跳过当前记录");
                    continue;
                }

                // 查询是否存在相同记录
                final MesPersonWage mesPersonWage = wageDataService.selectMesPersonWageById(personWage.getId());
                if (mesPersonWage != null) {
                    successCount += wageDataService.updateMesPersonWage(personWage);
                } else {
                    successCount += wageDataService.insertMesPersonWage(personWage);
                }
            } catch (JSONException jsonException) {
                logger.error("mes      解析 JSON 数据失败: " + jsonException.getMessage());
            } catch (Exception e) {
                logger.error("mes      保存工资数据失败: " + e.getMessage(), e);
            }
        }

        return successCount;
    }

    /**
     * 构造 MesPersonWage 对象，并校验字段有效性
     */
    private MesPersonWage buildPersonWage(JSONObject oneData) {
        try {
            MesPersonWage personWage = new MesPersonWage();
            personWage.setPersonCode(getSafeString(oneData, "personCode"));
            personWage.setPersonName(getSafeString(oneData, "personName"));
            personWage.setTeamName(getSafeString(oneData, "teamName"));
            personWage.setPieceCountWage(getSafeLong(oneData, "pieceCountWage"));
            personWage.setPieceHourWage(getSafeLong(oneData, "pieceHourWage"));
            personWage.setShouldGrantWage(getSafeLong(oneData, "shouldGrantWage"));
            personWage.setConfirmWage(getSafeLong(oneData, "confirmWage"));
            personWage.setMonthTime(getSafeString(oneData, "monthTime"));
            personWage.setCreateBy(getSafeString(oneData, "createBy"));
            personWage.setId(personWage.getPersonCode() + personWage.getMonthTime() + personWage.getPersonName());
            return personWage;
        } catch (Exception e) {
            logger.error("mes      构造 MesPersonWage 对象失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 安全获取字符串值，避免字段缺失或类型不匹配
     */
    private String getSafeString(JSONObject jsonObject, String key) {
        return jsonObject.containsKey(key) ? jsonObject.getString(key) : "";
    }

    /**
     * 安全获取长整型值，避免字段缺失或类型不匹配
     */
    private Long getSafeLong(JSONObject jsonObject, String key) {
        return jsonObject.containsKey(key) ? jsonObject.getLong(key) : 0L;
    }


}
