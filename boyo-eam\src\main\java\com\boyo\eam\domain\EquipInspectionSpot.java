package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 点检表(EquipInspectionSpot)实体类
 *
 * <AUTHOR>
 * @since 2021-11-15 16:16:30
 */
@Data
@TableName(value = "equip_inspection_spot")
public class EquipInspectionSpot implements Serializable {
    private static final long serialVersionUID = 283400344748825967L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 巡检编码
    */
    @TableField(value="code")
    private String code;
    /**
    * 设备openid：关联equip_ledger表的openid
    */
    @TableField(value="equip_ledger_openid")
    private String equipLedgerOpenid;

    /**
     * 审核人：关联sys_user_id表的user_id
     */
    @TableField(value="reviewer")
    private Integer reviewer;

    /**
     * 分类：0点检，1巡检
     */
    private String type;

    /**
     * 点检分类：0操作，1机械，2电器
     */
    private String spotType;

    /**
     * 产线：关联t_model_line表的openid
     */
    private String lineOpenid;

    /**
     * 点巡检人员
     */
    @TableField(value="sys_user_id")
    private Integer sysUserId;
    /**
     * 周期
     */
    @TableField(value="cycle")
    private Integer cycle;
    /**
     * 单位: 0月,1周,2日
     */
    @TableField(value="cycle_unit")
    private String cycleUnit;
    /**
     * 周期开始日期
     */
    @TableField(value="cycle_begin_date")
    private Date cycleBeginDate;
    /**
     * 周期结束日期
     */
    @TableField(value="cycle_end_date")
    private Date cycleEndDate;

    /**
     * 任务状态: 0待处理，1进行中，2已完成
     */
    @TableField(value="state")
    private String state;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;



    /** 额外字段 */
    @TableField(exist = false)
    private List<EquipInspectionSpotItem> itemList; //项目列表

    @TableField(exist = false)
    private String equipCode;//设备编码
    @TableField(exist = false)
    private String equipName;//设备名称
    @TableField(exist = false)
    private String lineName;//产线名称

    //查询用
    @TableField(exist = false)
    private Date beginDate;
    @TableField(exist = false)
    private Date endDate;
    @TableField(exist = false)
    private String spotContent;
}
