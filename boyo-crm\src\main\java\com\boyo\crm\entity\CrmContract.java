package com.boyo.crm.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import com.boyo.common.core.domain.BoyoBaseEntity;
import com.boyo.framework.annotation.PropertyMsg;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 合同表(CrmContract)实体类
 *
 * <AUTHOR>
 * @since 2022-03-27 17:04:54
 */
@Data
@TableName(value = "t_crm_contract")
public class CrmContract extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = -32412070693680768L;
            
    @TableId
    private Integer id;
    
    /**
    * 合同名称
    */
    @TableField(value="name")
    @PropertyMsg(value="合同名称")
    private String name;
    /**
    * 客户ID
    */
    @TableField(value="customer_id")
    @PropertyMsg(value="客户")
    private Integer customerId;
    /**
    * 商机ID
    */
    @TableField(value="business_id")
    @PropertyMsg(value="商机",type = "business")
    private Integer businessId;
    /**
    * 0 未审核 1 审核通过 2 审核拒绝 3 审核中 4 已撤回 5草稿 6 作废
    */
    @TableField(value="check_status")
    private Integer checkStatus;
    /**
    * 审核记录ID
    */
    @TableField(value="examine_record_id")
    private Integer examineRecordId;
    /**
    * 下单日期
    */
    @TableField(value="order_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @PropertyMsg(value="下单日期")
    private Date orderDate;
    /**
    * 创建人ID
    */
    @TableField(value="create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 负责人ID
    */
    @TableField(value="owner_user_id")
    private Long ownerUserId;

    @TableField(exist = false)
    private String ownerUserName;
    /**
    * 创建时间
    */
    @TableField(value="create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 更新时间
    */
    @TableField(value="update_time",fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
    * 合同编号
    */
    @TableField(value="num")
    @PropertyMsg(value="合同编号")
    private String num;
    /**
    * 开始时间
    */
    @TableField(value="start_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @PropertyMsg(value="开始时间")
    private Date startTime;
    /**
    * 结束时间
    */
    @TableField(value="end_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @PropertyMsg(value="结束时间")
    private Date endTime;
    /**
    * 合同金额
    */
    @TableField(value="money")
    @PropertyMsg(value="合同金额")
    private BigDecimal money;
    /**
    * 整单折扣
    */
    @TableField(value="discount_rate")
    @PropertyMsg(value="整单折扣")
    private Double discountRate;
    /**
    * 产品总金额
    */
    @TableField(value="total_price")
    @PropertyMsg(value="产品总金额")
    private BigDecimal totalPrice;
    /**
    * 合同类型
    */
    @TableField(value="contract_type")
    @PropertyMsg(value="合同类型",type = "base")
    private Integer contractType;
    /**
    * 付款方式
    */
    @TableField(value="payment_type")
    @PropertyMsg(value="付款方式",type = "base")
    private Integer paymentType;
    /**
    * 客户签约人（联系人id）
    */
    @TableField(value="contacts_id")
    private Integer contactsId;
    /**
    * 备注
    */
    @TableField(value="remark")
    private String remark;
    /**
    * 公司签约人
    */
    @TableField(value="company_user_id")
    private Integer companyUserId;
    /**
     * 商品清单
     */
    @TableField(exist = false)
    private List<CrmContractProduct> productList;

    /**
     * 客户名称
     */
    @TableField(exist = false)
    private String customerName;

    /**
     * 商机名称
     */
    @TableField(exist = false)
    private String businessName;

    /**
     * 已回款总数
     */
    @TableField(exist = false)
    private BigDecimal paymentCollection = new BigDecimal(0);

    /**
     * 合同类型
     */
    @TableField(exist = false)
    private String contractTypeName;

    /**
     * 付款方式
     */
    @TableField(exist = false)
    private String paymentTypeName;

    @TableField(exist = false)
    private String contactsName;

}
