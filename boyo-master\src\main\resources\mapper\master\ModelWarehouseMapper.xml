<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.ModelWarehouseMapper">

    <resultMap type="com.boyo.master.vo.ModelWarehouseVO" id="ModelWarehouseResult">
        <result property="id" column="id"/>
        <result property="warehouseOpenid" column="warehouse_openid"/>
        <result property="warehouseFactory" column="warehouse_factory"/>
        <result property="warehouseWorkshop" column="warehouse_workshop"/>
        <result property="warehouseLine" column="warehouse_line"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="warehouseAbbreviation" column="warehouse_abbreviation"/>
        <result property="warehouseCode" column="warehouse_code"/>
        <result property="warehouseType" column="warehouse_type"/>
        <result property="warehouseStatus" column="warehouse_status"/>
        <result property="warehouseImg" column="warehouse_img"/>
        <result property="warehouseContacts" column="warehouse_contacts"/>
        <result property="warehousePhone" column="warehouse_phone"/>
        <result property="createdAt" column="created_at"/>
        <result property="createdUser" column="created_user"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="updatedUser" column="updated_user"/>
        <result property="factoryName" column="factory_name"/>
        <result property="typeName" column="type_name"/>
    </resultMap>

    <sql id="selectModelWarehouseVo">
        select id,
               warehouse_openid,
               warehouse_factory,
               warehouse_workshop,
               warehouse_line,
               warehouse_name,
               warehouse_abbreviation,
               warehouse_code,
               warehouse_type,
               warehouse_status,
               warehouse_img,
               warehouse_contacts,
               warehouse_phone,
               created_at,
               created_user,
               updated_at,
               updated_user
        from t_model_warehouse
    </sql>

    <select id="selectModelWarehouseList" parameterType="com.boyo.master.vo.ModelWarehouseVO"
            resultMap="ModelWarehouseResult">
        SELECT
        t1.*,
        t2.factory_name,
        t3.base_desc as type_name
        FROM
        t_model_warehouse t1
        LEFT JOIN t_model_factory t2 ON t1.warehouse_factory = t2.factory_openid
        LEFT JOIN t_base_dict t3 ON t1.warehouse_type = t3.openid
        <where>
            <if test="warehouseOpenid != null  and warehouseOpenid != ''">
                and t1.warehouse_openid = #{warehouseOpenid}
            </if>
            <if test="warehouseFactory != null  and warehouseFactory != ''">
                and t1.warehouse_factory = #{warehouseFactory}
            </if>
            <if test="warehouseWorkshop != null  and warehouseWorkshop != ''">
                and t1.warehouse_workshop = #{warehouseWorkshop}
            </if>
            <if test="warehouseLine != null  and warehouseLine != ''">
                and t1.warehouse_line = #{warehouseLine}
            </if>
            <if test="warehouseName != null  and warehouseName != ''">
                and t1.warehouse_name like concat('%', #{warehouseName}, '%')
            </if>
            <if test="warehouseAbbreviation != null  and warehouseAbbreviation != ''">
                and t1.warehouse_abbreviation = #{warehouseAbbreviation}
            </if>
            <if test="warehouseCode != null  and warehouseCode != ''">
                and t1.warehouse_code = #{warehouseCode}
            </if>
            <if test="warehouseType != null  and warehouseType != ''">
                and t1.warehouse_type = #{warehouseType}
            </if>
            <if test="warehouseStatus != null  and warehouseStatus != ''">
                and t1.warehouse_status = #{warehouseStatus}
            </if>
            <if test="warehouseImg != null  and warehouseImg != ''">
                and t1.warehouse_img = #{warehouseImg}
            </if>
            <if test="warehouseContacts != null  and warehouseContacts != ''">
                and t1.warehouse_contacts = #{warehouseContacts}
            </if>
            <if test="warehousePhone != null  and warehousePhone != ''">
                and t1.warehouse_phone = #{warehousePhone}
            </if>
        </where>
    </select>
</mapper>
