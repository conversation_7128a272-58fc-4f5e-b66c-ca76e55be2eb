package com.boyo.master.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 销售订单
 * @TableName bu_enterprise_data_asset_sale_order
 */
@TableName(value ="bu_enterprise_data_asset_sale_order")
@Data
public class SaleOrder extends DataAssetBase implements Serializable {
    /**
     * 单据日期
     */
    @JSONField(name = "DBILLDATE")
    @TableField(value = "dbilldate")
    private Date dbilldate;

    /**
     * 客商编码
     */
    @JSONField(name = "CUSTCODE")
    @TableField(value = "custcode")
    private String custcode;

    /**
     * 客商名称
     */
    @JSONField(name = "CUSTNAME")
    @TableField(value = "custname")
    private String custname;

    /**
     * 存货编码
     */
    @JSONField(name = "INVCODE")
    @TableField(value = "invcode")
    private String invcode;

    /**
     * 存货名称
     */
    @JSONField(name = "INVNAME")
    @TableField(value = "invname")
    private String invname;

    /**
     * 规格
     */
    @JSONField(name = "INVSPEC")
    @TableField(value = "invspec")
    private String invspec;

    /**
     * 型号
     */
    @JSONField(name = "INVTYPE")
    @TableField(value = "invtype")
    private String invtype;

    /**
     * 数量
     */
    @JSONField(name = "NNUMBER")
    @TableField(value = "nnumber")
    private BigDecimal nnumber;

    /**
     * 原币含税单价
     */
    @JSONField(name = "NORIGINALCURTAXPRICE")
    @TableField(value = "noriginalcurtaxprice")
    private BigDecimal noriginalcurtaxprice;

    /**
     * 原币价税合计
     */
    @JSONField(name = "NORIGINALCURSUMMNY")
    @TableField(value = "noriginalcursummny")
    private BigDecimal noriginalcursummny;

    /**
     * 本币含税单价
     */
    @JSONField(name = "NTAXPRICE")
    @TableField(value = "ntaxprice")
    private BigDecimal ntaxprice;

    /**
     * 本币价税合计
     */
    @JSONField(name = "NSUMMNY")
    @TableField(value = "nsummny")
    private BigDecimal nsummny;

    /**
     * 单据编号
     */
    @JSONField(name = "VRECEIPTCODE")
    @TableField(value = "vreceiptcode")
    private String vreceiptcode;

    @JSONField(name = "CINVENTORYID")
    @TableField(value = "cinventoryid")
    private String cinventoryid;

    @TableField(value = "children_company")
    private String childrenCompany;

    @JSONField(name = "DAPPROVEDATE",format="yyyy-MM-dd")
    @TableField(value = "dapprovedate")
    private Date dapprovedate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
