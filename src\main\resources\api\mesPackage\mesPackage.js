import request from '@/utils/request'

const prefix = '/${module}'

// 查询打包管理(MesPackage)列表
export function listMesPackage(query) {
  return request({
    url: prefix + '/mesPackage/list',
    method: 'get',
    params: query,
  })
}

// 查询打包管理(MesPackage)详细
export function getMesPackage(id) {
  return request({
    url: prefix + '/mesPackage/' + id,
    method: 'get',
  })
}

// 新增打包管理(MesPackage)
export function addMesPackage(data) {
  return request({
    url: prefix + '/mesPackage',
    method: 'post',
    data: data,
  })
}

// 修改打包管理(MesPackage)
export function updateMesPackage(data) {
  return request({
    url: prefix + '/mesPackage',
    method: 'put',
    data: data,
  })
}

// 删除打包管理(MesPackage)
export function delMesPackage(id) {
  return request({
    url: prefix + '/mesPackage/' + id,
    method: 'delete',
  })
}
