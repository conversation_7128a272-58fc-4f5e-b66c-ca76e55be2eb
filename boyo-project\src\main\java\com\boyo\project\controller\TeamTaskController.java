package com.boyo.project.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.project.entity.TeamTask;
import com.boyo.project.service.ITeamTaskService;
import com.boyo.project.util.ExecuteStatus;
import com.boyo.project.util.TaskPriority;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

/**
 * 任务表(TeamTask)表控制层
 *
 * <AUTHOR>
 * @since 2022-02-13 13:05:06
 */
@Api("任务表")
@RestController
@RequestMapping("/project/teamTask")
@AllArgsConstructor
public class TeamTaskController extends BaseController{
    /**
     * 服务对象
     */
    private final ITeamTaskService teamTaskService;
    private final IEnterpriseUserService enterpriseUserService;

    /**
     * 查询任务表列表
     *
     */
    @ApiOperation("查询任务表列表")
    @GetMapping("/list")
    public TableDataInfo list(TeamTask teamTask) {
        startPage();
        List<TeamTask> list = teamTaskService.selectTeamTaskList(teamTask);
        return getDataTable(list);
    }

    @GetMapping("/listMyTask")
    public TableDataInfo listMyTask(){
//        startPage();
        List<TeamTask> list = teamTaskService.listMyTask();
        List<TeamTask> temp = new ArrayList<>();
        list.forEach(task ->{
            if(ObjectUtil.isNull(task.getBeginTime()) || ObjectUtil.isEmpty(task.getBeginTime())){
//                list.remove(task);
            }else{
                task.setPriName(TaskPriority.getText(task.getPri()));
                temp.add(task);
            }
        });
        return getDataTable(temp);
    }
    
    /**
     * 获取任务表详情
     */
    @ApiOperation("获取任务表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        TeamTask task = teamTaskService.getById(id);
        if(ObjectUtil.isNotNull(task)){
            EnterpriseUser user = enterpriseUserService.selectUserByOpenid(task.getAssignTo());
            if(ObjectUtil.isNotNull(user)){
                task.setAssignName(user.getUserFullName());
            }
        }
        return AjaxResult.success(task);
    }

    /**
     * 新增任务表
     */
    @ApiOperation("新增任务表")
    @PostMapping
    public AjaxResult add(@RequestBody TeamTask teamTask) {
        return toBooleanAjax(teamTaskService.save(teamTask));
    }

    /**
     * 修改任务表
     */
    @ApiOperation("修改任务表")
    @PutMapping
    public AjaxResult edit(@RequestBody TeamTask teamTask) {
        return toBooleanAjax(teamTaskService.updateById(teamTask));
    }

    /**
     * 删除任务表
     */
    @ApiOperation("删除任务表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(teamTaskService.removeByIds(Arrays.asList(ids)));
    }

}
