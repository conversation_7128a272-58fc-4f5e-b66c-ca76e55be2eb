package com.boyo.framework.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Configuration
@ConfigurationProperties("globaluse")
@Component
public class GlobalUseConfig {

    private String ssoUrl;

    private String mdUrl;

    private String appId;

    private String appSecret;

    private String xiyiUrl;

    public String getSsoUrl() {
        return ssoUrl;
    }

    public void setSsoUrl(String ssoUrl) {
        this.ssoUrl = ssoUrl;
    }

    public String getMdUrl() {
        return mdUrl;
    }

    public void setMdUrl(String mdUrl) {
        this.mdUrl = mdUrl;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getXiyiUrl() {
        return xiyiUrl;
    }

    public void setXiyiUrl(String xiyiUrl) {
        this.xiyiUrl = xiyiUrl;
    }
}
