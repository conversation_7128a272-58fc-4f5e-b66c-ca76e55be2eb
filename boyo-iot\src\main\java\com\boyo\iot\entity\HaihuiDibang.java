package com.boyo.iot.entity;

import com.boyo.common.annotation.Excel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class HaihuiDibang {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 磅单号 */
    @Excel(name = "磅单号")
    private String ticket_number;

    /** 汽车号 */
    @Excel(name = "汽车号")
    private String car_number;

    /** 货物名 */
    @Excel(name = "货物名")
    private String goods_name;

    /** 收货单位 */
    @Excel(name = "收货单位")
    private String receiver;

    /** 发货单位 */
    @Excel(name = "发货单位")
    private String sender;

    /** 运输单位 */
    @Excel(name = "运输单位")
    private String transporter;

    /** 过磅收费 */
    @Excel(name = "过磅收费", readConverterExp = "元")
    private BigDecimal weighing_fee;

    /** 单价 */
    @Excel(name = "单价", readConverterExp = "元/吨")
    private BigDecimal unit_price;

    /** 金额 */
    @Excel(name = "金额", readConverterExp = "元")
    private BigDecimal amount;

    /** 折方系数 */
    @Excel(name = "折方系数")
    private String conversion_factor;

    /** 方量 */
    @Excel(name = "方量", readConverterExp = "立方米")
    private String volume;

    /** 毛重 */
    @Excel(name = "毛重", readConverterExp = "吨")
    private Double gross_weight;

    /** 皮重 */
    @Excel(name = "皮重", readConverterExp = "吨")
    private Double tare_weight;

    /** 净重 */
    @Excel(name = "净重", readConverterExp = "吨")
    private Double net_weight;

    /** 折扣率 */
    @Excel(name = "折扣率", readConverterExp = "%")
    private Double deduction_rate;

    /** 扣杂 */
    @Excel(name = "扣杂", readConverterExp = "吨")
    private Double deduction;

    /** 实重 */
    @Excel(name = "实重", readConverterExp = "吨")
    private Double actual_weight;

    /** 毛重时间 */
    @Excel(name = "毛重时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date gross_time;

    /** 皮重时间 */
    @Excel(name = "皮重时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date tare_time;

    /** 设备id */
    @Excel(name = "设备id")
    private String DeviceId;

    /** 模块 */
    @Excel(name = "模块")
    private String Module;

    /** 创建时间 */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
