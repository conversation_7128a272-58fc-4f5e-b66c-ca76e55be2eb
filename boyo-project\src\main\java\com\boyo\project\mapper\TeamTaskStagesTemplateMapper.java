package com.boyo.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.project.entity.TeamTaskStagesTemplate;
import java.util.List;

/**
 * 任务列表模板表(TeamTaskStagesTemplate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-08 20:50:50
 */
public interface TeamTaskStagesTemplateMapper extends BaseMapper<TeamTaskStagesTemplate>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param teamTaskStagesTemplate 实例对象
     * @return 对象列表
     */
    List<TeamTaskStagesTemplate> selectTeamTaskStagesTemplateList(TeamTaskStagesTemplate teamTaskStagesTemplate);


}

