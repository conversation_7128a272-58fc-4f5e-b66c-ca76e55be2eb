package com.boyo.mes.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * (MesCutting)实体类
 *
 * <AUTHOR>
 * @since 2023-04-10 15:46:44
 */
@Data
@TableName(value = "t_mes_cutting")
public class MesCutting implements Serializable {
    private static final long serialVersionUID = -43834307378726310L;
            
    @TableId
    private Integer id;
    
    /**
    * 订单id
    */
    @TableField(value="order_id")
    private Integer orderId;
    /**
    * 下料长度
    */
    @TableField(value="all_length")
    private Double allLength;
    /**
    * 切割长度
    */
    @TableField(value="cutting_length")
    private Double cuttingLength;
    /**
    * 数量
    */
    @TableField(value="cutting_count")
    private Integer cuttingCount;
    /**
    * 余量
    */
    @TableField(value="surplus")
    private Double surplus;
    
    @TableField(value="create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    @TableField(value="create_by")
    private String createBy;

    @TableField(exist = false)
    private String orderNum;
}
