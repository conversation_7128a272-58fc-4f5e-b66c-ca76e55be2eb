package com.boyo.mes.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.text.Convert;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.mapper.IotEquipmentMapper;
import com.boyo.iot.util.IoTDBUtil;
import com.boyo.master.domain.BaseDict;
import com.boyo.master.service.IBaseDictService;
import com.boyo.mes.entity.MesYield;
import com.boyo.mes.mapper.MesYieldMapper;
import com.boyo.mes.vo.ModuleRecordVO;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.MesModulRecord;
import com.boyo.mes.mapper.MesModulRecordMapper;
import com.boyo.mes.service.IMesModulRecordService;

import java.util.Date;
import java.util.List;

/**
 * (MesModulRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
@Service("mesModulRecordService")
@AllArgsConstructor
@Tenant
public class MesModulRecordServiceImpl extends ServiceImpl<MesModulRecordMapper, MesModulRecord> implements IMesModulRecordService {
    private final MesModulRecordMapper mesModulRecordMapper;
    private final IotEquipmentMapper equipmentMapper;
    private final IoTDBUtil ioTDBUtil;
    private final MesYieldMapper yieldMapper;
    private final IBaseDictService baseDictService;


    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<MesModulRecord> selectMesModulRecordList(MesModulRecord mesModulRecord) {
        List<MesModulRecord> result = mesModulRecordMapper.selectMesModulRecordList(mesModulRecord);
        for (MesModulRecord obj : result) {
            IotEquipment equipment = equipmentMapper.selectById(obj.getEquipmentId());
            if (ObjectUtil.isNull(obj.getEndTime())) {
                obj.setLoadTime(NumberUtil.round(Convert.toDouble(DateUtil.between(obj.getStartTime(), new Date(), DateUnit.MINUTE)) / 60, 2).doubleValue());
                if (ObjectUtil.isNotNull(equipment)) {
                    JSONArray array = getMaxminService(equipment.getEquipmentCode(), "Number", SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), DateUtil.formatDateTime(obj.getStartTime()), DateUtil.formatDateTime(new Date()), false);
                    if (array.size() > 0) {
                        try {
                            obj.setProductTimes(array.getJSONObject(0).getInteger("max") - array.getJSONObject(0).getInteger("min"));
                        }catch (Exception e){
                        }
                    }
                    double tagCount = ioTDBUtil.getTagCount(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), equipment.getEquipmentCode(), "Product_Status", DateUtil.formatDateTime(obj.getStartTime()), DateUtil.formatDateTime(new Date()));
                    try {
                        obj.setFunctionTime(NumberUtil.round(tagCount, 2).doubleValue());
                    }catch (Exception e){

                    }
                }
            }else{
                obj.setLoadTime(NumberUtil.round(Convert.toDouble(DateUtil.between(obj.getStartTime(), obj.getEndTime(), DateUnit.MINUTE)) / 60, 2).doubleValue());

                if (ObjectUtil.isNotNull(equipment)) {
                    JSONArray array = getMaxminService(equipment.getEquipmentCode(), "Number", SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), DateUtil.formatDateTime(obj.getStartTime()), DateUtil.formatDateTime(obj.getEndTime()), false);
                    if (array.size() > 0) {
                        try {
                            obj.setProductTimes(array.getJSONObject(0).getInteger("max") - array.getJSONObject(0).getInteger("min"));
                        }catch (Exception e){
                        }
                    }
                    double tagCount = ioTDBUtil.getTagCount(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), equipment.getEquipmentCode(), "Product_Status", DateUtil.formatDateTime(obj.getStartTime()), DateUtil.formatDateTime(obj.getEndTime()));
                    try {
                        obj.setFunctionTime(NumberUtil.round(tagCount, 2).doubleValue());
                    }catch (Exception e){

                    }
                }
            }
        }
        return result;
    }

    @Override
    public List<ModuleRecordVO> listCurrentModule() {
        return mesModulRecordMapper.listCurrentModule();
    }

    @Override
    public void stopModule(ModuleRecordVO vo) {
        if(ObjectUtil.isNull(vo.getRecordId())){
            throw new CustomException("参数异常");
        }
        MesModulRecord record = mesModulRecordMapper.selectById(vo.getRecordId());
        record.setEndTime(vo.getEndTime());
        this.updateById(record);
    }

    @Override
    public void changeModule(ModuleRecordVO vo) {
        //校验模具是否被别的机器使用
        QueryWrapper<MesModulRecord> query = new QueryWrapper<>();
        query.eq("modul_id", vo.getModulId()).isNull("end_time").ne("id", vo.getId());
        if (super.count(query) > 0) {
            throw new CustomException("该模具当前已被使用");
        }

        query = new QueryWrapper<>();
        query.eq("modul_id", vo.getModulId()).gt("end_time",vo.getStartTime());
        if (super.count(query) > 0) {
            throw new CustomException("该模具当前当前时间已被使用");
        }

        QueryWrapper<MesModulRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("equipment_id", vo.getId()).isNull("end_time").orderByDesc("start_time");
        List<MesModulRecord> list = mesModulRecordMapper.selectList(queryWrapper);
        if (list != null && list.size() > 0) {
            MesModulRecord obj = list.get(0);
            obj.setEndTime(vo.getStartTime());
            mesModulRecordMapper.updateById(obj);
        }
        MesModulRecord current = new MesModulRecord();
        current.setModulId(vo.getModulId());
        current.setEquipmentId(vo.getId());
        current.setStartTime(vo.getStartTime());
        current.setCreateTime(new Date());
        current.setCreateUserId(SecurityUtils.getUserId());

        current.setProductionId(vo.getProductionId());
        current.setProductionMultiple(vo.getProductionMultiple());
        if (vo.getProductionIds() != null) {
            for (ModuleRecordVO.Product product : vo.getProductionIds()) {
                current.setProductionId(product.getProductionId());
                current.setProductionMultiple(product.getProductionMultiple());
                mesModulRecordMapper.insert(current);
            }
        } else {
            mesModulRecordMapper.insert(current);
        }
    }

    @Override
    public List<MesModulRecord> listCurrentModule1(MesModulRecord mesModul) {
        return mesModulRecordMapper.listCurrentModule1(mesModul);
    }

    @Override
    public boolean updateById(MesModulRecord entity) {
//        QueryWrapper<MesModulRecord> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("modul_id", entity.getModulId()).isNull("end_time").ne("id", entity.getId());
//        if (super.count(queryWrapper) > 0) {
//            throw new CustomException("该模具当前已被使用");
//        }
//        queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("equipment_id", entity.getEquipmentId()).isNull("end_time").ne("id", entity.getId());
//        if (super.count(queryWrapper) > 0) {
//            throw new CustomException("该设备当前已有使用的模具");
//        }
        QueryWrapper<MesModulRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("modul_id", entity.getModulId()).isNull("end_time");
        List<MesModulRecord> list = super.list(queryWrapper);
        for (MesModulRecord mesModulRecord : list) {
            mesModulRecord.setEndTime(entity.getEndTime());
            stop(mesModulRecord);
        }

        return true;
    }
    private void stop(MesModulRecord entity){
        IotEquipment equipment = equipmentMapper.selectById(entity.getEquipmentId());
        MesModulRecord old = super.getById(entity.getId());
        if (DateUtil.compare(old.getEndTime(), entity.getEndTime()) != 0 || DateUtil.compare(old.getStartTime(), entity.getStartTime()) != 0) {
            if (ObjectUtil.isNull(entity.getEndTime())) {
                entity.setLoadTime(0d);
                entity.setFunctionTime(0d);
                entity.setProductTimes(0);
            } else {
                super.updateById(entity);
//                根据开始结束时间获取载荷时间及生产次数
                entity.setLoadTime(NumberUtil.round(Convert.toDouble(DateUtil.between(entity.getStartTime(), entity.getEndTime(), DateUnit.MINUTE)) / 60, 2).doubleValue());
                if (ObjectUtil.isNotNull(equipment)) {
                    JSONArray array = getMaxminService(equipment.getEquipmentCode(), "Number", SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), DateUtil.formatDateTime(entity.getStartTime()), DateUtil.formatDateTime(entity.getEndTime()), false);
                    if (array.size() > 0) {
                        try {
                            entity.setProductTimes(array.getJSONObject(0).getInteger("max") - array.getJSONObject(0).getInteger("min"));
                        } catch (Exception e) {
                            entity.setProductTimes(0);
                            e.printStackTrace();
                        }
                    }else{
                        entity.setProductTimes(0);
                    }
                    try {
                        double tagCount = ioTDBUtil.getTagCount(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), equipment.getEquipmentCode(), "Product_Status", DateUtil.formatDateTime(entity.getStartTime()), DateUtil.formatDateTime(entity.getEndTime()));
                        entity.setFunctionTime(NumberUtil.round(tagCount , 2).doubleValue());
                    }catch (Exception e){
                        entity.setFunctionTime(0d);
                    }
                }
            }
        }
//        重新计算当前模具使用记录的产量信息
        if (ObjectUtil.isNotNull(entity.getEndTime())) {
            int times = entity.getProductTimes();
            QueryWrapper<MesYield> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("record_id", entity.getId());
            yieldMapper.delete(queryWrapper1);
            BaseDict baseDict = new BaseDict();
            baseDict.setBaseType("SHIFT_TIME");
            List<BaseDict> shiftTimes = baseDictService.selectBaseDictList(baseDict);
            String day = "08:00";
            String night = "20:00";
            for (BaseDict temp : shiftTimes) {
                if (temp.getBaseCode().equalsIgnoreCase("SHIFT_DAY")) {
                    day = temp.getBaseDesc();
                } else if (temp.getBaseCode().equalsIgnoreCase("SHIFT_NIGHT")) {
                    night = temp.getBaseDesc();
                }
            }
//        白班时长
            long daytime = DateUtil.between(DateUtil.parseDateTime("2022-01-01 " + day + ":00"),
                    DateUtil.parseDateTime("2022-01-01 " + night + ":00"), DateUnit.SECOND, true) + 30;
//        夜班时长
            long nighttime = 24 * 60 * 60 - daytime + 60;
//            计算白班
            String dayStart = DateUtil.formatDate(entity.getStartTime()) + " " + day + ":00";
            String dayEnd = DateUtil.formatDate(entity.getEndTime()) + " " + night + ":59";
//            String dayEnd = DateUtil.formatDateTime(DateUtil.offsetSecond(DateUtil.parseDateTime(DateUtil.formatDate(DateUtil.offsetDay(entity.getEndTime(), 1)) + " " + day + ":00"),10));
            {
                JSONArray array = ioTDBUtil.getMaxAndMinByShiftV2(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), equipment.getEquipmentCode(), "Number", dayStart, dayEnd, DateUtil.formatDateTime(entity.getStartTime()), DateUtil.formatDateTime(entity.getEndTime()), daytime + "s");
                for (int i = 0; i < array.size(); i++) {
                    JSONObject object = array.getJSONObject(i);
                    if (ObjectUtil.isNull(object.get("max")) || ObjectUtil.isNull(object.get("min")) || object.getString("max").equalsIgnoreCase("null") || object.getString("min").equalsIgnoreCase("null")) {
                        continue;
                    }
                    MesYield yield = new MesYield();
                    yield.setRq(object.getString("time"));
                    yield.setRecordId(entity.getId());
                    yield.setProductionId(entity.getProductionId());
                    yield.setShift("day");
                    yield.setEquipmentId(entity.getEquipmentId());
                    times = times - (object.getInteger("max") - object.getInteger("min"));
                    yield.setYieldCount((object.getInteger("max") - object.getInteger("min")) * entity.getProductionMultiple());
                    yieldMapper.insert(yield);
                }
            }
//            计算夜班
            String nightStart = DateUtil.formatDate(entity.getStartTime()) + " " + night + ":00";
//            String nightStart = DateUtil.formatDateTime(DateUtil.offsetSecond(DateUtil.parseDateTime(DateUtil.formatDate(entity.getStartTime()) + " " + night + ":00"),-10));

            String nightEnd = DateUtil.formatDate(DateUtil.offsetDay(entity.getEndTime(), 1)) + " " + day + ":10";
//            String nightEnd = DateUtil.formatDateTime(DateUtil.offsetSecond(DateUtil.parseDateTime(DateUtil.formatDate(DateUtil.offsetDay(entity.getEndTime(), 2)) + " " + day + ":00"),10));

//            result.put("night",ioTDBUtil.getMaxAndMinByShift(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(),equipmentCode,tag,nightStart,nightEnd,nighttime+"h"));
            {
                JSONArray array = ioTDBUtil.getMaxAndMinByShiftV2(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), equipment.getEquipmentCode(), "Number", nightStart, nightEnd, DateUtil.formatDateTime(entity.getStartTime()), DateUtil.formatDateTime(entity.getEndTime()), nighttime + "s");
                for (int i = array.size() - 1; i > -1; i--) {
                    JSONObject object = array.getJSONObject(i);
                    if (ObjectUtil.isNull(object.get("max")) || ObjectUtil.isNull(object.get("min")) || object.getString("max").equalsIgnoreCase("null") || object.getString("min").equalsIgnoreCase("null")) {
                        array.remove(object);
                    }
                }
                for (int i = 0; i < array.size(); i++) {
                    JSONObject object = array.getJSONObject(i);
                    if (ObjectUtil.isNull(object.get("max")) || ObjectUtil.isNull(object.get("min")) || object.getString("max").equalsIgnoreCase("null") || object.getString("min").equalsIgnoreCase("null")) {
                        continue;
                    }
                    MesYield yield = new MesYield();
                    yield.setRq(object.getString("time"));
                    yield.setRecordId(entity.getId());
                    yield.setProductionId(entity.getProductionId());
                    yield.setShift("night");
                    yield.setEquipmentId(entity.getEquipmentId());
                    times = times - (object.getInteger("max") - object.getInteger("min"));
                    if(i == (array.size() - 1)){
                        yield.setYieldCount((object.getInteger("max") - object.getInteger("min") - times) * entity.getProductionMultiple());
                    }else{
                        yield.setYieldCount((object.getInteger("max") - object.getInteger("min")) * entity.getProductionMultiple());
                    }
                    yieldMapper.insert(yield);
                }
            }
        }
        super.updateById(entity);
    }

    @Override
    public boolean save(MesModulRecord entity) {
        QueryWrapper<MesModulRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("modul_id", entity.getModulId()).isNull("end_time");
        if (super.count(queryWrapper) > 0) {
            throw new CustomException("该模具当前已被使用");
        }
        queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("equipment_id", entity.getEquipmentId()).isNull("end_time");
        if (super.count(queryWrapper) > 0) {
            throw new CustomException("该设备当前已有使用的模具");
        }
        return super.save(entity);
    }

    private JSONArray getMaxminService(String deviceStr, String tag, String tenant, String start, String end, boolean sum) {
        JSONArray array = ioTDBUtil.getMaxAndMin(tenant, deviceStr, tag, start, end);
        for (int i = 0; i < array.size(); i++) {
            array.getJSONObject(i).put("Device", array.getJSONObject(i).getString("Device").split("\\.")[2]);
        }
        return array;
    }
}
