package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipMaintTemplItemDetail;

import java.util.List;

/**
 * 设备-维保模板-维保明细(EquipMaintTemplItemDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:31
 */
public interface EquipMaintTemplItemDetailMapper extends BaseMapper<EquipMaintTemplItemDetail>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipMaintTemplItemDetail 实例对象
     * @return 对象列表
     */
    List<EquipMaintTemplItemDetail> selectEquipMaintTemplItemDetailList(EquipMaintTemplItemDetail equipMaintTemplItemDetail);


}

