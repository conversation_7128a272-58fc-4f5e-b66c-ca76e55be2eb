package com.boyo.master.register;


import cn.hutool.core.util.StrUtil;
import com.boyo.master.domain.annotations.DataAsset;
import com.boyo.master.service.DataAssetService;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 注册数据资产
 */
@Component
public class DataAssetRegister implements BeanPostProcessor {
    /**
     * 这里是可以直接放到Redis中的
     */
    public static Map<String, DataAssetService> DATA_ASSET_MAP = new HashMap<>(16);

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        Class<?> beanClass = bean.getClass();
        if (beanClass.isAnnotationPresent(DataAsset.class)) {
            DataAsset annotation = beanClass.getAnnotation(DataAsset.class);

            // 获取注解标注的数据
            String type = annotation.type();
            String dataAssetType = StrUtil.isEmpty(type)
                    ? bean.getClass().getSimpleName().replace("ServiceImpl", "")
                    : type;

            if (DATA_ASSET_MAP.containsKey(dataAssetType)) {
                throw new RuntimeException("数据资产类型重复");
            }
            DATA_ASSET_MAP.put(dataAssetType, (DataAssetService) bean);
        }
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    /**
     * 根据数据资产实例获取数据资产类型
     * @param dataAssetService
     * @return
     */
    public static String getDataAssetType(DataAssetService dataAssetService) {
        for (Map.Entry<String, DataAssetService> item : DATA_ASSET_MAP.entrySet()) {
            if (item.getValue().equals(dataAssetService)) {
                return item.getKey();
            }
        }
        throw new RuntimeException("数据资产类型不存在");
    }

    public static String getDataAssetType(Class<?> dataAssetService) {
        DataAsset annotation = dataAssetService.getAnnotation(DataAsset.class);

        // 获取注解标注的数据
        String type = annotation.type();
        return StrUtil.isEmpty(type)
                ? dataAssetService.getSimpleName().replace("ServiceImpl", "")
                : type;
    }
}
