<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmClueMapper">

    <resultMap type="com.boyo.crm.entity.CrmClue" id="CrmClueResult">
        <result property="id" column="id"/>
        <result property="clueName" column="clue_name"/>
        <result property="clueSource" column="clue_source"/>
        <result property="sourceName" column="source_name"/>
        <result property="clueEmail" column="clue_email"/>
        <result property="cluePhone" column="clue_phone"/>
        <result property="clueAddress" column="clue_address"/>
        <result property="clueIndustry" column="clue_industry"/>
        <result property="industryName" column="industry_name"/>
        <result property="clueLevel" column="clue_level"/>
        <result property="levelName" column="level_name"/>
        <result property="clueRemark" column="clue_remark"/>
        <result property="nextTime" column="next_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="ownerUserId" column="owner_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmClueList" parameterType="com.boyo.crm.entity.CrmClue" resultMap="CrmClueResult">
        select t1.*,t2.base_desc as source_name,t3.base_desc as industry_name,t4.base_desc as level_name from (
        select
        id, clue_name, clue_source, clue_email, clue_phone, clue_address, clue_industry, clue_level, clue_remark,
        next_time, create_user_id, owner_user_id, create_time, update_time
        from t_crm_clue
        <where>
            <if test="clueName != null and clueName != ''">
                AND clue_name like concat('%', #{clueName}, '%')
            </if>
            <if test="clueSource != null">
                and clue_source = #{clueSource}
            </if>
            <if test="clueEmail != null and clueEmail != ''">
                AND clue_email like concat('%', #{clueEmail}, '%')
            </if>
            <if test="cluePhone != null and cluePhone != ''">
                AND clue_phone like concat('%', #{cluePhone}, '%')
            </if>
            <if test="clueAddress != null and clueAddress != ''">
                AND clue_address like concat('%', #{clueAddress}, '%')
            </if>
            <if test="clueIndustry != null">
                and clue_industry = #{clueIndustry}
            </if>
            <if test="clueLevel != null">
                and clue_level = #{clueLevel}
            </if>
            <if test="clueRemark != null and clueRemark != ''">
                and clue_remark = #{clueRemark}
            </if>
            <if test="nextTime != null">
                and next_time = #{nextTime}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="ownerUserId != null">
                and owner_user_id = #{ownerUserId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            ${params.dataScope}
        </where>
        ) t1
        left join (select * from t_base_dict where base_type = 'CRM_CLUE_SOURCE') t2 on t1.clue_source = t2.id
        left join (select * from t_base_dict where base_type = 'CUSTOMER_INDUSTRY') t3 on t1.clue_industry = t3.id
        left join (select * from t_base_dict where base_type = 'CUSTOMER_LEVEL') t4 on t1.clue_level = t4.id
    </select>

    <select id="selectById" resultMap="CrmClueResult">
        select t1.*, t2.base_desc as source_name, t3.base_desc as industry_name, t4.base_desc as level_name
        from (
                 select id,
                        clue_name,
                        clue_source,
                        clue_email,
                        clue_phone,
                        clue_address,
                        clue_industry,
                        clue_level,
                        clue_remark,
                        next_time,
                        create_user_id,
                        owner_user_id,
                        create_time,
                        update_time
                 from t_crm_clue
                 where id = #{id}
             ) t1
                 left join (select * from t_base_dict where base_type = 'CRM_CLUE_SOURCE') t2 on t1.clue_source = t2.id
                 left join (select * from t_base_dict where base_type = 'CUSTOMER_INDUSTRY') t3
                           on t1.clue_industry = t3.id
                 left join (select * from t_base_dict where base_type = 'CUSTOMER_LEVEL') t4 on t1.clue_level = t4.id
    </select>
</mapper>

