<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipMaintTaskItemMapper">

    <resultMap type="com.boyo.eam.domain.EquipMaintTaskItem" id="EquipMaintTaskItemResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="equipMaintTaskOpenid" column="equip_maint_task_openid" />
        <result property="item" column="item" />
        <result property="hour" column="hour" />
        <result property="hourUnit" column="hour_unit" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipMaintTaskItemList" parameterType="com.boyo.eam.domain.EquipMaintTaskItem" resultMap="EquipMaintTaskItemResult">
        select
            EMTI.*,EMT.id as taskId,EMT.plan_code as taskPlanCode,EMT.date as taskDate,EL.`code` as equipCode,EL.`name` as equipName,EMT.cycle as taskCycle,EMT.day as taskDay,EMT.state as taskState,EMT.task_code as taskCode,TML.line_name as lineName
        from
        equip_maint_task_item EMTI left join
        equip_maint_task EMT on EMTI.equip_maint_task_openid=EMT.openid left join
        equip_ledger EL on EMT.equip_ledger_openid=EL.openid left join
        t_model_line TML on EMT.line_openid=TML.line_openid
        <where>
            <if test="openid != null and openid != ''">
                and EMTI.openid = #{openid}
            </if>
            <if test="equipMaintTaskOpenid != null and equipMaintTaskOpenid != ''">
                and EMTI.equip_maint_task_openid = #{equipMaintTaskOpenid}
            </if>
            <if test="item != null and item != ''">
                and EMTI.item = #{item}
            </if>
            <if test="hour != null">
                and EMTI.hour = #{hour}
            </if>
            <if test="hourUnit != null and hourUnit != ''">
                and EMTI.hour_unit = #{hourUnit}
            </if>
            <if test="createBy != null and createBy != ''">
                and EMTI.create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and EMTI.create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and EMTI.update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and EMTI.update_time = #{updateTime}
            </if>
            <if test="taskState != null and taskState != ''">
                and EMT.state = #{taskState}
            </if>
            <if test="equipName != null and equipName!=''">
                and EL.name like CONCAT('%',#{equipName},'%')
            </if>
            <if test="lineName != null and lineName!=''">
                and TML.line_name like CONCAT('%',#{lineName},'%')
            </if>
        </where>
    </select>
</mapper>

