package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 物模型表(ObjectInfo)实体类
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Data
@TableName(value = "object_info")
public class ObjectInfo implements Serializable {
    private static final long serialVersionUID = -45793784670741671L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 物模型名称
    */
    @TableField(value="name")
    private String name;
    /**
    * 物模型编号
    */
    @TableField(value="code")
    private String code;
    /**
    * 描述
    */
    @TableField(value="remark")
    private String remark;
    /**
    * 状态：0停用，1启用
    */
    @TableField(value="state")
    private String state;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

    @TableField(exist = false)
    private List<ObjectInfoProperty> properties;
}
