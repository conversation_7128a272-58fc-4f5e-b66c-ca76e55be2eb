<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.iot.mapper.IotTslAttrMapper">

    <resultMap type="com.boyo.iot.domain.IotTslAttr" id="IotTslAttrResult">
        <result property="id" column="id"/>
        <result property="tslId" column="tsl_id"/>
        <result property="attrName" column="attr_name"/>
        <result property="attrCode" column="attr_code"/>
        <result property="attrDesc" column="attr_desc"/>
        <result property="attrType" column="attr_type"/>
        <result property="attrUnit" column="attr_unit"/>
        <result property="attrClass" column="attr_class"/>
        <result property="attrMultiple" column="attr_multiple"/>
        <result property="attrOrder" column="attr_order"/>
        <result property="attrIcon" column="attr_icon"/>
    </resultMap>

    <sql id="selectIotTslAttrVo">
        select id,
               tsl_id,
               attr_name,
               attr_code,
               attr_desc,
               attr_type,
               attr_unit,
               attr_order,
               attr_icon,
               attr_multiple
        from iot_tsl_attr
    </sql>

    <select id="selectIotTslAttrList" parameterType="com.boyo.iot.domain.IotTslAttr" resultMap="IotTslAttrResult">
        <include refid="selectIotTslAttrVo"/>
        <where>
            <if test="tslId != null ">
                and tsl_id = #{tslId}
            </if>
            <if test="attrName != null  and attrName != ''">
                and attr_name like concat('%', #{attrName}, '%')
            </if>
            <if test="attrCode != null  and attrCode != ''">
                and attr_code = #{attrCode}
            </if>
            <if test="attrDesc != null  and attrDesc != ''">
                and attr_desc = #{attrDesc}
            </if>
            <if test="attrType != null  and attrType != ''">
                and attr_type = #{attrType}
            </if>
            <if test="attrUnit != null  and attrUnit != ''">
                and attr_unit = #{attrUnit}
            </if>
        </where>
        order by attr_order asc
    </select>
</mapper>
