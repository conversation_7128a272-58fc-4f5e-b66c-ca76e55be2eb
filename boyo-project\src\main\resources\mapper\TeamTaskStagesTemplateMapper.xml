<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.project.mapper.TeamTaskStagesTemplateMapper">

    <resultMap type="com.boyo.project.entity.TeamTaskStagesTemplate" id="TeamTaskStagesTemplateResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="projectTemplateCode" column="project_template_code"/>
        <result property="createTime" column="create_time"/>
        <result property="sort" column="sort"/>
        <result property="code" column="code"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectTeamTaskStagesTemplateList" parameterType="com.boyo.project.entity.TeamTaskStagesTemplate"
            resultMap="TeamTaskStagesTemplateResult">
        select
        id, name, project_template_code, create_time, sort, code
        from team_task_stages_template
        <where>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="projectTemplateCode != null and projectTemplateCode != ''">
                and project_template_code = #{projectTemplateCode}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="sort != null">
                and sort = #{sort}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
        </where>
        order by sort desc,create_time asc
    </select>
</mapper>

