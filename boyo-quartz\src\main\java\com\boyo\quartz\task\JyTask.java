package com.boyo.quartz.task;

import org.springframework.stereotype.Component;
import com.boyo.common.utils.StringUtils;

/**
 * 定时任务调度测试
 *
 * <AUTHOR>
 */
@Component("jyTask")
public class JyTask {
    public void jyMultipleParams(String s, <PERSON><PERSON><PERSON> b, <PERSON> l, Double d, Integer i) {
        System.out.println(StringUtils.format("执行多参方法： 字符串类型{}，布尔类型{}，长整型{}，浮点型{}，整形{}", s, b, l, d, i));
    }

    public void jyParams(String params) {
        System.out.println("执行有参方法：" + params);
    }

    public void jyNoParams() {
    }
}
