package com.boyo.master.mapper;

import com.boyo.master.domain.ScreenYield;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 大屏产量Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface ScreenYieldMapper
{
    /**
     * 查询大屏产量
     *
     * @param id 大屏产量主键
     * @return 大屏产量
     */
    public ScreenYield selectScreenYieldById(Long id);

    /**
     * 查询大屏产量列表
     *
     * @param screenYield 大屏产量
     * @return 大屏产量集合
     */
    public List<ScreenYield> selectScreenYieldList(ScreenYield screenYield);

    /**
     * 新增大屏产量
     *
     * @param screenYield 大屏产量
     * @return 结果
     */
    public int insertScreenYield(ScreenYield screenYield);

    /**
     * 修改大屏产量
     *
     * @param screenYield 大屏产量
     * @return 结果
     */
    public int updateScreenYield(ScreenYield screenYield);

    public int updateScreenYieldByYieldTime(ScreenYield screenYield);

    /**
     * 删除大屏产量
     *
     * @param id 大屏产量主键
     * @return 结果
     */
    public int deleteScreenYieldById(Long id);

    public int deleteScreenYieldByWorkshopId(Long id);

    /**
     * 批量删除大屏产量
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScreenYieldByIds(Long[] ids);

    public int deleteScreenYieldByyieldTime(String yieldTime);


    public Long getYieldCount (@Param("start")String start, @Param("end")String end);
}
