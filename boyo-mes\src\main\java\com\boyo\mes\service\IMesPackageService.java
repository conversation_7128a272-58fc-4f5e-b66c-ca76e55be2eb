package com.boyo.mes.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.mes.entity.MesPackage;
import java.util.List;

/**
 * 打包管理(MesPackage)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-10 15:46:38
 */
public interface IMesPackageService extends IService<MesPackage> {

    /**
     * 查询多条数据
     *
     * @param mesPackage 对象信息
     * @return 对象列表
     */
    List<MesPackage> selectMesPackageList(MesPackage mesPackage);


}
