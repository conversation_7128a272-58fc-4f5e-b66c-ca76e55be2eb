package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmCustomer;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * CRM客户表(CrmCustomer)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-25 16:07:33
 */
@Tenant
public interface CrmCustomerMapper extends BaseMapper<CrmCustomer>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmCustomer 实例对象
     * @return 对象列表
     */
    List<CrmCustomer> selectCrmCustomerList(CrmCustomer crmCustomer);

    List<CrmCustomer> selectPoolCustomerList(CrmCustomer crmCustomer);


}

