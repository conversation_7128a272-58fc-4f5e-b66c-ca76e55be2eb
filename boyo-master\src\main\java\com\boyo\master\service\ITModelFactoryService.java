package com.boyo.master.service;

import java.util.List;

import com.boyo.master.domain.TModelFactory;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 主数据-工厂管理Service接口
 *
 * <AUTHOR>
 */
public interface ITModelFactoryService extends IService<TModelFactory> {
    /**
     * 根据条件查询查询主数据-工厂管理列表
     *
     * @param tModelFactory 主数据-工厂管理
     * @return 主数据-工厂管理集合
     */
    List<TModelFactory> selectTModelFactoryList(TModelFactory tModelFactory);

    boolean checkExist(TModelFactory tModelFactory);
}
