package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 维修任务管理(EquipMaintFix)实体类
 *
 * <AUTHOR>
 * @since 2021-11-17 16:13:52
 */
@Data
@TableName(value = "equip_maint_fix")
public class EquipMaintFix implements Serializable {
    private static final long serialVersionUID = -47651156408621344L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 维修任务编号
    */
    @TableField(value="fix_code")
    private String fixCode;
    /**
    * 设备openid：关联equip_ledger表的openid
    */
    @TableField(value="equip_ledger_openid")
    private String equipLedgerOpenid;
    /**
    * 上报说明
    */
    @TableField(value="remark")
    private String remark;
    /**
    * 位置
    */
    @TableField(value="location")
    private String location;
    /**
    * 联系人电话
    */
    @TableField(value="phone")
    private String phone;
    /**
    * 上报人
    */
    @TableField(value="sys_user_id")
    private Integer sysUserId;
    /**
    * 上报时间
    */
    @TableField(value="report_time")
    private Date reportTime;
    /**
    * 任务状态：0待处理，1进行中，2已完成
    */
    @TableField(value="state")
    private String state;
    /**
    * 派工单号
    */
    @TableField(value="task_code")
    private String taskCode;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;


    /** 额外字段 */
    @TableField(exist = false)
    private String equipName;
    @TableField(exist = false)
    private String equipCode;
    @TableField(exist = false)
    private Date beginDate;//开始日期
    @TableField(exist = false)
    private Date endDate;//结束日期

}
