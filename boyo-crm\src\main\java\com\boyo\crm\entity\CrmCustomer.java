package com.boyo.crm.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import com.boyo.framework.annotation.PropertyMsg;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * CRM客户表(CrmCustomer)实体类
 *
 * <AUTHOR>
 * @since 2022-03-25 16:07:33
 */
@Data
@TableName(value = "t_crm_customer")
public class CrmCustomer extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = -15038689820997640L;
            
    @TableId
    private Integer id;
    
    /**
    * 客户名称
    */
    @TableField(value="customer_name")
    @PropertyMsg(value="客户名称")
    private String customerName;
    /**
    * 跟进状态 0未跟进1已跟进
    */
    @TableField(value="followup")
    private String followup;
    /**
    * 1锁定
    */
    @TableField(value="is_lock")
    private String isLock;
    /**
    * 下次联系时间
    */
    @TableField(value="next_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @PropertyMsg(value="下次联系时间")
    private Date nextTime;
    /**
    * 成交状态 0未成交 1已成交
    */
    @TableField(value="deal_status")
    private String dealStatus;

    /**
     * 行业
     */
    @TableField(value="industry")
    @PropertyMsg(value="行业",type = "base")
    private Integer industry;

    @TableField(exist = false)
    private String industryName;

    /**
     * 级别
     */
    @TableField(value="level")
    @PropertyMsg(value="级别",type = "base")
    private Integer level;

    @TableField(exist = false)
    private String levelName;
    /**
    * 手机
    */
    @TableField(value="mobile")
    @PropertyMsg(value="手机")
    private String mobile;
    /**
    * 电话
    */
    @TableField(value="telephone")
    @PropertyMsg(value="电话")
    private String telephone;
    /**
    * 网址
    */
    @TableField(value="website")
    @PropertyMsg(value="网址")
    private String website;
    /**
    * 备注
    */
    @TableField(value="remark")
    private String remark;
    /**
    * 创建人ID
    */
    @TableField(value="create_user_id", fill = FieldFill.INSERT)
    private String createUserId;
    /**
    * 负责人ID
    */
    @TableField(value="owner_user_id")
    private String ownerUserId;

    @TableField(exist = false)
    private String ownerUserName;
    /**
    * 省市区
    */
    @TableField(value="address")
    private String address;
    /**
    * 定位信息
    */
    @TableField(value="location")
    private String location;
    /**
    * 详细地址
    */
    @TableField(value="detail_address")
    @PropertyMsg(value="详细地址")
    private String detailAddress;
    /**
    * 地理位置经度
    */
    @TableField(value="lng")
    private String lng;
    /**
    * 地理位置维度
    */
    @TableField(value="lat")
    private String lat;
    /**
    * 创建时间
    */
    @TableField(value="create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 更新时间
    */
    @TableField(value="update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
