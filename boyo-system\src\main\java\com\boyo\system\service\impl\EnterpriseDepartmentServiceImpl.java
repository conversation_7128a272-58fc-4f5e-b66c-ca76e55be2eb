package com.boyo.system.service.impl;

import java.util.Date;
import java.util.List;

import cn.hutool.core.util.IdUtil;
import com.boyo.common.utils.SecurityUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.system.mapper.EnterpriseDepartmentMapper;
import com.boyo.system.domain.EnterpriseDepartment;
import com.boyo.system.service.IEnterpriseDepartmentService;

/**
 * 企业部门管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class EnterpriseDepartmentServiceImpl extends ServiceImpl<EnterpriseDepartmentMapper, EnterpriseDepartment> implements IEnterpriseDepartmentService {
    private final EnterpriseDepartmentMapper enterpriseDepartmentMapper;


    /**
     * 查询企业部门管理列表
     *
     * @param enterpriseDepartment 企业部门管理
     * @return enterpriseDepartment 列表
     */
    @Override
    public List<EnterpriseDepartment> selectEnterpriseDepartmentList(EnterpriseDepartment enterpriseDepartment) {
        return enterpriseDepartmentMapper.selectEnterpriseDepartmentList(enterpriseDepartment);
    }

    @Override
    public List<String> getDeptAndChildren(String openid) {
        return enterpriseDepartmentMapper.getDeptAndChildren(openid);
    }

    @Override
    public boolean save(EnterpriseDepartment entity) {
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setEnterpriseOpenid(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid());
        entity.setDepartmentOpenid(IdUtil.fastSimpleUUID());
        return super.save(entity);
    }
}
