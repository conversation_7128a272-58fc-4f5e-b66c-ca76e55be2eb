package com.boyo.system.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import org.apache.ibatis.annotations.Param;


/**
 * 企业用户管理Mapper接口
 *
 * <AUTHOR>
 */
public interface EnterpriseUserMapper extends BaseMapper<EnterpriseUser> {

    /**
     * 查询企业用户管理列表
     *
     * @param enterpriseUser 企业用户管理
     * @return EnterpriseUser集合
     */
    List<EnterpriseUser> selectEnterpriseUserList(EnterpriseUser enterpriseUser);

    EnterpriseUser selectEnterpriseUser(@Param("userName") String userName, @Param("enterpriseCode") String enterpriseCode);

    int saveEnterpriseUser(EnterpriseUser enterpriseUser);

    void updateEnterpriseUserById(EnterpriseUser enterpriseUser);
}
