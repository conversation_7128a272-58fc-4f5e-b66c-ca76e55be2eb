package com.boyo.mes.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工序设备关联关系(ProcessEquipment)实体类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Data
@TableName(value = "t_process_equipment")
public class ProcessEquipment implements Serializable {
    private static final long serialVersionUID = -29515932747348594L;
            
    @TableId
    private Integer id;
    
    
    @TableField(value="process_id")
    private Integer processId;
    
    @TableField(value="equipment_id")
    private Integer equipmentId;

    @TableField(exist = false)
    private String equipmentName;

    @TableField(exist = false)
    private String equipmentCode;

}
