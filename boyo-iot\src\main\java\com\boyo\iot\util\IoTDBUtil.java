package com.boyo.iot.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.boyo.iot.domain.HistoryData;
import com.boyo.iot.entity.IorealData;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.apache.iotdb.session.Session;
import org.apache.iotdb.session.SessionDataSet;
import org.apache.iotdb.tsfile.read.common.RowRecord;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
public class IoTDBUtil {
    @Resource
    private Session iotSession;

    public void addDataToIot(String enterprise, String device, List<IorealData> dataList) {
        List<String> measurements = new ArrayList<>();
        List<String> values = new ArrayList<>();
        if (dataList != null && dataList.size() > 0) {
            for (IorealData data : dataList) {
                if (StrUtil.isNotEmpty(data.getVal())) {
                    //升级后
                    // measurements.add("`"+data.getTag()+"`");
                    //升级前
                    measurements.add(data.getTag());
                    values.add(data.getVal());
                }
            }
            try {
                if (values.size() > 0) {
                    //升级后的插入语句
                    //iotSession.insertRecord("root." + enterprise + ".`" + device + "`", System.currentTimeMillis(), measurements, values);
                    //历史版本
                    iotSession.insertRecord("root." + enterprise + "." + device, System.currentTimeMillis(), measurements, values);
                }
            } catch (IoTDBConnectionException e) {
                e.printStackTrace();
            } catch (StatementExecutionException e) {
                e.printStackTrace();
            }
        }
    }

    public List<HistoryData> listData(String enterprise, String device, String code, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
        List<HistoryData> result = new ArrayList<>();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + device;
            if (ObjectUtils.isEmpty(start) || ObjectUtils.isEmpty(end)) {
                dataSet = iotSession.executeQueryStatement("select " + code + " from " + aim + " order by time desc limit 1");
            } else {
                dataSet = iotSession.executeQueryStatement("select last_value(" + code + ") from " + aim + " GROUP BY([" + start + ", " + end + "),1m) " + " fill(float[PREVIOUS])");
            }
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                HistoryData dataVO = new HistoryData();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                dataVO.setTime(dateFormat.format(new Date(record.getTimestamp())));
                dataVO.setVal(record.getFields().get(0).getStringValue());
                dataVO.setDevice(device);
                result.add(dataVO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.(dataSet);
        }
        return result;
    }

    public JSONArray listDataHistory(String enterprise, String deviceStr, String code, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
        JSONArray result = new JSONArray();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + deviceStr;
            if (ObjectUtils.isEmpty(start) || ObjectUtils.isEmpty(end)) {
                dataSet = iotSession.executeQueryStatement("select " + code + " from " + aim + " order by time desc limit 1");
            } else {
                //dataSet = iotSession.executeQueryStatement("select " + code + " from " + aim +  " where time >= " + start + " and time <" + end +" fill(PREVIOUS,2m)");
                dataSet = iotSession.executeQueryStatement("select last_value(" + code + ") from " + aim + " GROUP BY([" + start + ", " + end + "),1m) fill(PREVIOUS)");

            }
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                JSONObject object = new JSONObject();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
                for (int i = 0; i < measurements.size(); i++) {
                    object.put(measurements.get(i).substring(measurements.get(i).lastIndexOf(".") + 1), record.getFields().get(i).getStringValue());
                }
                result.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.(dataSet);
        }
        return result;
    }

    public JSONArray listDataHistoryV2(String enterprise, String device, List<String> codeList, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
        JSONArray result = new JSONArray();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + device;
            StringBuffer codestr = new StringBuffer("");
            for (String code : codeList) {
                codestr.append("last_value(" + code + ") as " + code + ",");
            }
            dataSet = iotSession.executeQueryStatement("select " + codestr.toString().substring(0, codestr.length() - 1) + " from " + aim + " GROUP BY([" + start + ", " + end + "),1m) fill(PREVIOUS)");
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                JSONObject object = new JSONObject();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
                for (int i = 0; i < measurements.size(); i++) {
                    object.put(measurements.get(i).substring(measurements.get(i).lastIndexOf(".") + 1), record.getFields().get(i).getStringValue());
                }
                result.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.(dataSet);
        }
        return result;
    }

    public List<HistoryData> getDataListWithoutFill(String enterprise, String device, String code, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
        List<HistoryData> result = new ArrayList<>();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + device;
            dataSet = iotSession.executeQueryStatement("select " + code + " from " + aim + " where time >= " + start + " and time <" + end);
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                HistoryData dataVO = new HistoryData();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                dataVO.setTime(dateFormat.format(new Date(record.getTimestamp())));
                dataVO.setVal(record.getFields().get(0).getStringValue());
                result.add(dataVO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.closeResultSet(dataSet);
        }
        return result;
    }

    public List<HistoryData> getDataListFillWithZero(String enterprise, String device, String code, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
        List<HistoryData> result = new ArrayList<>();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + device;
            dataSet = iotSession.executeQueryStatement("select last_value(" + code + ") from " + aim + " GROUP BY([" + start + ", " + end + "),1m) fill(PREVIOUS,2m) align by device");
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                HistoryData dataVO = new HistoryData();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                dataVO.setTime(dateFormat.format(new Date(record.getTimestamp())));
                dataVO.setDevice((record.getFields().get(0).getStringValue().equals(null) || record.getFields().get(0).getStringValue() == "null") ? "0" : record.getFields().get(0).getStringValue());
                dataVO.setVal((record.getFields().get(1).getStringValue().equals(null) || record.getFields().get(1).getStringValue() == "null") ? "0" : record.getFields().get(1).getStringValue());
                result.add(dataVO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.closeResultSet(dataSet);
        }
        return result;
    }

    /**
     * 获取某时间的OEE信息
     */
    public JSONObject getRealOEE(String enterprise, String device, String time) {
        JSONObject result = new JSONObject();
        SessionDataSet dataSet = null;
        try {
            if (StrUtil.isEmpty(device)) {
                device = "*";
            }
            String aim = "root." + enterprise + ".*";
            dataSet = iotSession.executeQueryStatement("select * from " + aim + " where time = " + time + "  fill(float[PREVIOUS])");
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                for (int i = 0; i < measurements.size(); i++) {
//                    System.out.println(measurements.get(i) + "==" + record.getFields().get(i).getStringValue());
                    if (measurements.get(i).contains("OEE")) {
                        result.put(measurements.get(i).replace("root.4bd8e784c72b47d8bc90faaa6b9364a8.", ""), record.getFields().get(i).getStringValue());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public JSONArray getMaxAndMinByHour(String enterprise, String device, String code, String start, String end) {
        JSONArray result = new JSONArray();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + device;
            dataSet = iotSession.executeQueryStatement("select MAX_VALUE(" + code + ") as max,MIN_VALUE(" + code + ") as min from " + aim + " where " + code + " > 0 group by ([" + start + ", " + end + "),1h)");
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                JSONObject object = new JSONObject();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
                for (int i = 0; i < measurements.size(); i++) {
                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
                }
                result.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.closeResultSet(dataSet);
        }
        return result;
    }

    public JSONArray getAllMaxAndMinByHour(String enterprise, String deviceStr, String code, String start, String end) {
        JSONArray result = new JSONArray();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + deviceStr;
            dataSet = iotSession.executeQueryStatement("select MAX_VALUE(" + code + ") as max,MIN_VALUE(" + code + ") as min from " + aim + " where " + code + " > 0  group by ([" + start + ", " + end + "),1h) align by device");
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                JSONObject object = new JSONObject();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
                for (int i = 0; i < measurements.size(); i++) {
                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
                }
                result.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.closeResultSet(dataSet);
        }
        return result;
    }

    public JSONArray getMaxAndMinByDay(String enterprise, String device, String code, String start, String end) {
        JSONArray result = new JSONArray();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + device;
            dataSet = iotSession.executeQueryStatement("select MAX_VALUE(" + code + ") as max,MIN_VALUE(" + code + ") as min from " + aim + " where " + code + " > 0 group by ([" + start + ", " + end + "),1d)");
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                JSONObject object = new JSONObject();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
                for (int i = 0; i < measurements.size(); i++) {
                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
                }
                result.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.closeResultSet(dataSet);
        }
        return result;
    }

    public JSONArray getMaxAndMin(String enterprise, String device, String code, String start, String end) {
        JSONArray result = new JSONArray();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + device;
            dataSet = iotSession.executeQueryStatement("select MAX_VALUE(" + code + ") as max,MIN_VALUE(" + code + ") as min from " + aim + " where " + code + " > 0 and time >= " + start + " and time <" + end + " align by device");
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                JSONObject object = new JSONObject();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
                for (int i = 0; i < measurements.size(); i++) {
                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
                }
                result.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.closeResultSet(dataSet);
        }
        return result;
    }

    public JSONArray getFirstAndLast(String enterprise, String device, String code, String start, String end) {
        JSONArray result = new JSONArray();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + device;
            dataSet = iotSession.executeQueryStatement("select FIRST_VALUE(" + code + ") as max,LAST_VALUE(" + code + ") as min from " + aim + " group by ([" + start + ", " + end + "),1d) align by device");
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                JSONObject object = new JSONObject();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh-mm-ss");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
                for (int i = 0; i < measurements.size(); i++) {
                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
                }
                result.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.closeResultSet(dataSet);
        }
        return result;
    }

    public JSONArray getAllMaxAndMinByDay(String enterprise, String device, String code, String start, String end) {
        JSONArray result = new JSONArray();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + device;
            dataSet = iotSession.executeQueryStatement("select MAX_VALUE(" + code + ") as max,MIN_VALUE(" + code + ") as min from " + aim + " group by ([" + start + ", " + end + "),1d) align by device");
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                JSONObject object = new JSONObject();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
                for (int i = 0; i < measurements.size(); i++) {
                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
                }
                result.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.closeResultSet(dataSet);
        }
        return result;
    }

    public JSONArray getMaxAndMinByShift(String enterprise, String device, String code, String start, String end, String sharding) {
        JSONArray result = new JSONArray();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + device;
            dataSet = iotSession.executeQueryStatement("select MAX_VALUE(" + code + ") as max,MIN_VALUE(" + code + ") as min from " + aim + " where " + code + " > 0 group by ([" + start + ", " + end + ")," + sharding + ",1d) align by device");
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                JSONObject object = new JSONObject();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
                for (int i = 0; i < measurements.size(); i++) {
                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
                }
                result.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.closeResultSet(dataSet);
        }
        return result;
    }

    public Double getTagCount(String enterprise, String device, String code, String start, String end) {
        Double result = 0d;
        Map<String, Double> run = new HashMap<>();
        Integer count = 0;
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + device;
//            String aim = "root." + enterprise + ".*";
            dataSet = iotSession.executeQueryStatement("select count(" + code + ") as c  from " + aim + " where " + code + "= '1' and time>= " + start + " and time <= " + end + " align by device");
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
//                count ++;
                RowRecord record = dataSet.next();
//                System.out.println(JSONObject.toJSONString(record));
//                run.put(record.getFields().get(0).getStringValue(),Convert.toDouble(record.getFields().get(1).getStringValue()));
                for (int i = 0; i < measurements.size(); i++) {
                    if (measurements.get(i).equalsIgnoreCase("c")) {
                        count = count + Convert.toInt(record.getFields().get(i).getStringValue());
                    }
                }
            }
            dataSet = iotSession.executeQueryStatement("select count(" + code + ") as c  from " + aim + " where " + code + "= '0' and time>= " + start + " and time <= " + end + " align by device");
            measurements.remove("Time");
            while (dataSet.hasNext()) {
//                count ++;
                RowRecord record = dataSet.next();
//                System.out.println(JSONObject.toJSONString(record));
//                run.put(record.getFields().get(0).getStringValue(),Convert.toDouble(record.getFields().get(1).getStringValue()));
                for (int i = 0; i < measurements.size(); i++) {
                    if (measurements.get(i).equalsIgnoreCase("c")) {
                        if (count == 0) {
                            result = 0d;
                        } else {
                            result = count / (Convert.toDouble(record.getFields().get(i).getStringValue()) + count);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
//            iotSession.closeResultSet(dataSet);
        }
        return result * DateUtil.between(DateUtil.parseDateTime(start), DateUtil.parseDateTime(end), DateUnit.HOUR);
    }

    public JSONArray getMaxAndMinByShiftV2(String enterprise, String device, String code, String start, String end, String startTime, String endTime, String sharding) {
        JSONArray result = new JSONArray();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + device;
            dataSet = iotSession.executeQueryStatement("select MAX_TIME(" + code + ") as maxTime,MIN_TIME(" + code + ") as minTime,MAX_VALUE(" + code + ") as max,MIN_VALUE(" + code + ") as min from " + aim + " where time>=" + startTime + " and time <= " + endTime + " group by ([" + start + ", " + end + ")," + sharding + ",1d) align by device");
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                JSONObject object = new JSONObject();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
                for (int i = 0; i < measurements.size(); i++) {
                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
                }
                result.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.closeResultSet(dataSet);
        }
        return result;
    }

    public List<HistoryData> listData1(String enterprise, String device, String code, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
        List<HistoryData> result = new ArrayList<>();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + device;
            if (ObjectUtils.isEmpty(start) || ObjectUtils.isEmpty(end)) {
                dataSet = iotSession.executeQueryStatement("select " + code + " from " + aim + " order by time desc limit 1");
            } else {
                dataSet = iotSession.executeQueryStatement("select last_value(" + code + ") from " + aim + " GROUP BY([" + start + ", " + end + "),1m) " + " fill(float[PREVIOUS])");
            }
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                HistoryData dataVO = new HistoryData();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                dataVO.setTime(dateFormat.format(new Date(record.getTimestamp())));
                dataVO.setVal(record.getFields().get(0).getStringValue());
                dataVO.setDevice(device);
                result.add(dataVO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.(dataSet);
        }
        return result;
    }
}
