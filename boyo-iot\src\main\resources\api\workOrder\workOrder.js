import request from '@/utils/request'

const prefix = '/${module}'

// 查询系统工单(WorkOrder)列表
export function listWorkOrder(query) {
  return request({
    url: prefix + '/workOrder/list',
    method: 'get',
    params: query,
  })
}

// 查询系统工单(WorkOrder)详细
export function getWorkOrder(id) {
  return request({
    url: prefix + '/workOrder/' + id,
    method: 'get',
  })
}

// 新增系统工单(WorkOrder)
export function addWorkOrder(data) {
  return request({
    url: prefix + '/workOrder',
    method: 'post',
    data: data,
  })
}

// 修改系统工单(WorkOrder)
export function updateWorkOrder(data) {
  return request({
    url: prefix + '/workOrder',
    method: 'put',
    data: data,
  })
}

// 删除系统工单(WorkOrder)
export function delWorkOrder(id) {
  return request({
    url: prefix + '/workOrder/' + id,
    method: 'delete',
  })
}
