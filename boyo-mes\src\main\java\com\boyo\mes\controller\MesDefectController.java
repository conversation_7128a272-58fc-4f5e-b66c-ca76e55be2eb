package com.boyo.mes.controller;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.mes.entity.MesDefect;
import com.boyo.mes.entity.MesYield;
import com.boyo.mes.service.IMesDefectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@RestController
@Api
@RequestMapping("/mes/defect")
public class MesDefectController extends BaseController {

    @Autowired
    private IMesDefectService mesDefectService;

    @ApiOperation("查询缺陷原因列表")
    @GetMapping("/list")
    public TableDataInfo list() {
        startPage();
        List<MesDefect> list = mesDefectService.list(null);
        return getDataTable(list);
    }


    /**
     * 新增缺陷原因
     */
    @ApiOperation("新增缺陷原因")
    @PostMapping
    public AjaxResult add(@RequestBody MesDefect mesDefect) {
        return toBooleanAjax(mesDefectService.save(mesDefect));
    }

    /**
     * 修改缺陷原因
     */
    @ApiOperation("修改缺陷原因")
    @PutMapping
    public AjaxResult edit(@RequestBody MesDefect mesDefect) {
        return toBooleanAjax(mesDefectService.updateById(mesDefect));
    }

    /**
     * 删除缺陷原因
     */
    @ApiOperation("删除缺陷原因")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(mesDefectService.removeByIds(Arrays.asList(ids)));
    }
}
