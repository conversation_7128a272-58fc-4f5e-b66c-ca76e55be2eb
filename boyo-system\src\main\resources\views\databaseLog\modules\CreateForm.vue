<template>
  <a-modal width="30%" :maskClosable="false" :visible="open" @cancel="cancel">
    <template #title>
      <a-icon type="security-scan" />
      {{ formTitle }}
    </template>
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      layout="horizontal"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
           <a-form-model-item label="OPENID">
        <a-input
          :size="formSize"
          v-model="form.databaseOpenid"
          :placeholder="$t('app.global.please.input') + 'OPENID'"
        />
      </a-form-model-item>
           <a-form-model-item label="数据库版本号">
        <a-input
          :size="formSize"
          v-model="form.databaseVersionCode"
          :placeholder="$t('app.global.please.input') + '数据库版本号'"
        />
      </a-form-model-item>
           <a-form-model-item label="数据库版本名称">
        <a-input
          :size="formSize"
          v-model="form.databaseVersionName"
          :placeholder="$t('app.global.please.input') + '数据库版本名称'"
        />
      </a-form-model-item>
           <a-form-model-item label="变更时间">
        <a-input
          :size="formSize"
          v-model="form.databaseVersionTime"
          :placeholder="$t('app.global.please.input') + '变更时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="数据库版本变更语句">
        <a-input
          :size="formSize"
          v-model="form.databaseChangeSql"
          :placeholder="$t('app.global.please.input') + '数据库版本变更语句'"
        />
      </a-form-model-item>
           <a-form-model-item label="数据库版本完整语句">
        <a-input
          :size="formSize"
          v-model="form.databaseFullSql"
          :placeholder="$t('app.global.please.input') + '数据库版本完整语句'"
        />
      </a-form-model-item>
           <a-form-model-item label="创建时间">
        <a-input
          :size="formSize"
          v-model="form.createTime"
          :placeholder="$t('app.global.please.input') + '创建时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="更新时间">
        <a-input
          :size="formSize"
          v-model="form.updateTime"
          :placeholder="$t('app.global.please.input') + '更新时间'"
        />
      </a-form-model-item>
        </a-form-model>
    <template #footer>
      <a-space>
        <a-button :size="formSize" icon="close" type="danger" @click="cancel">
          {{ $t('app.global.close') }}
        </a-button>
        <a-button :size="formSize" icon="save" type="primary" @click="submitForm">
          {{ $t('app.global.save') }}
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script>
import { addDatabaseLog, updateDatabaseLog, getDatabaseLog } from '@/api/databaseLog'
export default {
  data() {
    return {
      //新增或修改
      updateState: false,
      formTitle: '企业租户数据库变更',
      // 表单参数
      form: {
           id: '',
           databaseOpenid: '',
           databaseVersionCode: '',
           databaseVersionName: '',
           databaseVersionTime: '',
           databaseChangeSql: '',
           databaseFullSql: '',
           createTime: '',
           updateTime: '',
             },
      open: false,
      rules: {},
    }
  },
  created() {
    this.rules = {
    }
  },
  methods: {
    /**
     * 新增按钮操作
     * */
    handleAdd() {
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.add') + '企业租户数据库变更'
      this.form = {}
      this.updateState = false
    },
    /**
     * 修改按钮操作
     * */
    async handleUpdate($event, id) {
      $event.stopPropagation()
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.edit') + '企业租户数据库变更'
      const response = await getDatabaseLog(id)
      this.form = response.data
    },
    /**
     * 提交按钮
     * */
    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
            if (this.form.id) {
                await updateDatabaseLog(this.form)
                this.$alert.success(this.$t('app.global.edit.success'))
                this.open = false
                this.$emit('ok')
            } else {
                await addDatabaseLog(this.form)
                this.$alert.success(this.$t('app.global.add.success'))
                this.open = false
                this.$emit('ok')
            }
        } else {
            return false
        }
        })
    },
    /**
     * 取消按钮
     * */
    cancel() {
      this.open = false
      this.reset()
    },
    /**
     * 表单重置
     * */
    reset() {
      this.form = {}
      if (this.$refs.form) {
        this.$refs.form.resetFields()}
      }
    },
}
</script>

