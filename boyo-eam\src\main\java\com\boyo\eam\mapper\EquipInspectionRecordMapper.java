package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipInspectionRecord;

import java.util.List;

/**
 * (EquipInspectionRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-28 19:30:53
 */
public interface EquipInspectionRecordMapper extends BaseMapper<EquipInspectionRecord>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipInspectionRecord 实例对象
     * @return 对象列表
     */
    List<EquipInspectionRecord> selectEquipInspectionRecordList(EquipInspectionRecord equipInspectionRecord);


}

