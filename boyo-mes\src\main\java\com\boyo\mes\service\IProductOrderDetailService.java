package com.boyo.mes.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.mes.entity.ProductOrderDetail;
import java.util.List;

/**
 * 生产工单执行(ProductOrderDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
public interface IProductOrderDetailService extends IService<ProductOrderDetail> {

    /**
     * 查询多条数据
     *
     * @param productOrderDetail 对象信息
     * @return 对象列表
     */
    List<ProductOrderDetail> selectProductOrderDetailList(ProductOrderDetail productOrderDetail);

    List<ProductOrderDetail> listCurrentOrder();

    void completeOrder(ProductOrderDetail productOrderDetail);


}
