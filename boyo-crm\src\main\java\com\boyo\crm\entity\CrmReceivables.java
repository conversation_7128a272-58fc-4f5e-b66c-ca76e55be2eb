package com.boyo.crm.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import com.boyo.framework.annotation.PropertyMsg;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 回款表(CrmReceivables)实体类
 *
 * <AUTHOR>
 * @since 2022-03-28 10:22:52
 */
@Data
@TableName(value = "t_crm_receivables")
public class CrmReceivables extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = -93060653626608375L;
        /**
    * 回款ID
    */    
    @TableId
    private Integer id;
    
    /**
    * 回款编号
    */
    @TableField(value="number")
    @PropertyMsg(value="回款编号")
    private String number;
    /**
    * 回款计划ID
    */
    @TableField(value="plan_id")
    private Integer planId;
    /**
    * 客户ID
    */
    @TableField(value="customer_id")
    @PropertyMsg(value="客户",type = "customer")
    private Integer customerId;
    /**
    * 合同ID
    */
    @TableField(value="contract_id")
    @PropertyMsg(value="合同",type = "contract")
    private Integer contractId;
    /**
    * 0 未审核 1 审核通过 2 审核拒绝 3 审核中 4 已撤回
    */
    @TableField(value="check_status")
    private Integer checkStatus;
    /**
    * 审核记录ID
    */
    @TableField(value="examine_record_id")
    private Integer examineRecordId;
    /**
    * 回款日期
    */
    @TableField(value="return_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @PropertyMsg(value="回款日期")
    private Date returnTime;
    /**
    * 回款方式
    */
    @TableField(value="return_type")
    @PropertyMsg(value="回款方式",type = "base")
    private Integer returnType;
    /**
    * 回款金额
    */
    @TableField(value="money")
    @PropertyMsg(value="回款金额")
    private BigDecimal money;
    /**
    * 备注
    */
    @TableField(value="remark")
    private String remark;
    /**
    * 创建人ID
    */
    @TableField(value="create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 负责人ID
    */
    @TableField(value="owner_user_id")
    private Long ownerUserId;
    /**
    * 创建时间
    */
    @TableField(value="create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 更新时间
    */
    @TableField(value="update_time",fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
    * 备注
    */
    @TableField(value="remarks")
    private String remarks;

    /**
     * 负责人名称
     */
    @TableField(exist = false)
    private String ownerUserName;
    /**
     * 客户名称
     */
    @TableField(exist = false)
    private String customerName;

    /**
     * 合同名称
     */
    @TableField(exist = false)
    private String contractName;

    /**
     * 回款方式
     */
    @TableField(exist = false)
    private String returnTypeName;

}
