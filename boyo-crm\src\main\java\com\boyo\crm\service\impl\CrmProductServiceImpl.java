package com.boyo.crm.service.impl;

import com.boyo.common.annotation.DataScope;
import com.boyo.crm.entity.CrmVisit;
import com.boyo.crm.util.ActionEnum;
import com.boyo.crm.util.ActionUtil;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.entity.CrmProduct;
import com.boyo.crm.mapper.CrmProductMapper;
import com.boyo.crm.service.ICrmProductService;
import java.util.List;

/**
 * CRM产品表(CrmProduct)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-24 15:52:39
 */
@Service("crmProductService")
@AllArgsConstructor
public class CrmProductServiceImpl extends ServiceImpl<CrmProductMapper, CrmProduct> implements ICrmProductService {
    private final CrmProductMapper crmProductMapper;
    private final ActionUtil actionUtil;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
//    @DataScope(columns = "owner_user_id,create_user_id")
    public List<CrmProduct> selectCrmProductList(CrmProduct crmProduct) {
        return crmProductMapper.selectCrmProductList(crmProduct);
    }
    @Override
    public boolean save(CrmProduct entity) {
        super.save(entity);
        actionUtil.editRecord(null,null, ActionEnum.PRODUCT,entity.getProductId(), null);
        return true;
    }
    @Override
    public boolean updateById(CrmProduct entity) {
        actionUtil.editRecord(super.getById(entity.getProductId()),entity,ActionEnum.PRODUCT,entity.getProductId(), CrmProduct.class);
        return super.updateById(entity);
    }

}
