package com.boyo.iot.domain;

import lombok.Data;

@Data
public class HistoryData implements Comparable<HistoryData>{
    private String time;
    private Object val;
    private String device;


    @Override
    public int compareTo(HistoryData o) {
        if (!this.getVal().equals("null") && !o.getVal().equals("null")){
            return Double.compare(Double.parseDouble(String.valueOf(this.getVal())),Double.parseDouble(String.valueOf(o.getVal())));
        }
        return 0;
    }
}
