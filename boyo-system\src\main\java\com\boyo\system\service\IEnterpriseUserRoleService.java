package com.boyo.system.service;

import java.util.List;

import com.boyo.system.domain.EnterpriseUserRole;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 企业用户角色管理Service接口
 *
 * <AUTHOR>
 */
public interface IEnterpriseUserRoleService extends IService<EnterpriseUserRole> {
    /**
     * 根据条件查询查询企业用户角色管理列表
     *
     * @param enterpriseUserRole 企业用户角色管理
     * @return 企业用户角色管理集合
     */
    List<EnterpriseUserRole> selectEnterpriseUserRoleList(EnterpriseUserRole enterpriseUserRole);
}
