package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 维保任务管理-维保项目-明细(EquipMaintTaskItemDetail)实体类
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:33
 */
@Data
@TableName(value = "equip_maint_task_item_detail")
public class EquipMaintTaskItemDetail implements Serializable {
    private static final long serialVersionUID = 821619620310900955L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 关联equip_maint_task_item表的openid
    */
    @TableField(value="equip_maint_task_item_openid")
    private String equipMaintTaskItemOpenid;
    /**
    * 模板名称
    */
    @TableField(value="detail")
    private String detail;
    /**
    * 0启用，1停用
    */
    @TableField(value="value")
    private String value;
    /**
    * 模板类型
    */
    @TableField(value="type")
    private String type;
    /**
    * 最大值
    */
    @TableField(value="max_num")
    private Double maxNum;
    /**
    * 最小值
    */
    @TableField(value="min_num")
    private Double minNum;
    /**
    * 单位
    */
    @TableField(value="unit")
    private String unit;

    /**
    * 检测标准
    */
    @TableField(value="standard")
    private String standard;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;


    /** 额外字段 */
    @TableField(exist = false)
    private String taskOpenid;
    @TableField(exist = false)
    private String item;
    @TableField(exist = false)
    private String recordOpenid;
    @TableField(exist = false)
    private String pass;
    @TableField(exist = false)
    private String maintRemark;
    @TableField(exist = false)
    private Double actualValue;
    @TableField(exist = false)
    private String mediaIds;
    @TableField(exist = false)
    private List<String> mediaUrls;
    @TableField(exist = false)
    private String equipName;
    @TableField(exist = false)
    private String equipLocation;
}
