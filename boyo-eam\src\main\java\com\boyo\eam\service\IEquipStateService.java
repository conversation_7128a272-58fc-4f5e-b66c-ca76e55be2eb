package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipState;

import java.util.List;

/**
 * 设备状态表(EquipState)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
public interface IEquipStateService extends IService<EquipState> {

    /**
     * 查询多条数据
     *
     * @param equipState 对象信息
     * @return 对象列表
     */
    List<EquipState> selectEquipStateList(EquipState equipState);


}
