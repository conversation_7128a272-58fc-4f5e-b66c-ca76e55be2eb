package com.boyo.mes.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产量详情(MesYield)实体类
 *
 * <AUTHOR>
 * @since 2023-01-05 17:02:39
 */
@Data
@TableName(value = "t_mes_yield")
public class MesYield implements Serializable {
    private static final long serialVersionUID = -40616510478613635L;
            
    @TableId
    private Integer id;
    
    /**
    * 日期
    */
    @TableField(value="rq")
    private String rq;
    /**
    * 模具使用记录id
    */
    @TableField(value="record_id")
    private Integer recordId;
    /**
    * 班次
    */
    @TableField(value="shift")
    private String shift;
    /**
    * 设备id
    */
    @TableField(value="equipment_id")
    private Integer equipmentId;
    /**
    * 产品id
    */
    @TableField(value="production_id")
    private Integer productionId;
    /**
    * 产量
    */
    @TableField(value="yield_count")
    private Integer yieldCount = 0;

    /**
     * 已入库数
     */
    @TableField(exist = false)
    private Integer inCount;

    /**
     * 报废数量
     */
    @TableField(exist = false)
    private Integer scrapCount;

    @TableField(exist = false)
    private String produtionName;

    @TableField(exist = false)
    private String productionCode;

    @TableField(exist = false)
    private String userCode;
    /**
     * 设备名称
     */
    @TableField(exist = false)
    private String equipmentName;

    @TableField(exist = false)
    private String uuid;

    @TableField(exist = false)
    private Integer orderNum;


}
