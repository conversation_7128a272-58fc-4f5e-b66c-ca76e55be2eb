package com.boyo.mes.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.MesProduction;
import com.boyo.mes.mapper.MesProductionMapper;
import com.boyo.mes.service.IMesProductionService;
import java.util.List;

/**
 * 生产产品表(MesProduction)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
@Service("mesProductionService")
@AllArgsConstructor
@Tenant
public class MesProductionServiceImpl extends ServiceImpl<MesProductionMapper, MesProduction> implements IMesProductionService {
    private final MesProductionMapper mesProductionMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<MesProduction> selectMesProductionList(MesProduction mesProduction) {
        return mesProductionMapper.selectMesProductionList(mesProduction);
    }

}
