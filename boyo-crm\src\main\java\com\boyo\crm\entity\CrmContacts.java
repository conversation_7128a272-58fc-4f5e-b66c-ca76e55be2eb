package com.boyo.crm.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import com.boyo.framework.annotation.PropertyMsg;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 联系人表(CrmContacts)实体类
 *
 * <AUTHOR>
 * @since 2022-03-25 17:57:52
 */
@Data
@TableName(value = "t_crm_contacts")
public class CrmContacts extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = 950270574944425043L;
            
    @TableId
    private Integer contactsId;
    
    /**
    * 联系人名称
    */
    @TableField(value="name")
    @PropertyMsg(value="联系人名称")
    private String name;
    /**
    * 下次联系时间
    */
    @TableField(value="next_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @PropertyMsg(value="下次联系时间")
    private Date nextTime;
    /**
    * 手机
    */
    @TableField(value="mobile")
    @PropertyMsg(value="手机")
    private String mobile;
    /**
    * 电话
    */
    @TableField(value="telephone")
    @PropertyMsg(value="电话")
    private String telephone;
    /**
    * 电子邮箱
    */
    @TableField(value="email")
    @PropertyMsg(value="电子邮箱")
    private String email;
    /**
    * 职务
    */
    @TableField(value="post")
    @PropertyMsg(value="职务")
    private String post;
    /**
    * 客户ID
    */
    @TableField(value="customer_id")
    private Integer customerId;
    /**
    * 地址
    */
    @TableField(value="address")
    @PropertyMsg(value="地址")
    private String address;
    /**
    * 备注
    */
    @TableField(value="remark")
    private String remark;
    /**
    * 创建人ID
    */
    @TableField(value="create_user_id")
    private Long createUserId;
    /**
    * 负责人ID
    */
    @TableField(value="owner_user_id")
    private Long ownerUserId;
    /**
    * 创建时间
    */
    @TableField(value="create_time")
    private Date createTime;
    /**
    * 更新时间
    */
    @TableField(value="update_time")
    private Date updateTime;

}
