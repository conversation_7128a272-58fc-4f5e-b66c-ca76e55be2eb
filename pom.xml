<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.boyo</groupId>
    <artifactId>boyo</artifactId>
    <version>3.3.0</version>

    <name>boyocloud-api</name>

    <properties>
        <boyo.version>3.3.0</boyo.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <druid.version>1.1.23</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <swagger.version>2.9.2</swagger.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <pagehelper.boot.version>1.3.0</pagehelper.boot.version>
        <fastjson.version>1.2.75</fastjson.version>
        <oshi.version>5.3.6</oshi.version>
        <jna.version>5.6.0</jna.version>
        <commons.io.version>2.5</commons.io.version>
        <commons.fileupload.version>1.3.3</commons.fileupload.version>
        <poi.version>4.1.2</poi.version>
        <velocity.version>1.7</velocity.version>
        <jwt.version>0.9.1</jwt.version>
        <sonar.projectKey>boyo-projects_tsingtao-mes-api_AXpf2O197xDUhRv0Rkvr</sonar.projectKey>
        <sonar.moduleKey>${artifactId}</sonar.moduleKey>
        <sonar.qualitygate.wait>true</sonar.qualitygate.wait>
        <dynamic-datasource.version>3.1.1</dynamic-datasource.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.5.2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna-platform</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <!-- swagger2-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- swagger2-UI-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <!--io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!--文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!--velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!--Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!--验证码 -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>com.boyo</groupId>
                <artifactId>boyo-quartz</artifactId>
                <version>${boyo.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.12</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.boyo</groupId>
                <artifactId>boyo-framework</artifactId>
                <version>${boyo.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.boyo</groupId>
                <artifactId>boyo-system</artifactId>
                <version>${boyo.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>com.boyo</groupId>
                <artifactId>boyo-common</artifactId>
                <version>${boyo.version}</version>
            </dependency>

            <!-- wms-->
            <dependency>
                <groupId>com.boyo</groupId>
                <artifactId>boyo-wms</artifactId>
                <version>${boyo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.boyo</groupId>
                <artifactId>boyo-mes</artifactId>
                <version>${boyo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.boyo</groupId>
                <artifactId>boyo-view</artifactId>
                <version>${boyo.version}</version>
            </dependency>


            <!-- master-->
            <dependency>
                <groupId>com.boyo</groupId>
                <artifactId>boyo-master</artifactId>
                <version>${boyo.version}</version>
            </dependency>

            <!-- iot-->
            <dependency>
                <groupId>com.boyo</groupId>
                <artifactId>boyo-iot</artifactId>
                <version>${boyo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.boyo</groupId>
                <artifactId>boyo-crm</artifactId>
                <version>${boyo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.boyo</groupId>
                <artifactId>boyo-eam</artifactId>
                <version>${boyo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.boyo</groupId>
                <artifactId>boyo-project</artifactId>
                <version>${boyo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.4.2</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>rds20140815</artifactId>
                <version>1.0.2</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>4.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.10.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>boyo-admin</module>
        <module>boyo-framework</module>
        <module>boyo-system</module>
        <module>boyo-quartz</module>
        <module>boyo-common</module>
        <module>boyo-wms</module>
        <module>boyo-view</module>
        <module>boyo-master</module>
        <module>boyo-iot</module>
        <module>boyo-crm</module>
        <module>boyo-eam</module>
        <module>boyo-project</module>
        <module>boyo-mes</module>
    </modules>
    <packaging>pom</packaging>


    <dependencies>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>



</project>
