<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.system.mapper.EnterpriseApplyMapper">

    <resultMap type="com.boyo.system.domain.EnterpriseApply" id="EnterpriseApplyResult">
        <result property="id" column="id"/>
        <result property="enterprise" column="enterprise"/>
        <result property="phone" column="phone"/>
        <result property="username" column="username"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectEnterpriseApplyVo">
        select id, enterprise, phone, username, create_time
        from t_enterprise_apply
    </sql>

    <select id="selectEnterpriseApplyList" parameterType="com.boyo.system.domain.EnterpriseApply"
            resultMap="EnterpriseApplyResult">
        <include refid="selectEnterpriseApplyVo"/>
        <where>
            <if test="enterprise != null  and enterprise != ''">
                and enterprise = #{enterprise}
            </if>
            <if test="phone != null  and phone != ''">
                and phone = #{phone}
            </if>
            <if test="username != null  and username != ''">
                and username like concat('%', #{username}, '%')
            </if>
        </where>
    </select>
</mapper>
