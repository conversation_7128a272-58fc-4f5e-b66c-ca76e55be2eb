package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.ObjectInfo;
import com.boyo.eam.mapper.ObjectInfoMapper;
import com.boyo.eam.service.IObjectInfoService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 物模型表(ObjectInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Service("objectInfoService")
@AllArgsConstructor
@Tenant
public class ObjectInfoServiceImpl extends ServiceImpl<ObjectInfoMapper, ObjectInfo> implements IObjectInfoService {
    private final ObjectInfoMapper objectInfoMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<ObjectInfo> selectObjectInfoList(ObjectInfo objectInfo) {
        return objectInfoMapper.selectObjectInfoList(objectInfo);
    }

}
