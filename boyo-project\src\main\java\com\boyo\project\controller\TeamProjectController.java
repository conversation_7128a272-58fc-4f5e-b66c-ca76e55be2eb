package com.boyo.project.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.boyo.project.entity.TeamProject;
import com.boyo.project.entity.TeamTaskStages;
import com.boyo.project.service.ITeamProjectService;
import com.boyo.project.service.ITeamTaskStagesService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.Date;
import java.util.List;
import java.util.Arrays;

/**
 * 项目表(TeamProject)表控制层
 *
 * <AUTHOR>
 * @since 2022-02-09 11:07:52
 */
@Api("项目表")
@RestController
@RequestMapping("/project/teamProject")
@AllArgsConstructor
public class TeamProjectController extends BaseController{
    /**
     * 服务对象
     */
    private final ITeamProjectService teamProjectService;

    private final ITeamTaskStagesService teamTaskStagesService;

    /**
     * 查询项目表列表
     *
     */
    @ApiOperation("查询项目表列表")
    @GetMapping("/list")
    public TableDataInfo list(TeamProject teamProject) {
        startPage();
        List<TeamProject> list = teamProjectService.selectTeamProjectList(teamProject);
        return getDataTable(list);
    }

    @ApiOperation("查询项目表列表")
    @GetMapping("/listAll")
    public TableDataInfo listAll(TeamProject teamProject) {
        startPage();
        List<TeamProject> list = teamProjectService.selectAllProjectList(teamProject);
        return getDataTable(list);
    }

    /**
     * 获取项目表详情
     */
    @ApiOperation("获取项目表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(teamProjectService.getById(id));
    }

    /**
     * 新增项目表
     */
    @ApiOperation("新增项目表")
    @PostMapping
    public AjaxResult add(@RequestBody TeamProject teamProject) {
        return toBooleanAjax(teamProjectService.save(teamProject));
    }

    /**
     * 修改项目表
     */
    @ApiOperation("修改项目表")
    @PutMapping
    public AjaxResult edit(@RequestBody TeamProject teamProject) {
        return toBooleanAjax(teamProjectService.updateById(teamProject));
    }

    /**
     * 删除项目表
     */
    @ApiOperation("删除项目表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(teamProjectService.removeByIds(Arrays.asList(ids)));
    }

    @GetMapping("/projectGantt")
    public AjaxResult projectGantt(TeamTaskStages teamTaskStages){
        List<TeamTaskStages> list = teamTaskStagesService.selectTeamTaskStagesList(teamTaskStages);
        JSONArray array = new JSONArray();
        if(list != null && list.size() > 0){
            for (int i = 0; i < list.size(); i++) {
                JSONObject object = new JSONObject();
                object.put("id",list.get(i).getId());
                object.put("label",list.get(i).getName());
                object.put("user","-");
                object.put("start",System.currentTimeMillis());
                object.put("duration",15 * 24 * 60 * 60 * 1000);
                object.put("percent",15);
                object.put("type","stage");
                array.add(object);
            }
        }
        return AjaxResult.success(array);
    }

}
