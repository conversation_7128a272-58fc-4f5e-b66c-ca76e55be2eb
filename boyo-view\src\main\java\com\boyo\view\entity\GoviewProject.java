package com.boyo.view.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目表(GoviewProject)实体类
 *
 * <AUTHOR>
 * @since 2022-12-13 15:15:05
 */
@Data
@TableName(value = "t_goview_project")
public class GoviewProject implements Serializable {
    private static final long serialVersionUID = -46617435787041474L;
        /**
    * 主键
    */    
    @TableId
    private String id;
    
    /**
    * 项目名称
    */
    @TableField(value="project_name")
    private String projectName;
    /**
    * 项目状态[-1未发布,1发布]
    */
    @TableField(value="state")
    private Integer state;
    /**
    * 创建时间
    */
    @TableField(value="create_time")
    private Date createTime;
    /**
    * 创建人id
    */
    @TableField(value="create_user_id")
    private String createUserId;
    /**
    * 删除状态[1删除,-1未删除]
    */
    @TableField(value="is_delete")
    private Integer isDelete;
    /**
    * 首页图片
    */
    @TableField(value="index_image")
    private String indexImage;
    /**
    * 项目介绍
    */
    @TableField(value="remarks")
    private String remarks;

}
