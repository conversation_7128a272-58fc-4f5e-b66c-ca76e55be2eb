package com.boyo.view.controller;

import com.boyo.view.entity.GoviewProject;
import com.boyo.view.service.IGoviewProjectService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 项目表(GoviewProject)表控制层
 *
 * <AUTHOR>
 * @since 2022-12-13 15:15:05
 */
@Api("项目表")
@RestController
@RequestMapping("/view/goviewProject")
@AllArgsConstructor
public class GoviewProjectController extends BaseController{
    /**
     * 服务对象
     */
    private final IGoviewProjectService goviewProjectService;

    /**
     * 查询项目表列表
     *
     */
    @ApiOperation("查询项目表列表")
    @GetMapping("/list")
    public TableDataInfo list(GoviewProject goviewProject) {
        startPage();
        List<GoviewProject> list = goviewProjectService.selectGoviewProjectList(goviewProject);
        return getDataTable(list);
    }
    
    /**
     * 获取项目表详情
     */
    @ApiOperation("获取项目表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(goviewProjectService.getById(id));
    }

    /**
     * 新增项目表
     */
    @ApiOperation("新增项目表")
    @PostMapping
    public AjaxResult add(@RequestBody GoviewProject goviewProject) {
        return toBooleanAjax(goviewProjectService.save(goviewProject));
    }

    /**
     * 修改项目表
     */
    @ApiOperation("修改项目表")
    @PutMapping
    public AjaxResult edit(@RequestBody GoviewProject goviewProject) {
        return toBooleanAjax(goviewProjectService.updateById(goviewProject));
    }

    /**
     * 删除项目表
     */
    @ApiOperation("删除项目表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(goviewProjectService.removeByIds(Arrays.asList(ids)));
    }

}
