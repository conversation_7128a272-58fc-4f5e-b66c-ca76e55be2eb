package com.boyo.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.iot.domain.IotTslAttr;

import java.util.List;

/**
 * IoT物模型属性Service接口
 *
 * <AUTHOR>
 */
public interface IIotTslAttrService extends IService<IotTslAttr>
{
    /**
     * 根据条件查询查询IoT物模型属性列表
     *
     * @param iotTslAttr IoT物模型属性
     * @return IoT物模型属性集合
     */
    List<IotTslAttr> selectIotTslAttrList(IotTslAttr iotTslAttr);

    /**
     * 根据物模型id返回所有属性列表
     * @param tslId 物模型id
     * @return IotTslAttr 物模型属性列表
     */
    List<IotTslAttr> listAttr(Long tslId);
}
