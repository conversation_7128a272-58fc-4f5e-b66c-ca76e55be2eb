package com.boyo.master.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 供应商管理
 * 表名 t_supplier
 *
 * <AUTHOR>
 */
@ApiModel("主数据-供应商")
@Data
@TableName("t_supplier")
public class TSupplier extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "supplier_openid")
    private String supplierOpenid;
    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;
    /**
     * 供应商简称
     */
    @ApiModelProperty("供应商简称")
    @TableField(value = "supplier_abbreviation")
    private String supplierAbbreviation;
    /**
     * 供应商编码
     */
    @ApiModelProperty("供应商编码")
    @TableField(value = "supplier_code")
    private String supplierCode;
    /**
     * 供应商类型
     */
    @ApiModelProperty("供应商类型")
    @TableField(value = "supplier_type")
    private String supplierType;
    /**
     * 供应商描述
     */
    @ApiModelProperty("供应商描述")
    @TableField(value = "supplier_desc")
    private String supplierDesc;
    /**
     * 状态 1启用 0停用
     */
    @ApiModelProperty("状态 1启用 0停用")
    @TableField(value = "supplier_status")
    private String supplierStatus;
    /**
     * 供应商图片
     */
    @ApiModelProperty("供应商图片")
    @TableField(value = "supplier_img")
    private String supplierImg;
    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    @TableField(value = "supplier_contacts")
    private String supplierContacts;
    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    @TableField(value = "supplier_phone")
    private String supplierPhone;
    /**
     * 供应商地址
     */
    @ApiModelProperty("供应商地址")
    @TableField(value = "supplier_address")
    private String supplierAddress;
    /**
     * 传真号
     */
    @ApiModelProperty("传真号")
    @TableField(value = "supplier_fax")
    private String supplierFax;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "created_at")
    private Date createdAt;
    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    @TableField(value = "created_user")
    private String createdUser;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(value = "updated_at")
    private Date updatedAt;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "updated_user")
    private String updatedUser;
}
