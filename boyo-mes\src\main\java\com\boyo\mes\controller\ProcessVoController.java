package com.boyo.mes.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.text.Convert;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.mapper.IotEquipmentMapper;
import com.boyo.mes.entity.*;
import com.boyo.mes.service.*;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/mes/processDetailVo")
@AllArgsConstructor
public class ProcessVoController extends BaseController {
    @Autowired
    private IProductOrderDetailService productOrderDetailService;
    @Autowired
    private IProductOrderService orderService;
    @Autowired
    private IProcessGroupDetailService groupDetailService;

    @Autowired
    private TFactoryOrderService factoryOrderService;
    @Autowired
    private IProductProcessService productProcessService;
    @Autowired
    private IotEquipmentMapper equipmentMapper;

    @GetMapping("/list")
    public AjaxResult getList(Integer id) {
        QueryWrapper<ProductOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        List<ProductOrder> list = orderService.list(queryWrapper);
        List<ProcessGroupDetail> groupDetailList = new ArrayList<>();
        if (list.size() > 0) {
            for (ProductOrder productOrder : list) {
                QueryWrapper<ProcessGroupDetail> groupDetailQueryWrapper = new QueryWrapper<>();
                groupDetailQueryWrapper.eq("group_id", productOrder.getProcessGroupId());
                List<ProcessGroupDetail> groupDetailListTemp = groupDetailService.list(groupDetailQueryWrapper);
                if (groupDetailListTemp.size() > 0) {
                    for (int i = 0; i < groupDetailListTemp.size(); i++) {
                        ProcessGroupDetail processGroupDetail = new ProcessGroupDetail();
                        QueryWrapper<ProductOrderDetail> productOrderDetailQueryWrapper = new QueryWrapper<>();
                        int totalNum = 0;
                        productOrderDetailQueryWrapper.eq("process_id", groupDetailListTemp.get(i).getProcessId());
                        productOrderDetailQueryWrapper.eq("order_id",productOrder.getId());
                        List<ProductOrderDetail> list1 = productOrderDetailService.list(productOrderDetailQueryWrapper);
                        for (ProductOrderDetail detail : list1) {
                            if(detail.getProductNum()!=0){
                                totalNum += detail.getProductNum();
                            }
                            QueryWrapper<IotEquipment> equipmentQueryWrapper = new QueryWrapper<>();
                            equipmentQueryWrapper.eq("id",detail.getEquipmentId());
                            List<IotEquipment> list2 = equipmentMapper.selectList(equipmentQueryWrapper);
                            if(list2.size()>0){
                                detail.setEquipmentName(list2.get(0).getEquipmentName());
                            }

                        }
                        QueryWrapper<TFactoryOrder> tFactoryOrderQueryWrapper = new QueryWrapper<>();
                        tFactoryOrderQueryWrapper.eq("id",productOrder.getTaskId());
                        List<TFactoryOrder> list2 = factoryOrderService.list(tFactoryOrderQueryWrapper);
                        if(list2.size()>0){
                            for (TFactoryOrder tFactoryOrder : list2) {
                                processGroupDetail.setOrderNum(tFactoryOrder.getOrderNum());
                            }
                        }
                        QueryWrapper<ProductProcess> processQueryWrapper = new QueryWrapper<>();
                        processQueryWrapper.eq("id",groupDetailListTemp.get(i).getProcessId());
                        List<ProductProcess> list3 = productProcessService.list(processQueryWrapper);
                        if(list3.size()>0){
                            processGroupDetail.setProcessName(list3.get(0).getProcessName());
                        }
                        processGroupDetail.setProductNum(productOrder.getProductionNum());
                        processGroupDetail.setGroupId(groupDetailListTemp.get(i).getGroupId());
                        processGroupDetail.setProductOrderDetailList(list1);
                        processGroupDetail.setTotalNumber(totalNum);
                        processGroupDetail.setProcessId(Convert.toInt(groupDetailListTemp.get(i).getProcessId()));
                        groupDetailList.add(processGroupDetail);
                    }
                }
            }
        }
        return AjaxResult.success(groupDetailList);
    }
}