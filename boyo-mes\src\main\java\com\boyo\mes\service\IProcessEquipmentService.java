package com.boyo.mes.service;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.mes.entity.ProcessEquipment;
import java.util.List;

/**
 * 工序设备关联关系(ProcessEquipment)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
public interface IProcessEquipmentService extends IService<ProcessEquipment> {

    /**
     * 查询多条数据
     *
     * @param processEquipment 对象信息
     * @return 对象列表
     */
    List<ProcessEquipment> selectProcessEquipmentList(ProcessEquipment processEquipment);

    void addProcessEquipment(JSONObject object);


}
