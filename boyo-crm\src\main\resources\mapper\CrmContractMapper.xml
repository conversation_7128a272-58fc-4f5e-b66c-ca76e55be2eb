<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmContractMapper">

    <resultMap type="com.boyo.crm.entity.CrmContract" id="CrmContractResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="customerId" column="customer_id"/>
        <result property="businessId" column="business_id"/>
        <result property="checkStatus" column="check_status"/>
        <result property="examineRecordId" column="examine_record_id"/>
        <result property="orderDate" column="order_date"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="ownerUserId" column="owner_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="num" column="num"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="money" column="money"/>
        <result property="discountRate" column="discount_rate"/>
        <result property="totalPrice" column="total_price"/>
        <result property="contractType" column="contract_type"/>
        <result property="paymentType" column="payment_type"/>
        <result property="contactsId" column="contacts_id"/>
        <result property="remark" column="remark"/>
        <result property="companyUserId" column="company_user_id"/>
        <result property="customerName" column="customer_name"/>
        <result property="businessName" column="business_name"/>
        <result property="contractTypeName" column="contract_type_name"/>
        <result property="paymentTypeName" column="payment_type_name"/>
        <result property="contactsName" column="contacts_name"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmContractList" parameterType="com.boyo.crm.entity.CrmContract" resultMap="CrmContractResult">
        select t1.*,t2.customer_name,t3.business_name as business_name from (
        select
        id, name, customer_id, business_id, check_status, examine_record_id, order_date, create_user_id, owner_user_id,
        create_time, update_time, num, start_time, end_time, money, discount_rate, total_price, contract_type,
        payment_type, contacts_id, remark, company_user_id
        from t_crm_contract
        <where>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="customerId != null">
                and customer_id = #{customerId}
            </if>
            <if test="businessId != null">
                and business_id = #{businessId}
            </if>
            <if test="checkStatus != null">
                and check_status = #{checkStatus}
            </if>
            <if test="examineRecordId != null">
                and examine_record_id = #{examineRecordId}
            </if>
            <if test="orderDate != null">
                and order_date = #{orderDate}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="ownerUserId != null">
                and owner_user_id = #{ownerUserId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="num != null and num != ''">
                and num = #{num}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime}
            </if>
            <if test="endTime != null">
                and end_time = #{endTime}
            </if>
            <if test="money != null">
                and money = #{money}
            </if>
            <if test="discountRate != null">
                and discount_rate = #{discountRate}
            </if>
            <if test="totalPrice != null">
                and total_price = #{totalPrice}
            </if>
            <if test="contractType != null and contractType != ''">
                and contract_type = #{contractType}
            </if>
            <if test="paymentType != null and paymentType != ''">
                and payment_type = #{paymentType}
            </if>
            <if test="contactsId != null">
                and contacts_id = #{contactsId}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="companyUserId != null">
                and company_user_id = #{companyUserId}
            </if>
            ${params.dataScope}
        </where>
        ) t1 left join t_crm_customer t2 on t1.customer_id = t2.id
        left join t_crm_business t3 on t1.business_id = t3.id
    </select>
    <select id="selectById" resultMap="CrmContractResult">
        select t1.*,
               t2.customer_name,
               t3.business_name as business_name,
               t4.base_desc     as contract_type_name,
               t5.base_desc     as payment_type_name,
               t6.name as contacts_name
        from (
                 select id,
                        name,
                        customer_id,
                        business_id,
                        check_status,
                        examine_record_id,
                        order_date,
                        create_user_id,
                        owner_user_id,
                        create_time,
                        update_time,
                        num,
                        start_time,
                        end_time,
                        money,
                        discount_rate,
                        total_price,
                        contract_type,
                        payment_type,
                        contacts_id,
                        remark,
                        company_user_id
                 from t_crm_contract
                 where id = #{id}
             ) t1
                left join t_crm_contacts t6 on t1.contacts_id = t6.contacts_id
                 left join t_crm_customer t2 on t1.customer_id = t2.id
                 left join t_crm_business t3 on t1.business_id = t3.id
                 left join (select * from t_base_dict where base_type = 'CONTRACT_TYPE') t4 on t1.contract_type = t4.id
                 left join (select * from t_base_dict where base_type = 'PAYMENT_TYPE') t5 on t1.payment_type = t5.id
    </select>
</mapper>

