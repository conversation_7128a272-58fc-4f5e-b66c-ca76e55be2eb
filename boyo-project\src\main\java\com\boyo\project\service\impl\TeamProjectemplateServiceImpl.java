package com.boyo.project.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.project.entity.TeamProjectemplate;
import com.boyo.project.mapper.TeamProjectemplateMapper;
import com.boyo.project.service.ITeamProjectemplateService;
import java.util.List;

/**
 * 项目类型表(TeamProjectemplate)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-08 20:50:45
 */
@Service("teamProjectemplateService")
@AllArgsConstructor
@Tenant
public class TeamProjectemplateServiceImpl extends ServiceImpl<TeamProjectemplateMapper, TeamProjectemplate> implements ITeamProjectemplateService {
    private final TeamProjectemplateMapper teamProjectemplateMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<TeamProjectemplate> selectTeamProjectemplateList(TeamProjectemplate teamProjectemplate) {
        return teamProjectemplateMapper.selectTeamProjectemplateList(teamProjectemplate);
    }

}
