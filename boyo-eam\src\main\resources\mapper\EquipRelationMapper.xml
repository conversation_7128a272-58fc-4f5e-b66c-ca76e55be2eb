<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipRelationMapper">

    <resultMap type="com.boyo.eam.domain.EquipRelation" id="EquipRelationResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="relationType" column="relation_type" />
        <result property="relation" column="relation" />
        <result property="type" column="type" />
        <result property="ledgerOpenid" column="ledger_openid" />
        <result property="formula" column="formula" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipRelationList" parameterType="com.boyo.eam.domain.EquipRelation" resultMap="EquipRelationResult">
        select
          id, openid, relation_type, relation, type, ledger_openid, formula
        from equip_relation
        <where>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
            <if test="relationType != null and relationType != ''">
                and relation_type = #{relationType}
            </if>
            <if test="relation != null and relation != ''">
                and relation = #{relation}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="ledgerOpenid != null and ledgerOpenid != ''">
                and ledger_openid = #{ledgerOpenid}
            </if>
            <if test="formula != null and formula != ''">
                and formula = #{formula}
            </if>
        </where>
    </select>

    <select id="groupByType" resultType="Map">
        SELECT
        ER.type,
        case ER.type
            when 'air' then '压缩空气'
            when 'co2' then '二氧化碳'
            when 'electricity' then '电力'
            when 'gas' then '蒸汽'
            when 'water' then '自来水'
        ELSE ER.type END as typeName
        ,
        ML.line_name as lineName,
        GROUP_CONCAT(ER.ledger_openid)  as ledgerOpenid,
        IFNULL(ER.formula,'') as formula

        FROM equip_relation ER left join t_model_line ML on ER.relation=ML.line_openid
        where ER.relation_type=2 -- 产线
        group by ER.type,ER.relation
    </select>
</mapper>

