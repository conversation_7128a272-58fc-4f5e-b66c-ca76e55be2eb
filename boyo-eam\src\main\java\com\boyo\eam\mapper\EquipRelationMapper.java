package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipRelation;

import java.util.List;
import java.util.Map;

/**
 * 车间、产线、工段关联表(EquipRelation)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-21 15:23:24
 */
public interface EquipRelationMapper extends BaseMapper<EquipRelation>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipRelation 实例对象
     * @return 对象列表
     */
    List<EquipRelation> selectEquipRelationList(EquipRelation equipRelation);

    /**
     * 通过类型分组，合并设备字段
     *
     * @return
     */
    List<Map<String,Object>> groupByType();
}

