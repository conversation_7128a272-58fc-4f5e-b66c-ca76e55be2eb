package com.boyo.wms.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * (WmsFlowqcIndex)实体类
 *
 * <AUTHOR>
 * @since 2022-03-10 15:12:17
 */
@Data
@TableName(value = "t_wms_flowqc_index")
public class WmsFlowqcIndex implements Serializable {
    private static final long serialVersionUID = -16883858973419099L;
        /**
    * 主键
    */    
    @TableId
    private Integer id;
    
    /**
    * 出入库流水id
    */
    @TableField(value="flow_id")
    private Long flowId;
    /**
    * 质检模板名称
    */
    @TableField(value="qc_name")
    private String qcName;
    /**
    * 质检时间
    */
    @TableField(value="create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 质检人
    */
    @TableField(value="create_user")
    private String createUser;

    @TableField(exist = false)
    private String userName;

}
