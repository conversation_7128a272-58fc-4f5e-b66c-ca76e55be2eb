<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipMaintFixMapper">

    <resultMap type="com.boyo.eam.domain.EquipMaintFix" id="EquipMaintFixResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="fixCode" column="fix_code" />
        <result property="equipLedgerOpenid" column="equip_ledger_openid" />
        <result property="remark" column="remark" />
        <result property="location" column="location" />
        <result property="phone" column="phone" />
        <result property="sysUserId" column="sys_user_id" />
        <result property="reportTime" column="report_time" />
        <result property="state" column="state" />
        <result property="taskCode" column="task_code" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipMaintFixList" parameterType="com.boyo.eam.domain.EquipMaintFix" resultMap="EquipMaintFixResult">
        select
        EMF.*,EL.name as equipName,EL.code as equipCode
        from equip_maint_fix EMF left join equip_ledger EL on EMF.equip_ledger_openid=EL.openid
        <where>
            <if test="openid != null and openid != ''">
                and EMF.openid = #{openid}
            </if>
            <if test="fixCode != null and fixCode != ''">
                and EMF.fix_code = #{fixCode}
            </if>
            <if test="equipLedgerOpenid != null and equipLedgerOpenid != ''">
                and EMF.equip_ledger_openid = #{equipLedgerOpenid}
            </if>
            <if test="remark != null and remark != ''">
                and EMF.remark = #{remark}
            </if>
            <if test="location != null and location != ''">
                and EMF.location = #{location}
            </if>
            <if test="phone != null and phone != ''">
                and EMF.phone = #{phone}
            </if>
            <if test="sysUserId != null">
                and EMF.sys_user_id = #{sysUserId}
            </if>
            <if test="reportTime != null">
                and EMF.report_time = #{reportTime}
            </if>
            <if test="state != null and state != ''">
                and EMF.state = #{state}
            </if>
            <if test="taskCode != null and taskCode != ''">
                and EMF.task_code = #{taskCode}
            </if>
            <if test="createBy != null and createBy != ''">
                and EMF.create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and EMF.create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and EMF.update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and EMF.update_time = #{updateTime}
            </if>
            <if test="beginDate!=null and endDate!=null">
                and #{beginDate} &lt;= EMF.report_time
                and EMF.report_time &lt;= #{endDate}
            </if>
            <if test="equipName!=null and equipName!=null">
                and EL.name like CONCAT('%',#{equipName},'%')
            </if>
        </where>
    </select>
</mapper>

