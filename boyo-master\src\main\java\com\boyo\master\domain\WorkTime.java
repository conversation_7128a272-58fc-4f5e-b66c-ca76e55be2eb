package com.boyo.master.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Time;

@Data
@TableName("work_time")
public class WorkTime {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 排班名称
     */
    @TableField("shift_name")
    private String shiftName;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private String startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private String endTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @TableField("base_code")
    private String baseCode;

    /**
     * 休息时长，单位分钟
     */
    @TableField("rest_time")
    private Integer restTime;

}
