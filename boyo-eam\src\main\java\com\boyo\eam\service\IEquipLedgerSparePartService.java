package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipLedger;
import com.boyo.eam.domain.EquipLedgerSparePart;

import java.util.List;

/**
 * 台账和部件关联表(EquipLedgerSparePart)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-04 16:59:33
 */
public interface IEquipLedgerSparePartService extends IService<EquipLedgerSparePart> {

    /**
     * 查询多条数据
     *
     * @param equipLedgerSparePart 对象信息
     * @return 对象列表
     */
    List<EquipLedgerSparePart> selectEquipLedgerSparePartList(EquipLedgerSparePart equipLedgerSparePart);

    void removeEquipLedgerSparePart(EquipLedger equipLedger);


}
