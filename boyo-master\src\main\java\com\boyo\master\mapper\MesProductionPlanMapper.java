package com.boyo.master.mapper;

import com.boyo.master.domain.MesProductionPlan;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 生产计划Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
public interface MesProductionPlanMapper {

    /**
     * 查询生产计划列表
     *
     * @param mesProductionPlan 查询条件
     * @return 生产计划集合
     */
    @Select("SELECT * FROM mes_production_plan WHERE 1=1")
    List<MesProductionPlan> selectMesProductionPlanList(MesProductionPlan mesProductionPlan);

    /**
     * 根据ID查询生产计划
     *
     * @param id 主键ID
     * @return 生产计划对象
     */
    @Select("SELECT * FROM mes_production_plan WHERE id = #{id}")
    MesProductionPlan selectMesProductionPlanById(String id);

    /**
     * 新增生产计划
     *
     * @param mesProductionPlan 生产计划对象
     * @return 影响行数
     */
    @Insert("INSERT INTO mes_production_plan (" +
            "id,plan_code, project_number, project_name, quantity, split_quantity, not_split_quantity, " +
            "pre_opening_days, pre_completion_days, agent_type, status, notice_no, dispatch_quantity, " +
            "work_reports_quantity, work_not_reports_quantity, qualified_quantity, product_code, " +
            "product_name, create_by, create_time, update_by, update_time) " +
            "VALUES (" +
            "#{id},#{planCode}, #{projectNumber}, #{projectName}, #{quantity}, #{splitQuantity}, #{notSplitQuantity}, " +
            "#{preOpeningDays}, #{preCompletionDays}, #{agentType}, #{status}, #{noticeNo}, #{dispatchQuantity}, " +
            "#{workReportsQuantity}, #{workNotReportsQuantity}, #{qualifiedQuantity}, #{productCode}, " +
            "#{productName}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime})")
    int insertMesProductionPlan(MesProductionPlan mesProductionPlan);

    /**
     * 修改生产计划
     *
     * @param mesProductionPlan 生产计划对象
     * @return 影响行数
     */
    @Update("UPDATE mes_production_plan SET " +
            "plan_code = #{planCode}, project_number = #{projectNumber}, project_name = #{projectName}, " +
            "quantity = #{quantity}, split_quantity = #{splitQuantity}, not_split_quantity = #{notSplitQuantity}, " +
            "pre_opening_days = #{preOpeningDays}, pre_completion_days = #{preCompletionDays}, " +
            "agent_type = #{agentType}, status = #{status}, notice_no = #{noticeNo}, " +
            "dispatch_quantity = #{dispatchQuantity}, work_reports_quantity = #{workReportsQuantity}, " +
            "work_not_reports_quantity = #{workNotReportsQuantity}, qualified_quantity = #{qualifiedQuantity}, " +
            "product_code = #{productCode}, product_name = #{productName}, " +
            "update_by = #{updateBy}, update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int updateMesProductionPlan(MesProductionPlan mesProductionPlan);

    /**
     * 删除生产计划
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Delete("DELETE FROM mes_production_plan WHERE id = #{id}")
    int deleteMesProductionPlanById(Long id);
}