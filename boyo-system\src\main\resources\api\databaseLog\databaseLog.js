import request from '@/utils/request'

const prefix = '/${module}'

// 查询企业租户数据库变更(DatabaseLog)列表
export function listDatabaseLog(query) {
  return request({
    url: prefix + '/databaseLog/list',
    method: 'get',
    params: query,
  })
}

// 查询企业租户数据库变更(DatabaseLog)详细
export function getDatabaseLog(id) {
  return request({
    url: prefix + '/databaseLog/' + id,
    method: 'get',
  })
}

// 新增企业租户数据库变更(DatabaseLog)
export function addDatabaseLog(data) {
  return request({
    url: prefix + '/databaseLog',
    method: 'post',
    data: data,
  })
}

// 修改企业租户数据库变更(DatabaseLog)
export function updateDatabaseLog(data) {
  return request({
    url: prefix + '/databaseLog',
    method: 'put',
    data: data,
  })
}

// 删除企业租户数据库变更(DatabaseLog)
export function delDatabaseLog(id) {
  return request({
    url: prefix + '/databaseLog/' + id,
    method: 'delete',
  })
}
