package com.boyo.crm.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.entity.CrmContractProduct;
import com.boyo.crm.mapper.CrmContractProductMapper;
import com.boyo.crm.service.ICrmContractProductService;
import java.util.List;

/**
 * 合同产品关系表(CrmContractProduct)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-27 17:04:54
 */
@Service("crmContractProductService")
@AllArgsConstructor
public class CrmContractProductServiceImpl extends ServiceImpl<CrmContractProductMapper, CrmContractProduct> implements ICrmContractProductService {
    private final CrmContractProductMapper crmContractProductMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<CrmContractProduct> selectCrmContractProductList(CrmContractProduct crmContractProduct) {
        return crmContractProductMapper.selectCrmContractProductList(crmContractProduct);
    }

}
