package com.boyo.crm.controller;

import com.boyo.crm.entity.CrmInvoice;
import com.boyo.crm.service.ICrmInvoiceService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 发票表(CrmInvoice)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-28 13:51:42
 */
@Api("发票表")
@RestController
@RequestMapping("/crm/crmInvoice")
@AllArgsConstructor
public class CrmInvoiceController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmInvoiceService crmInvoiceService;

    /**
     * 查询发票表列表
     *
     */
    @ApiOperation("查询发票表列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmInvoice crmInvoice) {
        startPage();
        List<CrmInvoice> list = crmInvoiceService.selectCrmInvoiceList(crmInvoice);
        return getDataTable(list);
    }
    
    /**
     * 获取发票表详情
     */
    @ApiOperation("获取发票表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(crmInvoiceService.getById(id));
    }

    /**
     * 新增发票表
     */
    @ApiOperation("新增发票表")
    @PostMapping
    public AjaxResult add(@RequestBody CrmInvoice crmInvoice) {
        return toBooleanAjax(crmInvoiceService.save(crmInvoice));
    }

    /**
     * 修改发票表
     */
    @ApiOperation("修改发票表")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmInvoice crmInvoice) {
        return toBooleanAjax(crmInvoiceService.updateById(crmInvoice));
    }

    /**
     * 删除发票表
     */
    @ApiOperation("删除发票表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmInvoiceService.removeByIds(Arrays.asList(ids)));
    }

}
