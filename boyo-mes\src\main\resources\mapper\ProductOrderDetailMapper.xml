<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.ProductOrderDetailMapper">

    <resultMap type="com.boyo.mes.entity.ProductOrderDetail" id="ProductOrderDetailResult">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="processId" column="process_id"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>

        <result property="equipmentName" column="equipment_name"/>
        <result property="processName" column="process_name"/>

        <result property="taskId" column="task_id"/>
        <result property="taskName" column="task_name"/>
        <result property="taskNum" column="task_num"/>
        <result property="productNum" column="product_num"/>
        <result property="status" column="status"/>
        <result property="orderNum" column="order_num"/>
        <result property="productionName" column="production_name"/>
        <result property="customerName" column="customer_name"/>
        <result property="stopTime" column="stop_time"/>
        <result property="stopNumber" column="stop_number"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectProductOrderDetailList" parameterType="com.boyo.mes.entity.ProductOrderDetail"
            resultMap="ProductOrderDetailResult">
        select tt.* from (select t1.*,t2.equipment_name,t3.process_name,t4.production_name,t4.order_num,t4.task_name,t4.task_num from (select
        *
        from t_product_order_detail
        <where>
            <if test="orderId != null">
                and order_id = #{orderId}
            </if>
            <if test="processId != null">
                and process_id = #{processId}
            </if>
            <if test="equipmentId != null">
                and equipment_id = #{equipmentId}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime}
            </if>
            <if test="taskId != null and taskId !=0">
                and task_id = #{taskId}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        ) t1 left join iot_equipment t2 on t1.equipment_id = t2.id
        left join t_product_process t3 on t1.process_id = t3.id
        left join (select a.id,a.order_num,b.materiel_name as production_name ,a.task_name ,a.task_num from t_product_order a,t_material b where
        a.production_id = b.id) t4 on t4.id = t1.order_id
        )tt
        <where>
            <if test="orderNum != null">
                and tt.order_num like CONCAT('%',#{orderNum},'%')
            </if>
            <if test="productionName != null">
                and tt.production_name like CONCAT('%',#{productionName},'%')
            </if>
            <if test="processName != null">
                and tt.process_name like CONCAT('%',#{processName},'%')
            </if>
            <if test="equipmentName != null">
                and tt.equipment_name like CONCAT('%',#{equipmentName},'%')
            </if>
            <if test="taskNum != null">
                and tt.task_num like CONCAT('%',#{taskNum},'%')
            </if>
            <if test="taskName != null">
                and tt.task_name like CONCAT('%',#{taskName},'%')
            </if>
        </where>
        order by tt.id desc
    </select>

    <select id="listCurrentOrder" resultMap="ProductOrderDetailResult">
        select t1.*,
               t2.equipment_name,
               t3.process_name,
               t4.order_num,
               t5.materiel_name  as production_name,
               t5.materiel_code  as production_code,
               t5.materiel_norms as production_norms,
               t6.supplier_name  as customer_name
        from (select *
              from t_product_order_detail
              where user_id = #{userId}
                and end_time is null ) t1
                 left join iot_equipment t2 on t1.equipment_id = t2.id
                 left join t_product_process t3 on t1.process_id = t3.id
                 left join t_product_order t4 on t1.order_id = t4.id
                 left join t_material t5 on t4.production_Id = t5.id
                 left join t_supplier t6 on t4.customer_Id = t6.id
    </select>
</mapper>

