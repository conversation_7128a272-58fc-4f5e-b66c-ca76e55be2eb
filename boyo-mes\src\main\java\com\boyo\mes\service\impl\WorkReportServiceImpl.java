package com.boyo.mes.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.WorkReport;
import com.boyo.mes.mapper.WorkReportMapper;
import com.boyo.mes.service.IWorkReportService;

import java.util.ArrayList;
import java.util.List;

/**
 * 报工记录(WorkReport)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Service("workReportService")
@AllArgsConstructor
@Tenant
public class WorkReportServiceImpl extends ServiceImpl<WorkReportMapper, WorkReport> implements IWorkReportService {
    private final WorkReportMapper workReportMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<WorkReport> selectWorkReportList(WorkReport workReport) {
        return workReportMapper.selectWorkReportList(workReport);
    }

    @Override
    public List<WorkReport> getOrderReportDetail(Integer orderId) {
        List<WorkReport> list = workReportMapper.getOrderReportSum(orderId);
        WorkReport workReport = new WorkReport();
        workReport.setOrderId(orderId);
        List<WorkReport> details = workReportMapper.selectWorkReportList(workReport);
        if(list != null && list.size() > 0 && details != null && details.size() > 0){
            for (int i = 0;i<list.size();i++) {
                WorkReport temp = list.get(i);
                for (WorkReport detail:details) {
                    if(temp.getProcessId().equals(detail.getProcessId())){
                        List<WorkReport> reportList = temp.getReportList();
                        if(ObjectUtil.isNull(reportList)){
                            reportList = new ArrayList<>();
                        }
                        reportList.add(detail);
                        temp.setReportList(reportList);
                    }
                }
                list.set(i,temp);
            }
        }
        return list;
    }

}
