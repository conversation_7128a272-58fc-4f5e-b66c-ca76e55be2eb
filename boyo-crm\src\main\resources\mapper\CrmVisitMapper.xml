<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmVisitMapper">

    <resultMap type="com.boyo.crm.entity.CrmVisit" id="CrmVisitResult">
        <result property="id" column="id"/>
        <result property="visitNumber" column="visit_number"/>
        <result property="visitTime" column="visit_time"/>
        <result property="visitType" column="visit_type"/>
        <result property="satisfaction" column="satisfaction"/>
        <result property="opinion" column="opinion"/>
        <result property="ownerUserId" column="owner_user_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="contractId" column="contract_id"/>
        <result property="contactsId" column="contacts_id"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>

        <result property="customerName" column="customer_name"></result>
        <result property="contractCode" column="contract_code"></result>
        <result property="visitTypeName" column="visit_type_name"></result>
        <result property="satisfactionName" column="satisfaction_name"></result>
        <result property="contactsName" column="contacts_name"></result>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmVisitList" parameterType="com.boyo.crm.entity.CrmVisit" resultMap="CrmVisitResult">
        select t1.*,t2.customer_name,t3.num as contract_code,t4.base_desc as visit_type_name,t5.base_desc as
        satisfaction_name, t6.name as contacts_name
        from (select
        id, visit_number, visit_time, visit_type, satisfaction, opinion, owner_user_id, customer_id, contract_id,
        contacts_id, create_user_id, create_time, update_time
        from t_crm_visit
        <where>
            <if test="visitNumber != null and visitNumber != ''">
                and visit_number = #{visitNumber}
            </if>
            <if test="visitTime != null">
                and visit_time = #{visitTime}
            </if>
            <if test="visitType != null">
                and visit_type = #{visitType}
            </if>
            <if test="satisfaction != null">
                and satisfaction = #{satisfaction}
            </if>
            <if test="opinion != null and opinion != ''">
                and opinion = #{opinion}
            </if>
            <if test="ownerUserId != null">
                and owner_user_id = #{ownerUserId}
            </if>
            <if test="customerId != null">
                and customer_id = #{customerId}
            </if>
            <if test="contractId != null">
                and contract_id = #{contractId}
            </if>
            <if test="contactsId != null">
                and contacts_id = #{contactsId}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">and update_time = #{updateTime}
            </if>
            ${params.dataScope}
        </where>
        ) t1 left join t_crm_customer t2 on t1.customer_id = t2.id
        left join t_crm_contract t3 on t1.contract_id = t3.id
        left join (select * from t_base_dict where base_type = 'VISIT_TYPE') t4 on t1.visit_type = t4.id
        left join (select * from t_base_dict where base_type = 'CRM_SATISFACTION') t5 on t1.satisfaction = t5.id
        left join t_crm_contacts t6 on t1.contacts_id = t6.contacts_id
    </select>
    <select id="selectById" resultMap="CrmVisitResult">
        select t1.*,
               t2.customer_name,
               t3.num       as contract_code,
               t4.base_desc as visit_type_name,
               t5.base_desc as
                               satisfaction_name,
               t6.name      as contacts_name
        from (select id,
                     visit_number,
                     visit_time,
                     visit_type,
                     satisfaction,
                     opinion,
                     owner_user_id,
                     customer_id,
                     contract_id,
                     contacts_id,
                     create_user_id,
                     create_time,
                     update_time
              from t_crm_visit
              where id = #{id}
             ) t1
                 left join t_crm_customer t2 on t1.customer_id = t2.id
                 left join t_crm_contract t3 on t1.contract_id = t3.id
                 left join (select * from t_base_dict where base_type = 'VISIT_TYPE') t4 on t1.visit_type = t4.id
                 left join (select * from t_base_dict where base_type = 'CRM_SATISFACTION') t5
                           on t1.satisfaction = t5.id
                 left join t_crm_contacts t6 on t1.contacts_id = t6.contacts_id

    </select>
</mapper>

