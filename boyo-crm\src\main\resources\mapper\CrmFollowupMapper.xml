<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmFollowupMapper">

    <resultMap type="com.boyo.crm.entity.CrmFollowup" id="CrmFollowupResult">
        <result property="id" column="id"/>
        <result property="followupType" column="followup_type"/>
        <result property="followupMsg" column="followup_msg"/>
        <result property="nextTime" column="next_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="actionType" column="action_type"/>
        <result property="actionId" column="action_id"/>
        <result property="followupTypeName" column="followup_type_name"></result>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmFollowupList" parameterType="com.boyo.crm.entity.CrmFollowup" resultMap="CrmFollowupResult">
        select t1.*,t2.base_desc as followup_type_name from (select
        id, followup_type, followup_msg, next_time, create_time, create_user_id, action_type, action_id
        from t_crm_followup
        <where>
            <if test="followupType != null">
                and followup_type = #{followupType}
            </if>
            <if test="followupMsg != null and followupMsg != ''">
                and followup_msg = #{followupMsg}
            </if>
            <if test="nextTime != null">
                and next_time = #{nextTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="actionType != null and actionType != ''">
                and action_type = #{actionType}
            </if>
            <if test="actionId != null">and action_id = #{actionId}
            </if>
            ${params.dataScope}

        </where>
        ) t1 left join (select * from t_base_dict where base_type = 'FOLLOW_UP') t2 on t1.followup_type = t2.id
        order by t1.id desc
    </select>
</mapper>

