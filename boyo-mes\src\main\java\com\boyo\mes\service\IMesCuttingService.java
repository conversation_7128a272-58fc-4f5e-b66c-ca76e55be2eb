package com.boyo.mes.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.mes.entity.MesCutting;
import java.util.List;

/**
 * (MesCutting)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-10 15:46:44
 */
public interface IMesCuttingService extends IService<MesCutting> {

    /**
     * 查询多条数据
     *
     * @param mesCutting 对象信息
     * @return 对象列表
     */
    List<MesCutting> selectMesCuttingList(MesCutting mesCutting);


}
