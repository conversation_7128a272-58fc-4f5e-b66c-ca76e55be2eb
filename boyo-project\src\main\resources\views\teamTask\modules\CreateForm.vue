<template>
  <a-modal width="30%" :maskClosable="false" :visible="open" @cancel="cancel">
    <template #title>
      <a-icon type="security-scan" />
      {{ formTitle }}
    </template>
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      layout="horizontal"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
           <a-form-model-item label="编号">
        <a-input
          :size="formSize"
          v-model="form.code"
          :placeholder="$t('app.global.please.input') + '编号'"
        />
      </a-form-model-item>
           <a-form-model-item label="">
        <a-input
          :size="formSize"
          v-model="form.name"
          :placeholder="$t('app.global.please.input') + ''"
        />
      </a-form-model-item>
           <a-form-model-item label="紧急程度">
        <a-input
          :size="formSize"
          v-model="form.pri"
          :placeholder="$t('app.global.please.input') + '紧急程度'"
        />
      </a-form-model-item>
           <a-form-model-item label="执行状态">
        <a-input
          :size="formSize"
          v-model="form.executeStatus"
          :placeholder="$t('app.global.please.input') + '执行状态'"
        />
      </a-form-model-item>
           <a-form-model-item label="详情">
        <a-input
          :size="formSize"
          v-model="form.description"
          :placeholder="$t('app.global.please.input') + '详情'"
        />
      </a-form-model-item>
           <a-form-model-item label="创建人">
        <a-input
          :size="formSize"
          v-model="form.createBy"
          :placeholder="$t('app.global.please.input') + '创建人'"
        />
      </a-form-model-item>
           <a-form-model-item label="创建日期">
        <a-input
          :size="formSize"
          v-model="form.createTime"
          :placeholder="$t('app.global.please.input') + '创建日期'"
        />
      </a-form-model-item>
           <a-form-model-item label="指派给谁">
        <a-input
          :size="formSize"
          v-model="form.assignTo"
          :placeholder="$t('app.global.please.input') + '指派给谁'"
        />
      </a-form-model-item>
           <a-form-model-item label="回收站">
        <a-input
          :size="formSize"
          v-model="form.deleted"
          :placeholder="$t('app.global.please.input') + '回收站'"
        />
      </a-form-model-item>
           <a-form-model-item label="任务列表">
        <a-input
          :size="formSize"
          v-model="form.stageCode"
          :placeholder="$t('app.global.please.input') + '任务列表'"
        />
      </a-form-model-item>
           <a-form-model-item label="任务标签">
        <a-input
          :size="formSize"
          v-model="form.taskTag"
          :placeholder="$t('app.global.please.input') + '任务标签'"
        />
      </a-form-model-item>
           <a-form-model-item label="是否完成">
        <a-input
          :size="formSize"
          v-model="form.done"
          :placeholder="$t('app.global.please.input') + '是否完成'"
        />
      </a-form-model-item>
           <a-form-model-item label="开始时间">
        <a-input
          :size="formSize"
          v-model="form.beginTime"
          :placeholder="$t('app.global.please.input') + '开始时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="截止时间">
        <a-input
          :size="formSize"
          v-model="form.endTime"
          :placeholder="$t('app.global.please.input') + '截止时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="提醒时间">
        <a-input
          :size="formSize"
          v-model="form.remindTime"
          :placeholder="$t('app.global.please.input') + '提醒时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="父任务id">
        <a-input
          :size="formSize"
          v-model="form.pcode"
          :placeholder="$t('app.global.please.input') + '父任务id'"
        />
      </a-form-model-item>
           <a-form-model-item label="排序">
        <a-input
          :size="formSize"
          v-model="form.sort"
          :placeholder="$t('app.global.please.input') + '排序'"
        />
      </a-form-model-item>
           <a-form-model-item label="点赞数">
        <a-input
          :size="formSize"
          v-model="form.like"
          :placeholder="$t('app.global.please.input') + '点赞数'"
        />
      </a-form-model-item>
           <a-form-model-item label="收藏数">
        <a-input
          :size="formSize"
          v-model="form.star"
          :placeholder="$t('app.global.please.input') + '收藏数'"
        />
      </a-form-model-item>
           <a-form-model-item label="删除时间">
        <a-input
          :size="formSize"
          v-model="form.deletedTime"
          :placeholder="$t('app.global.please.input') + '删除时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="是否隐私模式">
        <a-input
          :size="formSize"
          v-model="form.private"
          :placeholder="$t('app.global.please.input') + '是否隐私模式'"
        />
      </a-form-model-item>
           <a-form-model-item label="任务id编号">
        <a-input
          :size="formSize"
          v-model="form.idNum"
          :placeholder="$t('app.global.please.input') + '任务id编号'"
        />
      </a-form-model-item>
           <a-form-model-item label="上级任务路径">
        <a-input
          :size="formSize"
          v-model="form.path"
          :placeholder="$t('app.global.please.input') + '上级任务路径'"
        />
      </a-form-model-item>
           <a-form-model-item label="进度百分比">
        <a-input
          :size="formSize"
          v-model="form.schedule"
          :placeholder="$t('app.global.please.input') + '进度百分比'"
        />
      </a-form-model-item>
           <a-form-model-item label="版本id">
        <a-input
          :size="formSize"
          v-model="form.versionCode"
          :placeholder="$t('app.global.please.input') + '版本id'"
        />
      </a-form-model-item>
           <a-form-model-item label="版本库id">
        <a-input
          :size="formSize"
          v-model="form.featuresCode"
          :placeholder="$t('app.global.please.input') + '版本库id'"
        />
      </a-form-model-item>
           <a-form-model-item label="预估工时">
        <a-input
          :size="formSize"
          v-model="form.workTime"
          :placeholder="$t('app.global.please.input') + '预估工时'"
        />
      </a-form-model-item>
           <a-form-model-item label="'执行状态。0：未开始，1：已完成，2：进行中，3：挂起，4：测试中'">
        <a-input
          :size="formSize"
          v-model="form.status"
          :placeholder="$t('app.global.please.input') + ''执行状态。0：未开始，1：已完成，2：进行中，3：挂起，4：测试中''"
        />
      </a-form-model-item>
           <a-form-model-item label="">
        <a-input
          :size="formSize"
          v-model="form.liked"
          :placeholder="$t('app.global.please.input') + ''"
        />
      </a-form-model-item>
        </a-form-model>
    <template #footer>
      <a-space>
        <a-button :size="formSize" icon="close" type="danger" @click="cancel">
          {{ $t('app.global.close') }}
        </a-button>
        <a-button :size="formSize" icon="save" type="primary" @click="submitForm">
          {{ $t('app.global.save') }}
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script>
import { addTeamTask, updateTeamTask, getTeamTask } from '@/api/teamTask'
export default {
  data() {
    return {
      //新增或修改
      updateState: false,
      formTitle: '任务表',
      // 表单参数
      form: {
           id: '',
           code: '',
           projectCode: '',
           name: '',
           pri: '',
           executeStatus: '',
           description: '',
           createBy: '',
           createTime: '',
           assignTo: '',
           deleted: '',
           stageCode: '',
           taskTag: '',
           done: '',
           beginTime: '',
           endTime: '',
           remindTime: '',
           pcode: '',
           sort: '',
           like: '',
           star: '',
           deletedTime: '',
           private: '',
           idNum: '',
           path: '',
           schedule: '',
           versionCode: '',
           featuresCode: '',
           workTime: '',
           status: '',
           liked: '',
             },
      open: false,
      rules: {},
    }
  },
  created() {
    this.rules = {
    }
  },
  methods: {
    /**
     * 新增按钮操作
     * */
    handleAdd() {
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.add') + '任务表'
      this.form = {}
      this.updateState = false
    },
    /**
     * 修改按钮操作
     * */
    async handleUpdate($event, id) {
      $event.stopPropagation()
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.edit') + '任务表'
      const response = await getTeamTask(id)
      this.form = response.data
    },
    /**
     * 提交按钮
     * */
    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
            if (this.form.id) {
                await updateTeamTask(this.form)
                this.$alert.success(this.$t('app.global.edit.success'))
                this.open = false
                this.$emit('ok')
            } else {
                await addTeamTask(this.form)
                this.$alert.success(this.$t('app.global.add.success'))
                this.open = false
                this.$emit('ok')
            }
        } else {
            return false
        }
        })
    },
    /**
     * 取消按钮
     * */
    cancel() {
      this.open = false
      this.reset()
    },
    /**
     * 表单重置
     * */
    reset() {
      this.form = {}
      if (this.$refs.form) {
        this.$refs.form.resetFields()}
      }
    },
}
</script>

