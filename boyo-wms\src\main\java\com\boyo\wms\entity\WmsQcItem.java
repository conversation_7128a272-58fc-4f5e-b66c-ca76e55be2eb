package com.boyo.wms.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Wms质检项(WmsQcItem)实体类
 *
 * <AUTHOR>
 * @since 2022-03-08 15:20:54
 */
@Data
@TableName(value = "t_wms_qc_item")
public class WmsQcItem implements Serializable {
    private static final long serialVersionUID = -90423367999181178L;
        /**
    * 主键
    */    
    @TableId
    private Integer id;
    
    /**
    * 质检模板id
    */
    @TableField(value="template_id")
    private Integer templateId;
    /**
    * 质检项
    */
    @TableField(value="item_name")
    private String itemName;
    /**
    * 质检项编码
    */
    @TableField(value="item_code")
    private String itemCode;
    /**
    * 质检方式
    */
    @TableField(value="item_type")
    private String itemType;
    /**
    * 输出结果，0 文本 1：单选 2：多选
    */
    @TableField(value="item_result")
    private String itemResult;
    /**
    * 选项，多个选项之间逗号分割
    */
    @TableField(value="item_str")
    private String itemStr;

    @TableField(value = "item_order")
    private Integer itemOrder;

    @TableField(exist = false)
    private String result;

}
