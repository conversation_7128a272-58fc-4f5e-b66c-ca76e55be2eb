package com.boyo.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.mes.entity.WorkReport;
import java.util.List;

/**
 * 报工记录(WorkReport)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
public interface WorkReportMapper extends BaseMapper<WorkReport>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param workReport 实例对象
     * @return 对象列表
     */
    List<WorkReport> selectWorkReportList(WorkReport workReport);
    List<WorkReport> getOrderReportSum(Integer orderId);


}

