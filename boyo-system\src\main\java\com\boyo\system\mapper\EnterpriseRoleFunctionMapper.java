package com.boyo.system.mapper;

import java.util.List;

import com.boyo.system.domain.EnterpriseRoleFunction;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;


/**
 * 企业角色权限管理Mapper接口
 *
 * <AUTHOR>
 */
public interface EnterpriseRoleFunctionMapper extends BaseMapper<EnterpriseRoleFunction> {

    /**
     * 查询企业角色权限管理列表
     *
     * @param enterpriseRoleFunction 企业角色权限管理
     * @return EnterpriseRoleFunction集合
     */
    List<EnterpriseRoleFunction> selectEnterpriseRoleFunctionList(EnterpriseRoleFunction enterpriseRoleFunction);

}
