package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备-维保模板-维保明细(EquipMaintTemplItemDetail)实体类
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:31
 */
@Data
@TableName(value = "equip_maint_templ_item_detail")
public class EquipMaintTemplItemDetail implements Serializable {
    private static final long serialVersionUID = -98799069073676635L;
        /**
    * 主键
    */
    @TableId
    private Integer id;


    @TableField(value="openid")
    private String openid;
    /**
    * 关联equip_maint_templ的openid
    */
    @TableField(value="equip_maint_templ_item_openid")
    private String equipMaintTemplItemOpenid;
    /**
    * 模板名称
    */
    @TableField(value="detail")
    private String detail;
    /**
    * 是否是数值：1是，0否
    */
    @TableField(value="value")
    private String value;
    /**
    * 模板类型
    */
    @TableField(value="type")
    private String type;
    /**
    * 最大值
    */
    @TableField(value="max_num")
    private Double maxNum;
    /**
    * 最小值
    */
    @TableField(value="min_num")
    private Double minNum;
    /**
    * 单位
    */
    @TableField(value="unit")
    private String unit;

    /**
     * 检测标准
     */
    @TableField(value="standard")
    private String standard;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

}
