package com.boyo.crm.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.boyo.common.annotation.DataScope;
import com.boyo.common.core.text.Convert;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.crm.util.ActionEnum;
import com.boyo.crm.util.ActionUtil;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.entity.CrmVisit;
import com.boyo.crm.mapper.CrmVisitMapper;
import com.boyo.crm.service.ICrmVisitService;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 回访表(CrmVisit)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-28 17:06:59
 */
@Service("crmVisitService")
@AllArgsConstructor
public class CrmVisitServiceImpl extends ServiceImpl<CrmVisitMapper, CrmVisit> implements ICrmVisitService {
    private final CrmVisitMapper crmVisitMapper;
    private final ActionUtil actionUtil;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    @DataScope(columns = "owner_user_id,create_user_id")
    public List<CrmVisit> selectCrmVisitList(CrmVisit crmVisit) {
        return crmVisitMapper.selectCrmVisitList(crmVisit);
    }

    @Override
    public boolean save(CrmVisit entity) {
        if(ObjectUtil.isNull(entity.getOwnerUserId())){
            entity.setOwnerUserId(SecurityUtils.getUserId());
        }
        super.save(entity);
        if(StrUtil.isEmpty(entity.getVisitNumber())){
            entity.setVisitNumber("HF-" + DateUtil.format(new Date(),"yyyyMMdd") + "-" + StrUtil.fillBefore(Convert.toStr(entity.getId()),'0',6));
        }
        super.updateById(entity);
        actionUtil.editRecord(null,null,ActionEnum.VISIT,entity.getId(), null);
        return true;
    }

    @Override
    public CrmVisit getById(Serializable id) {
        return super.getById(id);
    }

    @Override
    public boolean updateById(CrmVisit entity) {
        actionUtil.editRecord(super.getById(entity.getId()),entity,ActionEnum.VISIT,entity.getId(),CrmVisit.class);
        return super.updateById(entity);
    }
}
