package com.boyo.mes.controller;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.boyo.mes.entity.MesYield;
import com.boyo.mes.service.IMesYieldService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

/**
 * 产量详情(MesYield)表控制层
 *
 * <AUTHOR>
 * @since 2023-01-05 17:02:39
 */
@Api("产量详情")
@RestController
@RequestMapping("/mes/mesYield")
@AllArgsConstructor
public class MesYieldController extends BaseController{
    /**
     * 服务对象
     */
    private final IMesYieldService mesYieldService;

    /**
     * 查询产量详情列表
     *
     */
    @ApiOperation("查询产量详情列表")
    @GetMapping("/list")
    public TableDataInfo list(MesYield mesYield) {
        startPage();
        List<MesYield> list = mesYieldService.selectMesYieldList(mesYield);
        return getDataTable(list);
    }
    
    /**
     * 获取产量详情详情
     */
    @ApiOperation("获取产量详情详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(mesYieldService.getById(id));
    }

    /**
     * 新增产量详情
     */
    @ApiOperation("新增产量详情")
    @PostMapping
    public AjaxResult add(@RequestBody MesYield mesYield) {
        return toBooleanAjax(mesYieldService.save(mesYield));
    }

    /**
     * 修改产量详情
     */
    @ApiOperation("修改产量详情")
    @PutMapping
    public AjaxResult edit(@RequestBody MesYield mesYield) {
        return toBooleanAjax(mesYieldService.updateById(mesYield));
    }

    /**
     * 删除产量详情
     */
    @ApiOperation("删除产量详情")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(mesYieldService.removeByIds(Arrays.asList(ids)));
    }

    @ApiOperation("查询所有未入库信息")
    @GetMapping("/waitList")
    public AjaxResult waitList(MesYield mesYield) {
        List<MesYield> list = mesYieldService.listLessYield(mesYield);
        return AjaxResult.success(list);
    }

    @GetMapping("/listYieldByDevice")
    public AjaxResult listYieldByDevice(String rq){
        List<MesYield> list = mesYieldService.listYieldByDevice(rq);
        return AjaxResult.success(list);
    }

    @GetMapping("/listYieldBetween")
    public AjaxResult listYieldBetween(String start,String end){
        Long between = DateUtil.between(DateUtil.parse(start), DateUtil.parse(end), DateUnit.DAY);
        if(between > 7){
            throw new RuntimeException("查询时间间隔不能超过7天");
        }
        List<MesYield> list = new ArrayList<>();
        for (int i = 0; i <= between; i++) {
            list.addAll(mesYieldService.listYieldByDevice(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(start), i), "yyyy-MM-dd")));
        }
        return AjaxResult.success(list);
    }

}
