package com.boyo.master.service.impl;


import com.boyo.common.utils.DateUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.master.domain.ScreenWorkshop;
import com.boyo.master.mapper.ScreenWorkshopMapper;
import com.boyo.master.service.IScreenWorkshopService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 大屏车间Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
@Service
@Tenant
public class ScreenWorkshopServiceImpl implements IScreenWorkshopService
{
    @Autowired
    private ScreenWorkshopMapper screenWorkshopMapper;

    /**
     * 查询大屏车间
     *
     * @param workshopId 大屏车间主键
     * @return 大屏车间
     */
    @Override
    public ScreenWorkshop selectScreenWorkshopByWorkshopId(Long workshopId)
    {
        return screenWorkshopMapper.selectScreenWorkshopByWorkshopId(workshopId);
    }

    /**
     * 查询大屏车间列表
     *
     * @param screenWorkshop 大屏车间
     * @return 大屏车间
     */
    @Override
    public List<ScreenWorkshop> selectScreenWorkshopList(ScreenWorkshop screenWorkshop)
    {
        return screenWorkshopMapper.selectScreenWorkshopList(screenWorkshop);
    }

    /**
     * 新增大屏车间
     *
     * @param screenWorkshop 大屏车间
     * @return 结果
     */
    @Override
    public int insertScreenWorkshop(ScreenWorkshop screenWorkshop)
    {
        screenWorkshop.setCreateTime(DateUtils.getNowDate());
        return screenWorkshopMapper.insertScreenWorkshop(screenWorkshop);
    }

    /**
     * 修改大屏车间
     *
     * @param screenWorkshop 大屏车间
     * @return 结果
     */
    @Override
    public int updateScreenWorkshop(ScreenWorkshop screenWorkshop)
    {
        screenWorkshop.setUpdateTime(DateUtils.getNowDate());
        return screenWorkshopMapper.updateScreenWorkshop(screenWorkshop);
    }

    /**
     * 批量删除大屏车间
     *
     * @param workshopIds 需要删除的大屏车间主键
     * @return 结果
     */
    @Override
    public int deleteScreenWorkshopByWorkshopIds(Long[] workshopIds)
    {
        return screenWorkshopMapper.deleteScreenWorkshopByWorkshopIds(workshopIds);
    }

    /**
     * 删除大屏车间信息
     *
     * @param workshopId 大屏车间主键
     * @return 结果
     */
    @Override
    public int deleteScreenWorkshopByWorkshopId(Long workshopId)
    {
        return screenWorkshopMapper.deleteScreenWorkshopByWorkshopId(workshopId);
    }
}