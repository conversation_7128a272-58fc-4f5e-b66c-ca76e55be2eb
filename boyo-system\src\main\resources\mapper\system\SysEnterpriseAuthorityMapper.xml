<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.system.mapper.SysEnterpriseAuthorityMapper">

    <resultMap type="com.boyo.system.domain.SysEnterpriseAuthority" id="SysEnterpriseAuthorityResult">
        <result property="id" column="id"/>
        <result property="enterpriseOpenid" column="enterprise_openid"/>
        <result property="systemOpenid" column="system_openid"/>
        <result property="systemValidity" column="system_validity"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectSysEnterpriseAuthorityVo">
        select id, enterprise_openid, system_openid, system_validity, create_time, update_time
        from t_sys_enterprise_authority
    </sql>

    <select id="selectSysEnterpriseAuthorityList" parameterType="com.boyo.system.domain.SysEnterpriseAuthority"
            resultMap="SysEnterpriseAuthorityResult">
        <include refid="selectSysEnterpriseAuthorityVo"/>
        <where>
            <if test="enterpriseOpenid != null  and enterpriseOpenid != ''">
                and enterprise_openid = #{enterpriseOpenid}
            </if>
            <if test="systemOpenid != null  and systemOpenid != ''">
                and system_openid = #{systemOpenid}
            </if>
            <if test="systemValidity != null ">
                and system_validity = #{systemValidity}
            </if>
        </where>
    </select>
</mapper>
