package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipLedger;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备台账(EquipLedger)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-02 18:07:59
 */
public interface EquipLedgerMapper extends BaseMapper<EquipLedger>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipLedger 实例对象
     * @return 对象列表
     */
    List<EquipLedger> selectEquipLedgerList(EquipLedger equipLedger);

    /**
     * 获取详情
     * @param id
     * @return
     */
    EquipLedger getInfo(@Param("id") Integer id);
}

