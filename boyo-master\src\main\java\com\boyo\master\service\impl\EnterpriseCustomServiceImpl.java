package com.boyo.master.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.master.entity.EnterpriseCustom;
import com.boyo.master.mapper.EnterpriseCustomMapper;
import com.boyo.master.service.IEnterpriseCustomService;
import java.util.List;

/**
 * 企业自定义信息(EnterpriseCustom)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-07 20:19:55
 */
@Service("enterpriseCustomService")
@AllArgsConstructor
@Tenant
public class EnterpriseCustomServiceImpl extends ServiceImpl<EnterpriseCustomMapper, EnterpriseCustom> implements IEnterpriseCustomService {
    private final EnterpriseCustomMapper enterpriseCustomMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EnterpriseCustom> selectEnterpriseCustomList(EnterpriseCustom enterpriseCustom) {
        return enterpriseCustomMapper.selectEnterpriseCustomList(enterpriseCustom);
    }

    @Override
    public boolean save(EnterpriseCustom entity) {
        super.remove(null);
        return super.save(entity);
    }
}
