package com.boyo.crm.service.impl;

import com.boyo.crm.entity.CrmVisit;
import com.boyo.crm.util.ActionEnum;
import com.boyo.crm.util.ActionUtil;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.entity.CrmBusinessProduct;
import com.boyo.crm.mapper.CrmBusinessProductMapper;
import com.boyo.crm.service.ICrmBusinessProductService;

import java.util.List;

/**
 * 商机产品关系表(CrmBusinessProduct)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-26 09:56:24
 */
@Service("crmBusinessProductService")
@AllArgsConstructor
public class CrmBusinessProductServiceImpl extends ServiceImpl<CrmBusinessProductMapper, CrmBusinessProduct> implements ICrmBusinessProductService {
    private final CrmBusinessProductMapper crmBusinessProductMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<CrmBusinessProduct> selectCrmBusinessProductList(CrmBusinessProduct crmBusinessProduct) {
        return crmBusinessProductMapper.selectCrmBusinessProductList(crmBusinessProduct);
    }

}
