<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.system.mapper.BoyoMenuMapper">

    <resultMap type="com.boyo.system.domain.BoyoMenu" id="BoyoMenuResult">
        <result property="menuId" column="menu_id"/>
        <result property="systemOpenid" column="system_openid"/>
        <result property="menuName" column="menu_name"/>
        <result property="parentId" column="parent_id"/>
        <result property="orderNum" column="order_num"/>
        <result property="path" column="path"/>
        <result property="component" column="component"/>
        <result property="isFrame" column="is_frame"/>
        <result property="isCache" column="is_cache"/>
        <result property="menuType" column="menu_type"/>
        <result property="visible" column="visible"/>
        <result property="status" column="status"/>
        <result property="perms" column="perms"/>
        <result property="icon" column="icon"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectBoyoMenuVo">
        select menu_id,
               system_openid,
               menu_name,
               parent_id,
               order_num,
               path,
               component,
               is_frame,
               is_cache,
               menu_type,
               visible,
               status,
               perms,
               icon,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from t_sys_menu
    </sql>

    <select id="selectBoyoMenuList" parameterType="com.boyo.system.domain.BoyoMenu" resultMap="BoyoMenuResult">
        <include refid="selectBoyoMenuVo"/>
        <where>
            <if test="menuId != null ">
                and menu_id = #{menuId}
            </if>
            <if test="systemOpenid != null  and systemOpenid != ''">
                and system_openid = #{systemOpenid}
            </if>
            <if test="menuName != null  and menuName != ''">
                and menu_name like concat('%', #{menuName}, '%')
            </if>
            <if test="parentId != null ">
                and parent_id = #{parentId}
            </if>
            <if test="orderNum != null ">
                and order_num = #{orderNum}
            </if>
            <if test="path != null  and path != ''">
                and path = #{path}
            </if>
            <if test="component != null  and component != ''">
                and component = #{component}
            </if>
            <if test="isFrame != null ">
                and is_frame = #{isFrame}
            </if>
            <if test="isCache != null ">
                and is_cache = #{isCache}
            </if>
            <if test="menuType != null  and menuType != ''">
                and menu_type = #{menuType}
            </if>
            <if test="visible != null  and visible != ''">
                and visible = #{visible}
            </if>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>
            <if test="perms != null  and perms != ''">
                and perms = #{perms}
            </if>
            <if test="icon != null  and icon != ''">
                and icon = #{icon}
            </if>
        </where>
    </select>

    <select id="selectMenuTreeByUserId" resultMap="BoyoMenuResult">
        select distinct m.menu_id,
                        m.system_openid,
                        m.parent_id,
                        m.menu_name,
                        m.path,
                        m.component,
                        m.visible,
                        m.status,
                        ifnull(m.perms, '') as perms,
                        m.is_frame,
                        m.is_cache,
                        m.menu_type,
                        m.icon,
                        m.order_num,
                        m.create_time,
                        s.system_order
        from t_sys_menu m,t_sys_system s
        where m.status = '0'
          and m.system_openid = s.system_openid
          and m.system_openid in (
            select system_openid
            from t_sys_system
            where system_openid in (
                select system_openid
                from t_sys_enterprise_authority
                where enterprise_openid = #{userOpenid} or system_openid = 'a1b43dbe63444ee9930eeb9f57f27d0b'
            )
               or system_apply = 0
        )
        order by s.system_order asc,m.parent_id, m.order_num
    </select>

    <select id="selectMenuByUserId" resultMap="BoyoMenuResult">
        select *
        from t_sys_menu m
        where m.status = '0'
          and m.menu_id in (
            select t2.function_openid
            from t_enterprise_user_role t1,
                 t_enterprise_role_function t2
            where t1.role_openid = t2.role_openid
              and t1.user_openid = #{userOpenid}
        )
           or system_openid = 'a1b43dbe63444ee9930eeb9f57f27d0b'
    </select>
</mapper>
