package com.boyo.master.service.impl;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.framework.annotation.Tenant;
import com.boyo.master.vo.MaterialVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.master.mapper.TMaterialMapper;
import com.boyo.master.domain.TMaterial;
import com.boyo.master.service.ITMaterialService;

/**
 * 主数据-物料Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class TMaterialServiceImpl extends ServiceImpl<TMaterialMapper, TMaterial> implements ITMaterialService {

    private final TMaterialMapper tMaterialMapper;

    @Override
    public boolean save(TMaterial entity) {
        entity = cloneWarning(entity);
        return super.save(entity);
    }

    @Override
    public boolean updateById(TMaterial entity) {
        entity = cloneWarning(entity);
        return super.updateById(entity);
    }

    /**
     * 查询主数据-物料列表
     *
     * @param tMaterial 主数据-物料
     * @return tMaterial 列表
     */
    @Override
    public List<MaterialVO> selectTMaterialList(TMaterial tMaterial) {
        return tMaterialMapper.selectTMaterialList(tMaterial);
    }

    @Override
    public boolean checkExist(TMaterial tMaterial) {
        QueryWrapper<TMaterial> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("materiel_code", tMaterial.getMaterielCode());
        if(ObjectUtil.isNotNull(tMaterial.getId())){
            queryWrapper.ne("id",tMaterial.getId());
        }
        if (tMaterialMapper.selectCount(queryWrapper) > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 警戒值处理
     * @param entity
     * @return
     */
    private TMaterial cloneWarning(TMaterial entity){
        if(ObjectUtil.isNotNull(entity.getMinStock()) && ObjectUtil.isNull(entity.getMinWarning())){
            entity.setMinWarning(entity.getMinStock());
        }
        if(ObjectUtil.isNotNull(entity.getMaxStock()) && ObjectUtil.isNull(entity.getMaxWarning())){
            entity.setMaxWarning(entity.getMaxStock());
        }
        if(ObjectUtil.isNull(entity.getMinStock()) && ObjectUtil.isNotNull(entity.getMinWarning())){
            entity.setMinStock(entity.getMinWarning());
        }
        if(ObjectUtil.isNull(entity.getMaxStock()) && ObjectUtil.isNotNull(entity.getMaxWarning())){
            entity.setMaxStock(entity.getMaxWarning());
        }
        return entity;
    }
}
