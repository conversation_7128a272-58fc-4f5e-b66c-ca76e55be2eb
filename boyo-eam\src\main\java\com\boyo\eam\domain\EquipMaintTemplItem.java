package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 设备-维保模板-维保项目(EquipMaintTemplItem)实体类
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:31
 */
@Data
@TableName(value = "equip_maint_templ_item")
public class EquipMaintTemplItem implements Serializable {
    private static final long serialVersionUID = 779374995766947445L;
        /**
    * 主键
    */
    @TableId
    private Integer id;


    @TableField(value="openid")
    private String openid;
    /**
    * 对应equip_maint_templ的openid
    */
    @TableField(value="equip_maint_templ_openid")
    private String equipMaintTemplOpenid;
    /**
    * 项目
    */
    @TableField(value="item")
    private String item;
    /**
    * 标准工时
    */
    @TableField(value="hour")
    private Integer hour;
    /**
    * 工时单位（分钟/小时）
    */
    @TableField(value="hour_unit")
    private String hourUnit;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

    /** 额外字段 */
    @TableField(exist = false)
    private List<EquipMaintTemplItemDetail> equipMaintTemplItemDetails;
    /** 额外字段 */
}
