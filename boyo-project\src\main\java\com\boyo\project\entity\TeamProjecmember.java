package com.boyo.project.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目-成员表(TeamProjecmember)实体类
 *
 * <AUTHOR>
 * @since 2022-02-10 16:43:19
 */
@Data
@TableName(value = "team_project_member")
public class TeamProjecmember implements Serializable {
    private static final long serialVersionUID = 925050391084331025L;

    @TableId
    private Integer id;

    /**
    * 项目id
    */
    @TableField(value="project_code")
    private String projectCode;
    /**
    * 成员id
    */
    @TableField(value="member_code")
    private String memberCode;
    /**
    * 加入时间
    */
    @TableField(value="join_time")
    private String joinTime;
    /**
    * 拥有者
    */
    @TableField(value="is_owner")
    private Integer isOwner;
    /**
    * 角色
    */
    @TableField(value="authorize")
    private String authorize;

    @TableField(exist = false)
    private String memberName;

}
