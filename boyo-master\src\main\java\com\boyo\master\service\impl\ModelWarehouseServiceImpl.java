package com.boyo.master.service.impl;

import java.util.List;
import java.util.Arrays;

import cn.hutool.core.util.IdUtil;
import com.boyo.framework.annotation.Tenant;
import com.boyo.master.domain.ModelAllocation;
import com.boyo.master.domain.ModelArea;
import com.boyo.master.mapper.ModelAllocationMapper;
import com.boyo.master.mapper.ModelAreaMapper;
import com.boyo.master.vo.ModelWarehouseVO;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.master.mapper.ModelWarehouseMapper;
import com.boyo.master.domain.ModelWarehouse;
import com.boyo.master.service.IModelWarehouseService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 主数据-仓库管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class ModelWarehouseServiceImpl extends ServiceImpl<ModelWarehouseMapper, ModelWarehouse> implements IModelWarehouseService {
    private final ModelWarehouseMapper modelWarehouseMapper;

    private final ModelAreaMapper areaMapper;

    private final ModelAllocationMapper allocationMapper;


    /**
     * 查询主数据-仓库管理列表
     *
     * @param modelWarehouse 主数据-仓库管理
     * @return modelWarehouse 列表
     */
    @Override
    public List<ModelWarehouseVO> selectModelWarehouseList(ModelWarehouse modelWarehouse) {
        return modelWarehouseMapper.selectModelWarehouseList(modelWarehouse);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, propagation = Propagation.REQUIRED)
    public boolean save(ModelWarehouse entity) {
        entity.setWarehouseOpenid(IdUtil.fastSimpleUUID());
        ModelArea area = new ModelArea();
        area.setAreaOpenid(IdUtil.fastSimpleUUID());
        area.setAreaName(entity.getWarehouseName());
        area.setAreaCode(IdUtil.fastSimpleUUID());
        area.setAreaWarehouseId(entity.getWarehouseOpenid());

        ModelAllocation allocation = new ModelAllocation();
        allocation.setAllocationFactory(entity.getWarehouseFactory());
        allocation.setAllocationOpenid(IdUtil.fastSimpleUUID());
        allocation.setAllocationCode(IdUtil.fastSimpleUUID());
        allocation.setAllocationWarehouse(entity.getWarehouseOpenid());
        allocation.setAllocationArea(area.getAreaOpenid());
        allocation.setAllocationName(entity.getWarehouseName());
        allocation.setAllocationAbbreviation(entity.getWarehouseName());
        areaMapper.insert(area);
        allocationMapper.insert(allocation);
        return super.save(entity);
    }
}
