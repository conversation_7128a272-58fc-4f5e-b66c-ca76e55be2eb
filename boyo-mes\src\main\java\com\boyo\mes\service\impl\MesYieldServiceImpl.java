package com.boyo.mes.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.text.Convert;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.mapper.IotEquipmentMapper;
import com.boyo.iot.util.IoTDBUtil;
import com.boyo.master.domain.BaseDict;
import com.boyo.master.domain.TMaterial;
import com.boyo.master.domain.WorkTime;
import com.boyo.master.mapper.TMaterialMapper;
import com.boyo.master.service.IBaseDictService;
import com.boyo.master.service.IBaseWorkTimeService;
import com.boyo.mes.entity.MesModulRecord;
import com.boyo.mes.entity.MesProduction;
import com.boyo.mes.entity.MesWarehousing;
import com.boyo.mes.mapper.MesModulRecordMapper;
import com.boyo.mes.mapper.MesProductionMapper;
import com.boyo.mes.mapper.MesWarehousingMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.MesYield;
import com.boyo.mes.mapper.MesYieldMapper;
import com.boyo.mes.service.IMesYieldService;

import java.util.*;

/**
 * 产量详情(MesYield)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-05 17:02:39
 */
@Service("mesYieldService")
@AllArgsConstructor
@Tenant
public class MesYieldServiceImpl extends ServiceImpl<MesYieldMapper, MesYield> implements IMesYieldService {
    private final MesYieldMapper mesYieldMapper;
    private final MesModulRecordMapper modulRecordMapper;
    private final IoTDBUtil ioTDBUtil;
    private final IotEquipmentMapper equipmentMapper;
    private final IBaseDictService baseDictService;
    private final TMaterialMapper materialMapper;
    private final MesWarehousingMapper warehousingMapper;

    private IBaseWorkTimeService workTimeService;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<MesYield> selectMesYieldList(MesYield mesYield) {
        return mesYieldMapper.selectMesYieldList(mesYield);
    }

    @Override
    public List<MesYield> listLessYield(MesYield mesYield) {
        List<MesYield> list = mesYieldMapper.listLessYield(mesYield);
//        计算当前仍在使用中的模具的产量信息
        QueryWrapper<MesModulRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNull("end_time");
        List<MesModulRecord> records = modulRecordMapper.selectList(queryWrapper);
        if (records != null) {
            for (MesModulRecord record : records) {
                IotEquipment equipment = equipmentMapper.selectById(record.getEquipmentId());
                if (ObjectUtil.isNotNull(equipment)) {
                    JSONArray array = ioTDBUtil.getMaxAndMin(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), equipment.getEquipmentCode(), "Number", DateUtil.formatDateTime(record.getStartTime()), DateUtil.formatDateTime(new Date()));
                    for (MesYield yield : list) {
                        try {
                            if (yield.getId().equals(record.getProductionId())) {
                                yield.setYieldCount(yield.getYieldCount() + (array.getJSONObject(0).getInteger("max") - array.getJSONObject(0).getInteger("min")) * record.getProductionMultiple());
                                break;
                            }

                        } catch (Exception e) {

                        }
                    }
                }
            }
        }
        list.sort(Comparator.comparing(MesYield::getYieldCount).reversed());
        return list;
    }

    @Override
    public List<MesYield> listYieldByDevice(String rq) {
        QueryWrapper<MesWarehousing> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rq",rq);
        List<MesWarehousing> tempList = warehousingMapper.selectList(queryWrapper);
//        List<MesYield> list = mesYieldMapper.listYieldByDevice(rq);   todo 做权限，不同账号查看的不同
        List<MesYield> list = new ArrayList<>();

        //获取白班时间
        String dayStartTime = "08:00:00";
        String dayEndTime = "20:00:00";
        //晚班时间
        String nightStartTime = "20:00:00";
        String nightEndTime = "08:00:00";

        List<WorkTime> listWorkTime = null;
        try {
            listWorkTime = workTimeService.list();
        } catch (Exception e) {
//            e.printStackTrace();
        }
        if (ObjectUtil.isNotEmpty(listWorkTime)) {
            for (WorkTime temp : listWorkTime) {
                if ("SHIFT_DAY".equals(temp.getBaseCode())) {
                    dayStartTime = temp.getStartTime();
                    dayEndTime = temp.getEndTime();
                } else if ("SHIFT_NIGHT".equals(temp.getBaseCode())) {
                    nightStartTime = temp.getStartTime();
                    nightEndTime = temp.getEndTime();
                }
            }
        }
        long daytime = DateUtil.between(DateUtil.parseDateTime("2022-01-01 " + dayStartTime),
                DateUtil.parseDateTime("2022-01-01 " + dayEndTime), DateUnit.SECOND, true) + 30;
//        夜班时长
        long nighttime = 24 * 60 * 60 - daytime + 60;
//            计算白班
        String dayStart = rq + " " + dayStartTime;
        String dayEnd = rq + " " + dayEndTime;
        String nightStart = rq + " " + nightStartTime;
        String nightEnd = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(rq), 1)) + " " + nightEndTime;
        QueryWrapper<MesModulRecord> mesModulRecordQueryWrapper = new QueryWrapper<>();
        String finalDayStart = dayStart;
        mesModulRecordQueryWrapper.lt("start_time", nightEnd).and(i -> i.isNull("end_time").or().gt("end_time", finalDayStart));
        List<MesModulRecord> records = modulRecordMapper.selectList(mesModulRecordQueryWrapper);

        Long userAdmin = SecurityUtils.getLoginUser().getEnterpriseUser().getUserAdmin();

        if (records != null) {
            for (MesModulRecord record : records) {
                IotEquipment equipment = equipmentMapper.selectById(record.getEquipmentId());
                // 当userAdmin不为1时，需要检查当前用户的企业部门是否与设备的部门匹配   1是管理员标识
                if (userAdmin != 1) {
                    // 判断登录用户的部门开放ID是否与设备的部门ID相同
                    final boolean isDepart = SecurityUtils.getLoginUser().getEnterpriseUser().getDepartmentOpenid().equals(equipment.getDeptId());
                    // 如果不匹配，则跳过该设备的处理
                    if(!isDepart){
                        continue;
                    }
                }

                if (ObjectUtil.isNotNull(equipment)) {
//                    白班
                    String start = dayStart;
                    String end = dayEnd;
                    if (DateUtil.compare(record.getStartTime(), DateUtil.parseDateTime(dayStart)) > 0) {
                        start = DateUtil.formatDateTime(record.getStartTime());
                    }
                    if (ObjectUtil.isNotEmpty(record.getEndTime()) && DateUtil.compare(record.getEndTime(), DateUtil.parseDateTime(dayEnd)) < 0) {
                        end = DateUtil.formatDateTime(record.getEndTime());
                    }
                    JSONArray array = ioTDBUtil.getMaxAndMin(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), equipment.getEquipmentCode(), "Number", start, end);
                    if (array.size() != 0 && !array.getJSONObject(0).getString("min").equalsIgnoreCase("null")
                            && !array.getJSONObject(0).getString("max").equalsIgnoreCase("null")) {
                        boolean exist = false;
//                        for (MesYield temp : list) {
//                            if (temp.getProductionId().equals(record.getProductionId()) && temp.getEquipmentId().equals(record.getEquipmentId()) && temp.getShift().equalsIgnoreCase("day")) {
//                                temp.setYieldCount(temp.getYieldCount() + (array.getJSONObject(0).getInteger("max") - array.getJSONObject(0).getInteger("min")) * record.getProductionMultiple());
//                                exist = true;
//                                break;
//                            }
//                        }
                        if (!exist) {
                            MesYield temp = new MesYield();
                            temp.setOrderNum(equipment.getEquipmentSort());
                            temp.setEquipmentName(equipment.getEquipmentName());
                            temp.setEquipmentId(Convert.toInt(equipment.getId()));
                            TMaterial production = materialMapper.selectById(record.getProductionId());
                            if(ObjectUtil.isNotNull(production)){
                                temp.setProdutionName(production.getMaterielName());
                                temp.setProductionCode(production.getMaterielCode());
                                temp.setUserCode(production.getMaterielAbbreviation());
                                temp.setYieldCount((array.getJSONObject(0).getInteger("max") - array.getJSONObject(0).getInteger("min")) * record.getProductionMultiple());
                            }else{
                                temp.setYieldCount((array.getJSONObject(0).getInteger("max") - array.getJSONObject(0).getInteger("min")));
                            }
                            temp.setProductionId(record.getProductionId());
                            temp.setRq(rq);
                            temp.setShift("day");
                            temp.setUuid(UUID.randomUUID().toString());
                            list.add(temp);

//                            System.out.println("-----------"+JSONObject.toJSONString(temp));
//                            System.out.println("+++++++++++"+JSONObject.toJSONString(list));
                        }
                    }
//                    夜班
                    if (DateUtil.compare(record.getStartTime(), DateUtil.parseDateTime(nightStart)) > 0) {
                        start = DateUtil.formatDateTime(record.getStartTime());
                    }else{
                        start  = nightStart;
                    }
                    if (ObjectUtil.isNotEmpty(record.getEndTime()) && DateUtil.compare(record.getEndTime(), DateUtil.parseDateTime(nightEnd)) < 0) {
                        end = DateUtil.formatDateTime(record.getEndTime());
                    }else {
                        end = nightEnd;
                    }
                    array = ioTDBUtil.getMaxAndMin(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), equipment.getEquipmentCode(), "Number", start, end);
                    if (array.size() != 0 && !array.getJSONObject(0).getString("min").equalsIgnoreCase("null")
                            && !array.getJSONObject(0).getString("max").equalsIgnoreCase("null")) {
                        boolean exist = false;
//                        for (MesYield temp : list) {
//                            if (temp.getProductionId().equals(record.getProductionId()) && temp.getEquipmentId().equals(record.getEquipmentId()) && temp.getShift().equalsIgnoreCase("night")) {
//                                temp.setYieldCount(temp.getYieldCount() + (array.getJSONObject(0).getInteger("max") - array.getJSONObject(0).getInteger("min")) * record.getProductionMultiple());
//                                exist = true;
//                                break;
//                            }
//                        }
                        if (!exist) {
                            MesYield temp = new MesYield();
                            TMaterial production = materialMapper.selectById(record.getProductionId());
                            if(ObjectUtil.isNotNull(production)){
                                temp.setProdutionName(production.getMaterielName());
                                temp.setProductionId(Convert.toInt(production.getId()));
                                temp.setProductionCode(production.getMaterielCode());
                                temp.setUserCode(production.getMaterielAbbreviation());
                                temp.setYieldCount((array.getJSONObject(0).getInteger("max") - array.getJSONObject(0).getInteger("min")) * record.getProductionMultiple());
                            }else{
                                temp.setYieldCount((array.getJSONObject(0).getInteger("max") - array.getJSONObject(0).getInteger("min")));
                            }
                            temp.setOrderNum(equipment.getEquipmentSort());
                            temp.setEquipmentName(equipment.getEquipmentName());
                            temp.setEquipmentId(Convert.toInt(equipment.getId()));
                            temp.setRq(rq);
                            temp.setShift("night");
                            temp.setUuid(UUID.randomUUID().toString());
                            list.add(temp);
//                            System.out.println("1--------"+JSONObject.toJSONString(temp));
//                            System.out.println("2--------"+JSONObject.toJSONString(list));
                        }
                    }
                }
            }
        }
        if(list != null && tempList != null){
            for (MesYield yield:list) {
                for (MesWarehousing temp:tempList) {
                    if(yield.getEquipmentId().equals(temp.getEquipmentId()) && yield.getShift().equals(temp.getShift())){
                        yield.setInCount(temp.getInCount());
                        yield.setScrapCount(temp.getScrapCount());
                    }
                }
            }
        }
        list.sort((o1, o2) -> o1.getOrderNum().compareTo(o2.getOrderNum()));
        return list;
    }
}
