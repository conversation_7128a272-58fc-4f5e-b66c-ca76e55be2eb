<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.iot.mapper.IotEquipmentPropMapper">

    <resultMap type="com.boyo.iot.domain.IotEquipmentProp" id="IotEquipmentPropResult">
        <result property="id" column="id"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="attrId" column="attr_id"/>
        <result property="faultVal" column="fault_val"/>
        <result property="minVal" column="min_val"/>
        <result property="maxVal" column="max_val"/>
        <result property="enumList" column="enum_list"/>
        <result property="showType" column="show_type"/>
        <result property="autoFault" column="auto_fault"></result>
        <result property="autoBill" column="auto_bill"></result>
        <result property="faultTime" column="fault_time"></result>
        <result property="billTime" column="bill_time"></result>
        <result property="billUser" column="bill_user"></result>
        <result property="customMultiple" column="custom_multiple"></result>
    </resultMap>
    <resultMap id="faultProp" type="com.boyo.iot.entity.FaultProp">
        <result property="id" column="attr_id"></result>
        <result property="equipmentId" column="equipment_id"></result>
        <result property="code" column="attr_code"></result>
        <result property="faultVal" column="fault_val"></result>
        <result property="minVal" column="min_val"/>
        <result property="maxVal" column="max_val"/>
        <result property="autoFault" column="auto_fault"></result>
        <result property="autoBill" column="auto_bill"></result>
        <result property="faultTime" column="fault_time"></result>
        <result property="billTime" column="bill_time"></result>
        <result property="equipmentCode" column="equipment_code"></result>
        <result property="attrName" column="attr_name"></result>
        <result property="attrMultiple" column="attr_multiple"></result>
        <result property="attrType" column="attr_type"></result>
    </resultMap>

    <select id="selectIotEquipmentPropList" parameterType="com.boyo.iot.domain.IotEquipmentProp"
            resultType="com.boyo.iot.vo.IoTAttrVO">
        SELECT
        l1.*,
        l2.id,
        l2.min_val AS minVal,
        l2.max_val AS maxVal,
        l2.enum_list AS enumList,
        l2.show_type AS showType ,
        l2.fault_val,
        l2.auto_fault,
        l2.auto_bill,
        l2.fault_time,
        l2.bill_time,
        l2.bill_user,
        l2.custom_multiple,
        l1.attr_order
        FROM
        (
        SELECT
        t2.id AS attrId,
        t2.attr_name AS attrName,
        t2.attr_type AS attrType,
        t1.id AS equipmentId ,
        t2.attr_order,
        t2.attr_class as attrClass
        FROM
        iot_equipment t1,
        iot_tsl_attr t2
        WHERE
        t1.tsl_id = t2.tsl_id
        <if test="equipmentId != null ">
            and t1.id = #{equipmentId}
        </if>
        ) l1
        LEFT JOIN iot_equipment_prop l2 ON l1.equipmentId = l2.equipment_id and l1.attrId = l2.attr_id
        order by l1.attr_order asc
    </select>
    <select id="selectEquipmentFaultProp" resultMap="faultProp">
        select t3.*,(case when t3.custom_multiple is null then l1.attr_multiple else t3.custom_multiple end) as attr_multiple, l1.attr_code, l1.equipment_code, l1.attr_name, l1.attr_multiple, l1.attr_type
        from (select t1.id,
                     t1.equipment_code,
                     t2.id as attr_id,
                     t2.attr_code,
                     t2.attr_name,
                     t2.attr_multiple,
                     t2.attr_type
              from iot_equipment t1,
                   iot_tsl_attr t2
              where t1.equipment_code = #{code}
                and t1.tsl_id = t2.tsl_id) l1
                 left join iot_equipment_prop t3 on
            l1.id = t3.equipment_id and l1.attr_id = t3.attr_id
    </select>
</mapper>
