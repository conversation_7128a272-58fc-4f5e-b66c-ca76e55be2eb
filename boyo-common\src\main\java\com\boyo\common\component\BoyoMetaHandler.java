package com.boyo.common.component;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.boyo.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 自动填充默认数据
 */
@Component
@Slf4j
public class BoyoMetaHandler  implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
        this.strictInsertFill(metaObject, "createdUser", String.class, SecurityUtils.getUserOpenid());
        this.strictInsertFill(metaObject, "createUser", String.class, SecurityUtils.getUserOpenid());
        this.strictInsertFill(metaObject, "createUserId", Long.class, SecurityUtils.getLoginUser().getEnterpriseUser().getId());
        this.strictInsertFill(metaObject, "ownerUserId", Long.class, SecurityUtils.getLoginUser().getEnterpriseUser().getId());
        this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
        this.strictInsertFill(metaObject, "deptId", String.class, SecurityUtils.getLoginUser().getEnterpriseUser().getDepartmentOpenid());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "updateAt", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
        this.strictInsertFill(metaObject, "updatedUser", String.class, SecurityUtils.getUserOpenid());
    }

}
