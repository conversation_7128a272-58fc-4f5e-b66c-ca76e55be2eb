<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipLedgerSparePartMapper">

    <resultMap type="com.boyo.eam.domain.EquipLedgerSparePart" id="EquipLedgerSparePartResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="equipLedgerOpenid" column="equip_ledger_openid" />
        <result property="sparePartOpenid" column="spare_part_openid" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipLedgerSparePartList" parameterType="com.boyo.eam.domain.EquipLedgerSparePart" resultMap="EquipLedgerSparePartResult">
        select
          id, openid, equip_ledger_openid, spare_part_openid, create_by, create_time, update_by, update_time
        from equip_ledger_spare_part
        <where>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
            <if test="equipLedgerOpenid != null and equipLedgerOpenid != ''">
                and equip_ledger_openid = #{equipLedgerOpenid}
            </if>
            <if test="sparePartOpenid != null and sparePartOpenid != ''">
                and spare_part_openid = #{sparePartOpenid}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>
</mapper>

