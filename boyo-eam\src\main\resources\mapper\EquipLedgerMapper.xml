<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipLedgerMapper">

    <resultMap type="com.boyo.eam.domain.EquipLedger" id="EquipLedgerResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="parentOpenid" column="parent_openid" />
        <result property="code" column="code" />
        <result property="equipTypeOpenid" column="equip_type_openid" />
        <result property="name" column="name" />
        <result property="equipStateOpenid" column="equip_state_openid" />
        <result property="specifications" column="specifications" />
        <result property="factoryOpenid" column="factory_openid" />
        <result property="workshopOpenid" column="workshop_openid" />
        <result property="deptId" column="dept_id" />
        <result property="location" column="location" />
        <result property="useTime" column="use_time" />
        <result property="imgUrl" column="img_url" />
        <result property="isCheck" column="is_check" />
        <result property="modelOpenid" column="model_openid" />
        <result property="supplierOpenid" column="supplier_openid" />
        <result property="outFactoryCode" column="out_factory_code" />
        <result property="outFactoryTime" column="out_factory_time" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipLedgerList" parameterType="com.boyo.eam.domain.EquipLedger" resultType="com.boyo.eam.domain.EquipLedger">
        select
        el.*,
        et.name as equipTypeName,
        es.name as equipStateName,
        tmf.factory_name as factoryName,
        tmw.workshop_name as workshopName,
        oi.name as modelName
        from
        equip_ledger el left join
        equip_type et on el.equip_type_openid = et.openid left join
        equip_state es on el.equip_state_openid = es.openid left join
        t_model_factory tmf on el.factory_openid = tmf.factory_openid left join
        t_model_workshop tmw on el.workshop_openid = tmw.workshop_openid left join
        object_info oi on el.model_openid = oi.openid
        <where>
            <if test="openid != null and openid != ''">
                and el.openid = #{openid}
            </if>
            <if test="code != null and code != ''">
                and el.code = #{code}
            </if>
            <if test="equipTypeOpenid != null and equipTypeOpenid != ''">
                and el.equip_type_openid = #{equipTypeOpenid}
            </if>
            <if test="name != null and name != ''">
                and el.name = #{name}
            </if>
            <if test="equipStateOpenid != null and equipStateOpenid != ''">
                and el.equip_state_openid = #{equipStateOpenid}
            </if>
            <if test="specifications != null and specifications != ''">
                and el.specifications = #{specifications}
            </if>
            <if test="factoryOpenid != null and factoryOpenid != ''">
                and el.factory_openid = #{factoryOpenid}
            </if>
            <if test="workshopOpenid != null and workshopOpenid != ''">
                and el.workshop_openid = #{workshopOpenid}
            </if>
            <if test="location != null and location != ''">
                and el.location = #{location}
            </if>
            <if test="useTime != null">
                and el.use_time = #{useTime}
            </if>
            <if test="isCheck != null">
                and el.is_check = #{isCheck}
            </if>
            <if test="modelOpenid != null and modelOpenid != ''">
                and el.model_openid = #{modelOpenid}
            </if>
            <if test="supplierOpenid != null and supplierOpenid != ''">
                and el.supplier_openid = #{supplierOpenid}
            </if>
            <if test="outFactoryCode != null and outFactoryCode != ''">
                and el.out_factory_code = #{outFactoryCode}
            </if>
            <if test="outFactoryTime != null">
                and el.out_factory_time = #{outFactoryTime}
            </if>
            <if test="createBy != null and createBy != ''">
                and el.create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and el.create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and el.update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and el.update_time = #{updateTime}
            </if>
            <if test="parentOpenid != null and parentOpenid != ''">
                and el.parent_openid = #{parentOpenid}
            </if>
            <if test="type != null and type != ''">
                and el.type = #{type}
            </if>
            <if test="workshopSectionCode != null and workshopSectionCode != ''">
                and el.workshop_section_code = #{workshopSectionCode}
            </if>
        </where>
        order by el.id desc
    </select>

    <select id="getInfo" parameterType="com.boyo.eam.domain.EquipLedger" resultType="EquipLedger">
        select
        el.*,
        et.name as equipTypeName,
        es.name as equipStateName,
        tmf.factory_name as factoryName,
        tmw.workshop_name as workshopName,
        oi.name as modelName
        from
        equip_ledger el left join
        equip_type et on el.equip_type_openid = et.openid left join
        equip_state es on el.equip_state_openid = es.openid left join
        t_model_factory tmf on el.factory_openid = tmf.factory_openid left join
        t_model_workshop tmw on el.workshop_openid = tmw.workshop_openid left join
        object_info oi on el.model_openid = oi.openid
        where el.id=#{id}
    </select>
</mapper>

