package com.boyo.system.service.impl;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.system.domain.EnterpriseRoleFunction;
import com.boyo.system.mapper.EnterpriseRoleFunctionMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.system.mapper.EnterpriseRoleMapper;
import com.boyo.system.domain.EnterpriseRole;
import com.boyo.system.service.IEnterpriseRoleService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 企业角色管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class EnterpriseRoleServiceImpl extends ServiceImpl<EnterpriseRoleMapper, EnterpriseRole> implements IEnterpriseRoleService {
    private final EnterpriseRoleMapper enterpriseRoleMapper;

    private final EnterpriseRoleFunctionMapper enterpriseRoleFunctionMapper;


    /**
     * 查询企业角色管理列表
     *
     * @param enterpriseRole 企业角色管理
     * @return enterpriseRole 列表
     */
    @Override
    public List<EnterpriseRole> selectEnterpriseRoleList(EnterpriseRole enterpriseRole) {
        return enterpriseRoleMapper.selectEnterpriseRoleList(enterpriseRole);
    }

    @Override
    public void saveRoleAdmin(EnterpriseRole enterpriseRole) {
        enterpriseRoleMapper.saveRoleAdmin(enterpriseRole);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, propagation = Propagation.REQUIRED)
    public boolean save(EnterpriseRole entity) {
        entity.setCreateTime(new Date());
        entity.setEnterpriseOpenid(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid());
        entity.setRoleOpenid(IdUtil.fastSimpleUUID());
        List<Integer> menus = entity.getMenus();
        if(menus != null && menus.size() > 0){
            for (int i = 0; i < menus.size(); i++) {
                EnterpriseRoleFunction obj = new EnterpriseRoleFunction();
                obj.setRoleOpenid(entity.getRoleOpenid());
                obj.setFunctionOpenid(Convert.toStr(menus.get(i)));
                obj.setCreateTime(new Date());
                obj.setUpdateTime(new Date());
                enterpriseRoleFunctionMapper.insert(obj);
            }
        }
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, propagation = Propagation.REQUIRED)
    public boolean updateById(EnterpriseRole entity) {
        QueryWrapper<EnterpriseRoleFunction> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_openid",entity.getRoleOpenid());
        enterpriseRoleFunctionMapper.delete(queryWrapper);
        List<Integer> menus = entity.getMenus();
        if(menus != null && menus.size() > 0){
            for (int i = 0; i < menus.size(); i++) {
                EnterpriseRoleFunction obj = new EnterpriseRoleFunction();
                obj.setRoleOpenid(entity.getRoleOpenid());
                obj.setFunctionOpenid(Convert.toStr(menus.get(i)));
                obj.setCreateTime(new Date());
                obj.setUpdateTime(new Date());
                enterpriseRoleFunctionMapper.insert(obj);
            }
        }
        return super.updateById(entity);
    }

    @Override
    public EnterpriseRole getById(Serializable id) {
        EnterpriseRole role = super.getById(id);
        QueryWrapper<EnterpriseRoleFunction> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_openid",role.getRoleOpenid());
        List<Integer> menus = new ArrayList<>();
        List<EnterpriseRoleFunction> functionList = enterpriseRoleFunctionMapper.selectList(queryWrapper);
        if(functionList != null && functionList.size() > 0){
            for (int i = 0; i < functionList.size(); i++) {
                menus.add(Convert.toInt(functionList.get(i).getFunctionOpenid()));
            }
        }
        role.setMenus(menus);
        return role;
    }
}
