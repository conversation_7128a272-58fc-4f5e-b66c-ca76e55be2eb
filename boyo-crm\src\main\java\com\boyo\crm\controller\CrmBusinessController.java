package com.boyo.crm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.crm.entity.CrmBusiness;
import com.boyo.crm.service.ICrmBusinessService;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

/**
 * 商机表(CrmBusiness)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-26 09:56:24
 */
@Api("商机表")
@RestController
@RequestMapping("/crm/crmBusiness")
@AllArgsConstructor
public class CrmBusinessController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmBusinessService crmBusinessService;
    private final IEnterpriseUserService enterpriseUserService;


    /**
     * 查询商机表列表
     *
     */
    @ApiOperation("查询商机表列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusiness crmBusiness) {
        startPage();
        List<CrmBusiness> list = crmBusinessService.selectCrmBusinessList(crmBusiness);
        if(list != null && list.size() > 0){
            List<Long> ids = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                ids.add(list.get(i).getOwnerUserId());
            }
            List<EnterpriseUser> userList = enterpriseUserService.selectByIds(ids);
            if(userList != null && userList.size() > 0){
                for (int i = 0; i < list.size(); i++) {
                    for (int j = 0; j < userList.size(); j++) {
                        if(list.get(i).getOwnerUserId().equals(userList.get(j).getId())){
                            list.get(i).setOwnerUserName(userList.get(j).getUserFullName());
                            break;
                        }
                    }
                }
            }
        }
        return getDataTable(list);
    }
    
    /**
     * 获取商机表详情
     */
    @ApiOperation("获取商机表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        CrmBusiness business = crmBusinessService.getById(id);
        if(ObjectUtil.isNotNull(business)){
            EnterpriseUser user = enterpriseUserService.getById(business.getOwnerUserId());
            if(ObjectUtil.isNotNull(user)){
                business.setOwnerUserName(user.getUserFullName());
            }
        }
        return AjaxResult.success(business);
    }

    /**
     * 新增商机表
     */
    @ApiOperation("新增商机表")
    @PostMapping
    public AjaxResult add(@RequestBody CrmBusiness crmBusiness) {
        return toBooleanAjax(crmBusinessService.save(crmBusiness));
    }

    /**
     * 修改商机表
     */
    @ApiOperation("修改商机表")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmBusiness crmBusiness) {
        return toBooleanAjax(crmBusinessService.updateById(crmBusiness));
    }

    /**
     * 修改商机基本信息
     * @return
     */
    @PostMapping("/changeBuiness")
    public AjaxResult changeOwner(@RequestBody CrmBusiness crmBusiness){
        crmBusinessService.changeBusiness(crmBusiness);
        return AjaxResult.success();
    }

    /**
     * 删除商机表
     */
    @ApiOperation("删除商机表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmBusinessService.removeByIds(Arrays.asList(ids)));
    }

}
