package com.boyo.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.entity.WorkOrder;

import java.util.List;

/**
 * 系统工单(WorkOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-08 15:10:05
 */
@Tenant
public interface WorkOrderMapper extends BaseMapper<WorkOrder> {

    /**
     * 通过实体作为筛选条件查询
     *
     * @param workOrder 实例对象
     * @return 对象列表
     */
    List<WorkOrder> selectWorkOrderList(WorkOrder workOrder);
    List<WorkOrder> listWaitReceive(WorkOrder workOrder);

    WorkOrder getWorkOrderById(Integer id);


}

