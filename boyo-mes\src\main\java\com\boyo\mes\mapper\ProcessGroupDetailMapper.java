package com.boyo.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.mes.entity.ProcessGroupDetail;
import java.util.List;

/**
 * 工序组详情(ProcessGroupDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
public interface ProcessGroupDetailMapper extends BaseMapper<ProcessGroupDetail>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param processGroupDetail 实例对象
     * @return 对象列表
     */
    List<ProcessGroupDetail> selectProcessGroupDetailList(ProcessGroupDetail processGroupDetail);


}

