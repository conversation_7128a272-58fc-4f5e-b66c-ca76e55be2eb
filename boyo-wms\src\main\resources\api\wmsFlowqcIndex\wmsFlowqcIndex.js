import request from '@/utils/request'

const prefix = '/${module}'

// 查询(WmsFlowqcIndex)列表
export function listWmsFlowqcIndex(query) {
  return request({
    url: prefix + '/wmsFlowqcIndex/list',
    method: 'get',
    params: query,
  })
}

// 查询(WmsFlowqcIndex)详细
export function getWmsFlowqcIndex(id) {
  return request({
    url: prefix + '/wmsFlowqcIndex/' + id,
    method: 'get',
  })
}

// 新增(WmsFlowqcIndex)
export function addWmsFlowqcIndex(data) {
  return request({
    url: prefix + '/wmsFlowqcIndex',
    method: 'post',
    data: data,
  })
}

// 修改(WmsFlowqcIndex)
export function updateWmsFlowqcIndex(data) {
  return request({
    url: prefix + '/wmsFlowqcIndex',
    method: 'put',
    data: data,
  })
}

// 删除(WmsFlowqcIndex)
export function delWmsFlowqcIndex(id) {
  return request({
    url: prefix + '/wmsFlowqcIndex/' + id,
    method: 'delete',
  })
}
