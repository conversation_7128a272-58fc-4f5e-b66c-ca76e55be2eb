<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.project.mapper.TeamTaskWorkTimeMapper">

    <resultMap type="com.boyo.project.entity.TeamTaskWorkTime" id="TeamTaskWorkTimeResult">
        <result property="id" column="id" />
        <result property="taskCode" column="task_code" />
        <result property="memberCode" column="member_code" />
        <result property="createTime" column="create_time" />
        <result property="content" column="content" />
        <result property="beginTime" column="begin_time" />
        <result property="num" column="num" />
        <result property="code" column="code" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectTeamTaskWorkTimeList" parameterType="com.boyo.project.entity.TeamTaskWorkTime" resultMap="TeamTaskWorkTimeResult">
        select
          id, task_code, member_code, create_time, content, begin_time, num, code
        from team_task_work_time
        <where>
            <if test="taskCode != null and taskCode != ''">
                and task_code = #{taskCode}
            </if>
            <if test="memberCode != null and memberCode != ''">
                and member_code = #{memberCode}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="content != null and content != ''">
                and content = #{content}
            </if>
            <if test="beginTime != null and beginTime != ''">
                and begin_time = #{beginTime}
            </if>
            <if test="num != null">
                and num = #{num}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
        </where>
    </select>
</mapper>

