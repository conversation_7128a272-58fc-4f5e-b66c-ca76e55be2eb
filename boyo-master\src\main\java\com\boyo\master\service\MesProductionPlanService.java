package com.boyo.master.service;

import com.boyo.master.domain.MesProductionPlan;

import java.util.List;

/**
 * 生产计划Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
public interface MesProductionPlanService {

    /**
     * 查询生产计划列表
     *
     * @param mesProductionPlan 查询条件
     * @return 生产计划集合
     */
    List<MesProductionPlan> selectMesProductionPlanList(MesProductionPlan mesProductionPlan);

    /**
     * 根据ID查询生产计划
     *
     * @param id 主键ID
     * @return 生产计划对象
     */
    MesProductionPlan selectMesProductionPlanById(String id);

    /**
     * 新增生产计划
     *
     * @param mesProductionPlan 生产计划对象
     * @return 影响行数
     */
    int insertMesProductionPlan(MesProductionPlan mesProductionPlan);

    /**
     * 修改生产计划
     *
     * @param mesProductionPlan 生产计划对象
     * @return 影响行数
     */
    int updateMesProductionPlan(MesProductionPlan mesProductionPlan);

    /**
     * 删除生产计划
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteMesProductionPlanById(Long id);
}