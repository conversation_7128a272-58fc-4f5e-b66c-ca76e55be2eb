package com.boyo.common.result;

public class Result<T> {
	
	private int code;
	private String msg;
	private T data;

	public Result(){
		this.msg = "操作成功";
		this.code = 200;
	}
	
	/**
	 *  成功时候的调用
	 * */
	public static  <T> Result<T> success(T data){
		return new Result<T>(data);
	}

	public static  <T> Result<T> success(T data,String msg){
		return new Result<T>(data,msg);
	}


	public static <T> Result<T> fail(int code, String msg) {
		return new Result<T>(code, msg);
	}

	/**
	 *  失败时候的调用
	 * */
	public static  <T> Result<T> error(CodeMsg codeMsg){
		return new Result<T>(codeMsg);
	}
	
	private Result(T data) {
		this.data = data;
		this.msg = "操作成功";
		this.code = 200;
	}

	private Result(T data,String msg) {
		this.data = data;
		this.msg = msg;
		this.code = 200;
	}


	private Result(int code, String msg) {
		this.code = code;
		this.msg = msg;
	}
	
	private Result(CodeMsg codeMsg) {
		if(codeMsg != null) {
			this.code = codeMsg.getCode();
			this.msg = codeMsg.getMsg();
			this.data = null;
		}
	}
	
	
	public int getCode() {
		return code;
	}
	public void setCode(int code) {
		this.code = code;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public T getData() {
		return data;
	}
	public void setData(T data) {
		this.data = data;
	}
}
