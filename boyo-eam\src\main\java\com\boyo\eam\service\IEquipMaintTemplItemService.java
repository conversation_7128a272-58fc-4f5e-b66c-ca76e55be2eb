package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipMaintTemplItem;

import java.util.List;

/**
 * 设备-维保模板-维保项目(EquipMaintTemplItem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:31
 */
public interface IEquipMaintTemplItemService extends IService<EquipMaintTemplItem> {

    /**
     * 查询多条数据
     *
     * @param equipMaintTemplItem 对象信息
     * @return 对象列表
     */
    List<EquipMaintTemplItem> selectEquipMaintTemplItemList(EquipMaintTemplItem equipMaintTemplItem);


}
