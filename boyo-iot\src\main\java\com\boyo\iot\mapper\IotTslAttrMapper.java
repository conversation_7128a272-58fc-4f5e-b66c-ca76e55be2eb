package com.boyo.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.domain.IotTslAttr;

import java.util.List;


/**
 * IoT物模型属性Mapper接口
 *
 * <AUTHOR>
 */
@Tenant
public interface IotTslAttrMapper extends BaseMapper<IotTslAttr> {

    /**
     * 查询IoT物模型属性列表
     *
     * @param iotTslAttr IoT物模型属性
     * @return IotTslAttr集合
     */
    List<IotTslAttr> selectIotTslAttrList(IotTslAttr iotTslAttr);

}
