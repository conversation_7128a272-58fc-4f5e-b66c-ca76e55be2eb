package com.boyo.project.controller;

import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.project.entity.TeamTaskFile;
import com.boyo.project.service.ITeamTaskFileService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.Date;
import java.util.List;
import java.util.Arrays;

/**
 * (TeamTaskFile)表控制层
 *
 * <AUTHOR>
 * @since 2022-02-17 20:51:36
 */
@Api("")
@RestController
@RequestMapping("/project/teamTaskFile")
@AllArgsConstructor
@Tenant
public class TeamTaskFileController extends BaseController {
    /**
     * 服务对象
     */
    private final ITeamTaskFileService teamTaskFileService;

    /**
     * 查询列表
     */
    @ApiOperation("查询列表")
    @GetMapping("/list")
    public TableDataInfo list(TeamTaskFile teamTaskFile) {
        startPage();
        List<TeamTaskFile> list = teamTaskFileService.selectTeamTaskFileList(teamTaskFile);
        return getDataTable(list);
    }

    /**
     * 获取详情
     */
    @ApiOperation("获取详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(teamTaskFileService.getById(id));
    }

    /**
     * 新增
     */
    @ApiOperation("新增")
    @PostMapping
    public AjaxResult add(@RequestBody TeamTaskFile teamTaskFile) {
        return toBooleanAjax(teamTaskFileService.save(teamTaskFile));
    }

    /**
     * 修改
     */
    @ApiOperation("修改")
    @PutMapping
    public AjaxResult edit(@RequestBody TeamTaskFile teamTaskFile) {
        return toBooleanAjax(teamTaskFileService.updateById(teamTaskFile));
    }

    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(teamTaskFileService.removeByIds(Arrays.asList(ids)));
    }

}
