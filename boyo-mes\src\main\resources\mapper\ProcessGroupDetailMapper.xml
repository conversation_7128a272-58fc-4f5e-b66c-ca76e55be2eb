<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.ProcessGroupDetailMapper">

    <resultMap type="com.boyo.mes.entity.ProcessGroupDetail" id="ProcessGroupDetailResult">
        <result property="id" column="id" />
        <result property="groupId" column="group_id" />
        <result property="processId" column="process_id" />
        <result property="sortNum" column="sort_num" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectProcessGroupDetailList" parameterType="com.boyo.mes.entity.ProcessGroupDetail" resultMap="ProcessGroupDetailResult">
        select
          id, group_id, process_id, sort_num
        from t_process_group_detail
        <where>
            <if test="groupId != null">
                and group_id = #{groupId}
            </if>
            <if test="processId != null">
                and process_id = #{processId}
            </if>
            <if test="sortNum != null">
                and sort_num = #{sortNum}
            </if>
        </where>
        order by sort_num asc
    </select>
</mapper>

