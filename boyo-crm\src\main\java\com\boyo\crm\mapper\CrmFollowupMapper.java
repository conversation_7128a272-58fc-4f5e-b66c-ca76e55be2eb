package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmFollowup;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * (CrmFollowup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-30 10:24:43
 */
@Tenant
public interface CrmFollowupMapper extends BaseMapper<CrmFollowup>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmFollowup 实例对象
     * @return 对象列表
     */
    List<CrmFollowup> selectCrmFollowupList(CrmFollowup crmFollowup);


}

