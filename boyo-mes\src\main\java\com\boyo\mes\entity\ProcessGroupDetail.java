package com.boyo.mes.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工序组详情(ProcessGroupDetail)实体类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Data
@TableName(value = "t_process_group_detail")
public class ProcessGroupDetail implements Serializable {
    private static final long serialVersionUID = 942989154906111238L;
            
    @TableId
    private Integer id;
    
    /**
    * 工序组id
    */
    @TableField(value="group_id")
    private Integer groupId;
    /**
    * 工序id
    */
    @TableField(value="process_id")
    private Integer processId;
    /**
    * 排序
    */
    @TableField(value="sort_num")
    private Integer sortNum;

    @TableField(exist = false)
    private Integer totalNumber;

    @TableField(exist = false)
    private List<ProductOrderDetail> productOrderDetailList;
    @TableField(exist = false)
    private int productNum;
    @TableField(exist = false)
    private String orderNum;
    @TableField(exist = false)
    private String processName;

}
