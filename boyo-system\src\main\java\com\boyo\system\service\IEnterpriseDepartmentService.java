package com.boyo.system.service;

import java.util.List;

import com.boyo.system.domain.EnterpriseDepartment;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 企业部门管理Service接口
 *
 * <AUTHOR>
 */
public interface IEnterpriseDepartmentService extends IService<EnterpriseDepartment> {
    /**
     * 根据条件查询查询企业部门管理列表
     *
     * @param enterpriseDepartment 企业部门管理
     * @return 企业部门管理集合
     */
    List<EnterpriseDepartment> selectEnterpriseDepartmentList(EnterpriseDepartment enterpriseDepartment);

    public List<String> getDeptAndChildren(String openid);

}
