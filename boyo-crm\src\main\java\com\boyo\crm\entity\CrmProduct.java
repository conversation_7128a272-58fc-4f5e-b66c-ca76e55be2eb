package com.boyo.crm.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import com.boyo.framework.annotation.PropertyMsg;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * CRM产品表(CrmProduct)实体类
 *
 * <AUTHOR>
 * @since 2021-11-24 15:52:38
 */
@Data
@TableName(value = "t_crm_product")
public class CrmProduct extends BoyoBaseEntity implements Serializable{
    private static final long serialVersionUID = -80864105108281651L;

    @TableId
    private Integer productId;

    /**
    * 产品名称
    */
    @TableField(value="name")
    @PropertyMsg(value="产品名称")
    private String name;
    /**
    * 产品编码
    */
    @TableField(value="num")
    @PropertyMsg(value="产品编码")
    private String num;
    /**
    * 单位id
    */
    @TableField(value="unit_id")
    @PropertyMsg(value="单位",type = "base")
    private Integer unitId;

    /**
     * 单位名称
     */
    @TableField(exist = false)
    private String unitName;
    /**
    * 价格
    */
    @TableField(value="price")
    @PropertyMsg(value="价格")
    private Double price;
    /**
    * 状态 0 下架 1 上架 2 删除
    */
    @TableField(value="status")
    private Integer status;
    /**
    * 产品分类ID
    */
    @TableField(value="category_id")
    @PropertyMsg(value="产品分类",type = "base")
    private Integer categoryId;

    /**
     * 分类名称
     */
    @TableField(exist = false)
    private String categoryName;
    /**
    * 产品描述
    */
    @TableField(value="description")
    @PropertyMsg(value="产品描述")
    private String description;
    /**
    * 创建人ID
    */
    @TableField(value="create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 负责人ID
    */
    @TableField(value="owner_user_id",fill = FieldFill.INSERT)
    private Long ownerUserId;
    /**
     * 负责人名称
     */
    @TableField(exist = false)
    private String ownerUserName;
    /**
    * 创建时间
    */
    @TableField(value="create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 更新时间
    */
    @TableField(value="update_time",fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
    * 批次
    */
    @TableField(value="batch_id")
    private String batchId;

}
