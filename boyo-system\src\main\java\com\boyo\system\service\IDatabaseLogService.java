package com.boyo.system.service;

import java.util.List;

import com.boyo.system.domain.DatabaseLog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 数据库变更管理Service接口
 *
 * <AUTHOR>
 */
public interface IDatabaseLogService extends IService<DatabaseLog> {
    /**
     * 根据条件查询查询数据库变更管理列表
     *
     * @param databaseLog 数据库变更管理
     * @return 数据库变更管理集合
     */
    List<DatabaseLog> selectDatabaseLogList(DatabaseLog databaseLog);

    void executeSql(Integer id);
}
