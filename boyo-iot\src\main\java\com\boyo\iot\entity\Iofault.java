package com.boyo.iot.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * IoT故障清单(Iofault)实体类
 *
 * <AUTHOR>
 * @since 2022-04-07 15:16:28
 */
@Data
@TableName(value = "iot_fault")
public class Iofault extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = -74308127809567094L;
            
    @TableId
    private Integer id;
    
    /**
    * 设备id
    */
    @TableField(value="equipment_id")
    private Integer equipmentId;
    /**
    * 故障属性ID
    */
    @TableField(value="fault_id")
    private Integer faultId;

    /**
     * 故障描述
     */
    @TableField(value = "fault_msg")
    private String faultMsg;
    /**
    * 发生时间
    */
    @TableField(value="create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 解除时间
    */
    @TableField(value="relieve_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date relieveTime;

    /**
     * 设备名称
     */
    @TableField(exist = false)
    private String equipmentName;

    /**
     * 故障名称
     */
    @TableField(exist = false)
    private String faultName;
    @TableField(value = "create_user_id")
    private Long createUserId;
    @TableField(value = "dept_id")
    private String deptId;

    /**
     * 对应工单
     */
    @TableField(exist = false)
    private WorkOrder workOrder;

}
