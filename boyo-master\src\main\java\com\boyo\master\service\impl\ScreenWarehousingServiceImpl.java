package com.boyo.master.service.impl;


import com.boyo.common.utils.DateUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.master.domain.ScreenWarehousing;
import com.boyo.master.mapper.ScreenWarehousingMapper;
import com.boyo.master.service.IScreenWarehousingService;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 大屏入库信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@Service
@Tenant
public class ScreenWarehousingServiceImpl implements IScreenWarehousingService {
    @Autowired
    private ScreenWarehousingMapper screenWarehousingMapper;

    /**
     * 查询大屏入库信息
     *
     * @param id 大屏入库信息主键
     * @return 大屏入库信息
     */
    @Override
    public ScreenWarehousing selectScreenWarehousingById(Long id) {
        return screenWarehousingMapper.selectScreenWarehousingById(id);
    }

    @Override
    public List<ScreenWarehousing> selectScreenWarehousingBetween(ScreenWarehousing screenWarehousing, String start, String end) {
        return screenWarehousingMapper.selectScreenWarehousingBetween(screenWarehousing, start, end);
    }

    @Override
    public List<ScreenWarehousing> selectScreenWarehousingList(ScreenWarehousing screenWarehousing) {
        return screenWarehousingMapper.selectScreenWarehousingList(screenWarehousing);
    }


    /**
     * 新增大屏入库信息
     *
     * @param screenWarehousing 大屏入库信息
     * @return 结果
     */
    @Override
    public int insertScreenWarehousing(ScreenWarehousing screenWarehousing) {
        screenWarehousing.setCreateTime(DateUtils.getNowDate());
        return screenWarehousingMapper.insertScreenWarehousing(screenWarehousing);
    }

    /**
     * 修改大屏入库信息
     *
     * @param screenWarehousing 大屏入库信息
     * @return 结果
     */
    @Override
    public int updateScreenWarehousing(ScreenWarehousing screenWarehousing) {
        screenWarehousing.setUpdateTime(DateUtils.getNowDate());
        return screenWarehousingMapper.updateScreenWarehousing(screenWarehousing);
    }

    /**
     * 批量删除大屏入库信息
     *
     * @param ids 需要删除的大屏入库信息主键
     * @return 结果
     */
    @Override
    public int deleteScreenWarehousingByIds(Long[] ids) {
        return screenWarehousingMapper.deleteScreenWarehousingByIds(ids);
    }

    /**
     * 删除大屏入库信息信息
     *
     * @param id 大屏入库信息主键
     * @return 结果
     */
    @Override
    public int deleteScreenWarehousingById(Long id) {
        return screenWarehousingMapper.deleteScreenWarehousingById(id);
    }

    @Override
    public Long getWarehousingCount(String start, String end) {
        return screenWarehousingMapper.getWarehousingCount(start, end);

    }
}
