package com.boyo.crm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.annotation.DataScope;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.crm.entity.*;
import com.boyo.crm.mapper.CrmBusinessProductMapper;
import com.boyo.crm.mapper.CrmBusinessTypeMapper;
import com.boyo.crm.mapper.CrmCustomerMapper;
import com.boyo.crm.util.ActionEnum;
import com.boyo.crm.util.ActionUtil;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.mapper.CrmBusinessMapper;
import com.boyo.crm.service.ICrmBusinessService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;

/**
 * 商机表(CrmBusiness)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-26 09:56:24
 */
@Service("crmBusinessService")
@AllArgsConstructor
public class CrmBusinessServiceImpl extends ServiceImpl<CrmBusinessMapper, CrmBusiness> implements ICrmBusinessService {
    private final CrmBusinessMapper crmBusinessMapper;
    private final CrmBusinessProductMapper businessProductMapper;
    private final CrmCustomerMapper crmCustomerMapper;
    private final CrmBusinessTypeMapper businessTypeMapper;
    private final ActionUtil actionUtil;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    @DataScope(columns = "owner_user_id,create_user_id")
    public List<CrmBusiness> selectCrmBusinessList(CrmBusiness crmBusiness) {
        return crmBusinessMapper.selectCrmBusinessList(crmBusiness);
    }

    @Override
    public void changeBusiness(CrmBusiness crmBusiness) {
        super.updateById(crmBusiness);
    }

    @Override
    public boolean save(CrmBusiness entity) {
        List<CrmBusinessProduct> productList = entity.getProductList();
        if(productList == null || productList.size() == 0){
            throw new CustomException("未添加产品信息，请先添加");
        }
        if(ObjectUtil.isNull(entity.getOwnerUserId())){
            entity.setOwnerUserId(SecurityUtils.getUserId());
        }
        if(super.save(entity)){
            for (CrmBusinessProduct product:productList) {
                product.setBusinessId(entity.getId());
                businessProductMapper.insert(product);
            }
        }else{
            return false;
        }
        actionUtil.editRecord(null,null, ActionEnum.BUSINESS,entity.getId(), null);
        return true;
    }

    @Override
//    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, propagation = Propagation.REQUIRED)
    public boolean updateById(CrmBusiness entity) {
        List<CrmBusinessProduct> productList = entity.getProductList();
        if (productList == null || productList.size() == 0) {
            throw new CustomException("未添加产品信息，请先添加");
        }
        QueryWrapper<CrmBusinessProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("business_id", entity.getId());
        businessProductMapper.delete(queryWrapper);
        for (CrmBusinessProduct product : productList) {
            product.setBusinessId(entity.getId());
            businessProductMapper.insert(product);
        }
        actionUtil.editRecord(super.getById(entity.getId()), entity, ActionEnum.BUSINESS, entity.getId(), CrmBusiness.class);
        return super.updateById(entity);
    }

    @Override
    public CrmBusiness getById(Serializable id) {
        CrmBusiness business = super.getById(id);
        CrmCustomer customer = crmCustomerMapper.selectById(business.getCustomerId());
        if (ObjectUtil.isNotNull(customer)) {
            business.setCustomerName(customer.getCustomerName());
        }
        CrmBusinessType type = businessTypeMapper.selectById(business.getTypeId());
        if (ObjectUtil.isNotNull(type)) {
            business.setTypeName(type.getName());
        }
        return business;
    }
}
