<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.EquipmentApplyMapper">

    <resultMap type="com.boyo.mes.entity.EquipmentApply" id="EquipmentApplyResult">
        <result property="id" column="id"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="equipmentName" column="equipment_name"/>
        <result property="equipmentCode" column="equipment_code"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipmentApplyList" parameterType="com.boyo.mes.entity.EquipmentApply"
            resultMap="EquipmentApplyResult">
        select tt.* from (select t1.*,t2.equipment_name,t2.equipment_code from (select
        *
        from t_equipment_apply
        <where>
            <if test="equipmentId != null">
                and equipment_id = #{equipmentId}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime}
            </if>
            <if test="endTime != null">and end_time = #{endTime}</if>
        </where>
        ) t1 left join iot_equipment t2 on t1.equipment_id = t2.id
        ) tt
        <where>
            <if test="userName != null and userName != ''">
                and tt.user_name like CONCAT('%',#{userName},'%')
            </if>
            <if test="equipmentCode != null and equipmentCode != ''">
                and tt.equipment_code like CONCAT('%',#{equipmentCode},'%')
            </if>
            <if test="equipmentName != null and equipmentName != ''">
                and tt.equipment_name like CONCAT('%',#{equipmentName},'%')
            </if>
        </where>
        order by tt.id desc
    </select>
</mapper>

