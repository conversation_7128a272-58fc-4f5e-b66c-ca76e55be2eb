package com.boyo.wms.service;

import java.util.List;

import com.boyo.wms.entity.WmsStock;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.wms.dto.WmsAllotDTO;
import com.boyo.wms.vo.StockWarnVO;
import com.boyo.wms.vo.WmsStockVO;

/**
 * 库存管理Service接口
 *
 * <AUTHOR>
 */
public interface IWmsStockService extends IService<WmsStock> {
    /**
     * 根据条件查询查询库存管理列表
     *
     * @param wmsStock 库存管理
     * @return 库存管理集合
     */
    List<WmsStockVO> selectWmsStockList(WmsStockVO wmsStock);

    List<WmsStockVO> selectWmsStockByMateriel(WmsStock wmsStock);

    List<StockWarnVO> selectStockWarn();

    /**
     * 库存调拨
     * @return
     */
    boolean allotMaterial(WmsAllotDTO allotDTO);

}
