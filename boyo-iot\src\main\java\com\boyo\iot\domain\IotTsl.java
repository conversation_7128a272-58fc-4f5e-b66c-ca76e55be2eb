package com.boyo.iot.domain;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.BoyoBaseEntity;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * IoT物模型
 * 表名 iot_tsl
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("Iot物模型")
@Data
@TableName("iot_tsl")
public class IotTsl extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;
    /**
     * 物模型名称
     */
    @ApiModelProperty("物模型名称")
    @TableField(value = "tsl_name")
    private String tslName;
    /**
     * 物模型编码
     */
    @ApiModelProperty("物模型编码")
    @TableField(value = "tsl_code")
    private String tslCode;

    /**
     * 类型描述
     */
    @TableField(value = "tsl_desc")
    private String tslDesc;

    /**
     * 类型图片
     */
    @TableField(value = "tsl_img")
    private String tslImg;

    /**
     * 类型排序
     */
    @TableField(value = "tsl_sort")
    private Integer tslSort;

    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;
    @TableField(value = "dept_id",fill = FieldFill.INSERT)
    private String deptId;

    /**
     * 设备数量
     */
    @TableField(exist = false)
    private Integer tslCount;
}
