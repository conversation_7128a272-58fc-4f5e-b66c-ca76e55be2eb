package com.boyo.master.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业自定义信息(EnterpriseCustom)实体类
 *
 * <AUTHOR>
 * @since 2022-03-07 20:19:52
 */
@Data
@TableName(value = "t_enterprise_custom")
public class EnterpriseCustom implements Serializable {
    private static final long serialVersionUID = 836784473331850772L;
            
    @TableId
    private Integer id;
    
    /**
    * 自定义系统名称
    */
    @TableField(value="custom_name")
    private String customName;
    /**
    * 自定义系统logo
    */
    @TableField(value="custom_logo")
    private String customLogo;

}
