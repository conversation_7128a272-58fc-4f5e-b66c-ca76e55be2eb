package com.boyo.crm.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * (CrmFollowup)实体类
 *
 * <AUTHOR>
 * @since 2022-03-30 10:24:43
 */
@Data
@TableName(value = "t_crm_followup")
public class CrmFollowup extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = -15952581241316966L;
            
    @TableId
    private Integer id;
    
    /**
    * 跟进方式
    */
    @TableField(value="followup_type")
    private Integer followupType;
    /**
    * 跟进内容
    */
    @TableField(value="followup_msg")
    private String followupMsg;
    /**
    * 下次跟进时间
    */
    @TableField(value="next_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date nextTime;
    /**
    * 跟进时间
    */
    @TableField(value="create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 跟进人
    */
    @TableField(value="create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 跟进类型
    */
    @TableField(value="action_type")
    private String actionType;
    /**
    * 跟进id
    */
    @TableField(value="action_id")
    private Integer actionId;

    @TableField(exist = false)
    private String followupTypeName;

    @TableField(exist = false)
    private String createUserName;

}
