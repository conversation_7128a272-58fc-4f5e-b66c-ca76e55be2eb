package com.boyo.mes.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.ProductProcess;
import com.boyo.mes.mapper.ProductProcessMapper;
import com.boyo.mes.service.IProductProcessService;
import java.util.List;

/**
 * 工序管理(ProductProcess)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Service("productProcessService")
@AllArgsConstructor
@Tenant
public class ProductProcessServiceImpl extends ServiceImpl<ProductProcessMapper, ProductProcess> implements IProductProcessService {
    private final ProductProcessMapper productProcessMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<ProductProcess> selectProductProcessList(ProductProcess productProcess) {
        return productProcessMapper.selectProductProcessList(productProcess);
    }

}
