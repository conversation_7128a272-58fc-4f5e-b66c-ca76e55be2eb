package com.boyo.system.service.impl;

import java.util.List;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.system.mapper.TSysCategoryMapper;
import com.boyo.system.domain.TSysCategory;
import com.boyo.system.service.ITSysCategoryService;

/**
 * 系统类别管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class TSysCategoryServiceImpl extends ServiceImpl<TSysCategoryMapper, TSysCategory> implements ITSysCategoryService {

    private final TSysCategoryMapper tSysCategoryMapper;

    /**
     * 查询系统类别管理列表
     *
     * @param tSysCategory 系统类别管理
     * @return tSysCategory 列表
     */
    @Override
    public List<TSysCategory> selectTSysCategoryList(TSysCategory tSysCategory) {
        return tSysCategoryMapper.selectTSysCategoryList(tSysCategory);
    }
}
