<template>
  <base-page :config="config">
    <span slot="operation" slot-scope="text, record">
      <a @click="$refs.createForm.handleUpdate($event,record.id)">
      <a-icon type="edit" />{{ $t('app.global.edit') }}
      </a>
      <a-divider type="vertical" />
      <a @click="handleDelete(record.id)"> <a-icon type="delete" />{{ $t('app.global.delete') }}</a>
    </span>
    <!-- 弹窗 -->
    <create-form ref="createForm" :statusOptions="statusOptions" @ok="getList" />
  </base-page>
</template>

<script>
import CreateForm from './modules/CreateForm'
import { delDatabaseLog, listDatabaseLog } from '@/api/databaseLog'

export default {
  components: {
    CreateForm,
  },
  data() {
    return {
      // 页面加载状态
      loading: false,
      // 数据列表
      list: [],
      // 表格数据总数
      total: 0,
      // 状态数据字典
      statusOptions: [],
    }
  },
  async created() {
    this.getList()
  },
  computed: {
    config() {
      return {
        loading: this.loading,
        query: {
          onQuery: this.getList,
          items: [
                        { label: 'OPENID', name: 'databaseOpenid' },
                        { label: '数据库版本号', name: 'databaseVersionCode' },
                        { label: '数据库版本名称', name: 'databaseVersionName' },
                        { label: '变更时间', name: 'databaseVersionTime' },
                        { label: '数据库版本变更语句', name: 'databaseChangeSql' },
                        { label: '数据库版本完整语句', name: 'databaseFullSql' },
                        { label: '创建时间', name: 'createTime' },
                        { label: '更新时间', name: 'updateTime' },
                      ],
        },
        action: {
          add: () => this.$refs.createForm.handleAdd(),
          delete: this.handleDelete,
        },
        table: {
          total: this.total,
          list: this.list,
          columns: [
               { 
                title: 'OPENID', 
                dataIndex: 'databaseOpenid',
                align: 'center',
            },
                { 
                title: '数据库版本号', 
                dataIndex: 'databaseVersionCode',
                align: 'center',
            },
                { 
                title: '数据库版本名称', 
                dataIndex: 'databaseVersionName',
                align: 'center',
            },
                { 
                title: '变更时间', 
                dataIndex: 'databaseVersionTime',
                align: 'center',
            },
                { 
                title: '数据库版本变更语句', 
                dataIndex: 'databaseChangeSql',
                align: 'center',
            },
                { 
                title: '数据库版本完整语句', 
                dataIndex: 'databaseFullSql',
                align: 'center',
            },
                { 
                title: '创建时间', 
                dataIndex: 'createTime',
                align: 'center',
            },
                { 
                title: '更新时间', 
                dataIndex: 'updateTime',
                align: 'center',
            },
                        {
              title: this.$t('app.global.operation'),
              dataIndex: 'operation',
              scopedSlots: { customRender: 'operation' },
              align: 'center',
            },
          ],
        },
      }
    },
  },
  methods: {
    /**
     * 查询企业租户数据库变更列表
     */
    async getList(queryParam) {
      this.loading = true
      if (queryParam !== undefined) {
        this.queryParam = queryParam
      }
      this.loading = true
      const response = await listDatabaseLog(this.queryParam)
      this.list = response.rows
      this.total = response.total
      this.loading = false
    },
    /**
     * 删除按钮操作
     */
    handleDelete(id) {
      
      const ids = id || this.ids
      this.$alert.confirm({
        content: this.$t('app.global.delete.content'),
        onOk: async () => {
        await delDatabaseLog(ids)
        this.getList()
        this.$alert.success(this.$t('app.global.delete.success'))
        },
      })
    },
  },
}
</script>
}
