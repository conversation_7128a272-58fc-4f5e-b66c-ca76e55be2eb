package com.boyo.web.controller.haihui;


import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.core.text.Convert;
import com.boyo.common.exception.CustomException;
import com.boyo.iot.domain.HistoryData;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.domain.IotTsl;
import com.boyo.iot.domain.IotTslAttr;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.iot.service.IIotTslService;
import com.boyo.iot.util.IoTDBUtil;
import com.dhcc.openapi.gateway.client.GatewayClient;
import com.github.pagehelper.PageHelper;
import io.swagger.annotations.ApiOperation;
import lombok.val;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;


@RestController
@RequestMapping("/haihui/screen")
public class HaiHuiScreenController extends BaseController {

    final String TENANT = "2db4bfe2cbe14269b410e4747073ee47";
    final int TIME_FRAME = 1000 * 60 * 10; // 10min

    @Autowired
    private IIotEquipmentService iotEquipmentService;

    @Autowired
    private IIotTslService iotTslService;

    @Autowired
    private IoTDBUtil ioTDBUtil;

    @Resource
    private GatewayClient gatewayClient;


    List<IotEquipment> iotEquipmentList = null;
    long iotTime = 0L;

    JSONObject utilizationRateForBankPre = null;//缓存银行大屏稼动率
    long utilizationRateForBankPreTime = 0L;

    @ApiOperation("车间信息")
    @GetMapping("/shopwork/list")
    public TableDataInfo list(IotTsl iotTsl) {
        startPage();
        List<IotTsl> list = iotTslService.selectIotTslList(iotTsl);
        List<IotTsl> ans = new ArrayList<>();

        // 定义一个不可变集合来存储不允许的 ID
        //13是4个公司总电表  22为兰官庄，17是5#车间
        //11海汇环保车间电表
        Set<Long> forbiddenIds = new HashSet<>(Arrays.asList(13L, 11L, 22L));
        for (IotTsl temp : list) {
            if (!forbiddenIds.contains(temp.getId())) {
                ans.add(temp);
            }
        }
        return getDataTable(ans);
    }

    @ApiOperation("查询物联网设备管理列表")
    @GetMapping("/device/list")
    public TableDataInfo listOnly(IotEquipment iotEquipment) {
        startPage();
        // 从数据库中查询符合条件的物联网设备列表
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
        List<IotEquipment> ans = new ArrayList<>();
        for (IotEquipment equipment : list) {
            // 获取设备详细信息
            final IotEquipment equipmentDetail = iotEquipmentService.getEquipmentDetail(equipment.getId().intValue());
            boolean flag = false;//产量为0的设备标识
            if (equipmentDetail != null) {
                if (equipmentDetail.getAttrList() != null && equipmentDetail.getAttrList().size() > 0) {
                    for (IotTslAttr attr : equipmentDetail.getAttrList()) {
                        if ("Number".equalsIgnoreCase(attr.getAttrCode())) {
                            final String lastVal = attr.getLastVal();
                            if (StrUtil.isNotBlank(lastVal) && !"0".equalsIgnoreCase(lastVal)) {
                                flag = true;
                            }
                            break;
                        }
                    }
                }
            }
            // 过滤掉产量为0的设备和特定类型的设备
            if (flag && 11L != equipment.getTslId() && 13L != equipment.getTslId()) {
                ans.add(equipment);
            }
        }
        return getDataTable(ans);
    }

    @ApiOperation("车间用电量")
    @GetMapping("/electricity/consumption")
    public AjaxResult electricity(String id, String workshopName) {
        JSONObject ans = new JSONObject();
        List<String> dates = new ArrayList<>();
        List<Integer> energys = new ArrayList<>();
        Date start = DateUtil.beginOfDay(new Date());
        Date end = new Date();
        if (id != null) {
            long energy = getEnergy(start, end, id);
            dates.add(DateUtil.format(start, "MM/dd"));
            energys.add((int) energy);
            for (int i = 0; i < 7; i++) {
                end = start;
                start = DateUtil.offsetDay(start, -1);
                long energytemp = getEnergy(start, end, id);
                dates.add(DateUtil.format(start, "MM/dd"));
                energys.add((int) energytemp);
            }
        } else {
            if (workshopName != null) {
                IotEquipment iotEquipment = new IotEquipment();
                iotEquipment.setEquipmentName(workshopName);
                List<IotEquipment> list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
                for (int i = 0; i < 7; i++) {
                    dates.add(DateUtil.format(start, "MM/dd"));
                    int tempEnergy = 0;
                    for (IotEquipment iotEquipment1 : list) {
                        long energy = getEnergy(start, end, iotEquipment1.getEquipmentCode());
                        tempEnergy += energy;
                    }
                    energys.add(tempEnergy);
                    end = DateUtil.endOfDay(DateUtil.offsetDay(end, -1));
                    start = DateUtil.beginOfDay(end);

                }


            }
        }

        Collections.reverse(dates);
        Collections.reverse(energys);
        ans.put("date", dates);
        ans.put("energy", energys);
        return AjaxResult.success(ans);
    }

    @ApiOperation("日用电量统计")
    @RequestMapping(value = "/elect/energy", method = RequestMethod.GET)
    public AjaxResult getPEnum() {
        JSONObject ans = new JSONObject();
        List<String> dates = new ArrayList<>();
        List<Integer> energys = new ArrayList<>();
        Date start = DateUtil.beginOfDay(new Date());
        Date end = DateUtil.date();
//        List<IotEquipment> list = getIotEquipmentList(new IotEquipment());
        long energytemp = 0L;
        energytemp = getEnergy(start, end, null);
//        for (IotEquipment temp : list) {
//            if (temp.getTslId() == 11L || temp.getTslId() == 13L) {
//                energytemp += getEnergy(start, end, temp.getEquipmentCode());
//            }
//        }
        dates.add(DateUtil.format(start, "MM/dd"));
        energys.add((int) energytemp);
        for (int i = 0; i < 6; i++) {
            energytemp = 0L;
            end = start;
            start = DateUtil.offsetDay(start, -1);
//            for (IotEquipment temp : list) {
//                if (temp.getTslId() == 11L || temp.getTslId() == 13L) {
//                    energytemp += getEnergy(start, end, temp.getEquipmentCode());
//                }
//            }
            energytemp = getEnergy(start, end, null);

            dates.add(DateUtil.format(start, "MM/dd"));
            energys.add((int) energytemp);
//            energys.add((int) getEnergy(start, end, null));
        }
        Collections.reverse(dates);
        Collections.reverse(energys);
        ans.put("date", dates);
        ans.put("energy", energys);


        DateTime begin = DateUtil.beginOfDay(new Date());
        end = DateUtil.date();
        List<Double> corpList = new ArrayList<>();

        final Map<String, Integer> cropRate1 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), null);

//        DateUtil.between(D)
//
//        if(end.)
        corpList.add((1.0 * cropRate1.get("workTime")) / (cropRate1.get("devicesNum") * 8 * 60));

        for (int i = 0; i < 6; i++) {
            begin = DateUtil.offsetDay(begin, -1);
            end = DateUtil.endOfDay(begin);
            Map<String, Integer> cropRate2 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), null);
//            System.out.println(DateUtil.formatDateTime(begin) + "--" + DateUtil.formatDateTime(end) + ":" + cropRate2);
            corpList.add((1.0 * cropRate2.get("workTime")) / (cropRate2.get("devicesNum") * 8 * 60));
        }
        Collections.reverse(corpList);
        ans.put("cropRate", corpList);


//
//        String end2 = DateUtil.format(DateUtil.beginOfDay(new Date()), "yyyy-MM-dd");
//        String start2 = DateUtil.format(DateUtil.offsetDay(DateUtil.parseDate(end2), -6), "yyyy-MM-dd");
//
//        Date s, e;
//        if (StrUtil.isEmpty(start2)) {
//            s = DateUtil.offset(new Date(), DateField.HOUR_OF_DAY, -1);
//        } else {
//            s = DateUtil.parse(start2);
//        }
//        if (StrUtil.isEmpty(end2)) {
//            e = new Date();
//        } else {
//            e = DateUtil.parse(end2);
//        }
//        final JSONObject cropRate = getCropRate(DateUtil.format(s, "yyyy-MM-dd"), DateUtil.format(e, "yyyy-MM-dd"), null, null);
////        logger.info("cropRate计算完成，耗时{}ms", System.currentTimeMillis() - a);
//
//
//        double dayWork = 0;
//        double dayNoWork = 0;
//        double nightWork = 0;
//        double nightNoWork = 0;
//        Date todayDate = new Date();
////
//        List<Double> rateList = new ArrayList<>();
//        for (int i = 6; i >= 0; i--) {
//            Date temp = DateUtil.offsetDay(todayDate, -i);
//            if (cropRate.containsKey(DateUtil.format(temp, "yyyy-MM-dd"))) {
//                String oneDayDate = cropRate.getString(DateUtil.format(temp, "yyyy-MM-dd"));
//                cn.hutool.json.JSONObject time2 = new cn.hutool.json.JSONObject(oneDayDate);
//                for (String key : time2.keySet()) {
//                    cn.hutool.json.JSONObject oneDevice = new cn.hutool.json.JSONObject(time2.get(key));
//                    cn.hutool.json.JSONObject night = new cn.hutool.json.JSONObject(oneDevice.get("night"));
//                    cn.hutool.json.JSONObject day = new cn.hutool.json.JSONObject(oneDevice.get("day"));
//                    try {
//                        final String s1 = day.get("0").toString();
//                        if (s1 != null) {
//                            dayNoWork += Double.parseDouble(s1);
//                        }
//                        if ((night.get("0").toString() != null)) {
//                            nightNoWork += Double.parseDouble(night.get("0").toString());
//                        }
//                        if ((day.get("1").toString()) != null) {
//                            dayWork += Double.parseDouble(day.get("1").toString());
//                        }
//                        if ((night.get("1").toString()) != null) {
//                            dayNoWork += Double.parseDouble(night.get("1").toString());
//                        }
//                    } catch (Exception exception) {
//                    }
//                }
////                dateList.add(DateUtil.format(temp, "MM/dd") + "白班");
//                double dayRate = Math.abs((dayWork / (dayWork + dayNoWork - (time2.size() * 60))));
//                rateList.add(dayRate);
//////                dateList.add(DateUtil.format(temp, "MM/dd") + "夜班");
////                double nightRate = (nightNoWork / (nightWork + nightNoWork - 6000));
////                if (nightRate > 1) {
////                    nightRate = 0.11;
////                } else {
////                    nightRate *= 10;
////                }
////                rateList.add(nightRate);
//
//            } else {
////                cropRate.put(time, 1);
//            }
//        }
////        logger.info("cropRate计算完成2，耗时{}ms", System.currentTimeMillis() - a);
//
//        cropRate.clear();
//        ans.put("cropRate", rateList);


        return AjaxResult.success(ans);
    }

    @ApiOperation("设备稼动率")
    @RequestMapping(value = "/device/utilizationRate", method = RequestMethod.GET)
    public AjaxResult getUtilizationRate(String type) {

        JSONObject ans = new JSONObject();
        List<String> dates = new ArrayList<>();
        List<Double> corpList = new ArrayList<>();

        DateTime begin = DateUtil.beginOfDay(new Date());
        Date end = DateUtil.date();

        String shift = "92,87,100,105,119,122,127,189,192,202,184,187,188,195,201,206,224"; // 主要设备ID

        final Map<String, Integer> cropRate1;
        //type 1：主要设备；其他：全部设备
        if ("1".equals(type)) {
            cropRate1 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), shift);
        } else {
            cropRate1 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), null);
        }


        dates.add(DateUtil.format(begin, "MM/dd"));
        corpList.add((1.0 * cropRate1.get("workTime")) / (cropRate1.get("devicesNum") * 8 * 60));

        for (int i = 0; i < 6; i++) {
            begin = DateUtil.offsetDay(begin, -1);
            end = DateUtil.endOfDay(begin);
            Map<String, Integer> cropRate2 = new HashMap<>();
            if ("1".equals(type)) {
                cropRate2 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), shift);
            } else {
                cropRate2 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), null);
            }
            dates.add(DateUtil.format(begin, "MM/dd"));
            corpList.add((1.0 * cropRate2.get("workTime")) / (cropRate2.get("devicesNum") * 8 * 60));
        }
        Collections.reverse(corpList);
        Collections.reverse(dates);
        ans.put("date", dates);
        ans.put("cropRate", corpList);


        return AjaxResult.success(ans);
    }


    @ApiOperation("数据概览")
    @GetMapping("/data/overview")
    public JSONObject list(IotEquipment iotEquipment) {
        JSONObject result = new JSONObject();
        int onlineCount = 0;
        int total = 0;
        startPage();
        long energy = 0L;

        List<IotEquipment> list = getIotEquipmentList(iotEquipment);


        if (list != null && list.size() > 0) {
            for (IotEquipment temp : list) {
                if (temp.getTslId() != 22L && temp.getTslId() != 13L) {
                    total++;
                    for (IotTslAttr attr : temp.getAttrList()) {
                        if ("TotalPower".equals(attr.getAttrCode())) {
                            final Date lastUpdateTime = attr.getLastUpdateTime();
                            if (lastUpdateTime != null) {
                                final long between = DateUtil.between(lastUpdateTime, DateUtil.date(), DateUnit.MINUTE);
                                if (between <= 10) {
                                    onlineCount++;
                                    break;
                                }
                            }
                        }
                        if ("Power_ON".equals(attr.getAttrCode()) && attr.getLastUpdateTime() != null) {
                            final Date lastUpdateTime = attr.getLastUpdateTime();
                            if (lastUpdateTime != null) {
                                final long between = DateUtil.between(lastUpdateTime, DateUtil.date(), DateUnit.MINUTE);
                                if (between <= 10) {
                                    onlineCount++;
                                    break;
                                }
                            }
                        }
                    }
                }
//                Date start = DateUtil.beginOfDay(new Date());
//                Date end = new Date();
//                if (temp.getTslId() == 13L || temp.getTslId() == 11L) {
//                    energy = energy + getEnergy(start, end, temp.getEquipmentCode());
//                }
            }
            Date start = DateUtil.beginOfDay(new Date());
            Date end = new Date();
            energy = getEnergy(start, end, null);

            total++;//添加海汇环保总电表
            onlineCount++;//添加海汇环保总电表永远在线
        }


        result.put("todayEnergy", energy);
        result.put("devicesCount", total);
        result.put("onlineCount", onlineCount);
        result.put("offlineCount", total - onlineCount);

        return result;
    }

    @ApiOperation("设备运行情况")
    @GetMapping("/device/work/status")
    public JSONObject workStatus(IotEquipment iotEquipment) {
        JSONObject result = new JSONObject();
        float onlineCount = 0;
        float offlineCount = 0;
        float errorlineCount = 0;
        long openTime = 0;
        long stopTime = 0;
        long errorTime = 0;
        float total = 0;

        PageHelper.startPage(1, 1000);
        List<IotEquipment> list = getIotEquipmentList(iotEquipment);
        if (list != null && list.size() > 0) {
            for (IotEquipment temp : list) {
                if (temp.getTslId() == 22L || temp.getTslId() == 13L) {
                    continue;
                }
                total++;
                for (IotTslAttr attr : temp.getAttrList()) {
                    if ("TotalPower".equals(attr.getAttrCode())) {
                        final Date lastUpdateTime = attr.getLastUpdateTime();
                        final long between = DateUtil.between(lastUpdateTime, DateUtil.date(), DateUnit.MINUTE);
                        if (between <= 10) {
                            onlineCount++;
                        } else {
                            offlineCount++;
                        }
                        break;
                    }
                    if ("开机".equals(attr.getAttrName())) {
                        if (attr.getLastUpdateTime() == null || "null".equals(attr.getLastUpdateTime())) {
                            errorlineCount++;
                            break;
                        }

                        if ("0".equals(attr.getLastVal())) {
                            offlineCount++;
                            break;
                        } else if ("1".equals(attr.getLastVal())) {
                            final Date lastUpdateTime = attr.getLastUpdateTime();
                            final long between = DateUtil.between(lastUpdateTime, DateUtil.date(), DateUnit.MINUTE);
                            if (between <= 10) {
                                onlineCount++;
                            }
                            break;
                        } else {
                            errorlineCount++;
                            break;
                        }
                    }
                }

                JSONArray time = getTime(temp.getEquipmentCode());
                if (time != null) {
                    for (int i = 0; i < time.size(); i++) {
                        JSONObject jsonObject = time.getJSONObject(i);
                        final Object power_on = jsonObject.get("Power_ON");
                        final Object productStatus = jsonObject.get("Product_Status");
                        if (productStatus != null && !"".equals(productStatus)
                                && "0.0".equals(power_on.toString()) && "1.0".equals(productStatus.toString())) {
                            errorTime++;
//                            System.out.println(temp.getEquipmentName() + "================" + jsonObject.get("time"));
                            continue;
                        }
                        if (power_on != null && !"".equals(power_on)) {
                            if ("1.0".equals(power_on.toString())) {
                                openTime++;
                            }
                            if ("0.0".equals(power_on.toString())) {
                                stopTime++;
                            }
                        } else {
                            stopTime++;
//                            errorTime++;
                        }
                    }
                }
            }
        }
//        float total = offlineCount + onlineCount + errorlineCount;

        result.put("onlineCount", onlineCount / total);
        result.put("offlineCount", (total - onlineCount) / total);
        result.put("errorlineCount", errorlineCount / total);

//        logger.info(onlineCount + ":" + offlineCount + ":" + errorlineCount + ":" + total);

        result.put("openTime", (int) (openTime / 60));
        result.put("stopTime", (int) (stopTime / 60));
        result.put("errorTime", (int) (errorTime / 60));


        return result;
    }


    @ApiOperation("生产次数/产品趋势图")
    @GetMapping("/production/frequency")
    public AjaxResult frequency(String device) {
        String tag = "Number";
        Date start = DateUtil.beginOfDay(new Date());
        Date end = new Date();
        JSONObject ans = new JSONObject();
        List<String> dates = new ArrayList<>();
        List<Integer> frequency = new ArrayList<>();

        for (int i = 0; i < 7; i++) {
            if (i != 0) {
                end = start;
                start = DateUtil.offsetDay(start, -1);
            }
            try {
                List<HistoryData> list = ioTDBUtil.listData(TENANT, device, tag, DateUtil.formatDateTime(start), DateUtil.formatDateTime(end));
                if (list != null && list.size() > 0) {
                    IotTslAttr attr = new IotTslAttr();
                    IotEquipment equipment = iotEquipmentService.getEquipmentByCode(device);
                    if (equipment != null) {
                        List<IotTslAttr> attrs = iotEquipmentService.getEquipmentDetail(Convert.toInt(equipment.getId())).getAttrList();
                        if (attrs != null && attrs.size() > 0) {
                            for (IotTslAttr temp : attrs) {
                                if (temp.getAttrCode().equalsIgnoreCase(tag)) {
                                    attr = temp;
                                    break;
                                }
                            }
                        }
                    }
                    if (attr != null && (attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("integer"))) {
                        for (HistoryData data : list) {
                            if (ObjectUtil.isNotNull(data.getVal()) && NumberUtil.isNumber(Convert.toStr(data.getVal()))) {
                                data.setVal(NumberUtil.round(Convert.toDouble(data.getVal()) * attr.getAttrMultiple(), 2));
                            }
                        }
                    }
                }
                double begin = 0;
                double stop = 0;
                for (int j = list.size() - 1; j >= 0; j--) {
                    final HistoryData historyData = list.get(j);
                    if (!"null".equals(historyData.getVal())) {
//                        doubleList.add(Double.parseDouble(historyData.getVal().toString()));
                        stop = Double.parseDouble(historyData.getVal().toString());
                        break;
                    }
                }
                for (int j = 0; j < list.size(); j++) {
                    final HistoryData historyData = list.get(j);
                    if (!"null".equals(historyData.getVal())) {
//                        doubleList.add(Double.parseDouble(historyData.getVal().toString()));
                        begin = Double.parseDouble(historyData.getVal().toString());
                        break;
                    }
                }
                dates.add(DateUtil.format(start, "MM/dd"));
                frequency.add((int) (stop - begin));
            } catch (IoTDBConnectionException ex) {
                ex.printStackTrace();
            } catch (StatementExecutionException ex) {
                ex.printStackTrace();
            }
        }

        ans.put("date", dates);
        ans.put("frequency", frequency);
        Collections.reverse(frequency);
        Collections.reverse(dates);

        return AjaxResult.success(ans);

    }

    @ApiOperation("电表获取")
    @GetMapping("/electricity/meter/list")
    public AjaxResult electricityMeterList(IotEquipment iotEquipment) {
        iotEquipment.setTslId(11L);
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
        iotEquipment.setTslId(13L);
        List<IotEquipment> list2 = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
        if (list2 != null && list2.size() > 0) {
            list.add(0, list2.get(0));
        }
        return AjaxResult.success(list);
    }

    @ApiOperation("电表获取2")
    @GetMapping("/electricity/meter/list2")
    public AjaxResult electricityMeterList2() {
        List<IotEquipment> list = new ArrayList<>();
//        String[] equipmentNames = {"海汇环保总电表", "1#厂房", "3#厂房", "6#厂房", "7#厂房", "8#厂房", "9#厂房"};
        String[] equipmentNames = {"海汇环保总电表", "下料车间", "袋笼车间", "喷涂车间", "抛丸车间", "铆焊车间", "精加工车间"};

        for (String name : equipmentNames) {
            IotEquipment iotEquipment = new IotEquipment();
            iotEquipment.setEquipmentName(name);
            list.add(iotEquipment);
        }
        return AjaxResult.success(list);

    }


    @ApiOperation("设备总览")
    @GetMapping("/device/overview")
    public AjaxResult deviceOverview(IotEquipment iotEquipment) {

        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);

        List<JSONObject> ans = new ArrayList<>();
        list.forEach(item -> {
            JSONObject ansItem = new JSONObject();
            ansItem.put("workStatus", "-");
            ansItem.put("deviceName", "-");
            ansItem.put("workCount", "-");
            ansItem.put("cycle", "-");

            ansItem.put("deviceName", item.getEquipmentName());
            final IotEquipment equipmentDetail = iotEquipmentService.getEquipmentDetail(item.getId().intValue());
            if (equipmentDetail != null) {
                if (equipmentDetail.getAttrList() != null && equipmentDetail.getAttrList().size() > 0) {
                    for (IotTslAttr attr : equipmentDetail.getAttrList()) {
                        if ("Power_ON".equalsIgnoreCase(attr.getAttrCode())) {
                            final String lastVal = attr.getLastVal();
                            if ("1.0".equals(lastVal) || "1".equals(lastVal)) {
                                ansItem.put("workStatus", "开机");
                            } else if ("0.0".equals(lastVal) || "0".equals(lastVal)) {
                                ansItem.put("workStatus", "关机");
                            } else {
                                ansItem.put("workStatus", "未知");
                            }
//                            break;
                        } else if ("Number".equalsIgnoreCase(attr.getAttrCode())) {
                            final String lastVal = attr.getLastVal();
                            ansItem.put("workCount", lastVal);
//                            break;
                        } else if ("Cycle".equalsIgnoreCase(attr.getAttrCode())) {
                            final String lastVal = attr.getLastVal();
                            ansItem.put("cycle", lastVal);
//                            break;
                        }
                    }
                }
            }
            ans.add(ansItem);
        });
        return AjaxResult.success(ans);
    }

    @ApiOperation("当班稼动率")
    @GetMapping("/cropRate/middle")
    private AjaxResult cropRate1(String equipmentCode, String start, String end) {

        JSONObject cropRate = new JSONObject();
        List<String> dateList = new ArrayList<>();
        List<Double> rateList = new ArrayList<>();

        Date startDate = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -4));


        startDate = DateUtil.offset(startDate, DateField.HOUR_OF_DAY, 8);
        Date endDate = DateUtil.offset(startDate, DateField.HOUR_OF_DAY, 12);
        for (int i = 0; i < 8; i++) {
            if (i != 0) {
                startDate = DateUtil.offset(startDate, DateField.HOUR_OF_DAY, 12);
                endDate = DateUtil.offset(endDate, DateField.HOUR_OF_DAY, 12);
            }
            final Map<String, Integer> cropRate1 = getCropRate(startDate.toString(), endDate.toString(), null);
            if (i % 2 == 0) {
                dateList.add(DateUtil.format(startDate, "MM/dd") + "白班");
                rateList.add((1.0 * cropRate1.get("workTime")) / (cropRate1.get("devicesNum") * 8 * 60));
            } else {
                dateList.add(DateUtil.format(startDate, "MM/dd") + "夜班");
                rateList.add((1.0 * cropRate1.get("workTime")) / (cropRate1.get("devicesNum") * 8 * 60));
            }
        }


//        end = DateUtil.format(DateUtil.beginOfDay(new Date()), "yyyy-MM-dd");
//        start = DateUtil.format(DateUtil.offsetDay(DateUtil.parseDate(end), -5), "yyyy-MM-dd");
//
//        Date s, e;
//        if (StrUtil.isEmpty(start)) {
//            s = DateUtil.offset(new Date(), DateField.HOUR_OF_DAY, -1);
//        } else {
//            s = DateUtil.parse(start);
//        }
//        if (StrUtil.isEmpty(end)) {
//            e = new Date();
//        } else {
//            e = DateUtil.parse(end);
//        }
//        final JSONObject cropRate = getCropRate(DateUtil.format(s, "yyyy-MM-dd"), DateUtil.format(e, "yyyy-MM-dd"), null, null);
////        System.out.println(cropRate.toString());
//
//        double dayWork = 0;
//        double dayNoWork = 0;
//        double nightWork = 0;
//        double nightNoWork = 0;
//        Date todayDate = new Date();
//        List<String> dateList = new ArrayList<>();
//        List<Double> rateList = new ArrayList<>();
//        for (int i = 4; i > 0; i--) {
//            Date temp = DateUtil.offsetDay(todayDate, -i);
//            if (cropRate.containsKey(DateUtil.format(temp, "yyyy-MM-dd"))) {
//                String oneDayDate = cropRate.getString(DateUtil.format(temp, "yyyy-MM-dd"));
//                cn.hutool.json.JSONObject time2 = new cn.hutool.json.JSONObject(oneDayDate);
//                for (String key : time2.keySet()) {
//                    cn.hutool.json.JSONObject oneDevice = new cn.hutool.json.JSONObject(time2.get(key));
//                    cn.hutool.json.JSONObject night = new cn.hutool.json.JSONObject(oneDevice.get("night"));
//                    cn.hutool.json.JSONObject day = new cn.hutool.json.JSONObject(oneDevice.get("day"));
//                    try {
//                        final String s1 = day.get("0").toString();
//                        if (s1 != null) {
//                            dayNoWork += Double.parseDouble(s1);
//                        }
//                        if ((night.get("0").toString() != null)) {
//                            nightNoWork += Double.parseDouble(night.get("0").toString());
//                        }
//                        if ((day.get("1").toString()) != null) {
//                            dayWork += Double.parseDouble(day.get("1").toString());
//                        }
//                        if ((night.get("1").toString()) != null) {
//                            nightWork += Double.parseDouble(night.get("1").toString());
//                        }
//                    } catch (Exception exception) {
////                        exception.printStackTrace();
//                    }
//                }
////                System.out.println(DateUtil.format(temp, "yyyy-MM-dd") + "白班工作:" + dayWork + "白班不工作:" + dayNoWork);
////                System.out.println(DateUtil.format(temp, "yyyy-MM-dd") + "夜班工作:" + nightWork + "夜班不工作:" + nightNoWork);
//
//
//                dateList.add(DateUtil.format(temp, "MM/dd") + "白班");
//                double dayRate = (dayWork / (dayWork + dayNoWork - (time2.size() * 60)));
//
//                rateList.add(dayRate);
//                dateList.add(DateUtil.format(temp, "MM/dd") + "夜班");
//                double nightRate = (nightWork / (nightWork + nightNoWork - (time2.size() * 60)));
//                rateList.add(nightRate);
//
//            } else {
////                cropRate.put(time, 1);
//            }
//        }
//        cropRate.clear();
        cropRate.put("date", dateList);
        cropRate.put("rate", rateList);
        return AjaxResult.success(cropRate);
    }

    private JSONArray getTime(String device) {
        Date s, e;
        s = DateUtil.beginOfDay(new Date());
        e = new Date();
        JSONArray list = null;
        try {
            JSONObject result = new JSONObject();
            IotEquipment selectEquipment = new IotEquipment();
            selectEquipment.setEquipmentCode(device);
            IotEquipment equipment = iotEquipmentService.selectIotEquipmentList(selectEquipment).get(0);
//            IotEquipment equipment = iotEquipmentService.getEquipmentByCode(device);
            if (equipment != null) {
                List<IotTslAttr> attrs = iotEquipmentService.getEquipmentDetail(Convert.toInt(equipment.getId())).getAttrList();
                result.put("attrs", attrs);
                List<String> codes = new ArrayList<>();
                for (IotTslAttr attr : attrs) {
                    codes.add(attr.getAttrCode());
                }
                list = ioTDBUtil.listDataHistoryV2(TENANT, device, codes, DateUtil.formatDateTime(s), DateUtil.formatDateTime(e));

                for (IotTslAttr attr : attrs) {
                    if (attr != null && (attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("integer"))) {
                        for (int i = 0; i < list.size(); i++) {
                            JSONObject temp = list.getJSONObject(i);
                            for (String tag : temp.keySet()) {
                                if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                                    try {
                                        temp.put(tag, NumberUtil.round(Convert.toDouble(temp.getString(tag)) * attr.getAttrMultiple(), 2));
                                        list.set(i, temp);
                                        break;
                                    } catch (Exception e1) {

                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return list;
    }

    private long getEnergy(Date s, Date e, String device) {
        long a = System.currentTimeMillis();
        if (device == null) {
            device = "100001";
        }
        try {
            IotEquipment selectEquipment = new IotEquipment();
            selectEquipment.setEquipmentCode(device);
            IotEquipment equipment = iotEquipmentService.selectIotEquipmentList(selectEquipment).get(0);
            if (equipment != null) {
                List<IotTslAttr> attrs = iotEquipmentService.getEquipmentDetail(Convert.toInt(equipment.getId())).getAttrList();
                List<String> codes = new ArrayList<>();
                for (IotTslAttr attr : attrs) {
                    codes.add(attr.getAttrCode());
                }
                JSONArray list1 = ioTDBUtil.listDataHistoryV2(TENANT, device, codes, DateUtil.formatDateTime(s), DateUtil.formatDateTime(e));

                long startEnergy = 0L;
                long endEnergy = 0L;
                for (int i = 0; i < list1.size(); i++) {
                    final Object o = list1.get(i);
                    final JSONObject jsonObject = JSONObject.parseObject(o.toString());

                    Object totalEnergy = jsonObject.get("TotalEnergy");
                    if (totalEnergy == null) {
                        totalEnergy = jsonObject.get("TotalPower");
                    }
                    if ("1_E676E6911E4B03B80C2DFAFD4".equals(device)) { //热电发电电表特殊处理
                        totalEnergy = jsonObject.get("ReTotalPower");
                    }

                    if (totalEnergy != null && !"null".equalsIgnoreCase(totalEnergy.toString())) {
                        try {
                            startEnergy = Convert.toLong(totalEnergy);
                            // 如果开始值小于0，则不进行计算
                            if (startEnergy <= 0) {
                                continue;
                            }
                        } catch (Exception exception) {
                            exception.printStackTrace();
                        }
                        break;
                    }
                }
                for (int i = list1.size() - 1; i >= 0; i--) {
                    final Object o = list1.get(i);
                    final JSONObject jsonObject = JSONObject.parseObject(o.toString());
                    Object totalEnergy = jsonObject.get("TotalEnergy");
                    if (totalEnergy == null) {
                        totalEnergy = jsonObject.get("TotalPower");
                    }
                    if ("1_E676E6911E4B03B80C2DFAFD4".equals(device)) { //热电发电电表特殊处理
                        totalEnergy = jsonObject.get("ReTotalPower");
                    }
                    if (totalEnergy != null && !"null".equalsIgnoreCase(totalEnergy.toString())) {
                        try {
                            endEnergy = Convert.toLong(totalEnergy);
                            // 如果结束值小于开始值，则不进行计算
                            if (endEnergy <= 0) {
                                continue;
                            }
                            // 如果开始值大于结束值，则不进行计算
                            if (endEnergy < startEnergy) {
                                continue;
                            }
                        } catch (Exception exception) {
                            exception.printStackTrace();
                        }
                        break;
                    }
                }
//                logger.info("设备{}  {}-{} 的电量计算完成{}，耗时{}ms", device, s.toString(), e.toString(), (endEnergy - startEnergy), System.currentTimeMillis() - a);
//                logger.info("开始{}  结束{}",  startEnergy,endEnergy);
                return (endEnergy - startEnergy);
            }
        } catch (IoTDBConnectionException ex) {
            ex.printStackTrace();
        } catch (StatementExecutionException ex) {
            ex.printStackTrace();
        }
        return 0;
    }

    private JSONObject getCropRate(String start, String end, String equipmentCode, String shift) {
        JSONObject result = new JSONObject();
        //获取白班时间
        String dayStartTime = "08:00:00";
        String dayEndTime = "20:00:00";
        //晚班时间
        String nightStartTime = "20:00:00";
        String nightEndTime = "08:00:00";

        String tag = "Product_Status";
//        获取所有设备清单
        IotEquipment iotEquipment = new IotEquipment();
        iotEquipment.setEquipmentCode(equipmentCode);
        List<IotEquipment> list = getIotEquipmentList(iotEquipment);
        if (list != null && list.size() > 0) {

        } else {
            throw new CustomException("暂无可用设备");
        }

//        查询一天的
        if (start.equalsIgnoreCase(end)) {
//            白班
            if (StrUtil.isEmpty(shift) || shift.equalsIgnoreCase("day")) {
                String dayStart = start + " " + dayStartTime;
                String dayEnd = start + " " + dayEndTime;
                // 初始化标志变量，用于表示当前是否在工作时间内
                boolean in = false;
                // 当开始日期与当前日期相同时，判断当前时间是否在日班工作时间内
                if (start.equalsIgnoreCase(DateUtil.formatDate(new Date()))) {
                    // 如果当前时间在日班开始到结束时间内，更新标志变量和结束时间
                    if (DateUtil.isIn(new Date(), DateUtil.parseDateTime(dayStart), DateUtil.parseDateTime(dayEnd))) {
                        in = true;
                        dayEnd = DateUtil.formatDateTime(new Date());
                    }
                }
                // 判断设备编码是否为空，如果为空则赋予默认值
                if (StrUtil.isEmpty(equipmentCode)) {
                    equipmentCode = "**";
                }
                JSONArray array = getStatusTimeService(equipmentCode, tag, TENANT, dayStart, dayEnd, false);
                int sort = 0;
                for (IotEquipment temp : list) {
                    JSONObject object = new JSONObject();
                    if (!result.containsKey(start)) {
                        result.put(start, new JSONObject());
                    }
                    object = result.getJSONObject(start);
                    if (!object.containsKey(temp.getEquipmentName())) {
                        object.put(temp.getEquipmentName(), new JSONObject());
                        object.getJSONObject(temp.getEquipmentName()).put("sort", sort);
                        sort++;
                    }
                    object = object.getJSONObject(temp.getEquipmentName());
                    for (int i = 0; i < array.size(); i++) {
                        JSONObject obj = array.getJSONObject(i);
                        if (obj.getString("key").equalsIgnoreCase(temp.getEquipmentCode())) {
                            obj.put("in", in);
                            obj.put("long", DateUtil.between(new Date(), DateUtil.parseDateTime(dayStart), DateUnit.MINUTE));
                            object.put("day", obj);
                            break;
                        }
                    }
                }
            }
            //            夜班
            if (StrUtil.isEmpty(shift) || shift.equalsIgnoreCase("night")) {
                String nightStart = start + " " + nightStartTime;
                String nightEnd = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), 1)) + " " + nightEndTime;
                boolean in = false;
                if (start.equalsIgnoreCase(DateUtil.formatDate(new Date()))) {
                    if (DateUtil.isIn(new Date(), DateUtil.parseDateTime(nightStart), DateUtil.parseDateTime(nightEnd))) {
                        nightEnd = DateUtil.formatDateTime(new Date());
                        in = true;
                    }
                }
                if (StrUtil.isEmpty(equipmentCode)) {
                    equipmentCode = "**";
                }
                JSONArray array = getStatusTimeService(equipmentCode, tag, TENANT, nightStart, nightEnd, false);
                int sort = 0;
                for (IotEquipment temp : list) {
                    JSONObject object = new JSONObject();
                    if (!result.containsKey(start)) {
                        result.put(start, new JSONObject());
                    }
                    object = result.getJSONObject(start);
                    if (!object.containsKey(temp.getEquipmentName())) {
                        object.put(temp.getEquipmentName(), new JSONObject());
                        object.getJSONObject(temp.getEquipmentName()).put("sort", sort);
                        sort++;
                    }
                    object = object.getJSONObject(temp.getEquipmentName());
                    for (int i = 0; i < array.size(); i++) {
                        JSONObject obj = array.getJSONObject(i);
                        if (obj.getString("key").equalsIgnoreCase(temp.getEquipmentCode())) {
                            obj.put("in", in);
                            obj.put("long", DateUtil.between(new Date(), DateUtil.parseDateTime(nightStart), DateUnit.MINUTE));
                            object.put("night", obj);
                            break;
                        }
                    }
                }
//                result.put(start + "@night", getStatusTimeService(deviceStr, tag, TENANT, nightStart, nightEnd, false));
            }
        } else {
//            查询多天
            long days = DateUtil.betweenDay(DateUtil.parseDate(start), DateUtil.parseDate(end), true) + 1;
            for (int i = 0; i < days; i++) {
                if (StrUtil.isEmpty(shift) || shift.equalsIgnoreCase("day")) {
                    String dayStart = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)) + " " + dayStartTime;
                    String dayEnd = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)) + " " + dayEndTime;
                    if (DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)).equalsIgnoreCase(DateUtil.formatDate(new Date()))) {
                        if (DateUtil.isIn(new Date(), DateUtil.parseDateTime(dayStart), DateUtil.parseDateTime(dayEnd))) {
                            dayEnd = DateUtil.formatDateTime(new Date());
                        }
                    }
                    if (StrUtil.isEmpty(equipmentCode)) {
                        equipmentCode = "**";
                    }
                    JSONArray array = getStatusTimeService(equipmentCode, tag, TENANT, dayStart, dayEnd, false);
                    for (IotEquipment temp : list) {
                        JSONObject object = new JSONObject();
                        if (!result.containsKey(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)))) {
                            result.put(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)), new JSONObject());
                        }
                        object = result.getJSONObject(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)));
                        if (!object.containsKey(temp.getEquipmentName())) {
                            object.put(temp.getEquipmentName(), new JSONObject());
                        }
                        object = object.getJSONObject(temp.getEquipmentName());
                        for (int j = 0; j < array.size(); j++) {
                            JSONObject obj = array.getJSONObject(j);
                            if (obj.getString("key").equalsIgnoreCase(temp.getEquipmentCode())) {
                                object.put("day", obj);
                                break;
                            }
                        }
                    }
                }
                //            夜班
                if (StrUtil.isEmpty(shift) || shift.equalsIgnoreCase("night")) {
                    String nightStart = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)) + " " + nightStartTime;
                    String nightEnd = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), (i + 1))) + " " + nightEndTime;
                    if (DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)).equalsIgnoreCase(DateUtil.formatDate(new Date()))) {
                        if (DateUtil.isIn(new Date(), DateUtil.parseDateTime(nightStart), DateUtil.parseDateTime(nightEnd))) {
                            nightEnd = DateUtil.formatDateTime(new Date());
                        }
                    }
                    JSONArray array = getStatusTimeService(equipmentCode, tag, TENANT, nightStart, nightEnd, false);
                    for (IotEquipment temp : list) {
                        JSONObject object = new JSONObject();
                        if (!result.containsKey(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)))) {
                            result.put(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)), new JSONObject());
                        }
                        object = result.getJSONObject(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)));
                        if (!object.containsKey(temp.getEquipmentName())) {
                            object.put(temp.getEquipmentName(), new JSONObject());
                        }
                        object = object.getJSONObject(temp.getEquipmentName());
                        for (int j = 0; j < array.size(); j++) {
                            JSONObject obj = array.getJSONObject(j);
                            if (obj.getString("key").equalsIgnoreCase(temp.getEquipmentCode())) {
                                object.put("night", obj);
                                break;
                            }
                        }
                    }
                }
            }

        }

        return result;
    }

    public JSONArray getStatusTimeService(String deviceStr, String tag, String tenant, String start, String end,
                                          boolean sum) {
        try {
            JSONArray result = new JSONArray();
            JSONObject object = new JSONObject();
            List<HistoryData> onlyList = new ArrayList<>();
            List<HistoryData> list = ioTDBUtil.getDataListFillWithZero(tenant, deviceStr, tag, start, end);
            for (HistoryData data : list) {
                data.setDevice(data.getDevice().split("\\.")[2]);
            }
            if (list != null && list.size() > 0) {
                HistoryData temp = list.get(0);
                onlyList.add(temp);
                for (int i = 1; i < list.size(); i++) {
                    HistoryData obj = list.get(i);
                    if (!obj.getDevice().equalsIgnoreCase(temp.getDevice())) {
                        onlyList.add(list.get(i - 1));
                        object.put(temp.getDevice(), onlyList);
                        onlyList = new ArrayList<>();
                        temp = obj;
                        onlyList.add(obj);
                    }
                    if (NumberUtil.compare(Convert.toDouble(obj.getVal()), Convert.toDouble(temp.getVal())) == 0) {
                    } else {
                        temp = obj;
                        onlyList.add(temp);
                    }
                }
                onlyList.add(list.get(list.size() - 1));
                object.put(temp.getDevice(), onlyList);
            }
            if (object != null) {
                for (String device : object.keySet()) {
                    JSONObject json = new JSONObject();
                    json.put("key", device);
                    json.put("0", 0);
                    json.put("1", 0);
                    json.put("times", 0);
                    List<HistoryData> array = object.getObject(device, List.class);
                    if (array != null && array.size() > 1) {
                        HistoryData temp = array.get(0);
                        for (int i = 1; i < array.size(); i++) {
                            HistoryData obj = array.get(i);
                            double times = 0d;
                            String key = Convert.toStr(Convert.toInt(Convert.toDouble(temp.getVal())));
                            if (json.containsKey(key)) {
                                times = json.getDoubleValue(key);
                            }
                            if (Convert.toInt(Convert.toDouble(temp.getVal())) == 0) {
                                json.put("times", (json.getIntValue("times") + 1));
                            }
                            times = times + DateUtil.between(DateUtil.parseDateTime(temp.getTime()), DateUtil.parseDateTime(obj.getTime()), DateUnit.MINUTE);
                            temp = obj;
                            json.put(key, times);
                        }
                    }
                    result.add(json);
                }
            }

            return result;
        } catch (IoTDBConnectionException ex) {
            ex.printStackTrace();
        } catch (StatementExecutionException ex) {
            ex.printStackTrace();
        }
        return null;
    }


    @ApiOperation("测试")
    @GetMapping("/cropRate/test")
    private AjaxResult cropRate1() {
        JSONObject ans = new JSONObject();
        DateTime begin = DateUtil.beginOfDay(new Date());
        DateTime end = DateUtil.date();
        List<Double> corpList = new ArrayList<>();

        final Map<String, Integer> cropRate1 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), null);
        corpList.add((1.0 * cropRate1.get("workTime")) / (cropRate1.get("devicesNum") * 8 * 60));

        for (int i = 0; i < 6; i++) {
            begin = DateUtil.offsetDay(begin, -1);
            end = DateUtil.endOfDay(begin);
            Map<String, Integer> cropRate2 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), null);
            System.out.println(DateUtil.formatDateTime(begin) + "--" + DateUtil.formatDateTime(end) + ":" + cropRate2);
            corpList.add((1.0 * cropRate2.get("workTime")) / (cropRate2.get("devicesNum") * 8 * 60));
            System.out.println(corpList.get(i).toString());
        }
        double rate1 = corpList.stream().mapToDouble(Double::doubleValue).average().getAsDouble();
        ans.put("rate1", rate1);
        double aa = 0;
        for (int i = 0; i < corpList.size(); i++) {
            aa += corpList.get(i);
        }
        double rate2 = aa / corpList.size();
        ans.put("rate2", rate2);
        ans.put("corpList", corpList);

        return AjaxResult.success(ans);
    }

    private Map<String, Integer> getCropRate(String start, String end, String shift) {
        Map<String, Integer> result = new HashMap<>();
        int devicesNum = 0;
        int workTime = 0;

        PageHelper.startPage(1, 1000);
        List<IotEquipment> list = getIotEquipmentList(new IotEquipment());
        List<String> codes = new ArrayList<>();
        codes.add("Power_ON");
        codes.add("Product_Status");
        codes.add("Number");
        codes.add("Deviceld");
        if (list != null && list.size() > 0) {
            List<String> shiftList = null;
            if (StrUtil.isNotEmpty(shift)) {
                final String[] split = shift.split(",");
                shiftList = Arrays.asList(split);
            }
            for (IotEquipment temp : list) {
                //11海汇环保车间电表       13是4个公司总电表  22为兰官庄， 23为一体机
                if (11L == temp.getTslId() || 13L == temp.getTslId() || 22L == temp.getTslId() || 23L == temp.getTslId()) {
                    continue;
                }
                if (StrUtil.isNotEmpty(shift) && shiftList != null) {
                    if (!(shiftList.contains(temp.getId() + ""))) {
                        continue;
                    }
                }
                devicesNum++;
                try {
                    // 获取设备获取历史数据
                    final JSONArray objects = ioTDBUtil.listDataHistoryV2(TENANT, temp.getEquipmentCode(), codes, start, end);

                    if (objects != null && objects.size() > 0) {
                        for (int i = 0; i < objects.size(); i++) {
                            JSONObject jsonObject = objects.getJSONObject(i);
                            final Object productStatus = jsonObject.get("Product_Status");
                            if (productStatus != null && !"".equals(productStatus)) {
                                if ("1.0".equals(productStatus.toString())) {
                                    workTime++;
                                }
                            }
                        }
                    }
                } catch (IoTDBConnectionException e) {
                    e.printStackTrace();
                } catch (StatementExecutionException e) {
                    e.printStackTrace();
                }
            }
        }
        result.put("workTime", workTime);
        result.put("devicesNum", devicesNum);
        return result;
    }

    /**
     * 获取物联网设备列表
     * 该方法旨在通过减少频繁的数据库查询来优化性能，它利用了上次查询的结果在一定时间范围内仍然有效这一特性
     *
     * @param iotEquipment 物联网设备对象，用于查询条件
     * @return 返回一个物联网设备列表，包含符合条件的设备信息
     */
    private List<IotEquipment> getIotEquipmentList(IotEquipment iotEquipment) {
        List<IotEquipment> list = null;
        // 检查是否需要重新查询数据库
        // 如果从上次查询至今的时间超过了设定的时间框架（TIME_FRAME），则进行数据库查询
        if (System.currentTimeMillis() - iotTime > TIME_FRAME) {
            // 执行数据库查询，获取符合条件的物联网设备列表
            list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
            // 更新上次查询的时间，以便后续判断
            iotTime = System.currentTimeMillis();
            // 将本次查询结果保存为最新的有效列表
            iotEquipmentList = list;
        } else {
            // 如果不需要重新查询数据库，则直接返回之前查询的结果
            list = iotEquipmentList;
        }
        // 返回物联网设备列表
        return list;
    }


    @ApiOperation("设备总览")
    @GetMapping("/bank/deviceOverview")
    public AjaxResult bankDeviceOverview(IotEquipment iotEquipment, IotTsl iotTsl2) {
        JSONObject ans = new JSONObject();
        ArrayList<JSONObject> listOneWorkShop = new ArrayList<>();
        List<IotTsl> iotTslList = iotTslService.selectIotTslList(iotTsl2);

        List<IotEquipment> list = getIotEquipmentList(iotEquipment);

        if (list != null && list.size() > 0) {
            for (IotEquipment temp : list) {
                for (IotTslAttr attr : temp.getAttrList()) {
                    if ((attr.getAttrType().equalsIgnoreCase("Double") || attr.getAttrType().equalsIgnoreCase("Integer")) && StrUtil.isNotEmpty(attr.getLastVal())) {
                        attr.setLastVal(Convert.toStr(NumberUtil.round(Convert.toDouble(attr.getLastVal()) * attr.getAttrMultiple(), 2)));
                    }
                    if ("Power_ON".equals(attr.getAttrCode()) && attr.getLastUpdateTime() != null) {
                        final Date lastUpdateTime = attr.getLastUpdateTime();
                        final long between = DateUtil.between(lastUpdateTime, new Date(), DateUnit.MINUTE);
                        if (between > 5L) {
                            attr.setLastVal("0");
                        }
                    }
                }
            }
        }
        for (IotTsl iotTsl : iotTslList) {
            if (iotTsl.getId() == 23) {
                continue;
            }
            JSONObject jsonObject = new JSONObject();
            ArrayList<JSONObject> listOne = new ArrayList<>();
            jsonObject.put("workshopName", iotTsl.getTslName());
            jsonObject.put("picture", iotTsl.getTslImg());
            for (IotEquipment equipment : list) {
                JSONObject temp = new JSONObject();
                if (equipment.getTslId() == iotTsl.getId()) {
                    temp.put("deviceName", equipment.getEquipmentName());
                    equipment.getAttrList().forEach(attr -> {
                        if ("Power_ON".equals(attr.getAttrCode())) {
                            temp.put("workStatus", attr.getLastVal());
                        } else if ("TotalPower".equals(attr.getAttrCode()) || "TotalEnergy".equals(attr.getAttrCode())) {
                            temp.put("workStatus", "1");
                        }
                    });
                    listOne.add(temp);
                }
            }
            jsonObject.put("device", listOne);
            listOneWorkShop.add(jsonObject);
        }
        ans.put("listOneWorkShop", listOneWorkShop);
        return AjaxResult.success(ans);
    }

    @ApiOperation("银行大屏总电量4个公司总电表")
    @GetMapping("/bank/totalEnergy")
    public AjaxResult bankTotalEnergy(String type) {
        JSONObject ans = new JSONObject();
        List<String> dates = new ArrayList<>();
        List<Integer> energys = new ArrayList<>();
        Date start = DateUtil.beginOfDay(new Date());
        Date end = DateUtil.date();
        List<IotEquipment> list = getIotEquipmentList(new IotEquipment());
        long energytemp = 0L;
        // type不为空，分别计算
        if (type != null && !"".equals(type)) {
            switch (type) {
                case "海汇环保":
                    for (IotEquipment temp : list) {
                        if (temp.getId() == 73L) {
                            long energy = getEnergy(start, end, temp.getEquipmentCode());
                            energytemp += energy;
                        }
                    }
                    dates.add(DateUtil.format(start, "MM/dd"));
                    energys.add((int) energytemp);
                    for (int i = 0; i < 6; i++) {
                        energytemp = 0L;
                        end = start;
                        start = DateUtil.offsetDay(start, -1);
                        for (IotEquipment temp : list) {
                            if (temp.getId() == 73L) {
                                long energy = getEnergy(start, end, temp.getEquipmentCode());
                                energytemp += energy;
                            }
                        }
                        dates.add(DateUtil.format(start, "MM/dd"));
                        energys.add((int) energytemp);
                    }
                    break;
                case "招贤水泥":
                    for (IotEquipment temp : list) {
                        if (temp.getId() == 250L || temp.getId() == 251L) {
                            long energy = getEnergy(start, end, temp.getEquipmentCode());
                            energytemp += energy;
                        }
                    }
                    dates.add(DateUtil.format(start, "MM/dd"));
                    energys.add((int) energytemp);
                    for (int i = 0; i < 6; i++) {
                        energytemp = 0L;
                        end = start;
                        start = DateUtil.offsetDay(start, -1);
                        for (IotEquipment temp : list) {
                            if (temp.getId() == 250L || temp.getId() == 251L) {
                                long energy = getEnergy(start, end, temp.getEquipmentCode());
                                energytemp += energy;
                            }
                        }
                        dates.add(DateUtil.format(start, "MM/dd"));
                        energys.add((int) energytemp);
                    }
                    break;
                case "日照水泥":
                    for (IotEquipment temp : list) {
                        if (temp.getId() == 252L || temp.getId() == 253L) {
                            long energy = getEnergy(start, end, temp.getEquipmentCode());
                            energytemp += energy;
                        }
                    }
                    dates.add(DateUtil.format(start, "MM/dd"));
                    energys.add((int) energytemp);
                    for (int i = 0; i < 6; i++) {
                        energytemp = 0L;
                        end = start;
                        start = DateUtil.offsetDay(start, -1);
                        for (IotEquipment temp : list) {
                            if (temp.getId() == 252L || temp.getId() == 253L) {
                                long energy = getEnergy(start, end, temp.getEquipmentCode());
                                energytemp += energy;
                            }
                        }
                        dates.add(DateUtil.format(start, "MM/dd"));
                        energys.add((int) energytemp);
                    }
                    break;

                case "兰官庄水泥":
                    Set<Long> lgzID = new HashSet<>(Arrays.asList(74L, 76L, 77L, 78L, 79L, 80L, 81L));

                    for (IotEquipment temp : list) {
                        if (lgzID.contains(temp.getId())) {
                            long energy = getEnergy(start, end, temp.getEquipmentCode());
                            energytemp += energy;
                        }
                    }
                    dates.add(DateUtil.format(start, "MM/dd"));
                    energys.add((int) energytemp);
                    for (int i = 0; i < 6; i++) {
                        energytemp = 0L;
                        end = start;
                        start = DateUtil.offsetDay(start, -1);
                        for (IotEquipment temp : list) {
                            if (lgzID.contains(temp.getId())) {
                                long energy = getEnergy(start, end, temp.getEquipmentCode());
                                energytemp += energy;
                            }
                        }
                        dates.add(DateUtil.format(start, "MM/dd"));
                        energys.add((int) energytemp);
                    }
                    break;
                case "热电发电电表":
                    for (IotEquipment temp : list) {
                        if (temp.getId() == 265L) {
                            long energy = getEnergy(start, end, temp.getEquipmentCode());
                            energytemp += energy;
                        }
                    }
                    dates.add(DateUtil.format(start, "MM/dd"));
                    energys.add((int) energytemp);
                    for (int i = 0; i < 6; i++) {
                        energytemp = 0L;
                        end = start;
                        start = DateUtil.offsetDay(start, -1);
                        for (IotEquipment temp : list) {
                            if (temp.getId() == 265L) {
                                long energy = getEnergy(start, end, temp.getEquipmentCode());
                                energytemp += energy;
                            }
                        }
                        dates.add(DateUtil.format(start, "MM/dd"));
                        energys.add((int) energytemp);
                    }
                    break;
                default:
                    for (IotEquipment temp : list) {
                        if (temp.getTslId() == 13L) {
                            long energy = getEnergy(start, end, temp.getEquipmentCode());
                            energytemp += energy;
                        }
                    }
                    dates.add(DateUtil.format(start, "MM/dd"));
                    energys.add((int) energytemp);
                    for (int i = 0; i < 6; i++) {
                        energytemp = 0L;
                        end = start;
                        start = DateUtil.offsetDay(start, -1);
                        for (IotEquipment temp : list) {
                            if (temp.getTslId() == 13L) {
                                final long energy = getEnergy(start, end, temp.getEquipmentCode());
                                energytemp += energy;
                            }
                        }
                        dates.add(DateUtil.format(start, "MM/dd"));
                        energys.add((int) energytemp);
                    }
            }
        } else { // type不为空，分别总的电表
            for (IotEquipment temp : list) {
                if (temp.getTslId() == 13L) {
                    long energy = getEnergy(start, end, temp.getEquipmentCode());
//                System.out.println(temp.getEquipmentName()+"  "+start+"---"+end+"---"+energy);
                    energytemp += energy;
                }
            }
            dates.add(DateUtil.format(start, "MM/dd"));
            energys.add((int) energytemp);
            for (int i = 0; i < 6; i++) {
                energytemp = 0L;
                end = start;
                start = DateUtil.offsetDay(start, -1);
                for (IotEquipment temp : list) {
                    if (temp.getTslId() == 13L) {
                        final long energy = getEnergy(start, end, temp.getEquipmentCode());
//                    System.out.println(temp.getEquipmentName()+"  "+start+"---"+end+"==="+energy);
                        energytemp += energy;
                    }
                }
                dates.add(DateUtil.format(start, "MM/dd"));
                energys.add((int) energytemp);
            }
        }

        Collections.reverse(dates);
        Collections.reverse(energys);
        ans.put("date", dates);
        ans.put("energy", energys);

        return AjaxResult.success(ans);
    }


    @ApiOperation("银行大屏 设备管理")
    @GetMapping("/bank/device")
    public AjaxResult bankDevice(IotEquipment iotEquipment) {

        JSONObject ans = new JSONObject();
        AtomicInteger open = new AtomicInteger();
        final List<IotEquipment> iotEquipmentList = getIotEquipmentList(iotEquipment);

        for (IotEquipment equipment : iotEquipmentList) {
            equipment.getAttrList().forEach(attr -> {
                if ("Power_ON".equals(attr.getAttrCode())) {
                    if ("1".equals(attr.getLastVal())) {
                        open.getAndIncrement();
                    }
                } else if ("TotalPower".equals(attr.getAttrCode()) || "TotalEnergy".equals(attr.getAttrCode())) {
                    open.getAndIncrement();
                }
            });
        }

        int total = iotEquipmentList.size() - 1; // 机器总数量 -1是减去一体机
        int openCount = open.get() - 1; // 开机数量  -1是减去一体机
        ans.put("total", total);
        ans.put("open", openCount);
        ans.put("close", total - openCount);// 关机数量

        long energytemp = 0L;
        for (IotEquipment temp : iotEquipmentList) {
            if (temp.getTslId() == 13L) {
                final long energy = getEnergy(DateUtil.beginOfDay(new Date()), DateUtil.date(), temp.getEquipmentCode());
                energytemp += energy;
            }
        }
        ans.put("totayEnergy", energytemp);// 今日总电量

        return AjaxResult.success(ans);
    }


    @ApiOperation("银行端设备利用率")
    @RequestMapping(value = "/bank/device/utilizationRate", method = RequestMethod.GET)
    public AjaxResult getUtilizationRateForBank(String type) {
        final long currentTimeMillis = System.currentTimeMillis();
        if (currentTimeMillis - utilizationRateForBankPreTime <= TIME_FRAME) {
            if (!"1".equals(type)) {
                return AjaxResult.success(utilizationRateForBankPre);
            }
        }

        JSONObject ans = new JSONObject();
        List<String> dates = new ArrayList<>();
        List<String> corpList = new ArrayList<>();

        DateTime begin = DateUtil.beginOfDay(new Date());
        Date end = DateUtil.date();

        //
        String shift = "92,87,100,105,119,122,127,189,192,202,184,187,188,195,201,206,224"; // 主要设备ID

        final Map<String, Integer> cropRate1;
        //type 1：主要设备；其他：全部设备
        if ("1".equals(type)) {
            cropRate1 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), shift);
        } else {
            cropRate1 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), null);
        }


        dates.add(DateUtil.format(begin, "MM/dd"));
        corpList.add(String.format("%.2f", (1.0 * cropRate1.get("workTime")) / (cropRate1.get("devicesNum") * 8 * 60)));

        for (int i = 0; i < 6; i++) {
            begin = DateUtil.offsetDay(begin, -1);
            end = DateUtil.endOfDay(begin);
            Map<String, Integer> cropRate2 = new HashMap<>();
            if ("1".equals(type)) {
                cropRate2 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), shift);
            } else {
                cropRate2 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), null);
            }
            dates.add(DateUtil.format(begin, "MM/dd"));
            corpList.add(String.format("%.2f", (1.0 * cropRate1.get("workTime")) / (cropRate1.get("devicesNum") * 8 * 60)));
        }
        Collections.reverse(corpList);
        Collections.reverse(dates);
        ans.put("date", dates);
        ans.put("cropRate", corpList);

        if (!"1".equals(type)) {
            utilizationRateForBankPre = ans;
            utilizationRateForBankPreTime = System.currentTimeMillis();
        }
        return AjaxResult.success(ans);
    }


    //
    @Scheduled(cron = "10 0/10 * * * ?")
    private void getUtilizationRateForBankPre() {
        String type = "0";
        JSONObject ans = new JSONObject();
        List<String> dates = new ArrayList<>();
        List<Double> corpList = new ArrayList<>();

        DateTime begin = DateUtil.beginOfDay(new Date());
        Date end = DateUtil.date();

        String shift = "92,87,100,105,119,122,127,189,192,202,184,187,188,195,201,206,224"; // 主要设备ID

        final Map<String, Integer> cropRate1;
        //type 1：主要设备；其他：全部设备
        if ("1".equals(type)) {
            cropRate1 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), shift);
        } else {
            cropRate1 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), null);
        }


        dates.add(DateUtil.format(begin, "MM/dd"));
        corpList.add((1.0 * cropRate1.get("workTime")) / (cropRate1.get("devicesNum") * 8 * 60));

        for (int i = 0; i < 6; i++) {
            begin = DateUtil.offsetDay(begin, -1);
            end = DateUtil.endOfDay(begin);
            Map<String, Integer> cropRate2 = new HashMap<>();
            if ("1".equals(type)) {
                cropRate2 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), shift);
            } else {
                cropRate2 = getCropRate(DateUtil.formatDateTime(begin), DateUtil.formatDateTime(end), null);
            }
            dates.add(DateUtil.format(begin, "MM/dd"));
            corpList.add((1.0 * cropRate2.get("workTime")) / (cropRate2.get("devicesNum") * 8 * 60));
        }
        Collections.reverse(corpList);
        Collections.reverse(dates);
        ans.put("date", dates);
        ans.put("cropRate", corpList);

        utilizationRateForBankPre = ans;
        utilizationRateForBankPreTime = System.currentTimeMillis();
    }

    //    @Scheduled(cron = "0 10 00 * * ?")//每天00：10执行各一次
    @Scheduled(cron = "0 00 10 * * ?")//
    public void sendPowerData() {
        System.out.println("定时推送电量数据开始======================");

        Date start = DateUtil.beginOfDay(DateUtil.yesterday());
        Date end = DateUtil.endOfDay(start);
        // 今日总电量
        long dailyElectricityQuantity = 0L;
        // 实时总电量
        double realTimeElectricityQuantity = 0;

        System.out.println("定时推送电量数据开始======================" + start + "  " + end);

        IotEquipment equipmentD250 = iotEquipmentService.getEquipmentDetail(250);
        dailyElectricityQuantity += getEnergy(start, end, equipmentD250.getEquipmentCode());

        IotEquipment equipmentD251 = iotEquipmentService.getEquipmentDetail(251);
        dailyElectricityQuantity += getEnergy(start, end, equipmentD251.getEquipmentCode());

        System.out.println("dailyElectricityQuantity:" + dailyElectricityQuantity);
        final IotEquipment dianbiao250 = iotEquipmentService.getEquipmentByCode(250 + "");
        final IotEquipment dainbiao251 = iotEquipmentService.getEquipmentByCode(251 + "");
        System.out.println("dianbiao250:" + dianbiao250.toString());
        System.out.println("dainbiao251:" + dainbiao251);

        String totalPower250 = dianbiao250.getAttrList().get(0).getLastVal();
        String totalPower251 = dainbiao251.getAttrList().get(0).getLastVal();

        if (totalPower250 != null) {
            try {
                realTimeElectricityQuantity += Double.parseDouble(totalPower250);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (totalPower251 != null) {
            try {
                realTimeElectricityQuantity += Double.parseDouble(totalPower250);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        System.out.println("数据整理结束===========");

        String url = "/carbon/emission/cement/power";
        String apiCode = "A2683";
        System.out.println("定时推送带电量数据======================");
        System.out.println("dataDate:" + DateUtil.format(start, "yyyy-MM-dd"));
        System.out.println("realTimeElectricityQuantity:" + realTimeElectricityQuantity);
        System.out.println("dailyElectricityQuantity:" + dailyElectricityQuantity);
        System.out.println("=======================================");
        JSONObject jsonstr = new JSONObject();
        jsonstr.put("dataDate", DateUtil.format(start, "yyyy-MM-dd"));
        jsonstr.put("entpCode", "91371122725436442J");
        jsonstr.put("dailyElectricityQuantity", dailyElectricityQuantity);
        jsonstr.put("realTimeElectricityQuantity", realTimeElectricityQuantity);

        System.out.println(jsonstr.toString());

        JSONObject res = null;
        try {
            res = gatewayClient.send(url, jsonstr.toString(), JSONObject.class, apiCode);
        } catch (Exception e) {
            throw new RuntimeException("实时总电量计算出错==" + e);
        }

        //控制台输入json格式化
        System.out.println(JSON.toJSONString(res, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteDateUseDateFormat));
        //控制台输出的json非格式化
        System.out.println(res);

    }
}
