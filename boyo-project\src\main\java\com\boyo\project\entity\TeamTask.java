package com.boyo.project.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务表(TeamTask)实体类
 *
 * <AUTHOR>
 * @since 2022-02-13 13:05:06
 */
@Data
@TableName(value = "team_task")
public class TeamTask implements Serializable {
    private static final long serialVersionUID = 469892896444147779L;
            
    @TableId
    private Integer id;
    /**
    * 项目编号
    */    
    @TableField(value = "project_code")
    private String projectCode;
    
    /**
    * 编号
    */
    @TableField(value="code")
    private String code;
    
    @TableField(value="name")
    private String name;
    /**
    * 紧急程度
    */
    @TableField(value="pri")
    private String pri;

    @TableField(exist = false)
    private String priName;
    /**
    * 执行状态
    */
    @TableField(value="execute_status")
    private String executeStatus;

    @TableField(exist = false)
    private String executeName;
    /**
    * 详情
    */
    @TableField(value="description")
    private String description;
    /**
    * 创建人
    */
    @TableField(value="create_by")
    private String createBy;
    /**
    * 创建日期
    */
    @TableField(value="create_time")
    private String createTime;
    /**
    * 指派给谁
    */
    @TableField(value="assign_to")
    private String assignTo;

    @TableField(exist = false)
    private String assignName;
    /**
    * 回收站
    */
    @TableField(value="deleted")
    private Integer deleted;
    /**
    * 任务列表
    */
    @TableField(value="stage_code")
    private String stageCode;
    /**
    * 任务标签
    */
    @TableField(value="task_tag")
    private String taskTag;
    /**
    * 是否完成
    */
    @TableField(value="done")
    private String done;
    /**
    * 开始时间
    */
    @TableField(value="begin_time")
    private String beginTime;
    /**
    * 截止时间
    */
    @TableField(value="end_time")
    private String endTime;
    /**
    * 提醒时间
    */
    @TableField(value="remind_time")
    private String remindTime;
    /**
    * 父任务id
    */
    @TableField(value="pcode")
    private String pcode;
    /**
    * 排序
    */
    @TableField(value="sort")
    private Integer sort;
    /**
    * 收藏数
    */
    @TableField(value="star")
    private Integer star;
    /**
    * 删除时间
    */
    @TableField(value="deleted_time")
    private String deletedTime;

    /**
    * 任务id编号
    */
    @TableField(value="id_num")
    private Integer idNum;
    /**
    * 上级任务路径
    */
    @TableField(value="path")
    private String path;
    /**
    * 进度百分比
    */
    @TableField(value="schedule")
    private Integer schedule;
    /**
    * 版本id
    */
    @TableField(value="version_code")
    private String versionCode;
    /**
    * 版本库id
    */
    @TableField(value="features_code")
    private String featuresCode;
    /**
    * 预估工时
    */
    @TableField(value="work_time")
    private Integer workTime;
    /**
    * '执行状态。0：未开始，1：已完成，2：进行中，3：挂起，4：测试中'
    */
    @TableField(value="status")
    private Integer status;

    
    @TableField(value="liked")
    private Integer liked;

    /**
     * 项目名称
     */
    @TableField(exist = false)
    private String projectName;

}
