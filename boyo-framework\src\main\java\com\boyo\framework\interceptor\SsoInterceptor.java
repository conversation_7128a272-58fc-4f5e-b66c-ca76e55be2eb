package com.boyo.framework.interceptor;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;

import com.boyo.common.core.redis.RedisCache;
import com.boyo.common.result.CodeMsg;
import com.boyo.common.result.Result;
import com.boyo.framework.config.GlobalUseConfig;
import com.boyo.framework.key.AppKey;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;

/**
 * 登录拦截器
 * created by mica<PERSON> on 2018-08-03
 */
@Component
public class SsoInterceptor implements HandlerInterceptor {

    @Resource
    private RedisCache redisCache;

    private static final String APPID = "appId";

    private static final String TOKEN = "authorization";

    private static final String SIGN = "sign";

    private static final String TIMESTAMP = "timestamp";

    private static final ThreadLocal<String> PTHOLDER = new ThreadLocal<>();

    private static final ThreadLocal<Long> USERHOLDER = new ThreadLocal<>();


    @Autowired
    private GlobalUseConfig globalUseConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            String token = request.getHeader(TOKEN);
            String appId = request.getHeader(APPID);
            if(StringUtils.isBlank(appId)){
                appId = request.getParameter(APPID);
            }
            String timestamp = request.getHeader(TIMESTAMP);
            if(StringUtils.isBlank(timestamp)){
                timestamp = request.getParameter(TIMESTAMP);
            }
            String sign = request.getHeader(SIGN);
            if(StringUtils.isBlank(sign)){
                sign = request.getParameter(SIGN);
            }
            if (StringUtils.isBlank(appId) || appId.length()!=18 || !appId.startsWith("md")) {
                render(response, CodeMsg.BIND_ERROR.fillArgs("无效的APPID"));
                return false;
            }
            //String appJson =  redisCache.getCacheMapValue(AppKey.getAppList.getPrefix(), appId);
            //JSONObject app = StringUtils.isNotBlank(appJson)?JSONObject.parseObject(appJson):null;
            //String appSecret = app!=null?app.getString("appSecret"):null;
            //if (StringUtils.isBlank(appSecret) || appSecret.length()!=32) {
            //    render(response, CodeMsg.BIND_ERROR.fillArgs("无效的appSecret"));
            //    return false;
            //}
            String checkSign = SecureUtil.md5(appId+timestamp + globalUseConfig.getAppSecret());
            if (StringUtils.isBlank(sign) || !sign.equals(checkSign)) {
                render(response,CodeMsg.BIND_ERROR.fillArgs("无效的签名"));
                return false;
            }
            Long userId = -1L;
            if(StringUtils.isNotBlank(token)){
                /*JSONObject user = JwtUtil.getUser(token);
                if(user==null){
                    render(response,CodeMsg.BIND_ERROR.fillArgs("用户登录失效"));
                    return false;
                }
                userId = user.getLong("user_id");*/
            }
            USERHOLDER.set(userId);
            //PTHOLDER.set(app.getString("appCode"));
        }
        return true;
    }

    public static String getPlatform() {
        return PTHOLDER.get()==null?"sso":PTHOLDER.get();
    }
    public static Long getUser() {
        return USERHOLDER.get();
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        PTHOLDER.remove();
        USERHOLDER.remove();
    }

    private void render(HttpServletResponse response, CodeMsg cm) throws Exception {
        response.setContentType("application/json;charset=UTF-8");
        OutputStream out = response.getOutputStream();
        out.write(JSONObject.toJSONString(Result.error(cm)).getBytes("UTF-8"));
        out.flush();
        out.close();
    }
}
