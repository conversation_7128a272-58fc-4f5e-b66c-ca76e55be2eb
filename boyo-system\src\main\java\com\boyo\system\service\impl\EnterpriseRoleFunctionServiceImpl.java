package com.boyo.system.service.impl;

import java.util.List;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.system.mapper.EnterpriseRoleFunctionMapper;
import com.boyo.system.domain.EnterpriseRoleFunction;
import com.boyo.system.service.IEnterpriseRoleFunctionService;

/**
 * 企业角色权限管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class EnterpriseRoleFunctionServiceImpl extends ServiceImpl<EnterpriseRoleFunctionMapper, EnterpriseRoleFunction> implements IEnterpriseRoleFunctionService {
    private final EnterpriseRoleFunctionMapper enterpriseRoleFunctionMapper;


    /**
     * 查询企业角色权限管理列表
     *
     * @param enterpriseRoleFunction 企业角色权限管理
     * @return enterpriseRoleFunction 列表
     */
    @Override
    public List<EnterpriseRoleFunction> selectEnterpriseRoleFunctionList(EnterpriseRoleFunction enterpriseRoleFunction) {
        return enterpriseRoleFunctionMapper.selectEnterpriseRoleFunctionList(enterpriseRoleFunction);
    }
}
