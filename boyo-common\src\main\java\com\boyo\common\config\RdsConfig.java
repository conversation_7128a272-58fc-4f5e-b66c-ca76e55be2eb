package com.boyo.common.config;

import com.aliyun.rds20140815.Client;
import com.aliyun.teaopenapi.models.Config;
import org.springframework.stereotype.Component;

@Component
public class RdsConfig {
    public Client createClient() throws Exception {
        String accessKeyId = "LTAI5tMwJP3xBSXToDv6PUyX";
        String accessKeySecret = "******************************";
        Config config = new Config()
                // 您的AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 您的AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = "rds.aliyuncs.com";
        return new com.aliyun.rds20140815.Client(config);
    }
}
