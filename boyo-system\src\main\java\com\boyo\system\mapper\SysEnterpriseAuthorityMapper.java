package com.boyo.system.mapper;

import java.util.List;

import com.boyo.system.domain.SysEnterpriseAuthority;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;


/**
 * 企业权限管理Mapper接口
 *
 * <AUTHOR>
 */
public interface SysEnterpriseAuthorityMapper extends BaseMapper<SysEnterpriseAuthority> {

    /**
     * 查询企业权限管理列表
     *
     * @param sysEnterpriseAuthority 企业权限管理
     * @return SysEnterpriseAuthority集合
     */
    public List<SysEnterpriseAuthority> selectSysEnterpriseAuthorityList(SysEnterpriseAuthority sysEnterpriseAuthority);

}
