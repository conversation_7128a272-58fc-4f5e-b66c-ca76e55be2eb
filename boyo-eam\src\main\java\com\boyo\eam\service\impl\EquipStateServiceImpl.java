package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipState;
import com.boyo.eam.mapper.EquipStateMapper;
import com.boyo.eam.service.IEquipStateService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备状态表(EquipState)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Service("equipStateService")
@AllArgsConstructor
@Tenant
public class EquipStateServiceImpl extends ServiceImpl<EquipStateMapper, EquipState> implements IEquipStateService {
    private final EquipStateMapper equipStateMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipState> selectEquipStateList(EquipState equipState) {
        return equipStateMapper.selectEquipStateList(equipState);
    }

}
