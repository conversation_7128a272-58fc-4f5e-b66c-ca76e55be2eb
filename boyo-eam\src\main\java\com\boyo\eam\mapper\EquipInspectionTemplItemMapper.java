package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipInspectionTemplItem;

import java.util.List;

/**
 * 点检-项目(EquipInspectionTemplItem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-29 10:21:50
 */
public interface EquipInspectionTemplItemMapper extends BaseMapper<EquipInspectionTemplItem>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipInspectionTemplItem 实例对象
     * @return 对象列表
     */
    List<EquipInspectionTemplItem> selectEquipInspectionTemplItemList(EquipInspectionTemplItem equipInspectionTemplItem);


}

