package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipLedgerSparePart;

import java.util.List;

/**
 * 台账和部件关联表(EquipLedgerSparePart)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-04 16:59:33
 */
public interface EquipLedgerSparePartMapper extends BaseMapper<EquipLedgerSparePart>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipLedgerSparePart 实例对象
     * @return 对象列表
     */
    List<EquipLedgerSparePart> selectEquipLedgerSparePartList(EquipLedgerSparePart equipLedgerSparePart);


}

