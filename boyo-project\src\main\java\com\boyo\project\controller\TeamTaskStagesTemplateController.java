package com.boyo.project.controller;

import com.boyo.project.entity.TeamTaskStagesTemplate;
import com.boyo.project.service.ITeamTaskStagesTemplateService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 任务列表模板表(TeamTaskStagesTemplate)表控制层
 *
 * <AUTHOR>
 * @since 2022-02-08 20:50:50
 */
@Api("任务列表模板表")
@RestController
@RequestMapping("/project/teamTaskStagesTemplate")
@AllArgsConstructor
public class TeamTaskStagesTemplateController extends BaseController{
    /**
     * 服务对象
     */
    private final ITeamTaskStagesTemplateService teamTaskStagesTemplateService;

    /**
     * 查询任务列表模板表列表
     *
     */
    @ApiOperation("查询任务列表模板表列表")
    @GetMapping("/list")
    public TableDataInfo list(TeamTaskStagesTemplate teamTaskStagesTemplate) {
        startPage();
        List<TeamTaskStagesTemplate> list = teamTaskStagesTemplateService.selectTeamTaskStagesTemplateList(teamTaskStagesTemplate);
        return getDataTable(list);
    }

    /**
     * 获取任务列表模板表详情
     */
    @ApiOperation("获取任务列表模板表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(teamTaskStagesTemplateService.getById(id));
    }

    /**
     * 新增任务列表模板表
     */
    @ApiOperation("新增任务列表模板表")
    @PostMapping
    public AjaxResult add(@RequestBody TeamTaskStagesTemplate teamTaskStagesTemplate) {
        return toBooleanAjax(teamTaskStagesTemplateService.save(teamTaskStagesTemplate));
    }

    /**
     * 修改任务列表模板表
     */
    @ApiOperation("修改任务列表模板表")
    @PutMapping
    public AjaxResult edit(@RequestBody TeamTaskStagesTemplate teamTaskStagesTemplate) {
        return toBooleanAjax(teamTaskStagesTemplateService.updateById(teamTaskStagesTemplate));
    }

    /**
     * 删除任务列表模板表
     */
    @ApiOperation("删除任务列表模板表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(teamTaskStagesTemplateService.removeByIds(Arrays.asList(ids)));
    }

}
