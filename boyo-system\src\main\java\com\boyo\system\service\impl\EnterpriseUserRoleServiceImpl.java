package com.boyo.system.service.impl;

import java.util.List;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.system.mapper.EnterpriseUserRoleMapper;
import com.boyo.system.domain.EnterpriseUserRole;
import com.boyo.system.service.IEnterpriseUserRoleService;

/**
 * 企业用户角色管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class EnterpriseUserRoleServiceImpl extends ServiceImpl<EnterpriseUserRoleMapper, EnterpriseUserRole> implements IEnterpriseUserRoleService {
    private final EnterpriseUserRoleMapper enterpriseUserRoleMapper;


    /**
     * 查询企业用户角色管理列表
     *
     * @param enterpriseUserRole 企业用户角色管理
     * @return enterpriseUserRole 列表
     */
    @Override
    public List<EnterpriseUserRole> selectEnterpriseUserRoleList(EnterpriseUserRole enterpriseUserRole) {
        return enterpriseUserRoleMapper.selectEnterpriseUserRoleList(enterpriseUserRole);
    }
}
