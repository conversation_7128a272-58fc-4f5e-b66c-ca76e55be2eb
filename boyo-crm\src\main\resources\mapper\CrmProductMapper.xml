<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmProductMapper">

    <resultMap type="com.boyo.crm.entity.CrmProduct" id="CrmProductResult">
        <result property="productId" column="product_id"/>
        <result property="name" column="name"/>
        <result property="num" column="num"/>
        <result property="unitId" column="unit_id"/>
        <result property="price" column="price"/>
        <result property="status" column="status"/>
        <result property="categoryId" column="category_id"/>
        <result property="description" column="description"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="ownerUserId" column="owner_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="batchId" column="batch_id"/>

        <result property="categoryName" column="categoryName"></result>
        <result property="unitName" column="unitName"></result>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmProductList" parameterType="com.boyo.crm.entity.CrmProduct" resultMap="CrmProductResult">
        select t1.*,t2.base_desc as categoryName,t3.base_desc as unitName from (select
        product_id, name, num, unit_id, price, status, category_id, description, create_user_id, owner_user_id,
        create_time, update_time, batch_id
        from t_crm_product
        <where>
            <if test="name != null and name != ''">
                and name like concat('%',#{name},'%')
            </if>
            <if test="num != null and num != ''">
                and num = #{num}
            </if>
            <if test="unitId != null">
                and unit_id = #{unitId}
            </if>
            <if test="price != null">
                and price = #{price}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="categoryId != null">
                and category_id = #{categoryId}
            </if>
            <if test="description != null and description != ''">
                and description = #{description}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="ownerUserId != null">
                and owner_user_id = #{ownerUserId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="batchId != null and batchId != ''">
                and batch_id = #{batchId}
            </if>
        </where>
        ) t1 left join (select * from t_base_dict where base_type = 'CRM_PRODUCTION_TYPE') t2 on t1.category_id = t2.id
        left join (select * from t_base_dict where base_type = 'CRM_PRODUCTION_UNIT') t3 on t1.unit_id = t3.id
    </select>
</mapper>

