package com.boyo.iot.controller;

import com.boyo.iot.entity.OrderMaterial;
import com.boyo.iot.service.IOrderMaterialService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * (OrderMaterial)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-28 09:16:06
 */
@Api("")
@RestController
@RequestMapping("/iot/orderMaterial")
@AllArgsConstructor
public class OrderMaterialController extends BaseController{
    /**
     * 服务对象
     */
    private final IOrderMaterialService orderMaterialService;

    /**
     * 查询列表
     *
     */
    @ApiOperation("查询列表")
    @GetMapping("/list")
    public TableDataInfo list(OrderMaterial orderMaterial) {
        startPage();
        List<OrderMaterial> list = orderMaterialService.selectOrderMaterialList(orderMaterial);
        return getDataTable(list);
    }
    
    /**
     * 获取详情
     */
    @ApiOperation("获取详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(orderMaterialService.getById(id));
    }

    /**
     * 新增
     */
    @ApiOperation("新增")
    @PostMapping
    public AjaxResult add(@RequestBody OrderMaterial orderMaterial) {
        return toBooleanAjax(orderMaterialService.save(orderMaterial));
    }

    /**
     * 修改
     */
    @ApiOperation("修改")
    @PutMapping
    public AjaxResult edit(@RequestBody OrderMaterial orderMaterial) {
        return toBooleanAjax(orderMaterialService.updateById(orderMaterial));
    }

    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(orderMaterialService.removeByIds(Arrays.asList(ids)));
    }

}
