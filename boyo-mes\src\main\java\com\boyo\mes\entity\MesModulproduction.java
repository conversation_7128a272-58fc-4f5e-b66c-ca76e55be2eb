package com.boyo.mes.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 模具产品绑定关系(MesModulproduction)实体类
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
@Data
@TableName(value = "t_mes_modulproduction")
public class MesModulproduction implements Serializable {
    private static final long serialVersionUID = -43722442277297348L;
            
    @TableId
    private Integer id;
    
    /**
    * 模具id
    */
    @TableField(value="modul_id")
    private Integer modulId;
    /**
    * 产品id
    */
    @TableField(value="production_id")
    private Integer productionId;

}
