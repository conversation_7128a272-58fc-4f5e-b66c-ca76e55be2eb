import request from '@/utils/request'

const prefix = ''

// 查询项目-成员表(TeamProjecmember)列表
export function listTeamProjecmember(query) {
  return request({
    url: prefix + '/teamProjecmember/list',
    method: 'get',
    params: query,
  })
}

// 查询项目-成员表(TeamProjecmember)详细
export function getTeamProjecmember(id) {
  return request({
    url: prefix + '/teamProjecmember/' + id,
    method: 'get',
  })
}

// 新增项目-成员表(TeamProjecmember)
export function addTeamProjecmember(data) {
  return request({
    url: prefix + '/teamProjecmember',
    method: 'post',
    data: data,
  })
}

// 修改项目-成员表(TeamProjecmember)
export function updateTeamProjecmember(data) {
  return request({
    url: prefix + '/teamProjecmember',
    method: 'put',
    data: data,
  })
}

// 删除项目-成员表(TeamProjecmember)
export function delTeamProjecmember(id) {
  return request({
    url: prefix + '/teamProjecmember/' + id,
    method: 'delete',
  })
}
