<template>
  <base-page :config="config">
    <span slot="operation" slot-scope="text, record">
      <a @click="$refs.createForm.handleUpdate($event,record.id)">
      <a-icon type="edit" />{{ $t('app.global.edit') }}
      </a>
      <a-divider type="vertical" />
      <a @click="handleDelete(record.id)"> <a-icon type="delete" />{{ $t('app.global.delete') }}</a>
    </span>
    <!-- 弹窗 -->
    <create-form ref="createForm" :statusOptions="statusOptions" @ok="getList" />
  </base-page>
</template>

<script>
import CreateForm from './modules/CreateForm'
import { delTeamProject, listTeamProject } from '@/api/teamProject'

export default {
  components: {
    CreateForm,
  },
  data() {
    return {
      // 页面加载状态
      loading: false,
      // 数据列表
      list: [],
      // 表格数据总数
      total: 0,
      // 状态数据字典
      statusOptions: [],
    }
  },
  async created() {
    this.getList()
  },
  computed: {
    config() {
      return {
        loading: this.loading,
        query: {
          onQuery: this.getList,
          items: [
                        { label: '封面', name: 'cover' },
                        { label: '名称', name: 'name' },
                        { label: '编号', name: 'code' },
                        { label: '描述', name: 'description' },
                        { label: '访问控制l类型', name: 'accessControlType' },
                        { label: '可以访问项目的权限组（白名单）', name: 'whiteList' },
                        { label: '排序', name: 'order' },
                        { label: '删除标记', name: 'deleted' },
                        { label: '项目类型', name: 'templateCode' },
                        { label: '进度', name: 'schedule' },
                        { label: '创建时间', name: 'createTime' },
                        { label: '组织id', name: 'organizationCode' },
                        { label: '删除时间', name: 'deletedTime' },
                        { label: '是否私有', name: 'private' },
                        { label: '项目前缀', name: 'prefix' },
                        { label: '是否开启项目前缀', name: 'openPrefix' },
                        { label: '是否归档', name: 'archive' },
                        { label: '归档时间', name: 'archiveTime' },
                        { label: '是否开启任务开始时间', name: 'openBeginTime' },
                        { label: '是否开启新任务默认开启隐私模式', name: 'openTaskPrivate' },
                        { label: '看板风格', name: 'taskBoardTheme' },
                        { label: '项目开始日期', name: 'beginTime' },
                        { label: '项目截止日期', name: 'endTime' },
                        { label: '自动更新项目进度', name: 'autoUpdateSchedule' },
                      ],
        },
        action: {
          add: () => this.$refs.createForm.handleAdd(),
          delete: this.handleDelete,
        },
        table: {
          total: this.total,
          list: this.list,
          columns: [
               {
                title: '封面',
                dataIndex: 'cover',
                align: 'center',
            },
                {
                title: '名称',
                dataIndex: 'name',
                align: 'center',
            },
                {
                title: '编号',
                dataIndex: 'code',
                align: 'center',
            },
                {
                title: '描述',
                dataIndex: 'description',
                align: 'center',
            },
                {
                title: '访问控制l类型',
                dataIndex: 'accessControlType',
                align: 'center',
            },
                {
                title: '可以访问项目的权限组（白名单）',
                dataIndex: 'whiteList',
                align: 'center',
            },
                {
                title: '排序',
                dataIndex: 'order',
                align: 'center',
            },
                {
                title: '删除标记',
                dataIndex: 'deleted',
                align: 'center',
            },
                {
                title: '项目类型',
                dataIndex: 'templateCode',
                align: 'center',
            },
                {
                title: '进度',
                dataIndex: 'schedule',
                align: 'center',
            },
                {
                title: '创建时间',
                dataIndex: 'createTime',
                align: 'center',
            },
                {
                title: '组织id',
                dataIndex: 'organizationCode',
                align: 'center',
            },
                {
                title: '删除时间',
                dataIndex: 'deletedTime',
                align: 'center',
            },
                {
                title: '是否私有',
                dataIndex: 'private',
                align: 'center',
            },
                {
                title: '项目前缀',
                dataIndex: 'prefix',
                align: 'center',
            },
                {
                title: '是否开启项目前缀',
                dataIndex: 'openPrefix',
                align: 'center',
            },
                {
                title: '是否归档',
                dataIndex: 'archive',
                align: 'center',
            },
                {
                title: '归档时间',
                dataIndex: 'archiveTime',
                align: 'center',
            },
                {
                title: '是否开启任务开始时间',
                dataIndex: 'openBeginTime',
                align: 'center',
            },
                {
                title: '是否开启新任务默认开启隐私模式',
                dataIndex: 'openTaskPrivate',
                align: 'center',
            },
                {
                title: '看板风格',
                dataIndex: 'taskBoardTheme',
                align: 'center',
            },
                {
                title: '项目开始日期',
                dataIndex: 'beginTime',
                align: 'center',
            },
                {
                title: '项目截止日期',
                dataIndex: 'endTime',
                align: 'center',
            },
                {
                title: '自动更新项目进度',
                dataIndex: 'autoUpdateSchedule',
                align: 'center',
            },
                        {
              title: this.$t('app.global.operation'),
              dataIndex: 'operation',
              scopedSlots: { customRender: 'operation' },
              align: 'center',
            },
          ],
        },
      }
    },
  },
  methods: {
    /**
     * 查询项目表列表
     */
    async getList(queryParam) {
      this.loading = true
      if (queryParam !== undefined) {
        this.queryParam = queryParam
      }
      this.loading = true
      const response = await listTeamProject(this.queryParam)
      this.list = response.rows
      this.total = response.total
      this.loading = false
    },
    /**
     * 删除按钮操作
     */
    handleDelete(id) {

      const ids = id || this.ids
      this.$alert.confirm({
        content: this.$t('app.global.delete.content'),
        onOk: async () => {
        await delTeamProject(ids)
        this.getList()
        this.$alert.success(this.$t('app.global.delete.success'))
        },
      })
    },
  },
}
</script>
}
