package com.boyo.mes.controller;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.boyo.common.core.text.Convert;
import com.boyo.iot.domain.HistoryData;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.mapper.IotEquipmentMapper;
import com.boyo.iot.util.IoTDBUtil;
import com.boyo.mes.entity.ProductOrder;
import com.boyo.mes.entity.ProductOrderDetail;
import com.boyo.mes.entity.WorkReport;
import com.boyo.mes.mapper.ProductOrderMapper;
import com.boyo.mes.service.IProductOrderDetailService;
import com.boyo.mes.service.IProductOrderService;
import com.boyo.mes.service.IWorkReportService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.Date;
import java.util.List;
import java.util.Arrays;

/**
 * 生产工单执行(ProductOrderDetail)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Api("生产工单执行")
@RestController
@RequestMapping("/mes/productOrderDetail")
@AllArgsConstructor
public class ProductOrderDetailController extends BaseController{
    /**
     * 服务对象
     */
    @Autowired
    private final IProductOrderDetailService productOrderDetailService;

    @Autowired
    private final IProductOrderService productOrderService;


    @ApiOperation("工单完成状态表")
    @GetMapping("/list")
    public TableDataInfo getlist(ProductOrderDetail productOrderDetail) {
        startPage();
        List<ProductOrderDetail> list = productOrderDetailService.selectProductOrderDetailList(productOrderDetail);
        return getDataTable(list);
    }

    @ApiOperation("待质检工序执行单")
    @GetMapping("/completeList")
    public TableDataInfo getCompletelist(ProductOrderDetail orderDetail)  {
        startPage();
        List<ProductOrderDetail> orderDetails = productOrderDetailService.selectProductOrderDetailList(orderDetail);
        return getDataTable(orderDetails);
    }
    /**
     * 获取生产工单执行详情
     */
    @ApiOperation("获取生产工单执行详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(productOrderDetailService.getById(id));
    }

    /**
     * 新增生产工单执行
     */
    @ApiOperation("新增生产工单执行")
    @PostMapping
    public AjaxResult add(@RequestBody ProductOrderDetail productOrderDetail) {
        productOrderDetail.setStatus(0);
        return toBooleanAjax(productOrderDetailService.save(productOrderDetail));
    }

    @ApiOperation("新增手动报工工序单")
    @PostMapping("/addNonautomatic")
    public AjaxResult addNonautomatic(@RequestBody ProductOrderDetail productOrderDetail) {
        productOrderDetail.setStatus(1);
        productOrderDetail.setStopTime(new Date());
        return toBooleanAjax(productOrderDetailService.save(productOrderDetail));
    }

    /**
     * 修改生产工单执行
     */
    @ApiOperation("修改生产工单执行")
    @PutMapping
    public AjaxResult edit(@RequestBody ProductOrderDetail productOrderDetail) {
        return toBooleanAjax(productOrderDetailService.updateById(productOrderDetail));
    }

    /**
     * 修改生产工单执行
     */
    @ApiOperation("暂停生产工单执行")
    @PutMapping("/stop")
    public AjaxResult stopOrderDetail(@RequestBody ProductOrderDetail productOrderDetail) {
        UpdateWrapper<ProductOrderDetail> updateWrapper = new UpdateWrapper<>();
        updateWrapper
                .set("stop_time",new Date())
                .set("status",1)
                .eq("equipment_id",productOrderDetail.getEquipmentId())
                .eq("task_id",productOrderDetail.getTaskId())
                .isNull("stop_time");
        return toBooleanAjax(productOrderDetailService.update(updateWrapper));
    }

    /**
     * 删除生产工单执行
     */
    @ApiOperation("删除生产工单执行")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(productOrderDetailService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 获取当前正在执行的工单
     * @return
     */
    @ApiOperation("获取当前正在执行的工单")
    @GetMapping("/listCurrentOrder")
    public AjaxResult listCurrentOrder(){
        return AjaxResult.success(productOrderDetailService.listCurrentOrder());
    }

    /**
     * 完成订单
     */
    @ApiOperation("完成工序执行单")
    @PostMapping("/completeOrder")
    public AjaxResult completeOrder(@RequestBody ProductOrderDetail productOrderDetail){

        productOrderDetailService.completeOrder(productOrderDetail);
        return AjaxResult.success();
    }
}

