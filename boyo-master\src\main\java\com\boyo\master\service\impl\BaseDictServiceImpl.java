package com.boyo.master.service.impl;

import java.util.List;

import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.master.mapper.BaseDictMapper;
import com.boyo.master.domain.BaseDict;
import com.boyo.master.service.IBaseDictService;

/**
 * 租户数据字典管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class BaseDictServiceImpl extends ServiceImpl<BaseDictMapper, BaseDict> implements IBaseDictService {
    private final BaseDictMapper baseDictMapper;


    /**
     * 查询租户数据字典管理列表
     *
     * @param baseDict 租户数据字典管理
     * @return baseDict 列表
     */
    @Override
    public List<BaseDict> selectBaseDictList(BaseDict baseDict) {
        return baseDictMapper.selectBaseDictList(baseDict);
    }
}
