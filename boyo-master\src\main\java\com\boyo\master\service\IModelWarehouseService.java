package com.boyo.master.service;

import java.util.List;

import com.boyo.master.domain.ModelWarehouse;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.master.vo.ModelWarehouseVO;

/**
 * 主数据-仓库管理Service接口
 *
 * <AUTHOR>
 */
public interface IModelWarehouseService extends IService<ModelWarehouse> {
    /**
     * 根据条件查询查询主数据-仓库管理列表
     *
     * @param modelWarehouse 主数据-仓库管理
     * @return 主数据-仓库管理集合
     */
    List<ModelWarehouseVO> selectModelWarehouseList(ModelWarehouse modelWarehouse);
}
