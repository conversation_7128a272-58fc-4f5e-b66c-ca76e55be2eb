<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmBusinessTypeMapper">

    <resultMap type="com.boyo.crm.entity.CrmBusinessType" id="CrmBusinessTypeResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmBusinessTypeList" parameterType="com.boyo.crm.entity.CrmBusinessType"
            resultMap="CrmBusinessTypeResult">
        select
        id, name, create_user_id, create_time, update_time, status
        from t_crm_business_type
        <where>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        order by id desc
    </select>
</mapper>

