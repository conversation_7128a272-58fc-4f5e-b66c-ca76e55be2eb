package com.boyo.master.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.thoughtworks.xstream.converters.time.LocalDateConverter;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 采购入库单
 * @TableName bu_enterprise_data_asset_purchase_inventory
 */
@TableName(value ="bu_enterprise_data_asset_purchase_inventory")
@Data
public class PurchaseInventory extends DataAssetBase implements Serializable {
    /**
     * 库存组织编号
     */
    @JSONField(name = "BODYCODE")
    @TableField(value = "bodycode")
    private String bodycode;

    /**
     * 库存组织名称
     */
    @JSONField(name = "BODYNAME")
    @TableField(value = "bodyname")
    private String bodyname;

    /**
     * 客商编号
     */
    @JSONField(name = "CUSTCODE")
    @TableField(value = "custcode")
    private String custcode;

    /**
     * 客商名称
     */
    @JSONField(name = "CUSTNAME")
    @TableField(value = "custname")
    private String custname;

    /**
     * 业务类型编码
     */
    @JSONField(name = "BUSICODE")
    @TableField(value = "busicode")
    private String busicode;

    /**
     * 业务类型名称
     */
    @JSONField(name = "BUSINAME")
    @TableField(value = "businame")
    private String businame;

    /**
     * 单据类型名称
     */
    @JSONField(name = "VBILLTYPENAME")
    @TableField(value = "vbilltypename")
    private String vbilltypename;

    /**
     * 存货编码
     */
    @JSONField(name = "INVCODE")
    @TableField(value = "invcode")
    private String invcode;

    /**
     * 存货名称
     */
    @JSONField(name = "INVNAME")
    @TableField(value = "invname")
    private String invname;

    /**
     * 规格
     */
    @JSONField(name = "INVSPEC")
    @TableField(value = "invspec")
    private String invspec;

    /**
     * 型号
     */
    @JSONField(name = "INVTYPE")
    @TableField(value = "invtype")
    private String invtype;

    /**
     * 应入数量
     */
    @JSONField(name = "NSHOULDINNUM")
    @TableField(value = "nshouldinnum")
    private BigDecimal nshouldinnum;

    /**
     * 实入数量
     */
    @JSONField(name = "NINNUM")
    @TableField(value = "ninnum")
    private BigDecimal ninnum;

    /**
     * 单据日期
     */
    @JSONField(name = "DBILLDATE")
    @TableField(value = "dbilldate")
    private Date dbilldate;

    /**
     * 含税单价
     */
    @JSONField(name = "NTAXPRICE")
    @TableField(value = "ntaxprice")
    private BigDecimal ntaxprice;

    /**
     * 含税金额
     */
    @JSONField(name = "NTAXMNY")
    @TableField(value = "ntaxmny")
    private BigDecimal ntaxmny;

    /**
     * 单据编码
     */
    @JSONField(name = "VBILLCODE")
    @TableField(value = "vbillcode")
    private String vbillcode;

    /**
     * 源头单据编码
     */
    @JSONField(name = "VFIRSTBILLCODE")
    @TableField(value = "vfirstbillcode")
    private String vfirstbillcode;

    @TableField(value = "children_company")
    private String childrenCompany;

    @JSONField(name = "DACCOUNTDATE",format="yyyy-MM-dd")
    @TableField(value = "daccountdate")
    private LocalDate daccountdate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
