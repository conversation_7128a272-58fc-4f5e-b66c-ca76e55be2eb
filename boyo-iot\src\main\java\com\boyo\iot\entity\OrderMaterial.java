package com.boyo.iot.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (OrderMaterial)实体类
 *
 * <AUTHOR>
 * @since 2023-02-28 09:16:06
 */
@Data
@TableName(value = "t_order_material")
public class OrderMaterial implements Serializable {
    private static final long serialVersionUID = 201539848575897745L;
            
    @TableId
    private Integer id;
    
    /**
    * 工单id
    */
    @TableField(value="order_id")
    private Integer orderId;
    /**
    * 物料id
    */
    @TableField(value="material_id")
    private Integer materialId;
    /**
    * 消耗量
    */
    @TableField(value="material_count")
    private Integer materialCount;
    /**
     * 物料名称
     */
    @TableField(exist = false)
    private String materialName;

}
