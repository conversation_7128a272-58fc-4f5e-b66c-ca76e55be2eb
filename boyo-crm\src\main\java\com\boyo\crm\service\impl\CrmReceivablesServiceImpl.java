package com.boyo.crm.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.boyo.common.annotation.DataScope;
import com.boyo.common.core.text.Convert;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.crm.entity.CrmVisit;
import com.boyo.crm.util.ActionEnum;
import com.boyo.crm.util.ActionUtil;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.entity.CrmReceivables;
import com.boyo.crm.mapper.CrmReceivablesMapper;
import com.boyo.crm.service.ICrmReceivablesService;

import java.util.Date;
import java.util.List;

/**
 * 回款表(CrmReceivables)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-28 10:22:52
 */
@Service("crmReceivablesService")
@AllArgsConstructor
public class CrmReceivablesServiceImpl extends ServiceImpl<CrmReceivablesMapper, CrmReceivables> implements ICrmReceivablesService {
    private final CrmReceivablesMapper crmReceivablesMapper;
    private final ActionUtil actionUtil;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    @DataScope(columns = "owner_user_id,create_user_id")
    public List<CrmReceivables> selectCrmReceivablesList(CrmReceivables crmReceivables) {
        return crmReceivablesMapper.selectCrmReceivablesList(crmReceivables);
    }

    @Override
    public boolean save(CrmReceivables entity) {
        entity.setOwnerUserId(SecurityUtils.getUserId());
        super.save(entity);
        if(StrUtil.isEmpty(entity.getNumber())){
            entity.setNumber("R-" + DateUtil.format(new Date(),"yyyyMMdd") + "-" + StrUtil.fillBefore(Convert.toStr(entity.getId()),'0',6));
        }
        actionUtil.editRecord(null,null, ActionEnum.RECEIVABLES,entity.getId(), null);
        super.updateById(entity);
        return true;
    }
    @Override
    public boolean updateById(CrmReceivables entity) {
        actionUtil.editRecord(super.getById(entity.getId()),entity,ActionEnum.RECEIVABLES,entity.getId(), CrmReceivables.class);
        return super.updateById(entity);
    }
}


