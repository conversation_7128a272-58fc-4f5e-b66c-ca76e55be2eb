package com.boyo.mes.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.mes.entity.MesModulRecord;
import com.boyo.mes.vo.ModuleRecordVO;

import java.util.List;

/**
 * (MesModulRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
public interface IMesModulRecordService extends IService<MesModulRecord> {

    /**
     * 查询多条数据
     *
     * @param mesModulRecord 对象信息
     * @return 对象列表
     */
    List<MesModulRecord> selectMesModulRecordList(MesModulRecord mesModulRecord);

    List<ModuleRecordVO> listCurrentModule();

    void stopModule(ModuleRecordVO vo);
    void changeModule(ModuleRecordVO vo);

    List<MesModulRecord> listCurrentModule1(MesModulRecord mesModul);
}
