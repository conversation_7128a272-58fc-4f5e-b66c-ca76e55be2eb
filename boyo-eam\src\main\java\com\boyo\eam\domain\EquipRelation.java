package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 车间、产线、工段关联表(EquipRelation)实体类
 *
 * <AUTHOR>
 * @since 2021-12-21 15:23:24
 */
@Data
@TableName(value = "equip_relation")
public class EquipRelation implements Serializable {
    private static final long serialVersionUID = 591068005892532606L;

    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 关联关系类型：0生产非生产，1车间，2产线，3工段
    */
    @TableField(value="relation_type")
    private String relationType;;
    /**
    * 关联关系：根据relation_type字段判断是谁的openid
    */
    @TableField(value="relation")
    private String relation;
    /**
    * 能源类型
    */
    @TableField(value="type")
    private String type;
    /**
    * 设备openid：对应equip_ledger表的openid（多个用逗号隔开）
    */
    @TableField(value="ledger_openid")
    private String ledgerOpenid;
    /**
    * 公式
    */
    @TableField(value="formula")
    private String formula;

    /** 额外字段 */
    @TableField(exist = false)
    private List<Map<String,Object>> calList;//日历
    @TableField(exist = false)
    private String name;//名字
}
