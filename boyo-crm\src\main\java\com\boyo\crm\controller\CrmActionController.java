package com.boyo.crm.controller;

import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.crm.entity.CrmAction;
import com.boyo.crm.service.ICrmActionService;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 操作记录表(CrmAction)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-28 18:36:06
 */
@Api("操作记录表")
@RestController
@RequestMapping("/crm/crmAction")
@AllArgsConstructor
public class CrmActionController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmActionService crmActionService;
    private final IEnterpriseUserService enterpriseUserService;

    /**
     * 查询操作记录表列表
     *
     */
    @ApiOperation("查询操作记录表列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmAction crmAction) {
        startPage();
        List<CrmAction> list = crmActionService.selectCrmActionList(crmAction);
        if(list != null && list.size() > 0){
            List<Long> ids = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                ids.add(list.get(i).getCreateUserId());
            }
            List<EnterpriseUser> userList = enterpriseUserService.selectByIds(ids);
            if(userList != null && userList.size() > 0){
                for (int i = 0; i < list.size(); i++) {
                    CrmAction action = list.get(i);
                    List<EnterpriseUser> temps = userList.stream().filter(s -> s.getId().equals(action.getCreateUserId())).collect(Collectors.toList());
                    list.get(i).setCreateUserName(temps.get(0).getUserFullName());
//                    for (int j = 0; j < userList.size(); j++) {
//                        if(list.get(i).getCreateUserId().equals(userList.get(j).getId())){
//                            list.get(i).setCreateUserName(userList.get(j).getUserFullName());
//                            break;
//                        }
//                    }
                }
            }
        }
        return getDataTable(list);
    }
    
    /**
     * 获取操作记录表详情
     */
    @ApiOperation("获取操作记录表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(crmActionService.getById(id));
    }

    /**
     * 新增操作记录表
     */
    @ApiOperation("新增操作记录表")
    @PostMapping
    public AjaxResult add(@RequestBody CrmAction crmAction) {
        return toBooleanAjax(crmActionService.save(crmAction));
    }

    /**
     * 修改操作记录表
     */
    @ApiOperation("修改操作记录表")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmAction crmAction) {
        return toBooleanAjax(crmActionService.updateById(crmAction));
    }

    /**
     * 删除操作记录表
     */
    @ApiOperation("删除操作记录表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmActionService.removeByIds(Arrays.asList(ids)));
    }

}
