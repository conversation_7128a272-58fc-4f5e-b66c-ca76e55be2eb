<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmBusinessMapper">

    <resultMap type="com.boyo.crm.entity.CrmBusiness" id="CrmBusinessResult">
        <result property="id" column="id"/>
        <result property="typeId" column="type_id"/>
        <result property="statusId" column="status_id"/>
        <result property="nextTime" column="next_time"/>
        <result property="customerId" column="customer_id"/>
        <result property="dealDate" column="deal_date"/>
        <result property="businessName" column="business_name"/>
        <result property="money" column="money"/>
        <result property="discountRate" column="discount_rate"/>
        <result property="totalPrice" column="total_price"/>
        <result property="remark" column="remark"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="ownerUserId" column="owner_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isEnd" column="is_end"/>
        <result property="statusRemark" column="status_remark"/>
        <result property="customerName" column="customer_name"></result>
        <result property="statusName" column="status_name"></result>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmBusinessList" parameterType="com.boyo.crm.entity.CrmBusiness" resultMap="CrmBusinessResult">
        select t1.*,t2.customer_name,t3.name as status_name from (select
        id, type_id, status_id, next_time, customer_id, deal_date, business_name, money, discount_rate, total_price,
        remark, create_user_id, owner_user_id, create_time, update_time, is_end, status_remark
        from t_crm_business
        <where>
            <if test="typeId != null">
                and type_id = #{typeId}
            </if>
            <if test="statusId != null">
                and status_id = #{statusId}
            </if>
            <if test="nextTime != null">
                and next_time = #{nextTime}
            </if>
            <if test="customerId != null">
                and customer_id = #{customerId}
            </if>
            <if test="dealDate != null">
                and deal_date = #{dealDate}
            </if>
            <if test="businessName != null and businessName != ''">
                and business_name = #{businessName}
            </if>
            <if test="money != null">
                and money = #{money}
            </if>
            <if test="discountRate != null">
                and discount_rate = #{discountRate}
            </if>
            <if test="totalPrice != null">
                and total_price = #{totalPrice}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="ownerUserId != null">
                and owner_user_id = #{ownerUserId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isEnd != null">
                and is_end = #{isEnd}
            </if>
            <if test="statusRemark != null and statusRemark != ''">and status_remark = #{statusRemark}
            </if>
            ${params.dataScope}
        </where>
        ) t1 left join t_crm_customer t2 on t1.customer_id = t2.id
        left join t_crm_business_status t3 on t1.status_id = t3.id
    </select>
</mapper>

