package com.boyo.crm.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import com.boyo.common.core.domain.BoyoBaseEntity;
import com.boyo.framework.annotation.PropertyMsg;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 商机表(CrmBusiness)实体类
 *
 * <AUTHOR>
 * @since 2022-03-26 09:56:24
 */
@Data
@TableName(value = "t_crm_business")
public class CrmBusiness extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = 798088093888880731L;
            
    @TableId
    private Integer id;
    
    /**
    * 商机状态组
    */
    @TableField(value="type_id")
    private Integer typeId;

    /**
     * 商机组名称
     */
    @TableField(exist = false)
    private String typeName;
    /**
    * 销售阶段
    */
    @TableField(value="status_id")
    private Integer statusId;

    /**
     * 商机阶段名称
     */
    @TableField(exist = false)
    private String statusName;
    /**
    * 下次联系时间
    */
    @TableField(value="next_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @PropertyMsg(value="下次联系时间")
    private Date nextTime;
    /**
    * 客户ID
    */
    @TableField(value="customer_id")
    @PropertyMsg(value="客户",type="customer")
    private Integer customerId;

    /**
     * 客户名称
     */
    @TableField(exist = false)
    private String customerName;
    /**
    * 预计成交日期
    */
    @TableField(value="deal_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @PropertyMsg(value="预计成交日期")
    private Date dealDate;
    /**
    * 商机名称
    */
    @TableField(value="business_name")
    @PropertyMsg(value="商机名称")
    private String businessName;
    /**
    * 商机金额
    */
    @TableField(value="money")
    @PropertyMsg(value="商机金额")
    private BigDecimal money;
    /**
    * 整单折扣
    */
    @TableField(value="discount_rate")
    @PropertyMsg(value="整单折扣")
    private BigDecimal discountRate;
    /**
    * 产品总金额
    */
    @TableField(value="total_price")
    @PropertyMsg(value="产品总金额")
    private BigDecimal totalPrice;
    /**
    * 备注
    */
    @TableField(value="remark")
    private String remark;
    /**
    * 创建人ID
    */
    @TableField(value="create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 负责人ID
    */
    @TableField(value="owner_user_id")
    private Long ownerUserId;
    /**
     * 负责人姓名
     */
    @TableField(exist = false)
    private String ownerUserName;
    /**
    * 创建时间
    */
    @TableField(value="create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 更新时间
    */
    @TableField(value="update_time",fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
    * 1赢单2输单3无效
    */
    @TableField(value="is_end")
    private Integer isEnd;
    
    @TableField(value="status_remark")
    private String statusRemark;

    @TableField(exist = false)
    private List<CrmBusinessProduct> productList;
}
