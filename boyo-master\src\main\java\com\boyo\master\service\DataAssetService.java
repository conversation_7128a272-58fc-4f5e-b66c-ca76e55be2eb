package com.boyo.master.service;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.boyo.master.domain.annotations.Enterprise;
import com.boyo.master.domain.enums.MethodType;

import java.lang.annotation.Annotation;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.text.ParseException;
import java.util.*;


/**
 * 对外提供数据资产服务的抽象，下面一层是数据资产存储类型的抽象类：mysql、iot，然后是具体数据资产的实现类
 * 因为是单继承，所以实现类需要继承mybatisplus的实现类
 * 后续如果还有，则参照现有的创建一个抽象类
 */
public interface DataAssetService {

    //region 数据资产查询

    /**
     * 该企业是否有当前数据资产数据
     * 目前来说全部改为true也没有关系，新加其他客户后就有关系了
     * 先这样吧，也不用改了，人家都不要判断了还改个屁
     *
     * @param enterpriseId
     * @return
     */
    default boolean currentDataAssetExist(Long enterpriseId) throws Exception {
        return true;
    }

    /**
     * 获取数据资产列表
     *
     * @return
     */
    default Object getDataAssetList(IPage page, Map<String, String> params) {
        throw new RuntimeException("当前公司没有此项数据资产");
    }

    default Object getMainTableData(Long enterpriseId, Map<String, String> params) {
        throw new RuntimeException("当前公司没有此项数据资产");
    }
    //endregion

    //region 获取选择器选项

    /**
     * 获取选择器选项
     *
     * @param dataAssetType
     * @param enterpriseId
     * @return
     */
    default Map<String, Object> getSelectorList(String dataAssetType, Long enterpriseId) {
        Map<String, Object> result = new HashMap<>();
//        // 获取选择器bean
//        SelectorService selectorService = SpringUtil.getBean(SelectorService.class);
//        // 组装数据库中的选择器数据
//        Map<String, List<Selector>> selectorMap = selectorService.list(
//                        new LambdaQueryWrapper<Selector>()
//                                .eq(Selector::getEnterpriseId, enterpriseId)
//                                .like(Selector::getDataAssetType, "," + dataAssetType + ","))
//                .stream().collect(Collectors.groupingBy(Selector::getType));
//        for (Map.Entry<String, List<Selector>> item : selectorMap.entrySet()) {
//            result.put(item.getKey(), item.getValue().stream().collect(Collectors.toMap(Selector::getKey, Selector::getValue)));
//        }
        Map<String, String> innerMap= new HashMap<>();
//        innerMap.put("10103","山东海汇环保设备有限公司");
//        innerMap.put("10170","海汇环保设备股份有限公司");
//        innerMap.put("10160","莒州金属材料");
//        innerMap.put("10202","热电");
//        innerMap.put("10304","莒州水泥");
//        innerMap.put("10311","兰官庄");
//        innerMap.put("10303","浮来水泥");
        innerMap.put("10103","山东海汇环保设备有限公司"); //暂无
        innerMap.put("10170","海汇环保设备股份有限公司");
        innerMap.put("10160","莒州金属材料");
        innerMap.put("10202","热电"); //暂无
        innerMap.put("18803","莒州水泥");
        innerMap.put("18804","兰官庄");
        innerMap.put("18807","浮来水泥");
        result.put("company",innerMap);

        return result;
    }
    //endregion

    //region 导出Excel


    /**
     * 获取Service的实体类
     *
     * @return
     */
    default Class<?> getEntityClass() {
        Class<?> clazz = this.getClass();
        Type genericSuperclass = clazz.getGenericSuperclass();

        if (genericSuperclass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();

            if (actualTypeArguments.length > 1) {
                return (Class<?>) actualTypeArguments[1];
            }
        }
        throw new RuntimeException("获取实体类失败");
    }

    default List<List<String>> getExcelHead() {
        return null;
    }

    default List<List<String>> getExcelData(IPage<Object> page, Map<String, String> params) {
        return null;
    }
    //endregion

    //region 根据不同企业用户提供的个性化扩展

    /**
     * 同步数据资产
     * 直接已有开发的接口的步骤走就行，没必要提取共有的了，也没有共有的
     * remote_id 作为外部数据的最小保存粒度，可以先根据remote_id删除，然后再新增，达成更新数据的目的，但是这样操作注解最好不要使用自增主键
     */
    @Enterprise(value = "1845014766829256705", type = MethodType.TASK)
    default void syncDataAssetHaihui() throws ParseException, Exception {
    }

    @Enterprise(value = "1845014766829256705", type = MethodType.SCREEN)
    default Map<String, Object> getHaihuiScreenData(Map<String, String> params) {
        return new HashMap<>();
    }

    /**
     * 企业个性化扩展
     *
     * @param methodType   @Enterprise 方法类型
     * @param enterpriseId @Enterprise 企业id
     * @param args         dataAssetService中的执行目标方法参数
     * @return
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    default Object enterpriseSpecialExecute(MethodType methodType, Long enterpriseId, Object... args)
            throws InvocationTargetException, IllegalAccessException {
        // 寻找目标方法
        Class<? extends DataAssetService> aClass = this.getClass();
        Optional<Class<?>> dataAssetServiceInterface = Arrays.stream(aClass.getInterfaces())
                .filter(item -> item.equals(DataAssetService.class)).findFirst();
        if (!dataAssetServiceInterface.isPresent()) {
            throw new RuntimeException("当前企业没有扩展此项业务");
        }
        Method[] methods = dataAssetServiceInterface.get().getMethods();
        // 执行目标方法
        for (Method method : methods) {
            Enterprise annotation = method.getAnnotation(Enterprise.class);
            if (ObjUtil.isNotEmpty(annotation)
                    && methodType.equals(annotation.type())
                    && enterpriseId.toString().equals(annotation.value())) {
                return method.invoke(this, args);
            }
        }
        throw new RuntimeException("当前企业没有扩展此项业务");
    }

    /**
     * 获取当前方法上某个注解
     *
     * @param clazz
     * @param <T>
     * @return
     */
    default <T extends Annotation> T getCurrentMethodAnnotation(Class<T> clazz) {
        // 获取当前实现类名
        String currentClassName = this.getClass().getName();
        // 从栈中根据当前类获取执行的方法名
        String methodName = Arrays.stream(Thread.currentThread().getStackTrace()).filter(item -> item.getClassName().startsWith(currentClassName))
                .findFirst().get().getMethodName();
        try {
            // 获取当前接口上的方法上的注解
            return Arrays.stream(this.getClass().getInterfaces()).filter(anInterface -> anInterface.equals(DataAssetService.class))
                    .findAny().get().getMethod(methodName).getAnnotation(clazz);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
    }
    //endregion
}
