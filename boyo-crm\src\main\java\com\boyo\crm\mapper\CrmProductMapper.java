package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmProduct;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * CRM产品表(CrmProduct)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-24 15:52:38
 */
@Tenant
public interface CrmProductMapper extends BaseMapper<CrmProduct>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmProduct 实例对象
     * @return 对象列表
     */
    List<CrmProduct> selectCrmProductList(CrmProduct crmProduct);


}

