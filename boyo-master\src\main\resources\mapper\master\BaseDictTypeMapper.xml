<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.BaseDictTypeMapper">

    <resultMap type="com.boyo.master.domain.BaseDictType" id="BaseDictTypeResult">
        <result property="id" column="id"/>
        <result property="openid" column="openid"/>
        <result property="typeCode" column="type_code"/>
        <result property="typeDesc" column="type_desc"/>
    </resultMap>

    <sql id="selectBaseDictTypeVo">
        select id, openid, type_code, type_desc
        from t_base_dict_type
    </sql>

    <select id="selectBaseDictTypeList" parameterType="com.boyo.master.domain.BaseDictType"
            resultMap="BaseDictTypeResult">
        <include refid="selectBaseDictTypeVo"/>
        <where>
            <if test="openid != null  and openid != ''">
                and openid = #{openid}
            </if>
            <if test="typeCode != null  and typeCode != ''">
                and type_code = #{typeCode}
            </if>
            <if test="typeDesc != null  and typeDesc != ''">
                and type_desc = #{typeDesc}
            </if>
        </where>
    </select>
</mapper>
