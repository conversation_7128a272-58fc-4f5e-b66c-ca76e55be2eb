package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipLedger;
import com.boyo.eam.mapper.EquipLedgerMapper;
import com.boyo.eam.service.IEquipLedgerService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备台账(EquipLedger)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:04
 */
@Service("equipLedgerService")
@AllArgsConstructor
@Tenant
public class EquipLedgerServiceImpl extends ServiceImpl<EquipLedgerMapper, EquipLedger> implements IEquipLedgerService {
    private final EquipLedgerMapper equipLedgerMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipLedger> selectEquipLedgerList(EquipLedger equipLedger) {
        equipLedger.setParentOpenid("-1");
        List<EquipLedger> equipLedgerList = equipLedgerMapper.selectEquipLedgerList(equipLedger);
        for (EquipLedger e:equipLedgerList){
            e.setChildren(getChildren(e.getOpenid()));
        }
        return equipLedgerList;
    }

    /**
     * 递归查询出所有子设备
     * @param equipLedger
     * @return
     */
    private List<EquipLedger> getChildren(String parentOpenid){
        EquipLedger equipLedger = new EquipLedger();
        equipLedger.setParentOpenid(parentOpenid);
        List<EquipLedger> equipLedgerList = equipLedgerMapper.selectEquipLedgerList(equipLedger);
        if (equipLedgerList==null || equipLedgerList.size()==0){
            return null;
        }
        for (EquipLedger e:equipLedgerList){
            e.setChildren(getChildren(e.getOpenid()));
        }
        return equipLedgerList;
    }

    @Override
    public EquipLedger getInfo(Integer id) {
        return equipLedgerMapper.getInfo(id);
    }

}
