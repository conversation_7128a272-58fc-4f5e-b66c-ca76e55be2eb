<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipMaintTemplItemMapper">

    <resultMap type="com.boyo.eam.domain.EquipMaintTemplItem" id="EquipMaintTemplItemResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="equipMaintTemplOpenid" column="equip_maint_templ_openid" />
        <result property="item" column="item" />
        <result property="hour" column="hour" />
        <result property="hourUnit" column="hour_unit" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipMaintTemplItemList" parameterType="com.boyo.eam.domain.EquipMaintTemplItem" resultMap="EquipMaintTemplItemResult">
        select
          id, openid, equip_maint_templ_openid, item, hour, hour_unit, line_openid, create_by, create_time, update_by, update_time
        from equip_maint_templ_item
        <where>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
            <if test="equipMaintTemplOpenid != null and equipMaintTemplOpenid != ''">
                and equip_maint_templ_openid = #{equipMaintTemplOpenid}
            </if>
            <if test="item != null and item != ''">
                and item = #{item}
            </if>
            <if test="hour != null and hour != ''">
                and hour = #{hour}
            </if>
            <if test="hourUnit != null">
                and hour_unit = #{hourUnit}
            </if>
            <if test="lineOpenid != null and lineOpenid != ''">
                and line_openid = #{lineOpenid}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>
</mapper>

