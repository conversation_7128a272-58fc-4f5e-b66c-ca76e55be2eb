package com.boyo.crm.controller;

import com.boyo.crm.entity.CrmContractProduct;
import com.boyo.crm.service.ICrmContractProductService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 合同产品关系表(CrmContractProduct)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-27 17:04:54
 */
@Api("合同产品关系表")
@RestController
@RequestMapping("/crm/crmContractProduct")
@AllArgsConstructor
public class CrmContractProductController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmContractProductService crmContractProductService;

    /**
     * 查询合同产品关系表列表
     *
     */
    @ApiOperation("查询合同产品关系表列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmContractProduct crmContractProduct) {
        startPage();
        List<CrmContractProduct> list = crmContractProductService.selectCrmContractProductList(crmContractProduct);
        return getDataTable(list);
    }
    
    /**
     * 获取合同产品关系表详情
     */
    @ApiOperation("获取合同产品关系表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(crmContractProductService.getById(id));
    }

    /**
     * 新增合同产品关系表
     */
    @ApiOperation("新增合同产品关系表")
    @PostMapping
    public AjaxResult add(@RequestBody CrmContractProduct crmContractProduct) {
        return toBooleanAjax(crmContractProductService.save(crmContractProduct));
    }

    /**
     * 修改合同产品关系表
     */
    @ApiOperation("修改合同产品关系表")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmContractProduct crmContractProduct) {
        return toBooleanAjax(crmContractProductService.updateById(crmContractProduct));
    }

    /**
     * 删除合同产品关系表
     */
    @ApiOperation("删除合同产品关系表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmContractProductService.removeByIds(Arrays.asList(ids)));
    }

}
