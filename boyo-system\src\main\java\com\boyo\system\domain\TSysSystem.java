package com.boyo.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 系统管理
 * 表名 t_sys_system
 *
 * <AUTHOR>
 */
@ApiModel("系统表")
@Data
@TableName("t_sys_system")
public class TSysSystem extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "system_openid")
    private String systemOpenid;
    /**
     * 类别id
     */
    @ApiModelProperty("类别id")
    @TableField(value = "category_openid")
    private String categoryOpenid;
    /**
     * 系统名称
     */
    @ApiModelProperty("系统名称")
    @TableField(value = "system_name")
    private String systemName;
    /**
     * 系统编码
     */
    @ApiModelProperty("系统编码")
    @TableField(value = "system_code")
    private String systemCode;
    /**
     * 系统地址
     */
    @ApiModelProperty("系统地址")
    @TableField(value = "system_url")
    private String systemUrl;
    /**
     * 系统打开方式 0：当前标签 1：新标签
     */
    @ApiModelProperty("系统打开方式 0：当前标签 1：新标签")
    @TableField(value = "system_opentype")
    private String systemOpentype;
    /**
     * 系统图标
     */
    @ApiModelProperty("系统图标")
    @TableField(value = "system_icon")
    private String systemIcon;
    /**
     * 系统自定义图片
     */
    @ApiModelProperty("系统自定义图片")
    @TableField(value = "system_img")
    private String systemImg;
    /**
     * 是否需要申请开通 0：不需要 1：需要
     */
    @ApiModelProperty("是否需要申请开通 0：不需要 1：需要")
    @TableField(value = "system_apply")
    private String systemApply;
    /**
     * 开通费用
     */
    @ApiModelProperty("开通费用")
    @TableField(value = "system_price")
    private BigDecimal systemPrice;
    /**
     * 系统描述
     */
    @ApiModelProperty("系统描述")
    @TableField(value = "system_desc")
    private String systemDesc;
    /**
     * 状态 1启用 0停用
     */
    @ApiModelProperty("状态 1启用 0停用")
    @TableField(value = "system_status")
    private String systemStatus;
    /**
     * 序号
     */
    @ApiModelProperty("序号")
    @TableField(value = "system_order")
    private Integer systemOrder;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(value = "pc_show")
    private String pcShow;

    @TableField(exist = false)
    private List<BoyoMenu> functionList;
}
