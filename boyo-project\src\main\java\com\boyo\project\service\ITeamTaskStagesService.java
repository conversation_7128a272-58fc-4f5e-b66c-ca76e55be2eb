package com.boyo.project.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.project.entity.TeamTaskStages;
import java.util.List;

/**
 * 任务列表表(TeamTaskStages)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-10 11:03:15
 */
public interface ITeamTaskStagesService extends IService<TeamTaskStages> {

    /**
     * 查询多条数据
     *
     * @param teamTaskStages 对象信息
     * @return 对象列表
     */
    List<TeamTaskStages> selectTeamTaskStagesList(TeamTaskStages teamTaskStages);


}
