package com.boyo.wms.service;

import java.util.List;

import com.boyo.wms.entity.WmsFlow;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.wms.dto.WmsCheckOutDTO;
import com.boyo.wms.dto.WmsFlowDTO;
import com.boyo.wms.dto.WmsWarehousingDTO;
import com.boyo.wms.vo.DateFlowVO;
import com.boyo.wms.vo.WmsFlowVO;

/**
 * 出入库流水管理Service接口
 *
 * <AUTHOR>
 */
public interface IWmsFlowService extends IService<WmsFlow> {
    /**
     * 根据条件查询查询出入库流水管理列表
     *
     * @param wmsFlow 出入库流水管理
     * @return 出入库流水管理集合
     */
    List<WmsFlowVO> selectWmsFlowList(WmsFlow wmsFlow);

    boolean quickWarehousing(WmsWarehousingDTO warehousingDTO);

    boolean quickCheckOut(WmsCheckOutDTO checkOutDTO);

    boolean saveFlow(WmsFlowDTO entity);

    List<DateFlowVO> listDateFlow();

}
