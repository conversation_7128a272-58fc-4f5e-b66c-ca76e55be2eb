package com.boyo.framework.key;

/**
 * SSO键值
 */
public class Sso<PERSON><PERSON> extends BasePrefix {

	private SsoKey(int expireSeconds, String prefix) {
		super(expireSeconds, prefix);
	}

	public static SsoKey getCode = new SsoKey(0, "code");

	public static SsoKey getValidCode = new SsoKey(600, "validCode");
	public static SsoKey getTmpTicket = new SsoKey(0, "tmpTicket");
	public static SsoKey getUserTicket = new SsoKey(0, "userTicket");
	public static SsoKey getLoginUser = new SsoKey(0, "loginUser");
}
