package com.boyo.master.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 采购订单
 * @TableName bu_enterprise_data_asset_purchase_orders
 */
@TableName(value ="bu_enterprise_data_asset_purchase_orders")
@Data
public class PurchaseOrders extends DataAssetBase implements Serializable {
    /**
     * 订单编号
     */
    @JSONField(name = "VORDERCODE")
    @TableField(value = "vordercode")
    private String vordercode;

    /**
     * 订单日期
     */
    @JSONField(name = "DORDERDATE", format="yyyy-MM-dd")
    @TableField(value = "dorderdate")
    private LocalDate dorderdate;

    /**
     * 采购组织名称
     */
    @JSONField(name = "NAME")
    @TableField(value = "name")
    private String name;

    /**
     * 客商名称
     */
    @JSONField(name = "CUSTNAME")
    @TableField(value = "custname")
    private String custname;

    /**
     * 合同名称
     */
    @JSONField(name = "CT_CODE")
    @TableField(value = "ct_code")
    private String ctCode;

    @JSONField(name = "CT_NAME")
    @TableField(value = "ct_name")
    private String ctName;

    /**
     * 存货编码
     */
    @JSONField(name = "INVCODE")
    @TableField(value = "invcode")
    private String invcode;

    /**
     * 存货名称
     */
    @JSONField(name = "INVNAME")
    @TableField(value = "invname")
    private String invname;

    /**
     * 规格
     */
    @JSONField(name = "INVSPEC")
    @TableField(value = "invspec")
    private String invspec;

    /**
     * 型号
     */
    @JSONField(name = "INVTYPE")
    @TableField(value = "invtype")
    private String invtype;

    /**
     * 订货数量
     */
    @JSONField(name = "NORDERNUM")
    @TableField(value = "nordernum")
    private BigDecimal nordernum;

    /**
     * 计量单位名称
     */
    @JSONField(name = "MEASNAME")
    @TableField(value = "measname")
    private String measname;

    /**
     * 原币含税单价
     */
    @JSONField(name = "NORGTAXPRICE")
    @TableField(value = "NORGTAXPRICE")
    private BigDecimal norgtaxprice;

    /**
     * 原币价税合计
     */
    @JSONField(name = "NORIGINALTAXPRICEMNY")
    @TableField(value = "noriginaltaxpricemny")
    private BigDecimal noriginaltaxpricemny;

    @TableField(value = "children_company")
    private String childrenCompany;

    @JSONField(name = "DAUDITDATE",format="yyyy-MM-dd")
    @TableField(value = "dauditdate")
    private LocalDate dauditdate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
