package com.boyo.master.domain;

import com.boyo.common.annotation.Excel;
import com.boyo.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 大屏车间对象 screen_workshop
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
public class ScreenWorkshop extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long workshopId;

    /** 车间名 */
    @Excel(name = "车间名")
    private String workshopName;

    /** 车间编码 */
    @Excel(name = "车间编码")
    private String workshopCode;

    public void setWorkshopId(Long workshopId)
    {
        this.workshopId = workshopId;
    }

    public Long getWorkshopId()
    {
        return workshopId;
    }
    public void setWorkshopName(String workshopName)
    {
        this.workshopName = workshopName;
    }

    public String getWorkshopName()
    {
        return workshopName;
    }
    public void setWorkshopCode(String workshopCode)
    {
        this.workshopCode = workshopCode;
    }

    public String getWorkshopCode()
    {
        return workshopCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("workshopId", getWorkshopId())
                .append("workshopName", getWorkshopName())
                .append("workshopCode", getWorkshopCode())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
