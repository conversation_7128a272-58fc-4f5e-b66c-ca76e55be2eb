package com.boyo.iot.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FaultProp implements Serializable {
    private static final long serialVersionUID = -74308127809537094L;
//    属性id
    private Integer id;
//    设备id
    private Integer equipmentId;
//    设备编码
    private String equipmentCode;
//    属性编码
    private String code;
//    故障值
    private String faultVal;
    private String attrName;

    private Double attrMultiple;
    private String attrType;

    private Integer autoFault;
    private Integer autoBill;
    private Integer faultTime;
    private Integer billTime;
    /**
     * 最小值
     */
    private String minVal;
    /**
     * 最大值
     */
    private String maxVal;
}
