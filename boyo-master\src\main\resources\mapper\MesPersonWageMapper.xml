<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.MesPersonWageMapper">

    <resultMap type="com.boyo.master.domain.MesPersonWage" id="MesPersonWageResult">
        <result property="id" column="id"/>
        <result property="monthTime" column="monthTime"/>
        <result property="personCode" column="personCode"/>
        <result property="personName" column="personName"/>
        <result property="teamName" column="teamName"/>
        <result property="pieceCountWage" column="pieceCountWage"/>
        <result property="pieceHourWage" column="pieceHourWage"/>
        <result property="shouldGrantWage" column="shouldGrantWage"/>
        <result property="confirmWage" column="confirmWage"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectMesPersonWageVo">
        select monthTime,
               personCode,
               personName,
               teamName,
               pieceCountWage,
               pieceHourWage,
               shouldGrantWage,
               confirmWage,
               create_by,
               create_time,
               update_by,
               update_time
        from mes_person_wage

    </sql>

    <select id="selectMesPersonWageList" parameterType="MesPersonWage" resultMap="MesPersonWageResult">
        <include refid="selectMesPersonWageVo"/>
        <where>
            <if test="monthTime != null  and monthTime != ''">and monthTime = #{monthTime}</if>
            <if test="personCode != null  and personCode != ''">and personCode = #{personCode}</if>
            <if test="personName != null  and personName != ''">and personName like concat('%', #{personName}, '%')</if>
            <if test="teamName != null  and teamName != ''">and teamName like concat('%', #{teamName}, '%')</if>
            <if test="pieceCountWage != null ">and pieceCountWage = #{pieceCountWage}</if>
            <if test="pieceHourWage != null ">and pieceHourWage = #{pieceHourWage}</if>
            <if test="shouldGrantWage != null ">and shouldGrantWage = #{shouldGrantWage}</if>
            <if test="confirmWage != null ">and confirmWage = #{confirmWage}</if>
        </where>
        order by create_time desc,monthTime desc
    </select>

    <select id="selectMesPersonWageById" parameterType="String" resultMap="MesPersonWageResult">
        <include refid="selectMesPersonWageVo"/>
        where id = #{id}
    </select>

    <insert id="insertMesPersonWage" parameterType="MesPersonWage" useGeneratedKeys="true" keyProperty="id">
        insert into mes_person_wage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="monthTime != null and monthTime != ''">monthTime,</if>
            <if test="personCode != null and personCode != ''">personCode,</if>
            <if test="personName != null and personName != ''">personName,</if>
            <if test="teamName != null">teamName,</if>
            <if test="pieceCountWage != null">pieceCountWage,</if>
            <if test="pieceHourWage != null">pieceHourWage,</if>
            <if test="shouldGrantWage != null">shouldGrantWage,</if>
            <if test="confirmWage != null">confirmWage,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="monthTime != null and monthTime != ''">#{monthTime},</if>
            <if test="personCode != null and personCode != ''">#{personCode},</if>
            <if test="personName != null and personName != ''">#{personName},</if>
            <if test="teamName != null">#{teamName},</if>
            <if test="pieceCountWage != null">#{pieceCountWage},</if>
            <if test="pieceHourWage != null">#{pieceHourWage},</if>
            <if test="shouldGrantWage != null">#{shouldGrantWage},</if>
            <if test="confirmWage != null">#{confirmWage},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateMesPersonWage" parameterType="MesPersonWage">
        update mes_person_wage
        <trim prefix="SET" suffixOverrides=",">
            <if test="monthTime != null and monthTime != ''">monthTime = #{monthTime},</if>
            <if test="personCode != null and personCode != ''">personCode = #{personCode},</if>
            <if test="personName != null and personName != ''">personName = #{personName},</if>
            <if test="teamName != null">teamName = #{teamName},</if>
            <if test="pieceCountWage != null">pieceCountWage = #{pieceCountWage},</if>
            <if test="pieceHourWage != null">pieceHourWage = #{pieceHourWage},</if>
            <if test="shouldGrantWage != null">shouldGrantWage = #{shouldGrantWage},</if>
            <if test="confirmWage != null">confirmWage = #{confirmWage},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMesPersonWageById" parameterType="Long">
        delete
        from mes_person_wage
        where id = #{id}
    </delete>

    <delete id="deleteMesPersonWageByIds" parameterType="String">
        delete from mes_person_wage where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>