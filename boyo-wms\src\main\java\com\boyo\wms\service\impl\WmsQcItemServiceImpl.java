package com.boyo.wms.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.wms.entity.WmsQcItem;
import com.boyo.wms.mapper.WmsQcItemMapper;
import com.boyo.wms.service.IWmsQcItemService;
import java.util.List;

/**
 * Wms质检项(WmsQcItem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-08 15:20:56
 */
@Service("wmsQcItemService")
@AllArgsConstructor
@Tenant
public class WmsQcItemServiceImpl extends ServiceImpl<WmsQcItemMapper, WmsQcItem> implements IWmsQcItemService {
    private final WmsQcItemMapper wmsQcItemMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<WmsQcItem> selectWmsQcItemList(WmsQcItem wmsQcItem) {
        return wmsQcItemMapper.selectWmsQcItemList(wmsQcItem);
    }

}
