package com.boyo.master.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 工厂模型-产线
 * 表名 t_model_line
 *
 * <AUTHOR>
 */
@ApiModel("工厂模型-产线")
@Data
@TableName("t_model_line")
public class ModelLine extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "line_openid")
    private String lineOpenid;
    /**
     * 所属工厂
     */
    @ApiModelProperty("所属工厂")
    @TableField(value = "line_factory")
    private String lineFactory;
    /**
     * 所属车间
     */
    @ApiModelProperty("所属车间")
    @TableField(value = "line_workshop")
    private String lineWorkshop;
    /**
     * 产线名称
     */
    @ApiModelProperty("产线名称")
    @TableField(value = "line_name")
    private String lineName;
    /**
     * 产线简称
     */
    @ApiModelProperty("产线简称")
    @TableField(value = "line_abbreviation")
    private String lineAbbreviation;
    /**
     * 产线编码
     */
    @ApiModelProperty("产线编码")
    @TableField(value = "line_code")
    private String lineCode;
    /**
     * 状态 1启用 0停用
     */
    @ApiModelProperty("状态 1启用 0停用")
    @TableField(value = "line_status")
    private String lineStatus;
    /**
     * 产线logo
     */
    @ApiModelProperty("产线logo")
    @TableField(value = "line_img")
    private String lineImg;
    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    @TableField(value = "line_contacts")
    private String lineContacts;
    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    @TableField(value = "line_phone")
    private String linePhone;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "created_at")
    private Date createdAt;
    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    @TableField(value = "created_user")
    private String createdUser;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(value = "updated_at")
    private Date updatedAt;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "updated_user")
    private String updatedUser;

    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remark;

    /**
     * 工厂名称
     */
    @TableField(exist = false)
    private String factoryName;

    /**
     * 车间名称
     */
    @TableField(exist = false)
    private String workshopName;
    @TableField(exist = false)
    private String openid;
    @TableField(exist = false)
    private String name;
}
