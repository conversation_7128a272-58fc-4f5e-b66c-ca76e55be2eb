<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.system.mapper.EnterpriseRoleFunctionMapper">

    <resultMap type="com.boyo.system.domain.EnterpriseRoleFunction" id="EnterpriseRoleFunctionResult">
        <result property="id" column="id"/>
        <result property="roleOpenid" column="role_openid"/>
        <result property="functionOpenid" column="function_openid"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectEnterpriseRoleFunctionVo">
        select id, role_openid, function_openid, create_time, update_time
        from t_enterprise_role_function
    </sql>

    <select id="selectEnterpriseRoleFunctionList" parameterType="com.boyo.system.domain.EnterpriseRoleFunction"
            resultMap="EnterpriseRoleFunctionResult">
        <include refid="selectEnterpriseRoleFunctionVo"/>
        <where>
        </where>
    </select>
</mapper>
