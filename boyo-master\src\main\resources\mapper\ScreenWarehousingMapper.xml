<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.ScreenWarehousingMapper">

    <resultMap type="com.boyo.master.domain.ScreenWarehousing" id="ScreenWarehousingResult">
        <result property="id" column="id"/>
        <result property="warehousing" column="warehousing"/>
        <result property="storageTime" column="storage_time"/>
        <result property="workshopId" column="workshop_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectScreenWarehousingVo">
        select id, warehousing, storage_time, workshop_id, create_time, update_time
        from screen_warehousing
    </sql>

    <select id="selectScreenWarehousingList" parameterType="com.boyo.master.domain.ScreenWarehousing"
            resultMap="ScreenWarehousingResult">
        <include refid="selectScreenWarehousingVo"/>
        <where>
            <if test="workshopId != null ">and workshop_id = #{workshopId}</if>
            <if test="storageTime != null ">and storage_time = #{storageTime}</if>
        </where>
        order by storage_time
    </select>

    <select id="selectScreenWarehousingBetween" parameterType="com.boyo.master.domain.ScreenWarehousing"
            resultMap="ScreenWarehousingResult">
        <include refid="selectScreenWarehousingVo"/>
        <where>
            <if test="start != null and end !=null">
                storage_time BETWEEN DATE (#{start})
                AND DATE (#{end})
            </if>

        </where>
        order by storage_time
    </select>

    <select id="selectScreenWarehousingById" parameterType="Long" resultMap="ScreenWarehousingResult">
        <include refid="selectScreenWarehousingVo"/>
        where id = #{id}
    </select>
    <select id="getWarehousingCount" resultType="java.lang.Long">
        select COALESCE(SUM(warehousing), 0)
        from screen_warehousing
        WHERE storage_time BETWEEN DATE (#{start})
          AND DATE (#{end})

    </select>

    <insert id="insertScreenWarehousing" parameterType="com.boyo.master.domain.ScreenWarehousing"
            useGeneratedKeys="true" keyProperty="id">
        insert into screen_warehousing
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehousing != null">warehousing,</if>
            <if test="storageTime != null">storage_time,</if>
            <if test="workshopId != null">workshop_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warehousing != null">#{warehousing},</if>
            <if test="storageTime != null">#{storageTime},</if>
            <if test="workshopId != null">#{workshopId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateScreenWarehousing" parameterType="com.boyo.master.domain.ScreenWarehousing">
        update screen_warehousing
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehousing != null">warehousing = #{warehousing},</if>
            <if test="workshopId != null">workshop_id = #{workshopId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where storage_time = #{storageTime}
    </update>

    <delete id="deleteScreenWarehousingById" parameterType="Long">
        delete
        from screen_warehousing
        where id = #{id}
    </delete>

    <delete id="deleteScreenWarehousingByIds" parameterType="String">
        delete from screen_warehousing where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>