import request from '@/utils/request'

const prefix = '/${module}'

// 查询IoT故障清单(Iofault)列表
export function listIofault(query) {
  return request({
    url: prefix + '/iofault/list',
    method: 'get',
    params: query,
  })
}

// 查询IoT故障清单(Iofault)详细
export function getIofault(id) {
  return request({
    url: prefix + '/iofault/' + id,
    method: 'get',
  })
}

// 新增IoT故障清单(Iofault)
export function addIofault(data) {
  return request({
    url: prefix + '/iofault',
    method: 'post',
    data: data,
  })
}

// 修改IoT故障清单(Iofault)
export function updateIofault(data) {
  return request({
    url: prefix + '/iofault',
    method: 'put',
    data: data,
  })
}

// 删除IoT故障清单(Iofault)
export function delIofault(id) {
  return request({
    url: prefix + '/iofault/' + id,
    method: 'delete',
  })
}
