package com.boyo.crm.controller;

import cn.hutool.core.util.StrUtil;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.crm.entity.CrmCustomer;
import com.boyo.crm.service.ICrmCustomerService;
import com.boyo.crm.vo.CustomerVO;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

/**
 * CRM客户表(CrmCustomer)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-25 16:07:33
 */
@Api("CRM客户表")
@RestController
@RequestMapping("/crm/crmCustomer")
@AllArgsConstructor
public class CrmCustomerController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmCustomerService crmCustomerService;
    private final IEnterpriseUserService enterpriseUserService;

    /**
     * 查询CRM客户表列表
     *
     */
    @ApiOperation("查询CRM客户表列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmCustomer crmCustomer) {
        startPage();
        List<CrmCustomer> list = crmCustomerService.selectCrmCustomerList(crmCustomer);
        if(list != null && list.size() > 0){
            List<String> ids = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                if(StrUtil.isNotEmpty(list.get(i).getOwnerUserId())){
                    ids.add(list.get(i).getOwnerUserId());
                }
            }
            List<EnterpriseUser> userList = enterpriseUserService.selectByOpenIds(ids);
            if(userList != null && userList.size() > 0){
                for (int i = 0; i < list.size(); i++) {
                    for (int j = 0; j < userList.size(); j++) {
                        if(StrUtil.isNotEmpty(list.get(i).getOwnerUserId()) && list.get(i).getOwnerUserId().equals(userList.get(j).getUserOpenid())){
                            list.get(i).setOwnerUserName(userList.get(j).getUserFullName());
                            break;
                        }
                    }
                }
            }
        }
        return getDataTable(list);
    }

    @GetMapping("/listPoolCustomer")
    public TableDataInfo listPoolCustomer(CrmCustomer crmCustomer) {
        startPage();
        List<CrmCustomer> list = crmCustomerService.selectPoolCustomerList(crmCustomer);
        return getDataTable(list);
    }

    @GetMapping( "/moveToPool")
    public AjaxResult moveToPool(Integer id){
        crmCustomerService.moveToPool(id);
        return AjaxResult.success();
    }

    @GetMapping( "/claimCustomer")
    public AjaxResult claimCustomer(Integer id){
        crmCustomerService.claimCustomer(id);
        return AjaxResult.success();
    }
    /**
     * 获取CRM客户表详情
     */
    @ApiOperation("获取CRM客户表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(crmCustomerService.getById(id));
    }

    @ApiOperation("获取CRM客户表详情")
    @GetMapping(value = "/getCustomerDetail/{id}")
    public AjaxResult getCustomerDetail(@PathVariable("id") Integer id){
        CustomerVO customerVO = new CustomerVO();
        CrmCustomer customer = new CrmCustomer();
        customer.setId(id);
        List<CrmCustomer> list = crmCustomerService.selectCrmCustomerList(customer);
        if(list != null && list.size() > 0){
            customer = list.get(0);
            EnterpriseUser user = enterpriseUserService.selectByOpenId(customer.getOwnerUserId());
            customer.setOwnerUserName(user.getUserFullName());
            customerVO.setCustomer(customer);
        }else{
            return null;
        }
        return AjaxResult.success(customerVO);
    }
    /**
     * 新增CRM客户表
     */
    @ApiOperation("新增CRM客户表")
    @PostMapping
    public AjaxResult add(@RequestBody CrmCustomer crmCustomer) {
        return toBooleanAjax(crmCustomerService.save(crmCustomer));
    }

    /**
     * 修改CRM客户表
     */
    @ApiOperation("修改CRM客户表")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmCustomer crmCustomer) {
        return toBooleanAjax(crmCustomerService.updateById(crmCustomer));
    }

    /**
     * 删除CRM客户表
     */
    @ApiOperation("删除CRM客户表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmCustomerService.removeByIds(Arrays.asList(ids)));
    }

}
