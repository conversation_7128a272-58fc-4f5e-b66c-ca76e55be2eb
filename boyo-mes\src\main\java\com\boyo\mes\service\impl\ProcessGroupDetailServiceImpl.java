package com.boyo.mes.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.framework.annotation.Tenant;
import com.boyo.mes.entity.ProcessEquipment;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.ProcessGroupDetail;
import com.boyo.mes.mapper.ProcessGroupDetailMapper;
import com.boyo.mes.service.IProcessGroupDetailService;
import java.util.List;

/**
 * 工序组详情(ProcessGroupDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Service("processGroupDetailService")
@AllArgsConstructor
@Tenant

public class ProcessGroupDetailServiceImpl extends ServiceImpl<ProcessGroupDetailMapper, ProcessGroupDetail> implements IProcessGroupDetailService {
    private final ProcessGroupDetailMapper processGroupDetailMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<ProcessGroupDetail> selectProcessGroupDetailList(ProcessGroupDetail processGroupDetail) {
        return processGroupDetailMapper.selectProcessGroupDetailList(processGroupDetail);
    }

    @Override
    public void addProcessGroupDetail(JSONObject object) {
        Integer id = object.getInteger("id");
        QueryWrapper<ProcessGroupDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_id",id);
        processGroupDetailMapper.delete(queryWrapper);
        String[] ids = object.getString("processIds").split(",");
        for (String process:ids) {
            if(StrUtil.isNotEmpty(process)){
                ProcessGroupDetail temp = new ProcessGroupDetail();
                temp.setGroupId(id);
                temp.setProcessId(Convert.toInt(process));
                processGroupDetailMapper.insert(temp);
            }
        }
//        for (int i = 0; i < ids.length; i++) {
//            ProcessGroupDetail temp = new ProcessGroupDetail();
//            String[] split = ids[i].split("-");
//            temp.setGroupId(id);
//            temp.setProcessId(Integer.valueOf(split[0]));
//            temp.setSortNum(Integer.valueOf(split[1]));
//            processGroupDetailMapper.insert(temp);
//        }
    }
}
