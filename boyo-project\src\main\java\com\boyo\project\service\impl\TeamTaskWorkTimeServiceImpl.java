package com.boyo.project.service.impl;

import cn.hutool.core.date.DateUtil;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.project.entity.TeamTaskWorkTime;
import com.boyo.project.mapper.TeamTaskWorkTimeMapper;
import com.boyo.project.service.ITeamTaskWorkTimeService;

import java.util.Date;
import java.util.List;

/**
 * 任务工时表(TeamTaskWorkTime)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-18 11:23:24
 */
@Service("teamTaskWorkTimeService")
@AllArgsConstructor
@Tenant
public class TeamTaskWorkTimeServiceImpl extends ServiceImpl<TeamTaskWorkTimeMapper, TeamTaskWorkTime> implements ITeamTaskWorkTimeService {
    private final TeamTaskWorkTimeMapper teamTaskWorkTimeMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<TeamTaskWorkTime> selectTeamTaskWorkTimeList(TeamTaskWorkTime teamTaskWorkTime) {
        return teamTaskWorkTimeMapper.selectTeamTaskWorkTimeList(teamTaskWorkTime);
    }

    @Override
    public boolean save(TeamTaskWorkTime entity) {
        entity.setCreateTime(DateUtil.formatDateTime(new Date()));
        entity.setMemberCode(SecurityUtils.getUserOpenid());
        return super.save(entity);
    }
}
