package com.boyo.system.mapper;

import java.util.List;

import com.boyo.system.domain.TSysSystem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;


/**
 * 系统管理Mapper接口
 *
 * <AUTHOR>
 */
public interface TSysSystemMapper extends BaseMapper<TSysSystem> {

    /**
     * 查询系统管理列表
     *
     * @param tSysSystem 系统管理
     * @return TSysSystem集合
     */
    List<TSysSystem> selectTSysSystemList(TSysSystem tSysSystem);

    /**
     * 获取当前企业有权限的系统
     * @param enterpriseOpenid
     * @return
     */
    List<TSysSystem> findEnterpriseSystem(String enterpriseOpenid);

    List<TSysSystem> findUserSystem(@Param("enterpriseOpenid") String enterpriseOpenid, @Param("userOpenid") String userOpenid);

}
