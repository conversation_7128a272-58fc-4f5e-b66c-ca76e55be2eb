<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.MesCuttingMapper">

    <resultMap type="com.boyo.mes.entity.MesCutting" id="MesCuttingResult">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="allLength" column="all_length"/>
        <result property="cuttingLength" column="cutting_length"/>
        <result property="cuttingCount" column="cutting_count"/>
        <result property="surplus" column="surplus"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="orderNum" column="order_num"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectMesCuttingList" parameterType="com.boyo.mes.entity.MesCutting" resultMap="MesCuttingResult">
        select t1.*,t2.order_num from (select
        *
        from t_mes_cutting
        <where>
            <if test="orderId != null">
                and order_id = #{orderId}
            </if>
            <if test="allLength != null">
                and all_length = #{allLength}
            </if>
            <if test="cuttingLength != null">
                and cutting_length = #{cuttingLength}
            </if>
            <if test="cuttingCount != null">
                and cutting_count = #{cuttingCount}
            </if>
            <if test="surplus != null">
                and surplus = #{surplus}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
        </where>
        ) t1 left join t_product_order t2 on t1.order_id = t2.id
    </select>
</mapper>

