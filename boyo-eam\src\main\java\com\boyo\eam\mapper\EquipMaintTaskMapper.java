package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipMaintTask;

import java.util.List;

/**
 * 维保任务管理(EquipMaintTask)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:32
 */
public interface EquipMaintTaskMapper extends BaseMapper<EquipMaintTask>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipMaintTask 实例对象
     * @return 对象列表
     */
    List<EquipMaintTask> selectEquipMaintTaskList(EquipMaintTask equipMaintTask);


}

