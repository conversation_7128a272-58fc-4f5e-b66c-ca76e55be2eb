package com.boyo.web.controller.haihui;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.master.domain.MesPersonWage;
import com.boyo.master.domain.MesProductionPlan;
import com.boyo.master.service.IMesPersonWageService;
import com.boyo.master.service.MesProductionPlanService;
import com.boyo.web.lysso.controller.HttpRequest;
import com.boyo.web.lysso.controller.HttpResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 生产计划Controller
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RestController
@RequestMapping("/haihui/u9c")
public class U9CController extends BaseController {


    int a=0;
    private static final String MES_BASEURL = "http://192.168.222.33:9114/api/outApi/listMesProductionSchedule?pageSize=10000&pageNum=1";
    private static final String WAGE_BASEURL = "http://192.168.222.33:9114/api/outApi/listMesPerformance?pageSize=10000&pageNum=1";
    private static final String U9C_BASE_URL = "http://192.168.222.55/U9C";
    private static final String U9C_TOKEN_URL = "http://192.168.222.55/U9C/webapi/OAuth2/AuthLogin?clientid=BPM&clientsecret=bef5f863e9a2483f9126b9fa267a8705&entcode=01&orgcode=100&usercode=16";
    private static final String Token = "";

    @Autowired
    private MesProductionPlanService mesProductionPlanService;
    @Autowired
    private IMesPersonWageService wageDataService;


    @Scheduled(cron = "0 20 12,00 * * ?")//每天12：20和00：20执行各一次
    public AjaxResult getPlanData() {
        String url = MES_BASEURL;
        try {
            HttpRequest requestPlanData = HttpRequest.builder()
                    .setUrl(url)
                    .setMethod(HttpRequest.Method.GET)
                    .addHeader("Content-Type", "application/json; charset=UTF-8")
                    .addHeader("Accept", "*/*")
                    .addHeader("Connection", "keep-alive");
            HttpResponse response = requestPlanData.get();
            JSONObject jsonObject = JSONObject.parseObject(response.getText());
            savePlanData(jsonObject);
            return AjaxResult.success(jsonObject);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;


    }

        @Scheduled(cron = "0 20 12,00 * * ?")//每天12：20和00：20执行各一次
//    @Scheduled(cron = "10 0/10 * * * ?")//
    public AjaxResult getU9CData() {
        if(a++>=3){
            return null;
        }
        final String token = getToken();
        System.out.println("================================U9C======================================");
        System.out.println(token);

        // 获取accessToken
        HttpRequest request1 = HttpRequest.builder()
                .setUrl(U9C_BASE_URL + "/webapi/RcvRptDoc/Query")
                .setMethod(HttpRequest.Method.POST)
                .addHeader("Content-Type", "application/json; charset=UTF-8")
//                .addHeader("token", "Bearer "+token)
                .addHeader("token", token)
                .addPostData("rcvRptDocDatas", "[{}]");
        HttpResponse response = request1.post();

        JSONObject responseJson = JSONObject.parseObject(response.getText());
        System.out.println("================================U9CresponseJson1======================================");
        System.out.println(responseJson);
        System.out.println("================================U9CresponseJson2======================================");
        System.out.println(responseJson.toJSONString());

        return null;


    }


    public String getToken() {
        try {
            HttpRequest requestPlanData = HttpRequest.builder()
                    .setUrl(U9C_TOKEN_URL)
                    .setMethod(HttpRequest.Method.GET)
                    .addHeader("Content-Type", "application/json; charset=UTF-8")
                    .addHeader("Accept", "*/*")
                    .addHeader("Connection", "keep-alive");
            HttpResponse response = requestPlanData.get();
            JSONObject jsonObject = JSONObject.parseObject(response.getText());
            return jsonObject.getString("Data");

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;


    }


    private int savePlanData(JSONObject jsonObject) {
        if (!jsonObject.containsKey("rows")) {
            return 0;
        }
        final JSONArray rows = (JSONArray) jsonObject.get("rows");
        int successCount = 0;
        if (rows == null) {
            return 0;
        }
        for (int i = 0; i < rows.size(); i++) {
            try {
                JSONObject oneData = rows.getJSONObject(i);
                MesProductionPlan productionPlan = new MesProductionPlan();
                // 设置销售数据对象的各个字段
                productionPlan.setPlanCode(oneData.getString("planCode"));
                productionPlan.setProjectNumber(oneData.getString("projectNumber"));
                productionPlan.setProjectName(oneData.getString("projectName"));
                productionPlan.setQuantity(oneData.getLong("quantity"));
                productionPlan.setSplitQuantity(oneData.getLong("splitQuantity"));
                productionPlan.setNotSplitQuantity(oneData.getLong("notSplitQuantity"));
                productionPlan.setPreOpeningDays(oneData.getString("preOpeningDays"));
                productionPlan.setPreCompletionDays(oneData.getString("preCompletionDays"));
                productionPlan.setAgentType(oneData.getString("agentType"));
                productionPlan.setStatus(oneData.getString("status"));
                productionPlan.setNoticeNo(oneData.getString("noticeNo"));
                productionPlan.setDispatchQuantity(oneData.getLong("dispatchQuantity"));
                productionPlan.setWorkReportsQuantity(oneData.getLong("workReportsQuantity"));
                productionPlan.setWorkNotReportsQuantity(oneData.getLong("workNotReportsQuantity"));
                productionPlan.setQualifiedQuantity(oneData.getLong("qualifiedQuantity"));
                productionPlan.setProductCode(oneData.getString("productCode"));
                productionPlan.setProductName(oneData.getString("productName"));
                productionPlan.setCreateTime(oneData.getDate("createTime"));
                productionPlan.setUpdateTime(new Date());
                productionPlan.setCreateBy(oneData.getString("createBy"));
                productionPlan.setId(UUID.randomUUID().toString());
                productionPlan.setId(oneData.get("id").toString());
                final MesProductionPlan mesProductionPlan = mesProductionPlanService.selectMesProductionPlanById(productionPlan.getId());
                if (mesProductionPlan == null) {
                    successCount += mesProductionPlanService.insertMesProductionPlan(productionPlan);
                } else {
                    successCount += mesProductionPlanService.updateMesProductionPlan(productionPlan);
                }
            } catch (Exception e) {
                logger.error("mes savePlanData保存数据失败");
                e.printStackTrace();
            }
        }
        return successCount;
    }

    @Scheduled(cron = "0 28 12,00 * * ?")//每天12：28和00：28执行各一次
    public AjaxResult getWageData() {
        String url = WAGE_BASEURL;
        try {
            HttpRequest request1 = HttpRequest.builder()
                    .setUrl(url)
                    .setMethod(HttpRequest.Method.GET)
                    .addHeader("Content-Type", "application/json; charset=UTF-8")
                    .addHeader("Accept", "*/*")
                    .addHeader("Connection", "keep-alive");
            HttpResponse response = request1.get();
            JSONObject jsonObject = JSONObject.parseObject(response.getText());
            saveWageData(jsonObject);
            return AjaxResult.success(jsonObject);

        } catch (Exception e) {
            AjaxResult.error(e.getMessage());
        }
        return null;

    }


    private int saveWageData(JSONObject jsonObject) {
        // 非空校验
        if (jsonObject == null || !jsonObject.containsKey("rows")) {
            logger.error("mes      输入的 JSON 数据为空或缺少 rows 字段");
            return 0;
        }

        final JSONArray rows = jsonObject.getJSONArray("rows");
        if (rows == null || rows.isEmpty()) {
            logger.warn("mes      rows 数组为空，无需保存工资数据");
            return 0;
        }

        int successCount = 0;

        for (int i = 0; i < rows.size(); i++) {
            try {
                JSONObject oneData = rows.getJSONObject(i);

                // 校验并构造 MesPersonWage 对象
                MesPersonWage personWage = buildPersonWage(oneData);
                if (personWage == null) {
                    logger.error("mes      构造工资数据失败，跳过当前记录");
                    continue;
                }

                // 查询是否存在相同记录
                final MesPersonWage mesPersonWage = wageDataService.selectMesPersonWageById(personWage.getId());
                if (mesPersonWage != null) {
                    successCount += wageDataService.updateMesPersonWage(personWage);
                } else {
                    successCount += wageDataService.insertMesPersonWage(personWage);
                }
            } catch (JSONException jsonException) {
                logger.error("mes      解析 JSON 数据失败: " + jsonException.getMessage());
            } catch (Exception e) {
                logger.error("mes      保存工资数据失败: " + e.getMessage(), e);
            }
        }

        return successCount;
    }

    /**
     * 构造 MesPersonWage 对象，并校验字段有效性
     */
    private MesPersonWage buildPersonWage(JSONObject oneData) {
        try {
            MesPersonWage personWage = new MesPersonWage();
            personWage.setPersonCode(getSafeString(oneData, "personCode"));
            personWage.setPersonName(getSafeString(oneData, "personName"));
            personWage.setTeamName(getSafeString(oneData, "teamName"));
            personWage.setPieceCountWage(getSafeLong(oneData, "pieceCountWage"));
            personWage.setPieceHourWage(getSafeLong(oneData, "pieceHourWage"));
            personWage.setShouldGrantWage(getSafeLong(oneData, "shouldGrantWage"));
            personWage.setConfirmWage(getSafeLong(oneData, "confirmWage"));
            personWage.setMonthTime(getSafeString(oneData, "monthTime"));
            personWage.setCreateBy(getSafeString(oneData, "createBy"));
            personWage.setId(personWage.getPersonCode() + personWage.getMonthTime() + personWage.getPersonName());
            return personWage;
        } catch (Exception e) {
            logger.error("mes      构造 MesPersonWage 对象失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 安全获取字符串值，避免字段缺失或类型不匹配
     */
    private String getSafeString(JSONObject jsonObject, String key) {
        return jsonObject.containsKey(key) ? jsonObject.getString(key) : "";
    }

    /**
     * 安全获取长整型值，避免字段缺失或类型不匹配
     */
    private Long getSafeLong(JSONObject jsonObject, String key) {
        return jsonObject.containsKey(key) ? jsonObject.getLong(key) : 0L;
    }


}
