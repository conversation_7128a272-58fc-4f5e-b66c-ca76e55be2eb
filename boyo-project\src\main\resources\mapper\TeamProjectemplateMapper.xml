<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.project.mapper.TeamProjectemplateMapper">

    <resultMap type="com.boyo.project.entity.TeamProjectemplate" id="TeamProjectemplateResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="sort" column="sort"/>
        <result property="createTime" column="create_time"/>
        <result property="code" column="code"/>
        <result property="organizationCode" column="organization_code"/>
        <result property="cover" column="cover"/>
        <result property="memberCode" column="member_code"/>
        <result property="isSystem" column="is_system"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectTeamProjectemplateList" parameterType="com.boyo.project.entity.TeamProjectemplate"
            resultMap="TeamProjectemplateResult">
        select
        id, name, description, sort, create_time, code, organization_code, cover, member_code, is_system
        from team_project_template
        <where>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="description != null and description != ''">
                and description = #{description}
            </if>
            <if test="sort != null">
                and sort = #{sort}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="organizationCode != null and organizationCode != ''">
                and organization_code = #{organizationCode}
            </if>
            <if test="cover != null and cover != ''">
                and cover = #{cover}
            </if>
            <if test="memberCode != null and memberCode != ''">
                and member_code = #{memberCode}
            </if>
            <if test="isSystem != null">
                and is_system = #{isSystem}
            </if>
        </where>
        order by id desc
    </select>
</mapper>

