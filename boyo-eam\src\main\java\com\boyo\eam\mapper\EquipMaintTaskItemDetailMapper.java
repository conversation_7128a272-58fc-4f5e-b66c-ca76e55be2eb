package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipMaintTaskItemDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 维保任务管理-维保项目-明细(EquipMaintTaskItemDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:33
 */
public interface EquipMaintTaskItemDetailMapper extends BaseMapper<EquipMaintTaskItemDetail>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipMaintTaskItemDetail 实例对象
     * @return 对象列表
     */
    List<EquipMaintTaskItemDetail> selectEquipMaintTaskItemDetailList(EquipMaintTaskItemDetail equipMaintTaskItemDetail);


    EquipMaintTaskItemDetail getDetailAndRecord(@Param("id") Integer id);
}

