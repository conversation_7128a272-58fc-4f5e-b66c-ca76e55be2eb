package com.boyo.wms.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.wms.entity.WmsFlowqcIndex;
import com.boyo.wms.mapper.WmsFlowqcIndexMapper;
import com.boyo.wms.service.IWmsFlowqcIndexService;
import java.util.List;

/**
 * (WmsFlowqcIndex)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-10 15:12:17
 */
@Service("wmsFlowqcIndexService")
@AllArgsConstructor
@Tenant
public class WmsFlowqcIndexServiceImpl extends ServiceImpl<WmsFlowqcIndexMapper, WmsFlowqcIndex> implements IWmsFlowqcIndexService {
    private final WmsFlowqcIndexMapper wmsFlowqcIndexMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<WmsFlowqcIndex> selectWmsFlowqcIndexList(WmsFlowqcIndex wmsFlowqcIndex) {
        return wmsFlowqcIndexMapper.selectWmsFlowqcIndexList(wmsFlowqcIndex);
    }

}
