<template>
  <a-modal width="30%" :maskClosable="false" :visible="open" @cancel="cancel">
    <template #title>
      <a-icon type="security-scan" />
      {{ formTitle }}
    </template>
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      layout="horizontal"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
           <a-form-model-item label="">
        <a-input
          :size="formSize"
          v-model="form.code"
          :placeholder="$t('app.global.please.input') + ''"
        />
      </a-form-model-item>
           <a-form-model-item label="操作人id">
        <a-input
          :size="formSize"
          v-model="form.memberCode"
          :placeholder="$t('app.global.please.input') + '操作人id'"
        />
      </a-form-model-item>
           <a-form-model-item label="操作内容">
        <a-input
          :size="formSize"
          v-model="form.content"
          :placeholder="$t('app.global.please.input') + '操作内容'"
        />
      </a-form-model-item>
           <a-form-model-item label="">
        <a-input
          :size="formSize"
          v-model="form.remark"
          :placeholder="$t('app.global.please.input') + ''"
        />
      </a-form-model-item>
           <a-form-model-item label="操作类型">
        <a-input
          :size="formSize"
          v-model="form.type"
          :placeholder="$t('app.global.please.input') + '操作类型'"
        />
      </a-form-model-item>
           <a-form-model-item label="添加时间">
        <a-input
          :size="formSize"
          v-model="form.createTime"
          :placeholder="$t('app.global.please.input') + '添加时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="任务id">
        <a-input
          :size="formSize"
          v-model="form.sourceCode"
          :placeholder="$t('app.global.please.input') + '任务id'"
        />
      </a-form-model-item>
           <a-form-model-item label="场景类型">
        <a-input
          :size="formSize"
          v-model="form.actionType"
          :placeholder="$t('app.global.please.input') + '场景类型'"
        />
      </a-form-model-item>
           <a-form-model-item label="">
        <a-input
          :size="formSize"
          v-model="form.toMemberCode"
          :placeholder="$t('app.global.please.input') + ''"
        />
      </a-form-model-item>
           <a-form-model-item label="是否评论，0：否">
        <a-input
          :size="formSize"
          v-model="form.isComment"
          :placeholder="$t('app.global.please.input') + '是否评论，0：否'"
        />
      </a-form-model-item>
           <a-form-model-item label="">
        <a-input
          :size="formSize"
          v-model="form.projectCode"
          :placeholder="$t('app.global.please.input') + ''"
        />
      </a-form-model-item>
           <a-form-model-item label="">
        <a-input
          :size="formSize"
          v-model="form.icon"
          :placeholder="$t('app.global.please.input') + ''"
        />
      </a-form-model-item>
           <a-form-model-item label="是否机器人">
        <a-input
          :size="formSize"
          v-model="form.isRobot"
          :placeholder="$t('app.global.please.input') + '是否机器人'"
        />
      </a-form-model-item>
        </a-form-model>
    <template #footer>
      <a-space>
        <a-button :size="formSize" icon="close" type="danger" @click="cancel">
          {{ $t('app.global.close') }}
        </a-button>
        <a-button :size="formSize" icon="save" type="primary" @click="submitForm">
          {{ $t('app.global.save') }}
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script>
import { addTeamProjeclog, updateTeamProjeclog, getTeamProjeclog } from '@/api/teamProjeclog'
export default {
  data() {
    return {
      //新增或修改
      updateState: false,
      formTitle: '项目日志表',
      // 表单参数
      form: {
           id: '',
           code: '',
           memberCode: '',
           content: '',
           remark: '',
           type: '',
           createTime: '',
           sourceCode: '',
           actionType: '',
           toMemberCode: '',
           isComment: '',
           projectCode: '',
           icon: '',
           isRobot: '',
             },
      open: false,
      rules: {},
    }
  },
  created() {
    this.rules = {
    }
  },
  methods: {
    /**
     * 新增按钮操作
     * */
    handleAdd() {
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.add') + '项目日志表'
      this.form = {}
      this.updateState = false
    },
    /**
     * 修改按钮操作
     * */
    async handleUpdate($event, id) {
      $event.stopPropagation()
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.edit') + '项目日志表'
      const response = await getTeamProjeclog(id)
      this.form = response.data
    },
    /**
     * 提交按钮
     * */
    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
            if (this.form.id) {
                await updateTeamProjeclog(this.form)
                this.$alert.success(this.$t('app.global.edit.success'))
                this.open = false
                this.$emit('ok')
            } else {
                await addTeamProjeclog(this.form)
                this.$alert.success(this.$t('app.global.add.success'))
                this.open = false
                this.$emit('ok')
            }
        } else {
            return false
        }
        })
    },
    /**
     * 取消按钮
     * */
    cancel() {
      this.open = false
      this.reset()
    },
    /**
     * 表单重置
     * */
    reset() {
      this.form = {}
      if (this.$refs.form) {
        this.$refs.form.resetFields()}
      }
    },
}
</script>

