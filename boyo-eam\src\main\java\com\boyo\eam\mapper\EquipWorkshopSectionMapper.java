package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipWorkshopSection;

import java.util.List;

/**
 * 工段表(EquipWorkshopSection)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-21 15:23:24
 */
public interface EquipWorkshopSectionMapper extends BaseMapper<EquipWorkshopSection>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipWorkshopSection 实例对象
     * @return 对象列表
     */
    List<EquipWorkshopSection> selectEquipWorkshopSectionList(EquipWorkshopSection equipWorkshopSection);


}

