package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmBusinessStatus;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * 商机状态(CrmBusinessStatus)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-25 14:53:11
 */
@Tenant
public interface CrmBusinessStatusMapper extends BaseMapper<CrmBusinessStatus>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmBusinessStatus 实例对象
     * @return 对象列表
     */
    List<CrmBusinessStatus> selectCrmBusinessStatusList(CrmBusinessStatus crmBusinessStatus);


}

