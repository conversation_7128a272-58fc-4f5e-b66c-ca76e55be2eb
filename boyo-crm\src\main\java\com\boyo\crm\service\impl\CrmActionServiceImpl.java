package com.boyo.crm.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.entity.CrmAction;
import com.boyo.crm.mapper.CrmActionMapper;
import com.boyo.crm.service.ICrmActionService;
import java.util.List;

/**
 * 操作记录表(CrmAction)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-28 18:36:06
 */
@Service("crmActionService")
@AllArgsConstructor
public class CrmActionServiceImpl extends ServiceImpl<CrmActionMapper, CrmAction> implements ICrmActionService {
    private final CrmActionMapper crmActionMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<CrmAction> selectCrmActionList(CrmAction crmAction) {
        return crmActionMapper.selectCrmActionList(crmAction);
    }

}
