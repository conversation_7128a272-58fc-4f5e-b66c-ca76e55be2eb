package com.boyo.system.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 企业管理
 * 表名 t_sys_enterprise
 *
 * <AUTHOR>
 */
@ApiModel("企业表")
@Data
@TableName("t_sys_enterprise")
public class TSysEnterprise extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "enterprise_openid")
    private String enterpriseOpenid;
    /**
     * 企业名称
     */
    @ApiModelProperty("企业名称")
    @TableField(value = "enterprise_name")
    private String enterpriseName;
    /**
     * 企业简称
     */
    @ApiModelProperty("企业简称")
    @TableField(value = "enterprise_abbreviation")
    private String enterpriseAbbreviation;
    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    @TableField(value = "enterprise_code")
    private String enterpriseCode;
    /**
     * 企业登录标识
     */
    @ApiModelProperty("企业登录标识")
    @TableField(value = "enterprise_logincode")
    private String enterpriseLogincode;
    /**
     * 企业最大注册人数
     */
    @ApiModelProperty("企业最大注册人数")
    @TableField(value = "enterprise_max_register")
    private Long enterpriseMaxRegister;
    /**
     * 企业联系人
     */
    @ApiModelProperty("企业联系人")
    @TableField(value = "enterprise_contacts")
    private String enterpriseContacts;
    /**
     * 固定电话
     */
    @ApiModelProperty("固定电话")
    @TableField(value = "enterprise_telephone")
    private String enterpriseTelephone;
    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    @TableField(value = "enterprise_phone")
    private String enterprisePhone;
    /**
     * 邮箱地址
     */
    @ApiModelProperty("邮箱地址")
    @TableField(value = "enterprise_email")
    private String enterpriseEmail;
    /**
     * 企业logo
     */
    @ApiModelProperty("企业logo")
    @TableField(value = "enterprise_logo")
    private String enterpriseLogo;
    /**
     * 省
     */
    @ApiModelProperty("省")
    @TableField(value = "enterprise_province")
    private String enterpriseProvince;
    /**
     * 市
     */
    @ApiModelProperty("市")
    @TableField(value = "enterprise_city")
    private String enterpriseCity;
    /**
     * 区
     */
    @ApiModelProperty("区")
    @TableField(value = "enterprise_district")
    private String enterpriseDistrict;
    /**
     * 企业地址
     */
    @ApiModelProperty("企业地址")
    @TableField(value = "enterprise_address")
    private String enterpriseAddress;
    /**
     * 经度
     */
    @ApiModelProperty("经度")
    @TableField(value = "enterprise_lng")
    private String enterpriseLng;
    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    @TableField(value = "enterprise_lat")
    private String enterpriseLat;
    /**
     * 初始化状态
     */
    @ApiModelProperty("初始化状态")
    @TableField(value = "enterprise_init")
    private String enterpriseInit;
    /**
     * 系统定制logo
     */
    @ApiModelProperty("系统定制logo")
    @TableField(value = "enterprise_sys_logo")
    private String enterpriseSysLogo;
    /**
     * 系统定制名称
     */
    @ApiModelProperty("系统定制名称")
    @TableField(value = "enterprise_sys_name")
    private String enterpriseSysName;
    /**
     * 授权码
     */
    @ApiModelProperty("授权码")
    @TableField(value = "enterprise_authorization")
    private String enterpriseAuthorization;
    /**
     * 过期日期
     */
    @ApiModelProperty("过期日期")
    @TableField(value = "enterprise_validity")
    private Date enterpriseValidity;
    /**
     * 访问域名
     */
    @ApiModelProperty("访问域名")
    @TableField(value = "enterprise_domain_name")
    private String enterpriseDomainName;
    /**
     * 企业租户数据库版本号
     */
    @ApiModelProperty("企业租户数据库版本号")
    @TableField(value = "enterprise_database_version")
    private Long enterpriseDatabaseVersion;
    /**
     * 租户数据库连接驱动
     */
    @ApiModelProperty("租户数据库连接驱动")
    @TableField(value = "enterprise_database_driver")
    private String enterpriseDatabaseDriver;
    /**
     * 租户数据库地址
     */
    @ApiModelProperty("租户数据库地址")
    @TableField(value = "enterprise_database_url")
    private String enterpriseDatabaseUrl;
    /**
     * 租户数据库用户名
     */
    @ApiModelProperty("租户数据库用户名")
    @TableField(value = "enterprise_database_username")
    private String enterpriseDatabaseUsername;
    /**
     * 租户数据库密码
     */
    @ApiModelProperty("租户数据库密码")
    @TableField(value = "enterprise_database_password")
    private String enterpriseDatabasePassword;
    
    @TableField(value = "enterprise_screen")
    private String enterpriseScreen;
    /**
     * 状态 1启用 0停用
     */
    @ApiModelProperty("状态 1启用 0停用")
    @TableField(value = "enterprise_status")
    private String enterpriseStatus;
    /**
     * 企业创建人所属部门
     */
    @ApiModelProperty("企业创建人所属部门")
    @TableField(value = "creat_user_dep")
    private String creatUserDep;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(value = "update_time")
    private Date updateTime;
}
