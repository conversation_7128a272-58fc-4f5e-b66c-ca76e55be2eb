<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipMaintTemplMapper">

    <resultMap type="com.boyo.eam.domain.EquipMaintTempl" id="EquipMaintTemplResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="name" column="name" />
        <result property="remark" column="remark" />
        <result property="type" column="type" />
        <result property="equipOpenid" column="equip_openid" />
        <result property="state" column="state" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipMaintTemplList" parameterType="com.boyo.eam.domain.EquipMaintTempl" resultMap="EquipMaintTemplResult">
        select
          EMT.*,TML.line_name as lineName
        from equip_maint_templ EMT left join t_model_line TML on EMT.line_openid=TML.line_openid
        <where>
            <if test="openid != null and openid != ''">
                and EMT.openid = #{openid}
            </if>
            <if test="name != null and name != ''">
                and EMT.name = #{name}
            </if>
            <if test="remark != null and remark != ''">
                and EMT.remark = #{remark}
            </if>
            <if test="type != null and type != ''">
                and EMT.type = #{type}
            </if>
            <if test="equipOpenid != null and equipOpenid != ''">
                and EMT.equip_openid like CONCAT('%',#{equipOpenid},'%')
            </if>
            <if test="state != null and state != ''">
                and EMT.state = #{state}
            </if>
            <if test="lineOpenid != null and lineOpenid != ''">
                and EMT.line_openid = #{lineOpenid}
            </if>
            <if test="createBy != null and createBy != ''">
                and EMT.create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and EMT.create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and EMT.update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and EMT.update_time = #{updateTime}
            </if>
            <if test="lineOpenid != null and lineOpenid!=''">
                and TML.line_openid = #{lineOpenid}
            </if>
        </where>
    </select>
</mapper>

