package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 工段表(EquipWorkshopSection)实体类
 *
 * <AUTHOR>
 * @since 2021-12-21 15:23:24
 */
@Data
@TableName(value = "equip_workshop_section")
public class EquipWorkshopSection implements Serializable {
    private static final long serialVersionUID = -67598309229542860L;

    @TableId
    private Integer id;

    /**
    * 工段编码
    */
    @TableField(value="code")
    private String code;
    /**
    * 工段名
    */
    @TableField(value="name")
    private String name;
    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 工厂openid
    */
    @TableField(value="factory_openid")
    private String factoryOpenid;
    /**
    * 车间openid
    */
    @TableField(value="workshop_openid")
    private String workshopOpenid;
    /**
    * 产线openid
    */
    @TableField(value="line_openid")
    private String lineOpenid;

}
