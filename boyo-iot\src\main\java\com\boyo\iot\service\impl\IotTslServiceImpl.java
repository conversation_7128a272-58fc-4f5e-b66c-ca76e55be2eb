package com.boyo.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.common.annotation.DataScope;
import com.boyo.iot.domain.IotTsl;
import com.boyo.iot.mapper.IotTslMapper;
import com.boyo.iot.service.IIotTslService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * IoT物模型Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class IotTslServiceImpl extends ServiceImpl<IotTslMapper, IotTsl> implements IIotTslService {
    private final IotTslMapper iotTslMapper;

    @Autowired
    public IotTslServiceImpl(IotTslMapper iotTslMapper) {
        this.iotTslMapper = iotTslMapper;
    }


    /**
     * 查询IoT物模型列表
     *
     * @param iotTsl IoT物模型
     * @return IotTsl 列表
     */
    @Override
    @DataScope
    public List<IotTsl> selectIotTslList(IotTsl iotTsl) {
        return iotTslMapper.selectIotTslList(iotTsl);
    }
}
