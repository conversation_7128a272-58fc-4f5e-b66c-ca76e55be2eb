package com.boyo.master.domain;


import com.boyo.common.annotation.Excel;
import com.boyo.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 大屏入库信息对象 screen_warehousing
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
public class ScreenWarehousing extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 入库数 */
    @Excel(name = "入库数")
    private Long warehousing;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date storageTime;

    /** 仓库id */
    @Excel(name = "仓库id")
    private Long workshopId;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setWarehousing(Long warehousing)
    {
        this.warehousing = warehousing;
    }

    public Long getWarehousing()
    {
        return warehousing;
    }
    public void setStorageTime(Date storageTime)
    {
        this.storageTime = storageTime;
    }

    public Date getStorageTime()
    {
        return storageTime;
    }
    public void setWorkshopId(Long workshopId)
    {
        this.workshopId = workshopId;
    }

    public Long getWorkshopId()
    {
        return workshopId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("warehousing", getWarehousing())
                .append("storageTime", getStorageTime())
                .append("workshopId", getWorkshopId())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
