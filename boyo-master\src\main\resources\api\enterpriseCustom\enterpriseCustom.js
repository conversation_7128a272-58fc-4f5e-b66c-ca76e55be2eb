import request from '@/utils/request'

const prefix = '/${module}'

// 查询企业自定义信息(EnterpriseCustom)列表
export function listEnterpriseCustom(query) {
  return request({
    url: prefix + '/enterpriseCustom/list',
    method: 'get',
    params: query,
  })
}

// 查询企业自定义信息(EnterpriseCustom)详细
export function getEnterpriseCustom(id) {
  return request({
    url: prefix + '/enterpriseCustom/' + id,
    method: 'get',
  })
}

// 新增企业自定义信息(EnterpriseCustom)
export function addEnterpriseCustom(data) {
  return request({
    url: prefix + '/enterpriseCustom',
    method: 'post',
    data: data,
  })
}

// 修改企业自定义信息(EnterpriseCustom)
export function updateEnterpriseCustom(data) {
  return request({
    url: prefix + '/enterpriseCustom',
    method: 'put',
    data: data,
  })
}

// 删除企业自定义信息(EnterpriseCustom)
export function delEnterpriseCustom(id) {
  return request({
    url: prefix + '/enterpriseCustom/' + id,
    method: 'delete',
  })
}
