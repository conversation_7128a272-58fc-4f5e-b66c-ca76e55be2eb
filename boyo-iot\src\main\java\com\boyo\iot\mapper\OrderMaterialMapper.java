package com.boyo.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.entity.OrderMaterial;
import java.util.List;

/**
 * (OrderMaterial)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-28 09:16:06
 */
@Tenant
public interface OrderMaterialMapper extends BaseMapper<OrderMaterial>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param orderMaterial 实例对象
     * @return 对象列表
     */
    List<OrderMaterial> selectOrderMaterialList(OrderMaterial orderMaterial);


}

