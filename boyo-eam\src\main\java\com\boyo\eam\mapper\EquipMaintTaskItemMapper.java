package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipMaintTaskItem;

import java.util.List;

/**
 * 维保任务管理-维保项目(EquipMaintTaskItem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:32
 */
public interface EquipMaintTaskItemMapper extends BaseMapper<EquipMaintTaskItem>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipMaintTaskItem 实例对象
     * @return 对象列表
     */
    List<EquipMaintTaskItem> selectEquipMaintTaskItemList(EquipMaintTaskItem equipMaintTaskItem);


}

