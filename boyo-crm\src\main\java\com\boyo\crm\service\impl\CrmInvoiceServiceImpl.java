package com.boyo.crm.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.boyo.common.annotation.DataScope;
import com.boyo.common.core.text.Convert;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.crm.util.ActionEnum;
import com.boyo.crm.util.ActionUtil;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.entity.CrmInvoice;
import com.boyo.crm.mapper.CrmInvoiceMapper;
import com.boyo.crm.service.ICrmInvoiceService;

import java.util.Date;
import java.util.List;

/**
 * 发票表(CrmInvoice)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-28 13:51:42
 */
@Service("crmInvoiceService")
@AllArgsConstructor
public class CrmInvoiceServiceImpl extends ServiceImpl<CrmInvoiceMapper, CrmInvoice> implements ICrmInvoiceService {
    private final CrmInvoiceMapper crmInvoiceMapper;
    private final ActionUtil actionUtil;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    @DataScope(columns = "owner_user_id,create_user_id")
    public List<CrmInvoice> selectCrmInvoiceList(CrmInvoice crmInvoice) {
        return crmInvoiceMapper.selectCrmInvoiceList(crmInvoice);
    }

    @Override
    public boolean save(CrmInvoice entity) {
        entity.setOwnerUserId(SecurityUtils.getUserId());
        super.save(entity);
        if(StrUtil.isEmpty(entity.getInvoiceApplyNumber())){
            entity.setInvoiceApplyNumber("I-" + DateUtil.format(new Date(),"yyyyMMdd") + "-" + StrUtil.fillBefore(Convert.toStr(entity.getId()),'0',6));
        }
        actionUtil.editRecord(null,null,ActionEnum.INVOICE,entity.getId(), null);
        return super.updateById(entity);
    }

    @Override
    public boolean updateById(CrmInvoice entity) {
        CrmInvoice invoice = super.getById(entity);
        if(invoice.getInvoiceStatus().equals(1)){
            throw new CustomException("发票已开具，不能修改信息!");
        }
        actionUtil.editRecord(super.getById(entity.getId()),entity,ActionEnum.INVOICE,entity.getId(), CrmInvoice.class);
        return super.updateById(entity);
    }
}
