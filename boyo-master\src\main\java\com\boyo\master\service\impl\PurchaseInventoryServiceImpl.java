package com.boyo.master.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.master.domain.EnterpriseConstant;
import com.boyo.master.domain.annotations.DataAsset;
import com.boyo.master.domain.annotations.Enterprise;
import com.boyo.master.entity.PurchaseInventory;
import com.boyo.master.mapper.PurchaseInventoryMapper;
import com.boyo.master.service.DataAssetService;
import com.boyo.master.service.PurchaseInventoryService;
import com.boyo.master.utils.HttpUtil;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.boyo.master.register.DataAssetRegister.getDataAssetType;
import static com.boyo.master.utils.DateUtil2.getDateLate;

/**
* <AUTHOR>
* @description 针对表【bu_enterprise_data_asset_purchase_inventory(采购入库单)】的数据库操作Service实现
* @createDate 2024-11-16 14:16:13
*/
@Slf4j
@DataAsset("采购入库")
@Service
public class PurchaseInventoryServiceImpl extends ServiceImpl<PurchaseInventoryMapper, PurchaseInventory>
    implements PurchaseInventoryService, DataAssetService {

        private String ncUrl = EnterpriseConstant.NCURL;

    @Override
    public Object getDataAssetList(IPage page, Map<String, String> params) {
       PageHelper.clearPage();
        return this.page(page, new LambdaQueryWrapper<PurchaseInventory>()
                .eq(PurchaseInventory::getEnterpriseId, params.get(EnterpriseConstant.ENTERPRISE_ID))
                .eq(StrUtil.isNotEmpty(params.get("childrenCompany")), PurchaseInventory::getChildrenCompany, params.get("childrenCompany"))
                .like(StrUtil.isNotEmpty(params.get("custname")), PurchaseInventory::getCustname, params.get("custname"))
                .eq(StrUtil.isNotEmpty(params.get("vbillcode")), PurchaseInventory::getVbillcode, params.get("vbillcode"))
                .like(StrUtil.isNotEmpty(params.get("invname")), PurchaseInventory::getInvname, params.get("invname"))
                .between(StrUtil.isNotEmpty(params.get("dbilldateStart")) && StrUtil.isNotEmpty(params.get("dbilldateEnd")), PurchaseInventory::getDbilldate, params.get("dbilldateStart"), params.get("dbilldateEnd"))
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncDataAssetHaihui() throws NoSuchMethodException, ParseException, IoTDBConnectionException, StatementExecutionException {
        Enterprise annotation = getCurrentMethodAnnotation(Enterprise.class);
        Long enterpriseId = Convert.toLong(annotation.value());
        Map<String, String> company = (Map<String, String>) getSelectorList(getDataAssetType(this), enterpriseId)
                .getOrDefault("company", new HashMap<>());
        for (String companyItem : company.keySet()) {
            // 1. 拼装查询参数
            Map<String, Object> params = new HashMap<>();
            params.put("begindate", getDateLate(5));
            params.put("enddate", getDateLate(1));
            params.put("url", "api/nc57/index/purchaseInventory");
            params.put("unitname", companyItem);
            log.info("同步数据开始，查询参数【{}】", params.toString());
            // 2. 发送请求获取数据
            final ResponseEntity<?> responseEntity = HttpUtil.postData(params);
            String post = responseEntity.getBody().toString();
            // 3. 解析响应数据
            JSONObject response = JSON.parseObject(post);
            String data = response.getString("data");
            if (StrUtil.isEmpty(data)) {
                continue;
            }
            PurchaseInventory[] dataArr = JSON.parseObject(data).getObject("data", PurchaseInventory[].class);
            // 4. 循环处理数据
            List<String> remoteIdList = new ArrayList<>();
            List<PurchaseInventory> dataList = new ArrayList<>();
            for (PurchaseInventory item : dataArr) {
                item.setChildrenCompany(companyItem);
                item.setEnterpriseId(enterpriseId);
                item.setRemoteId(item.getVbillcode());
                remoteIdList.add(item.getRemoteId());
                dataList.add(item);
            }
            // 5. 更新数据
            this.remove(new LambdaQueryWrapper<PurchaseInventory>()
                    .eq(PurchaseInventory::getEnterpriseId, enterpriseId)
                    .in(PurchaseInventory::getRemoteId, remoteIdList)
            );
            if (!this.saveBatch(dataList)) {
                log.error("同步数据失败，查询参数【{}】", params.toString());
            }
        }
    }
}




