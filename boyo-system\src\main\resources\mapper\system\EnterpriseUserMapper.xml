<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.system.mapper.EnterpriseUserMapper">

    <resultMap type="com.boyo.common.core.domain.entity.EnterpriseUser" id="EnterpriseUserResult">
        <result property="id" column="id"/>
        <result property="enterpriseOpenid" column="enterprise_openid"/>
        <result property="departmentOpenid" column="department_openid"/>
        <result property="userOpenid" column="user_openid"/>
        <result property="userName" column="user_name"/>
        <result property="userPassword" column="user_password"/>
        <result property="userImg" column="user_img"/>
        <result property="userPhone" column="user_phone"/>
        <result property="userEmail" column="user_email"/>
        <result property="userFullName" column="user_full_name"/>
        <result property="userEmpCode" column="user_emp_code"/>
        <result property="userAdmin" column="user_admin"/>
        <result property="userWechat" column="user_wechat"/>
        <result property="userAlipay" column="user_alipay"/>
        <result property="userDingding" column="user_dingding"/>
        <result property="userStatus" column="user_status"/>
        <result property="needModifyPassword" column="need_modify_password"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectEnterpriseUserVo">
        select id,
               enterprise_openid,
               user_openid,
               user_name,
               user_password,
               user_img,
               user_phone,
               user_email,
               user_full_name,
               user_emp_code,
               user_admin,
               user_wechat,
               user_alipay,
               user_dingding,
               user_status,
               need_modify_password,
               last_login_time,
               create_time,
               update_time
        from t_enterprise_user
    </sql>

    <select id="selectEnterpriseUserList" parameterType="com.boyo.common.core.domain.entity.EnterpriseUser"
            resultMap="EnterpriseUserResult">
        <include refid="selectEnterpriseUserVo"/>
        <where>
            <if test="enterpriseOpenid != null  and enterpriseOpenid != ''">
                and enterprise_openid = #{enterpriseOpenid}
            </if>
            <if test="departmentOpenid != null  and departmentOpenid != ''">
                and department_openid in(
                SELECT
                department_openid
                FROM
                (
                SELECT
                t1.department_openid,
                t1.department_name,
                IF
                (
                find_in_set( parent_openid, @pids ) > 0,
                @pids := concat( @pids, ',', department_openid ),
                0
                ) AS ischild
                FROM
                (
                SELECT
                department_openid,
                parent_openid,
                department_name
                FROM
                t_enterprise_department t
                ORDER BY
                parent_openid,
                department_openid
                ) t1,
                ( SELECT @pids := #{departmentOpenid} ) t2
                ) t3
                WHERE
                ischild != 0 UNION
                SELECT
                department_openid
                FROM
                t_enterprise_department
                WHERE
                department_openid = #{departmentOpenid}
                )
            </if>
            <if test="userOpenid != null  and userOpenid != ''">
                and user_openid = #{userOpenid}
            </if>
            <if test="userName != null  and userName != ''">
                and user_name like concat('%', #{userName}, '%')
            </if>
            <if test="userPassword != null  and userPassword != ''">
                and user_password = #{userPassword}
            </if>
            <if test="userImg != null  and userImg != ''">
                and user_img = #{userImg}
            </if>
            <if test="userPhone != null  and userPhone != ''">
                and user_phone = #{userPhone}
            </if>
            <if test="userEmail != null  and userEmail != ''">
                and user_email = #{userEmail}
            </if>
            <if test="userFullName != null  and userFullName != ''">
                and user_full_name like concat('%', #{userFullName}, '%')
            </if>
            <if test="userEmpCode != null  and userEmpCode != ''">
                and user_emp_code = #{userEmpCode}
            </if>
            <if test="userAdmin != null ">
                and user_admin = #{userAdmin}
            </if>
            <if test="userWechat != null  and userWechat != ''">
                and user_wechat = #{userWechat}
            </if>
            <if test="userAlipay != null  and userAlipay != ''">
                and user_alipay = #{userAlipay}
            </if>
            <if test="userDingding != null  and userDingding != ''">
                and user_dingding = #{userDingding}
            </if>
            <if test="userStatus != null  and userStatus != ''">
                and user_status = #{userStatus}
            </if>
            <if test="needModifyPassword != null  and needModifyPassword != ''">
                and need_modify_password = #{needModifyPassword}
            </if>
            <if test="lastLoginTime != null ">
                and last_login_time = #{lastLoginTime}
            </if>
        </where>
    </select>

    <select id="selectEnterpriseUser" resultType="com.boyo.common.core.domain.entity.EnterpriseUser">
        select *
        from t_enterprise_user
        where (user_name = #{userName} or user_phone = #{userName} or user_email = #{userName} or user_emp_code = #{userName})
          and enterprise_openid = (
            select enterprise_openid from t_sys_enterprise where (enterprise_code = #{enterpriseCode} or enterprise_abbreviation = #{enterpriseCode})
        )
    </select>

    <insert id="saveEnterpriseUser" parameterType="com.boyo.common.core.domain.entity.EnterpriseUser">
        insert into t_enterprise_user (enterprise_openid,user_openid,user_name,user_password,user_phone,user_full_name,user_status,create_time,update_time)
        values(#{enterpriseOpenid},#{userOpenid},#{userName},#{userPassword},#{userPhone},#{userFullName},#{userStatus},#{createTime},#{updateTime})

    </insert>

    <update id="updateEnterpriseUserById" parameterType="com.boyo.common.core.domain.entity.EnterpriseUser">
        update t_enterprise_user set department_openid = #{departmentOpenid} where id = #{id}
    </update>
</mapper>
