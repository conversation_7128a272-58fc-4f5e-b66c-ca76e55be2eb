<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.MesWarehousingMapper">

    <resultMap type="com.boyo.mes.entity.MesWarehousing" id="MesWarehousingResult">
        <result property="id" column="id"/>
        <result property="rq" column="rq"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="shift" column="shift"/>
        <result property="productionId" column="production_id"/>
        <result property="inCount" column="in_count"/>
        <result property="scrapCount" column="scrap_count"/>
        <result property="productionName" column="prodution_name"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectMesWarehousingList" parameterType="com.boyo.mes.entity.MesWarehousing"
            resultMap="MesWarehousingResult">
        select
        t1.*,t2.materiel_name as prodution_name
        from t_mes_warehousing t1,t_material t2
        <where>
            t1.production_id = t2.id
            <if test="rq != null and rq != ''">
                and t1.rq = #{rq}
            </if>
            <if test="shift != null and shift != ''">
                and t1.shift = #{shift}
            </if>
            <if test="productionId != null">
                and t1.production_id = #{productionId}
            </if>
            <if test="inCount != null">
                and t1.in_count = #{inCount}
            </if>
            <if test="scrapCount != null">
                and t1.scrap_count = #{scrapCount}
            </if>
        </where>
    </select>
</mapper>

