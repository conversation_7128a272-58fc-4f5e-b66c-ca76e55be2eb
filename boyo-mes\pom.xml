<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>boyo</artifactId>
        <groupId>com.boyo</groupId>
        <version>3.3.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>boyo-mes</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-framework</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-iot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-master</artifactId>
        </dependency>
    </dependencies>
</project>