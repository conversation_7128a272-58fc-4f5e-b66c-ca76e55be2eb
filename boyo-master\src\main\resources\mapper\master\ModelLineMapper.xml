<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.ModelLineMapper">

    <resultMap type="com.boyo.master.domain.ModelLine" id="ModelLineResult">
        <result property="id" column="id"/>
        <result property="lineOpenid" column="line_openid"/>
        <result property="lineFactory" column="line_factory"/>
        <result property="lineWorkshop" column="line_workshop"/>
        <result property="lineName" column="line_name"/>
        <result property="lineAbbreviation" column="line_abbreviation"/>
        <result property="lineCode" column="line_code"/>
        <result property="lineStatus" column="line_status"/>
        <result property="lineImg" column="line_img"/>
        <result property="lineContacts" column="line_contacts"/>
        <result property="linePhone" column="line_phone"/>
        <result property="createdAt" column="created_at"/>
        <result property="createdUser" column="created_user"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="updatedUser" column="updated_user"/>
        <result property="remark" column="remark"/>
        <result property="factoryName" column="factoryName"></result>
        <result property="workshopName" column="workshopName"></result>
    </resultMap>

    <sql id="selectModelLineVo">
        select id,
               line_openid,
               line_factory,
               line_workshop,
               line_name,
               line_abbreviation,
               line_code,
               line_status,
               line_img,
               line_contacts,
               line_phone,
               created_at,
               created_user,
               updated_at,
               updated_user,
               remark
        from t_model_line
    </sql>

    <select id="selectModelLineList" parameterType="com.boyo.master.domain.ModelLine" resultMap="ModelLineResult">
        SELECT
        t1.*,
        t2.factory_name as factoryName,
        t3.workshop_name as workshopName
        FROM
        t_model_line t1
        LEFT JOIN t_model_factory t2 ON t1.line_factory = t2.factory_openid
        LEFT JOIN t_model_workshop t3 ON t1.line_workshop = t3.workshop_openid
        <where>
            <if test="lineOpenid != null  and lineOpenid != ''">
                and t1.line_openid = #{lineOpenid}
            </if>
            <if test="lineFactory != null  and lineFactory != ''">
                and t1.line_factory = #{lineFactory}
            </if>
            <if test="lineWorkshop != null  and lineWorkshop != ''">
                and t1.line_workshop = #{lineWorkshop}
            </if>
            <if test="lineName != null  and lineName != ''">
                and t1.line_name like concat('%', #{lineName}, '%')
            </if>
            <if test="lineAbbreviation != null  and lineAbbreviation != ''">
                and t1.line_abbreviation like concat('%', #{lineAbbreviation}, '%')
            </if>
            <if test="lineCode != null  and lineCode != ''">
                and t1.line_code like concat('%', #{lineCode}, '%')
            </if>
            <if test="lineStatus != null  and lineStatus != ''">
                and t1.line_status = #{lineStatus}
            </if>
        </where>
    </select>
</mapper>
