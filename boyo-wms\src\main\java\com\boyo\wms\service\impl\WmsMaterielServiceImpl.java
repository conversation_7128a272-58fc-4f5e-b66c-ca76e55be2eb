package com.boyo.wms.service.impl;

import java.util.List;

import com.boyo.framework.annotation.Tenant;
import com.boyo.wms.vo.WmsMaterielVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.wms.mapper.WmsMaterielMapper;
import com.boyo.wms.entity.WmsMateriel;
import com.boyo.wms.service.IWmsMaterielService;

/**
 * 出入库计划明细Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class WmsMaterielServiceImpl extends ServiceImpl<WmsMaterielMapper, WmsMateriel> implements IWmsMaterielService {
    private final WmsMaterielMapper wmsMaterielMapper;


    /**
     * 查询出入库计划明细列表
     *
     * @param wmsMateriel 出入库计划明细
     * @return wmsMateriel 列表
     */
    @Override
    public List<WmsMaterielVO> selectWmsMaterielList(WmsMateriel wmsMateriel) {
        return wmsMaterielMapper.selectWmsMaterielList(wmsMateriel);
    }
}
