package com.boyo.wms.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.wms.entity.WmsFlowqcDetail;
import com.boyo.wms.mapper.WmsFlowqcDetailMapper;
import com.boyo.wms.service.IWmsFlowqcDetailService;
import java.util.List;

/**
 * (WmsFlowqcDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-10 15:12:10
 */
@Service("wmsFlowqcDetailService")
@AllArgsConstructor
@Tenant
public class WmsFlowqcDetailServiceImpl extends ServiceImpl<WmsFlowqcDetailMapper, WmsFlowqcDetail> implements IWmsFlowqcDetailService {
    private final WmsFlowqcDetailMapper wmsFlowqcDetailMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<WmsFlowqcDetail> selectWmsFlowqcDetailList(WmsFlowqcDetail wmsFlowqcDetail) {
        return wmsFlowqcDetailMapper.selectWmsFlowqcDetailList(wmsFlowqcDetail);
    }

}
