package com.boyo.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.project.entity.TeamProject;
import java.util.List;

/**
 * 项目表(TeamProject)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-09 11:07:52
 */
public interface TeamProjectMapper extends BaseMapper<TeamProject>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param teamProject 实例对象
     * @return 对象列表
     */
    List<TeamProject> selectTeamProjectList(TeamProject teamProject);

    List<TeamProject> selectAllProjectList(TeamProject teamProject);


}

