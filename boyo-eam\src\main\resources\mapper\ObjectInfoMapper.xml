<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.ObjectInfoMapper">

    <resultMap type="com.boyo.eam.domain.ObjectInfo" id="ObjectInfoResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="name" column="name" />
        <result property="code" column="code" />
        <result property="remark" column="remark" />
        <result property="state" column="state" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectObjectInfoList" parameterType="com.boyo.eam.domain.ObjectInfo" resultMap="ObjectInfoResult">
        select
          id, openid, name, code, remark, state, create_by, create_time, update_by, update_time
        from object_info
        <where>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="state != null">
                and state = #{state}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>
</mapper>

