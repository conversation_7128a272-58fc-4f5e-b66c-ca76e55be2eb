package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipInspectionTemplItem;
import com.boyo.eam.mapper.EquipInspectionTemplItemMapper;
import com.boyo.eam.service.IEquipInspectionTemplItemService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 点检-项目(EquipInspectionTemplItem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-29 10:21:50
 */
@Service("equipInspectionTemplItemService")
@AllArgsConstructor
@Tenant
public class EquipInspectionTemplItemServiceImpl extends ServiceImpl<EquipInspectionTemplItemMapper, EquipInspectionTemplItem> implements IEquipInspectionTemplItemService {
    private final EquipInspectionTemplItemMapper equipInspectionTemplItemMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipInspectionTemplItem> selectEquipInspectionTemplItemList(EquipInspectionTemplItem equipInspectionTemplItem) {
        return equipInspectionTemplItemMapper.selectEquipInspectionTemplItemList(equipInspectionTemplItem);
    }

}
