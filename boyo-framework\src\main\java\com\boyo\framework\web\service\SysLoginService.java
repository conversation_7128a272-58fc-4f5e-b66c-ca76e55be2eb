package com.boyo.framework.web.service;

import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.core.domain.entity.SysUser;
import com.boyo.common.core.domain.model.LoginBody;
import com.boyo.common.enums.UserStatus;
import com.boyo.common.exception.BaseException;
import com.boyo.common.utils.StringUtils;
import com.boyo.system.mapper.EnterpriseUserMapper;
import com.boyo.system.mapper.SysMenuMapper;
import com.boyo.system.mapper.SysUserMapper;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import com.boyo.common.constant.Constants;
import com.boyo.common.core.domain.model.LoginUser;
import com.boyo.common.core.redis.RedisCache;
import com.boyo.common.exception.user.CaptchaException;
import com.boyo.common.exception.user.CaptchaExpireException;
import com.boyo.common.exception.user.UserPasswordNotMatchException;
import com.boyo.common.utils.MessageUtils;
import com.boyo.framework.manager.AsyncManager;
import com.boyo.framework.manager.factory.AsyncFactory;

import java.util.*;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {

    private static final Logger log = LoggerFactory.getLogger(SysLoginService.class);

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private SysMenuMapper menuMapper;
    @Autowired
    private EnterpriseUserMapper enterpriseUserMapper;
    @Autowired
    private WxMpService wxService;



    private final static String BOYO = "c72fca2c-d92a-4cdc-b321-211987893c52";

    /**
     * 登录验证
     *
     * @param loginBody 登录用户信息
     * @return 结果
     */
    public String login(LoginBody loginBody, HttpServletRequest request) {
        String verifyKey = Constants.CAPTCHA_CODE_KEY + loginBody.getUuid();
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginBody.getUsername(), Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
            throw new CaptchaExpireException();
        }
        if (!loginBody.getCode().equalsIgnoreCase(captcha)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginBody.getUsername(), Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
            throw new CaptchaException();
        }
        LoginUser userDetails = null;
        /**
         * 管理平台用户
         */
        userDetails = (LoginUser) findSysUser(loginBody);
        if (ObjectUtil.isNull(userDetails)) {
            throw new UserPasswordNotMatchException();
        }
        if (!BCrypt.checkpw(loginBody.getPassword(), userDetails.getPassword())) {
            throw new UserPasswordNotMatchException();
        }
        UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(loginBody.getUsername(), loginBody.getPassword());
        authRequest.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authRequest);

        // 生成token
        return tokenService.createToken(userDetails);
    }

    /**
     * 登录验证
     *
     * @param loginBody 登录用户信息
     * @return 结果
     */
    public String loginFromLingYang(LoginBody loginBody, HttpServletRequest request) {
        String verifyKey = Constants.CAPTCHA_CODE_KEY + loginBody.getUuid();
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
//        if (captcha == null) {
//            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginBody.getUsername(), Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
//            throw new CaptchaExpireException();
//        }
//        if (!loginBody.getCode().equalsIgnoreCase(captcha)) {
//            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginBody.getUsername(), Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
//            throw new CaptchaException();
//        }
        LoginUser userDetails = null;
        /**
         * 管理平台用户
         */
        userDetails = (LoginUser) findSysUser(loginBody);
//        if (ObjectUtil.isNull(userDetails)) {
//            throw new UserPasswordNotMatchException();
//        }
//        if (!BCrypt.checkpw(loginBody.getPassword(), userDetails.getPassword())) {
//            throw new UserPasswordNotMatchException();
//        }
        UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(loginBody.getUsername(), loginBody.getPassword());
        authRequest.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authRequest);

        // 生成token
        return tokenService.createToken(userDetails);
    }

    private UserDetails findSysUser(LoginBody loginBody) throws UsernameNotFoundException {
        SysUser user = null;
        String username = loginBody.getUsername();
        EnterpriseUser enterpriseUser = null;
        if (loginBody.getEnterpriseCode().equals(BOYO) || "".equals(loginBody.getEnterpriseCode())) {
            user = userMapper.selectUserByUserName(username);
            if (StringUtils.isNull(user)) {
                log.info("登录用户：{} 不存在.", username);
                throw new UsernameNotFoundException("登录用户：" + username + " 不存在");
            } else if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
                log.info("登录用户：{} 已被删除.", username);
                throw new BaseException("对不起，您的账号：" + username + " 已被删除");
            } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
                log.info("登录用户：{} 已被停用.", username);
                throw new BaseException("对不起，您的账号：" + username + " 已停用");
            }
        } else {
            enterpriseUser = enterpriseUserMapper.selectEnterpriseUser(loginBody.getUsername(), loginBody.getEnterpriseCode());
            if (StringUtils.isNull(enterpriseUser)) {
                log.info("登录用户：{} 不存在.", username);
                throw new UsernameNotFoundException("登录用户：" + username + " 不存在");
            }
            if(StrUtil.isEmpty(enterpriseUser.getUserWechat()) && StrUtil.isNotEmpty(loginBody.getWechat())){
                try {
                    WxMpUser mpUser = wxService.switchoverTo("wx8485cacdf80ab61e").getUserService().userInfo(loginBody.getWechat());
                    enterpriseUser.setUserWechat(loginBody.getWechat());
                    enterpriseUser.setUserImg(mpUser.getHeadImgUrl());
                    enterpriseUserMapper.updateById(enterpriseUser);
                } catch (WxErrorException e) {
                    e.printStackTrace();
                }

            }else if(!StrUtil.isEmpty(enterpriseUser.getUserWechat()) && StrUtil.isNotEmpty(loginBody.getWechat())){
                throw new UsernameNotFoundException("当前用户已与其他微信号绑定,请先联系管理员进行解绑!");
            }
            enterpriseUser.setLastLoginTime(new Date());
            enterpriseUserMapper.updateById(enterpriseUser);
        }

        return createLoginUser(user, enterpriseUser);
    }

    private UserDetails createLoginUser(SysUser user, EnterpriseUser enterpriseUser) {
        Set<String> perms = new HashSet<String>();
        // 管理员拥有所有权限
//        if (user.isAdmin()) {
        perms.add("*:*:*");
//        } else {
//            perms.addAll(selectMenuPermsByUserId(user.getUserId()));
//        }
        if (ObjectUtil.isNotNull(user)) {
            return new LoginUser(user.getUserName(), user.getPassword(), user, enterpriseUser, perms);
        } else {
            return new LoginUser(enterpriseUser.getUserName(), enterpriseUser.getUserPassword(), user, enterpriseUser, perms);
        }
    }

    private Set<String> selectMenuPermsByUserId(Long userId) {
        List<String> perms = menuMapper.selectMenuPermsByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms) {
            if (StringUtils.isNotEmpty(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }
}
