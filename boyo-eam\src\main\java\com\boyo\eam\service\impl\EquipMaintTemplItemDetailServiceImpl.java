package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipMaintTemplItemDetail;
import com.boyo.eam.mapper.EquipMaintTemplItemDetailMapper;
import com.boyo.eam.service.IEquipMaintTemplItemDetailService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备-维保模板-维保明细(EquipMaintTemplItemDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:31
 */
@Service("equipMaintTemplItemDetailService")
@AllArgsConstructor
@Tenant
public class EquipMaintTemplItemDetailServiceImpl extends ServiceImpl<EquipMaintTemplItemDetailMapper, EquipMaintTemplItemDetail> implements IEquipMaintTemplItemDetailService {
    private final EquipMaintTemplItemDetailMapper equipMaintTemplItemDetailMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipMaintTemplItemDetail> selectEquipMaintTemplItemDetailList(EquipMaintTemplItemDetail equipMaintTemplItemDetail) {
        return equipMaintTemplItemDetailMapper.selectEquipMaintTemplItemDetailList(equipMaintTemplItemDetail);
    }

}
