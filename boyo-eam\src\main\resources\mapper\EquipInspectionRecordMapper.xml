<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipInspectionRecordMapper">

    <resultMap type="com.boyo.eam.domain.EquipInspectionRecord" id="EquipInspectionRecordResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="equipInspectionSpotOpenid" column="equip_inspection_spot_openid" />
        <result property="equipInspectionSpotItemOpenid" column="equip_inspection_spot_item_openid" />
        <result property="inspectionDate" column="inspection_date" />
        <result property="pass" column="pass" />
        <result property="remark" column="remark" />
        <result property="mediaId" column="media_id" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipInspectionRecordList" parameterType="com.boyo.eam.domain.EquipInspectionRecord" resultMap="EquipInspectionRecordResult">
        select
          id, openid, equip_inspection_spot_openid, equip_inspection_spot_item_openid, inspection_date, pass, remark, media_id, create_by, create_time, update_by, update_time
        from equip_inspection_record
        <where>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
            <if test="equipInspectionSpotOpenid != null and equipInspectionSpotOpenid != ''">
                and equip_inspection_spot_openid = #{equipInspectionSpotOpenid}
            </if>
            <if test="equipInspectionSpotItemOpenid != null and equipInspectionSpotItemOpenid != ''">
                and equip_inspection_spot_item_openid = #{equipInspectionSpotItemOpenid}
            </if>
            <if test="inspectionDate != null">
                and inspection_date = #{inspectionDate}
            </if>
            <if test="pass != null and pass != ''">
                and pass = #{pass}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="mediaId != null and mediaId != ''">
                and media_id = #{mediaId}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>
</mapper>

