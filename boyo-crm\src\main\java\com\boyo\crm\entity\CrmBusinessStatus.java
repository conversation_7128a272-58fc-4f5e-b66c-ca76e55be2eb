package com.boyo.crm.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import lombok.Data;

/**
 * 商机状态(CrmBusinessStatus)实体类
 *
 * <AUTHOR>
 * @since 2022-03-25 14:53:11
 */
@Data
@TableName(value = "t_crm_business_status")
public class CrmBusinessStatus extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = 892276604169898221L;
            
    @TableId
    private Integer id;
    
    /**
    * 商机状态类别ID
    */
    @TableField(value="type_id")
    private Integer typeId;
    /**
    * 标识
    */
    @TableField(value="name")
    private String name;
    /**
    * 赢单率
    */
    @TableField(value="rate")
    private String rate;
    /**
    * 排序
    */
    @TableField(value="order_num")
    private Integer orderNum;

}
