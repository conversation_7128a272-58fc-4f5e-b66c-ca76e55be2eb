<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmContractProductMapper">

    <resultMap type="com.boyo.crm.entity.CrmContractProduct" id="CrmContractProductResult">
        <result property="id" column="id"/>
        <result property="contractId" column="contract_id"/>
        <result property="productId" column="product_id"/>
        <result property="price" column="price"/>
        <result property="quantity" column="quantity"/>
        <result property="discount" column="discount"/>
        <result property="subtotal" column="subtotal"/>
        <result property="unit" column="unit"/>
        <result property="categoryName" column="category_name"></result>
        <result property="name" column="production_name"></result>
        <result property="num" column="num"></result>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmContractProductList" parameterType="com.boyo.crm.entity.CrmContractProduct"
            resultMap="CrmContractProductResult">
        select t1.*,t2.name as production_name,t2.num,t3.base_desc as category_name from (select
        id, contract_id, product_id, price, quantity, discount, subtotal, unit
        from t_crm_contract_product
        <where>
            <if test="contractId != null">
                and contract_id = #{contractId}
            </if>
            <if test="productId != null">
                and product_id = #{productId}
            </if>
            <if test="price != null">
                and price = #{price}
            </if>
            <if test="quantity != null">
                and quantity = #{quantity}
            </if>
            <if test="discount != null">
                and discount = #{discount}
            </if>
            <if test="subtotal != null">
                and subtotal = #{subtotal}
            </if>
            <if test="unit != null and unit != ''">and unit = #{unit}
            </if>
            ${params.dataScope}
        </where>
        ) t1 left join t_crm_product t2 on t1.product_id = t2.product_id
        left join t_base_dict t3 on t2.category_id = t3.id
    </select>
</mapper>

