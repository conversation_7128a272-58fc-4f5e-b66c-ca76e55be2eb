<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.TMaterialMapper">

    <resultMap type="com.boyo.master.vo.MaterialVO" id="TMaterialResult">
        <result property="id" column="id"/>
        <result property="materielOpenid" column="materiel_openid"/>
        <result property="materielName" column="materiel_name"/>
        <result property="materielAbbreviation" column="materiel_abbreviation"/>
        <result property="materielCode" column="materiel_code"/>
        <result property="materielType" column="materiel_type"/>
        <result property="materielDesc" column="materiel_desc"/>
        <result property="materielUnit" column="materiel_unit"/>
        <result property="materielAbc" column="materiel_abc"/>
        <result property="materielNorms" column="materiel_norms"/>
        <result property="materielSource" column="materiel_source"/>
        <result property="materielSupplier" column="materiel_supplier"/>
        <result property="materielStatus" column="materiel_status"/>
        <result property="materielImg" column="materiel_img"/>
        <result property="materielSunit" column="materiel_sunit"/>
        <result property="materielNetWeight" column="materiel_net_weight"/>
        <result property="materielKeyParts" column="materiel_key_parts"/>
        <result property="materielGrossWeight" column="materiel_gross_weight"/>
        <result property="materielMpq" column="materiel_mpq"/>
        <result property="createdAt" column="created_at"/>
        <result property="createdUser" column="created_user"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="updatedUser" column="updated_user"/>
        <result property="unitName" column="unit_name" />
        <result property="typeName" column="type_name" />
        <result property="categoryName" column="category_name" />
        <result property="sourceName" column="source_name" />
        <result property="minStock" column="min_stock" />
        <result property="minWarning" column="min_warning" />
        <result property="maxStock" column="max_stock" />
        <result property="maxWarning" column="max_warning" />
        <result property="minTime" column="min_time" />
        <result property="maxTime" column="max_time" />
        <result property="remark" column="remark" />
    </resultMap>

    <select id="selectTMaterialList" parameterType="com.boyo.master.domain.TMaterial" resultMap="TMaterialResult">
        select t1.*,t2.base_desc as unit_name,t3.base_desc as type_name,t4.base_desc as category_name,t5.base_desc as source_name from t_material t1
        left join t_base_dict t2 on t1.materiel_unit = t2.openid
        left join t_base_dict t3 on t1.materiel_type = t3.openid
        left join t_base_dict t4 on t1.materiel_abc = t4.openid
        left join t_base_dict t5 on t1.materiel_source = t5.openid

        <where>
            <if test="materielOpenid != null  and materielOpenid != ''">
                and t1.materiel_openid = #{materielOpenid}
            </if>
            <if test="materielName != null  and materielName != ''">
                and t1.materiel_name like concat('%', #{materielName}, '%')
            </if>
            <if test="materielAbbreviation != null  and materielAbbreviation != ''">
                and t1.materiel_abbreviation like concat('%', #{materielAbbreviation}, '%')
            </if>
            <if test="materielCode != null  and materielCode != ''">
                and t1.materiel_code like concat('%', #{materielCode}, '%')
            </if>
            <if test="materielType != null  and materielType != ''">
                and t1.materiel_type = #{materielType}
            </if>
            <if test="materielDesc != null  and materielDesc != ''">
                and t1.materiel_desc = #{materielDesc}
            </if>
            <if test="materielUnit != null  and materielUnit != ''">
                and t1.materiel_unit = #{materielUnit}
            </if>
            <if test="materielAbc != null  and materielAbc != ''">
                and t1.materiel_abc = #{materielAbc}
            </if>
            <if test="materielNorms != null  and materielNorms != ''">
                and t1.materiel_norms = #{materielNorms}
            </if>
            <if test="materielSource != null  and materielSource != ''">
                and t1.materiel_source = #{materielSource}
            </if>
            <if test="materielSupplier != null  and materielSupplier != ''">
                and t1.materiel_supplier = #{materielSupplier}
            </if>
            <if test="materielStatus != null  and materielStatus != ''">
                and t1.materiel_status = #{materielStatus}
            </if>
        </where>
    </select>
</mapper>
