## 数据源配置
spring:
    datasource:
        # 非多租户项目使用默认数据源配置
        url: ${SPRING_DATASOURCE_URL:***********************************************************************************************************************************************************************************************************}
        username: ${SPRING_DATASOURCE_USERNAME:root}
        password: ${SPRING_DATASOURCE_PASSWORD:ee88d8df50a7e525}
        driver-class-name: com.mysql.cj.jdbc.Driver
        # 多租户项目使用动态数据源配置
        dynamic:
            druid:
                wall:
                    multiStatementAllow: true
                    noneBaseStatementAllow: true
                fail-fast: true
                break-after-acquire-failure: true
                # 初始连接数
                initialSize: 5
                # 最小连接池数量
                minIdle: 5
                # 最大连接池数量
                maxActive: 20
                # 配置获取连接等待超时的时间
                maxWait: 6000
                # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
                timeBetweenEvictionRunsMillis: 60000
                # 配置一个连接在池中最小生存的时间，单位是毫秒
                minEvictableIdleTimeMillis: 300000
                # 配置一个连接在池中最大生存的时间，单位是毫秒
                maxEvictableIdleTimeMillis: 900000
                filter:
                    stat:
                        enabled: true
                        # 慢SQL记录
                        log-slow-sql: true
                        slow-sql-millis: 1000
                        merge-sql: true

            strict: true
            seata: true
            datasource:
                master:
                    url: ${SPRING_DATASOURCE_URL:***********************************************************************************************************************************************************************************************************}
                    username: ${SPRING_DATASOURCE_USERNAME:root}
                    password: ${SPRING_DATASOURCE_PASSWORD:ee88d8df50a7e525}
                    driver-class-name: com.mysql.cj.jdbc.Driver

openapi:
    gateway:
        chnl-code: ********-T
        chnl-secret: 86ad201fd4924466b36e3cdf5ffc9aa4
        tx-br-no: BOP-2021041911800
        server-encrypt-public-key: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAm9iqnkBnlY3YDatIrgVgdWScvqEygcZL/dVTwvSIielA0uhxU+72QBAyUS8LlATFXFFnnqeGxT+tF6mH2bGUI9rTyTPiZzETcmJ6ILmy28B154j80QOVN+ZLiFPqfFcKnv1eeX1m4qdwAVWOLjJtUR8/bjMRYLZSJ+6/Tffu2znZSXj1XJ5oviGKl3YuUgJZQQoi52ZmvL3V58DCezsOsDDZ7ZFORNEjb8RWlYs+SAyxGYMUehrCrimpZHtWg/8J7PwADBhSIntPj6sP1crFnE1szJcq0dzp8H8ZIcWGSnckggoz89znxXAANedjSHvVjX+LoWRvOsCe2IneWakIzQIDAQAB'
        client-encrypt-private-key: 'MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCRoI7fr2VsjIyRhBXARqyzaGgGLXO5TQujbA7R0jrQFK5GfL3CRTRVePUoRT5PAlZ5omjaLeFHf63sqhUEHNIJR4VwLnBxDN6nqEIFG+Hqwo7y2EKfppiuvg+ju2/XjpgH3hsasu8hcFcKb7+uuxAZN4ljVKe4ehAGgkNnSy0p0bW/86LQ454NbQk1LJ17q7LQe3v9CYiIuMJTR+LSCzG+xIH+bXllBu7GHk6dubQOxAgjLCi67I42riBQsv+50PTWflzowjBjd3jyUll5KU+nsFCIpb+4ny+/8XCpYnspCeC1qD8cbZ0QaUvEQkB5ZvqL5oewlkeDW4SfVS+UFxvXAgMBAAECggEAbzWt5iSLz9mFf5rfAXb3bArPZehP97JesO5UYuepAj7I2Atq3nD7BkrY7WfzrWKbp0ffyr4+y9s1dwZsBjf8LhnwTTspoyuw3VghEsW4QQ9TGyELv9/5uXhreR4qyvKaJ2f2Su1asK6Zyd6NA2LXaGqBANolOzv8APx5fOhsFQJlGyFAIQEGHrckRmoVIR9qEvSb063MT2GM+0qY6g+LrnNvridPopLBTuzqP56f5LZeVdwDK21v+5ARWZNgmJc2JiOhBwx1oxsNdroDwmKjfVmPHhcG7Uh5oEp1fpNUyYnrJ0XMz8V3Z5R0zO1UovkwXU21/uZIt4LozutWyVR8AQKBgQDpPN256XkEZtHxdH1N+fugFXRMEwtONHJhwWTt62I9c5BGN8n4zxXL+KGyb19on94R5IQS/GR8Pxg0qaHNclKhK/MYYC62c6G1OcO+/vonj/4SmuyCLaHx91sZMltjWEY4NQgC1Pcy92HbzkeyaRsc9qiFqW5hvWSRciBdeY5ZtQKBgQCf1tvTnRk2n77CpmwPSRyziXBcEOZ+b4n/MdkIRgs3afseJCXOHu6pLX7vwkbRotXnxzJuv+ty/CsiHBOEjO/Y6uc4h7BNK/wlL1pAzCgWlBw+oEUp4QHz6vCVSnX4kWz20Y/tmd1/eTbCnRaJZasHsrcI1o+iLv1U974fF7Gm2wKBgESzUPw58ROC1cQxc9dMEvoMV70wvtvCRw7UUE5kTwOazpxgQnWvSdRS4A4XEBtZO6g5j02oWlDepNqePLKvQfeHYouDiT7kwHUJFsdjg3uzxjrkt1WA63rCSWsakiCpF8XmmP18jXPNul1sXWIH45ycavcz+PeBOeY9n5ro+y0hAoGAXqbkbE62dJNqamgUy4R3G5ZC1DIp3Ct+44EITY706262ByZOwN2uZL6NUbuRJmkwFF3wbob2DZRokn1fzXmMOr74B5DKhRiL6xU8c+yxvJUemLNd4avd9SxmJXH/pB+yEAMPIRuhf/AuzlYp11k+S4O4QeKVEeyTlfJTQqRMTksCgYBLUdSK9ZkjTGYKDsQFdEetj+mpecBXPKXjbwt4cL6OF7+KSQIeFUSUVtTEh6UXkJyTMw/k7SbzBIZQabjDApDeozSqQEnlRmjxFv0ANHcliaTb941/o9n/rclbWDzny6j6k8uGozvjTlV2w5xDlqK54MvakpxqkEJiqnyyU9V7iw=='


        #    测试地址
        gateway-address: https://ttt.ebanktest.com.cn:19007/app
        token-enabled: true      #开启token校验
        encrypt-algo: AES        #报文加解密算法
        encrypt-enabled: true     #开启报文加解密
        encrypt-type: 1

        sign-type: RSA
        sign-method: SHA1WithRSA
        sign-enabled: false   #关闭加签
        client-sign-private-key: ''
        server-sign-public-key: ''