package com.boyo.mes.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.mes.entity.ProductOrder;
import java.util.List;

/**
 * 生产订单(ProductOrder)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
public interface IProductOrderService extends IService<ProductOrder> {

    /**
     * 查询多条数据
     *
     * @param productOrder 对象信息
     * @return 对象列表
     */
    List<ProductOrder> selectProductOrderList(ProductOrder productOrder);
    List<ProductOrder> listExecuteOrder(Long equipmentId);


}
