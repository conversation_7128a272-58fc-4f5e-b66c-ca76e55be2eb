package com.boyo.iot.service.impl;

import com.boyo.iot.entity.HaihuiDibang;
import com.boyo.iot.mapper.HaihuiDibangMapper;
import com.boyo.iot.service.IHaihuiDibangService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class HaihuiDibangServiceImpl implements IHaihuiDibangService {

    @Autowired
    private HaihuiDibangMapper haihuiDibangMapper;

    @Override
    public List<HaihuiDibang> selectHaihuiDibangList(HaihuiDibang haihuiDibang) {
        return haihuiDibangMapper.selectHaihuiDibangList(haihuiDibang);
    }

    @Override
    public int insertHaihuiDibang(HaihuiDibang haihuiDibang) {
        return haihuiDibangMapper.insertHaihuiDibang(haihuiDibang);
    }

    @Override
    public int updateHaihuiDibang(HaihuiDibang haihuiDibang) {
        return haihuiDibangMapper.updateHaihuiDibang(haihuiDibang);
    }

    @Override
    public int deleteHaihuiDibangByIds(Long[] ids) {
        return haihuiDibangMapper.deleteHaihuiDibangByIds(ids);
    }

    @Override
    public HaihuiDibang selectHaihuiDibangById(Long id) {
        return haihuiDibangMapper.selectHaihuiDibangById(id);
    }

    @Override
    public HaihuiDibang selectHaihuiDibangByticketNumber(String ticketNumber) {
        return haihuiDibangMapper.selectHaihuiDibangByticketNumber(ticketNumber);
    }

}
