package com.boyo.crm.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.crm.entity.CrmCustomer;
import java.util.List;

/**
 * CRM客户表(CrmCustomer)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-25 16:07:33
 */
public interface ICrmCustomerService extends IService<CrmCustomer> {

    /**
     * 查询多条数据
     *
     * @param crmCustomer 对象信息
     * @return 对象列表
     */
    List<CrmCustomer> selectCrmCustomerList(CrmCustomer crmCustomer);

    List<CrmCustomer> selectPoolCustomerList(CrmCustomer crmCustomer);

    /**
     * 客户退回到公海
     * @param id
     */
    void moveToPool(Integer id);

    void claimCustomer(Integer id);


}
