package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmBusiness;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * 商机表(CrmBusiness)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-26 09:56:24
 */
@Tenant
public interface CrmBusinessMapper extends BaseMapper<CrmBusiness>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmBusiness 实例对象
     * @return 对象列表
     */
    List<CrmBusiness> selectCrmBusinessList(CrmBusiness crmBusiness);


}

