package com.boyo.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.framework.annotation.Tenant;
import com.boyo.master.domain.TMaterial;
import com.boyo.master.mapper.TMaterialMapper;
import com.boyo.mes.entity.MesProduction;
import com.boyo.mes.mapper.MesProductionMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.MesModulproduction;
import com.boyo.mes.mapper.MesModulproductionMapper;
import com.boyo.mes.service.IMesModulproductionService;

import java.util.ArrayList;
import java.util.List;

/**
 * 模具产品绑定关系(MesModulproduction)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
@Service("mesModulproductionService")
@AllArgsConstructor
@Tenant
public class MesModulproductionServiceImpl extends ServiceImpl<MesModulproductionMapper, MesModulproduction> implements IMesModulproductionService {
    private final MesModulproductionMapper mesModulproductionMapper;
    private final MesProductionMapper productionMapper;

    private final TMaterialMapper materialMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<MesModulproduction> selectMesModulproductionList(MesModulproduction mesModulproduction) {
        return mesModulproductionMapper.selectMesModulproductionList(mesModulproduction);
    }

    @Override
    public List<TMaterial> getModulProduction(Integer id) {
        QueryWrapper<MesModulproduction> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("modul_id",id);
        List<MesModulproduction> mesModulproductions = mesModulproductionMapper.selectList(queryWrapper);
        if(mesModulproductions != null && mesModulproductions.size() > 0){
            List<Integer> ids = new ArrayList<>();
            for (MesModulproduction temp:mesModulproductions) {
                ids.add(temp.getProductionId());
            }
//            QueryWrapper<MesProduction> productionQueryWrapper = new QueryWrapper<>();
//            productionQueryWrapper.in("id",ids);
            QueryWrapper<TMaterial> materialQueryWrapper = new QueryWrapper<>();
            materialQueryWrapper.in("id",ids);
            return materialMapper.selectList(materialQueryWrapper);
        }
        return null;
    }

}
