package com.boyo.master.service.impl;

import java.util.List;
import java.util.Arrays;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.exception.CustomException;
import com.boyo.framework.annotation.Tenant;
import com.boyo.master.domain.ModelAllocation;
import com.boyo.master.domain.ModelWarehouse;
import com.boyo.master.mapper.ModelAllocationMapper;
import com.boyo.master.mapper.ModelWarehouseMapper;
import com.boyo.master.vo.ModelAreaVO;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.master.mapper.ModelAreaMapper;
import com.boyo.master.domain.ModelArea;
import com.boyo.master.service.IModelAreaService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 主数据-区域管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class ModelAreaServiceImpl extends ServiceImpl<ModelAreaMapper, ModelArea> implements IModelAreaService {

    private final ModelAreaMapper modelAreaMapper;

    private final ModelAllocationMapper allocationMapper;

    private final ModelWarehouseMapper warehouseMapper;



    /**
     * 查询主数据-区域管理列表
     *
     * @param modelArea 主数据-区域管理
     * @return modelArea 列表
     */
    @Override
    public List<ModelAreaVO> selectModelAreaList(ModelArea modelArea) {
        return modelAreaMapper.selectModelAreaList(modelArea);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, propagation = Propagation.REQUIRED)
    public boolean save(ModelArea entity) {
        QueryWrapper<ModelWarehouse> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("warehouse_openid",entity.getAreaWarehouseId());
        ModelWarehouse obj = warehouseMapper.selectOne(queryWrapper);
        if(ObjectUtil.isNull(obj)){
            throw new CustomException("所选区域不属于任何库区!");
        }
        ModelAllocation allocation = new ModelAllocation();
        allocation.setAllocationOpenid(IdUtil.fastSimpleUUID());
        allocation.setAllocationCode(IdUtil.fastSimpleUUID());
        allocation.setAllocationWarehouse(entity.getAreaWarehouseId());
        allocation.setAllocationArea(entity.getAreaOpenid());
        allocation.setAllocationName(entity.getAreaName());
        allocation.setAllocationAbbreviation(entity.getAreaName());
        allocation.setAllocationFactory(obj.getWarehouseFactory());
        allocationMapper.insert(allocation);
        return super.save(entity);
    }
}
