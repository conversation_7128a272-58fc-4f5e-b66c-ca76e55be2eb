package com.boyo.mes.controller;

import com.boyo.mes.entity.EquipmentApply;
import com.boyo.mes.service.IEquipmentApplyService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 上下班记录(EquipmentApply)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:47:21
 */
@Api("上下班记录")
@RestController
@RequestMapping("/mes/equipmentApply")
@AllArgsConstructor
public class EquipmentApplyController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipmentApplyService equipmentApplyService;

    /**
     * 查询上下班记录列表
     *
     */
    @ApiOperation("查询上下班记录列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipmentApply equipmentApply) {
        startPage();
        List<EquipmentApply> list = equipmentApplyService.selectEquipmentApplyList(equipmentApply);
        return getDataTable(list);
    }
    
    /**
     * 获取上下班记录详情
     */
    @ApiOperation("获取上下班记录详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipmentApplyService.getById(id));
    }

    /**
     * 新增上下班记录
     */
    @ApiOperation("新增上下班记录")
    @PostMapping
    public AjaxResult add(@RequestBody EquipmentApply equipmentApply) {
        return toBooleanAjax(equipmentApplyService.save(equipmentApply));
    }

    /**
     * 修改上下班记录
     */
    @ApiOperation("修改上下班记录")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipmentApply equipmentApply) {
        return toBooleanAjax(equipmentApplyService.updateById(equipmentApply));
    }

    /**
     * 删除上下班记录
     */
    @ApiOperation("删除上下班记录")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipmentApplyService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 扫码获取设备信息进行上下班操作
     * @param openid
     * @return
     */
    @GetMapping("/scanApply")
    public AjaxResult scanApply(String openid){
        return AjaxResult.success(equipmentApplyService.scanApply(openid));
    }

    @GetMapping("/listUsedEquipment")
    public AjaxResult listUsedEquipment(){
        return AjaxResult.success(equipmentApplyService.listUsedEquipment());
    }


}
