<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.project.mapper.TeamProjectMapper">

    <resultMap type="com.boyo.project.entity.TeamProject" id="TeamProjectResult">
        <result property="id" column="id"/>
        <result property="cover" column="cover"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="description" column="description"/>
        <result property="accessControlType" column="access_control_type"/>
        <result property="whiteList" column="white_list"/>
        <result property="order" column="order"/>
        <result property="deleted" column="deleted"/>
        <result property="templateCode" column="template_code"/>
        <result property="templateName" column="template_name"/>
        <result property="schedule" column="schedule"/>
        <result property="createTime" column="create_time"/>
        <result property="organizationCode" column="organization_code"/>
        <result property="deletedTime" column="deleted_time"/>
        <result property="prefix" column="prefix"/>
        <result property="openPrefix" column="open_prefix"/>
        <result property="archive" column="archive"/>
        <result property="archiveTime" column="archive_time"/>
        <result property="openBeginTime" column="open_begin_time"/>
        <result property="openTaskPrivate" column="open_task_private"/>
        <result property="taskBoardTheme" column="task_board_theme"/>
        <result property="beginTime" column="begin_time"/>
        <result property="endTime" column="end_time"/>
        <result property="autoUpdateSchedule" column="auto_update_schedule"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectTeamProjectList" parameterType="com.boyo.project.entity.TeamProject"
            resultMap="TeamProjectResult">
        select t1.*,t2.name as template_name from (select
        id, cover, name, code, description, access_control_type, white_list, `order`, deleted, template_code, schedule,
        create_time, organization_code, deleted_time, prefix, open_prefix, archive, archive_time, open_begin_time,
        open_task_private, task_board_theme, begin_time, end_time, auto_update_schedule
        from team_project
        <where>
            code in (
            select project_code from team_project_member where member_code = #{currentUser}
            )
            <if test="cover != null and cover != ''">
                and cover = #{cover}
            </if>
            <if test="name != null and name != ''">
                and name like concat('%',#{name},'%')
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="description != null and description != ''">
                and description = #{description}
            </if>
            <if test="accessControlType != null and accessControlType != ''">
                and access_control_type = #{accessControlType}
            </if>
            <if test="whiteList != null and whiteList != ''">
                and white_list = #{whiteList}
            </if>
            <if test="order != null">
                and order = #{order}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="templateCode != null and templateCode != ''">
                and template_code = #{templateCode}
            </if>
            <if test="schedule != null">
                and schedule = #{schedule}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="organizationCode != null and organizationCode != ''">
                and organization_code = #{organizationCode}
            </if>
            <if test="deletedTime != null and deletedTime != ''">
                and deleted_time = #{deletedTime}
            </if>
            <if test="prefix != null and prefix != ''">
                and prefix = #{prefix}
            </if>
            <if test="openPrefix != null">
                and open_prefix = #{openPrefix}
            </if>
            <if test="archive != null">
                and archive = #{archive}
            </if>
            <if test="archiveTime != null and archiveTime != ''">
                and archive_time = #{archiveTime}
            </if>
            <if test="openBeginTime != null">
                and open_begin_time = #{openBeginTime}
            </if>
            <if test="openTaskPrivate != null">
                and open_task_private = #{openTaskPrivate}
            </if>
            <if test="taskBoardTheme != null and taskBoardTheme != ''">
                and task_board_theme = #{taskBoardTheme}
            </if>
            <if test="beginTime != null and beginTime != ''">
                and begin_time = #{beginTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and end_time = #{endTime}
            </if>
            <if test="autoUpdateSchedule != null">and auto_update_schedule = #{autoUpdateSchedule}
            </if>
        </where>
        ) t1 left join team_project_template t2 on t1.template_code = t2.code
        order by t1.id desc
    </select>

    <select id="selectAllProjectList" parameterType="com.boyo.project.entity.TeamProject"
            resultMap="TeamProjectResult">
        select t1.*,t2.name as template_name from (select
        id, cover, name, code, description, access_control_type, white_list, `order`, deleted, template_code, schedule,
        create_time, organization_code, deleted_time, prefix, open_prefix, archive, archive_time, open_begin_time,
        open_task_private, task_board_theme, begin_time, end_time, auto_update_schedule
        from team_project
        <where>
            <if test="cover != null and cover != ''">
                and cover = #{cover}
            </if>
            <if test="name != null and name != ''">
                and name like concat('%',#{name},'%')
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="description != null and description != ''">
                and description = #{description}
            </if>
            <if test="accessControlType != null and accessControlType != ''">
                and access_control_type = #{accessControlType}
            </if>
            <if test="whiteList != null and whiteList != ''">
                and white_list = #{whiteList}
            </if>
            <if test="order != null">
                and order = #{order}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="templateCode != null and templateCode != ''">
                and template_code = #{templateCode}
            </if>
            <if test="schedule != null">
                and schedule = #{schedule}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="organizationCode != null and organizationCode != ''">
                and organization_code = #{organizationCode}
            </if>
            <if test="deletedTime != null and deletedTime != ''">
                and deleted_time = #{deletedTime}
            </if>
            <if test="prefix != null and prefix != ''">
                and prefix = #{prefix}
            </if>
            <if test="openPrefix != null">
                and open_prefix = #{openPrefix}
            </if>
            <if test="archive != null">
                and archive = #{archive}
            </if>
            <if test="archiveTime != null and archiveTime != ''">
                and archive_time = #{archiveTime}
            </if>
            <if test="openBeginTime != null">
                and open_begin_time = #{openBeginTime}
            </if>
            <if test="openTaskPrivate != null">
                and open_task_private = #{openTaskPrivate}
            </if>
            <if test="taskBoardTheme != null and taskBoardTheme != ''">
                and task_board_theme = #{taskBoardTheme}
            </if>
            <if test="beginTime != null and beginTime != ''">
                and begin_time = #{beginTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and end_time = #{endTime}
            </if>
            <if test="autoUpdateSchedule != null">and auto_update_schedule = #{autoUpdateSchedule}
            </if>
        </where>
        ) t1 left join team_project_template t2 on t1.template_code = t2.code
        order by t1.id desc
    </select>
</mapper>

