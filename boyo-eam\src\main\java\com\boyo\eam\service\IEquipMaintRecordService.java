package com.boyo.eam.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.*;
import com.boyo.eam.domain.VO.EquipMaintRecordTaskVO;

import java.util.List;

/**
 * 维保记录表(EquipMaintRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-19 14:56:16
 */
public interface IEquipMaintRecordService extends IService<EquipMaintRecord> {

    /**
     * 查询多条数据
     *
     * @param equipMaintRecord 对象信息
     * @return 对象列表
     */
    List<EquipMaintRecord> selectEquipMaintRecordList(EquipMaintRecord equipMaintRecord);

    /**
     * 通过维保任务openid新增任务下所有明细的维保记录
     */
    Boolean insertByTaskOpenid(String taskOpenid);

    /**
     * 通过维保任务openid更新任务下所有明细的维保记录的结束时间
     */
    Boolean updateByTaskOpenid(String taskOpenid);


    /**
     * 查询维保任务记录
     */
    List<EquipMaintRecordTaskVO> selectTask(EquipMaintRecordTaskVO equipMaintRecordTaskVO);

}
