package com.boyo.mes.service.impl;

import com.boyo.common.core.text.Convert;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.MesCutting;
import com.boyo.mes.mapper.MesCuttingMapper;
import com.boyo.mes.service.IMesCuttingService;

import java.util.Date;
import java.util.List;

/**
 * (MesCutting)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-10 15:46:44
 */
@Service("mesCuttingService")
@AllArgsConstructor
@Tenant
public class MesCuttingServiceImpl extends ServiceImpl<MesCuttingMapper, MesCutting> implements IMesCuttingService {
    private final MesCuttingMapper mesCuttingMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<MesCutting> selectMesCuttingList(MesCutting mesCutting) {
        return mesCuttingMapper.selectMesCuttingList(mesCutting);
    }

    @Override
    public boolean save(MesCutting entity) {
        entity.setCuttingCount(Convert.toInt(entity.getAllLength() / entity.getCuttingLength()));
        entity.setSurplus(entity.getAllLength() % entity.getCuttingLength());
        entity.setCreateTime(new Date());
        entity.setCreateBy(SecurityUtils.getUsername());
        return super.save(entity);
    }

    @Override
    public boolean updateById(MesCutting entity) {
        entity.setCuttingCount(Convert.toInt(entity.getAllLength() / entity.getCuttingLength()));
        entity.setSurplus(entity.getAllLength() % entity.getCuttingLength());
        return super.updateById(entity);
    }
}
