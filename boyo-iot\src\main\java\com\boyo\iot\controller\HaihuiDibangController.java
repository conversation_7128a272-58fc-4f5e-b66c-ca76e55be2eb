package com.boyo.iot.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSONException;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.utils.poi.ExcelUtil;
import com.boyo.iot.entity.HaihuiDibang;
import com.boyo.iot.service.IHaihuiDibangService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/haihui/haihuiDibang")
public class HaihuiDibangController extends BaseController {

    @Autowired
    private IHaihuiDibangService haihuiDibangService;

    /**
     * 查询磅单信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HaihuiDibang haihuiDibang) {
        startPage();
        List<HaihuiDibang> list = haihuiDibangService.selectHaihuiDibangList(haihuiDibang);
        return getDataTable(list);
    }

    /**
     * 导出磅单信息列表
     */
    @PostMapping("/export")
    public AjaxResult export(HaihuiDibang haihuiDibang) {
        List<HaihuiDibang> list = haihuiDibangService.selectHaihuiDibangList(haihuiDibang);
        ExcelUtil<HaihuiDibang> util = new ExcelUtil<>(HaihuiDibang.class);
        return util.exportExcel(list, "磅单信息");
    }


    /**
     * 新增磅单信息
     */
    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)  // 确保所有异常都会触发回滚
    public AjaxResult add(@Valid @RequestBody JSONObject jsonObject) {
        try {
            // 检查jsonObject是否为空或格式不正确
            if (jsonObject == null || !jsonObject.containsKey("Body")) {
                throw new IllegalArgumentException("请求数据格式错误");
            }

            // 获取JSON对象中的Body数组，其中包含磅单详细信息
            JSONArray body = jsonObject.getJSONArray("Body");

            // 检查Body数组是否非空且至少包含一个元素
            if (body != null && body.size() > 0) {
                for (int i = 0; i < body.size(); i++) {
                    JSONObject jsonObject1 = body.getJSONObject(i);
                    // 将JSON对象转换为HaihuiDibang类实例
                    HaihuiDibang haihuiDibang = jsonObject1.toBean(HaihuiDibang.class);

                    // 检查转换后的HaihuiDibang对象是否非空且ticket_number字段不为空
                    if (haihuiDibang != null && haihuiDibang.getTicket_number() != null) {
                        // 根据ticket_number查询数据库中已存在的磅单信息
                        HaihuiDibang dibangByticketNumber = haihuiDibangService.selectHaihuiDibangByticketNumber(haihuiDibang.getTicket_number());

                        // 删除数据库中已存在的磅单信息，以避免重复插入
                        if (dibangByticketNumber != null) {
                            haihuiDibangService.deleteHaihuiDibangByIds(new Long[]{dibangByticketNumber.getId()});
                        }

                        // 插入新的磅单信息到数据库，并返回操作结果
                        int insertResult = haihuiDibangService.insertHaihuiDibang(haihuiDibang);
                        if (insertResult <= 0) {
                            throw new RuntimeException("插入磅单信息失败");
                        }
                    } else {
                        throw new IllegalArgumentException("无效的磅单信息");
                    }
                }
                return AjaxResult.success();
            } else {
                // 如果Body数组为空或不包含任何元素，返回错误提示
                throw new IllegalArgumentException("数据为空");
            }
        } catch (JSONException e) {
            // 如果发生JSON解析错误，记录错误日志并返回错误提示
            logger.error("JSON解析错误: " + jsonObject.toString(), e);
            throw new RuntimeException("JSON解析错误", e);
        } catch (IllegalArgumentException e) {
            // 如果发生非法参数错误，记录错误日志并返回错误提示
            logger.error("非法参数错误: " + jsonObject.toString(), e);
            throw e;
        } catch (Exception e) {
            // 如果发生其他异常，记录错误日志并返回错误提示
            logger.error("未知错误: " + jsonObject.toString(), e);
            throw e;
        }
    }


    /**
     * 修改磅单信息
     */
//    @PutMapping
//    public AjaxResult edit(@RequestBody HaihuiDibang haihuiDibang) {
//        return toAjax(haihuiDibangService.updateHaihuiDibang(haihuiDibang));
//    }

    /**
     * 删除磅单信息
     */
    @DeleteMapping("del/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(haihuiDibangService.deleteHaihuiDibangByIds(ids));
    }

    /**
     * 根据ID查询磅单信息
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(haihuiDibangService.selectHaihuiDibangById(id));
    }
}