package com.boyo.mes.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.master.domain.TMaterial;
import com.boyo.mes.entity.MesModulproduction;
import com.boyo.mes.entity.MesProduction;

import java.util.List;

/**
 * 模具产品绑定关系(MesModulproduction)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
public interface IMesModulproductionService extends IService<MesModulproduction> {

    /**
     * 查询多条数据
     *
     * @param mesModulproduction 对象信息
     * @return 对象列表
     */
    List<MesModulproduction> selectMesModulproductionList(MesModulproduction mesModulproduction);

    /**
     * 获取模具对应的产品列表
     * @param id
     * @return
     */
    List<TMaterial> getModulProduction(Integer id);


}
