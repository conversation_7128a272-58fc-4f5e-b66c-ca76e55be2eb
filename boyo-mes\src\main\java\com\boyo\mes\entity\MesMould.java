package com.boyo.mes.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 模具基本信息(MesMould)实体类
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
@Data
@TableName(value = "t_mes_mould")
public class MesMould implements Serializable {
    private static final long serialVersionUID = 186431225008759428L;
            
    @TableId
    private Integer id;
    
    /**
    * 模具名称
    */
    @TableField(value="mould_name")
    private String mouldName;
    /**
    * 模具编码
    */
    @TableField(value="mouldc_code")
    private String mouldCode;
    /**
    * 模具理论寿命（小时）
    */
    @TableField(value="mould_life")
    private Double mouldLife;

    /**
     * 已使用时间
     */
    @TableField(exist = false)
    private Double usedLife;

    /**
     * 关联产品id
     */
    @TableField(exist = false)
    private String productionIdStr;
    /**
     * 产品名称
     */
    @TableField(exist = false)
    private String produtionName;
    /**
     * 产品编码
     */
    @TableField(exist = false)
    private String produtionCode;
    /**
    * 保养频率
    */
    @TableField(value="maintain_frequency")
    private Double maintainFrequency;
    /**
    * 创建时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(value="create_time",fill = FieldFill.INSERT)
    private Date createTime;
    /**
    * 创建人
    */
    @TableField(value="create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 创建部门
    */
    @TableField(value="dept_id",fill = FieldFill.INSERT)
    private String deptId;
    @TableField(exist = false)
    private boolean life = true;

}
