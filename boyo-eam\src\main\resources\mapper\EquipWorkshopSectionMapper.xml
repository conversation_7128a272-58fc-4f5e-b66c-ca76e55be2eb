<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipWorkshopSectionMapper">

    <resultMap type="com.boyo.eam.domain.EquipWorkshopSection" id="EquipWorkshopSectionResult">
        <result property="id" column="id" />
        <result property="code" column="code" />
        <result property="name" column="name" />
        <result property="openid" column="openid" />
        <result property="factoryOpenid" column="factory_openid" />
        <result property="workshopOpenid" column="workshop_openid" />
        <result property="lineOpenid" column="line_openid" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipWorkshopSectionList" parameterType="com.boyo.eam.domain.EquipWorkshopSection" resultMap="EquipWorkshopSectionResult">
        select
          id, code, name, openid, factory_openid, workshop_openid, line_openid
        from equip_workshop_section
        <where>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
            <if test="factoryOpenid != null and factoryOpenid != ''">
                and factory_openid = #{factoryOpenid}
            </if>
            <if test="workshopOpenid != null and workshopOpenid != ''">
                and workshop_openid = #{workshopOpenid}
            </if>
            <if test="lineOpenid != null and lineOpenid != ''">
                and line_openid = #{lineOpenid}
            </if>
        </where>
    </select>
</mapper>

