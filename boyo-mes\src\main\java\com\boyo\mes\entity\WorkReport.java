package com.boyo.mes.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 报工记录(WorkReport)实体类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Data
@TableName(value = "t_work_report")
public class WorkReport implements Serializable {
    private static final long serialVersionUID = -16039072294090517L;

    @TableId
    private Integer id;

    /**
    * 订单id
    */
    @TableField(value="order_id")
    private Integer orderId;
    /**
    * 工序id
    */
    @TableField(value="process_id")
    private Integer processId;

    /**
     * 设备id
     */
    @TableField(value = "equipment_id")
    private Integer equipmentId;
    /**
    * 用户id
    */
    @TableField(value="user_id")
    private Integer userId;
    /**
    * 用户名
    */
    @TableField(value="user_name")
    private String userName;
    /**
    * 报工量
    */
    @TableField(value="report_num")
    private Integer reportNum;
    /**
    * 废品量
    */
    @TableField(value="waste_num")
    private Integer wasteNum;
    /**
    * 报工时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(value="report_time")
    private Date reportTime;

    @TableField(value = "task_id")
    private Integer taskId;

    /**
     * 设备名称
     */
    @TableField(exist = false)
    private String equipmentName;

    /**
     * 工序名称
     */
    @TableField(exist = false)
    private String processName;

    /**
     * 产品名称
     */
    @TableField(exist = false)
    private String productionName;

    /**
     * 订单号
     */
    @TableField(exist = false)
    private String orderNum;

    /**
     * 订单详情
     */
    @TableField(exist = false)
    private List<WorkReport> reportList;

    /**
     * 质检人
     */
    @TableField(value = "quality_inspector")
    private String qualityInspector;
    /**
     * 缺陷原因
     */
    private String defectCause;

    /**
     * 附件
     */
    private String attachments;
    @TableField(exist = false)
    private String taskName;
    @TableField(exist = false)
    private String taskNum;

    @TableField(value = "start_time")
    private Date startTime;


    @TableField(exist = false)
    private Date endTime;

    @TableField(value = "product_num")
    private Integer productNum;

}
