package com.boyo.master.service.impl;


import com.boyo.common.utils.DateUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.master.domain.ScreenYield;
import com.boyo.master.mapper.ScreenYieldMapper;
import com.boyo.master.service.IScreenYieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 大屏产量Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@Service
@Tenant
public class ScreenYieldServiceImpl implements IScreenYieldService {
    @Autowired
    private ScreenYieldMapper screenYieldMapper;

    /**
     * 查询大屏产量
     *
     * @param id 大屏产量主键
     * @return 大屏产量
     */
    @Override
    public ScreenYield selectScreenYieldById(Long id) {
        return screenYieldMapper.selectScreenYieldById(id);
    }

    /**
     * 查询大屏产量列表
     *
     * @param screenYield 大屏产量
     * @return 大屏产量
     */
    @Override
    public List<ScreenYield> selectScreenYieldList(ScreenYield screenYield) {
        return screenYieldMapper.selectScreenYieldList(screenYield);
    }

    /**
     * 新增大屏产量
     *
     * @param screenYield 大屏产量
     * @return 结果
     */
    @Override
    public int insertScreenYield(ScreenYield screenYield) {
        screenYield.setCreateTime(DateUtils.getNowDate());
        return screenYieldMapper.insertScreenYield(screenYield);
    }

    /**
     * 修改大屏产量
     *
     * @param screenYield 大屏产量
     * @return 结果
     */
    @Override
    public int updateScreenYield(ScreenYield screenYield) {
        screenYield.setUpdateTime(DateUtils.getNowDate());
        return screenYieldMapper.updateScreenYieldByYieldTime(screenYield);
    }

    /**
     * 批量删除大屏产量
     *
     * @param ids 需要删除的大屏产量主键
     * @return 结果
     */
    @Override
    public int deleteScreenYieldByIds(Long[] ids) {
        return screenYieldMapper.deleteScreenYieldByIds(ids);
    }

    @Override
    public int deleteScreenYieldByyieldTime(String yieldTime) {
        return screenYieldMapper.deleteScreenYieldByyieldTime(yieldTime);
    }

    /**
     * 删除大屏产量信息
     *
     * @param id 大屏产量主键
     * @return 结果
     */
    @Override
    public int deleteScreenYieldById(Long id) {
        return screenYieldMapper.deleteScreenYieldById(id);
    }

    @Override
    public int deleteScreenYieldByWorkshopId(Long id) {
        return screenYieldMapper.deleteScreenYieldByWorkshopId(id);
    }

    @Override
    public Long getYieldCount(String start, String end) {
        return screenYieldMapper.getYieldCount(start, end);
    }
}
