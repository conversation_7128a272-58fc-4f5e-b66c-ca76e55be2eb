package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipLedgerProperty;

import java.util.List;

/**
 * 设备台账属性表(EquipLedgerProperty)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
public interface EquipLedgerPropertyMapper extends BaseMapper<EquipLedgerProperty>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipLedgerProperty 实例对象
     * @return 对象列表
     */
    List<EquipLedgerProperty> selectEquipLedgerPropertyList(EquipLedgerProperty equipLedgerProperty);


}

