<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.wms.mapper.WmsStockMapper">

    <resultMap type="com.boyo.wms.vo.WmsStockVO" id="WmsStockResult">
        <result property="id" column="id"/>
        <result property="materielOpenid" column="materiel_openid"/>
        <result property="materielBatch" column="materiel_batch"/>
        <result property="warehouseOpenid" column="warehouse_openid"/>
        <result property="areaOpenid" column="area_openid"/>
        <result property="allocationOpenid" column="allocation_openid"/>
        <result property="materielQuantity" column="materiel_quantity"/>
        <result property="version" column="version"/>
        <result property="updateTime" column="update_time"/>
        <result property="warehouseName" column="warehouse_name"></result>
        <result property="areaName" column="area_name"></result>
        <result property="allocationName" column="allocation_name"></result>
        <result property="materielName" column="materiel_name"></result>
        <result property="materielCode" column="materiel_code"></result>
        <result property="materielNorms" column="materiel_norms"></result>
        <result property="materielUnit" column="materiel_unit"></result>

        <result property="max" column="max_stock"></result>
        <result property="maxWarn" column="max_warning"></result>
        <result property="min" column="min_stock"></result>
        <result property="minWarn" column="min_warning"></result>

    </resultMap>

    <select id="selectWmsStockList" parameterType="com.boyo.wms.entity.WmsStock" resultMap="WmsStockResult">
        select t1.*,t2.warehouse_name,t3.area_name,t4.allocation_name,t5.materiel_name,
        t5.materiel_norms,t5.materiel_code,t6.base_desc as materiel_unit
        from t_wms_stock t1
        left join t_model_warehouse t2 on t1.warehouse_openid = t2.warehouse_openid
        left join t_model_area t3 on t1.area_openid = t3.area_openid
        left join t_model_allocation t4 on t1.allocation_openid = t4.allocation_openid
        left join t_material t5 on t1.materiel_openid = t5.materiel_openid
        left join t_base_dict t6 on t5.materiel_unit = t6.openid
        <where>
            <if test="materielOpenid != null  and materielOpenid != ''">
                and t1.materiel_openid = #{materielOpenid}
            </if>
            <if test="materielBatch != null  and materielBatch != ''">
                and t1.materiel_batch = #{materielBatch}
            </if>
            <if test="warehouseOpenid != null  and warehouseOpenid != ''">
                and t1.warehouse_openid = #{warehouseOpenid}
            </if>
            <if test="areaOpenid != null  and areaOpenid != ''">
                and t1.area_openid = #{areaOpenid}
            </if>
            <if test="allocationOpenid != null  and allocationOpenid != ''">
                and t1.allocation_openid = #{allocationOpenid}
            </if>
            <if test="materielName != null  and materielName != ''">
                and t5.materiel_name like concat('%', #{materielName}, '%')
            </if>
        </where>
        order by materiel_name,materiel_batch asc
    </select>

    <select id="selectWmsStockByMateriel" parameterType="com.boyo.wms.entity.WmsStock" resultMap="WmsStockResult">
        select t1.*,t5.materiel_name,
        t5.materiel_norms,t5.materiel_code,t6.base_desc as materiel_unit,t5.max_stock,t5.max_warning,t5.min_stock,t5.min_warning
        from (select materiel_openid,sum(materiel_quantity) as materiel_quantity from t_wms_stock group by materiel_openid) t1
        left join t_material t5 on t1.materiel_openid = t5.materiel_openid
        left join t_base_dict t6 on t5.materiel_unit = t6.openid
        <where>
            <if test="materielOpenid != null  and materielOpenid != ''">
                and t1.materiel_openid = #{materielOpenid}
            </if>
            <if test="materielBatch != null  and materielBatch != ''">
                and t1.materiel_batch = #{materielBatch}
            </if>
            <if test="warehouseOpenid != null  and warehouseOpenid != ''">
                and t1.warehouse_openid = #{warehouseOpenid}
            </if>
            <if test="areaOpenid != null  and areaOpenid != ''">
                and t1.area_openid = #{areaOpenid}
            </if>
            <if test="allocationOpenid != null  and allocationOpenid != ''">
                and t1.allocation_openid = #{allocationOpenid}
            </if>
            <if test="materielName != null  and materielName != ''">
                and t5.materiel_name like concat('%', #{materielName}, '%')
            </if>
        </where>
        order by materiel_name
    </select>
    <select id="selectStockWarn" resultType="com.boyo.wms.vo.StockWarnVO">
        SELECT t3.materiel_name  materielName,
               t3.materiel_code  materielCode,
               t3.materiel_norms materielNorms,
               t3.min_stock      `min`,
               t3.min_warning    minWarn,
               t3.max_stock      `max`,
               t3.max_warning    maxWarn,
               t3.stock
        FROM (
                 SELECT t1.*,
                        IFNULL(t2.stock, 0) stock
                 FROM t_material t1
                          LEFT JOIN (
                     SELECT sum(materiel_quantity) AS stock,
                            materiel_openid
                     FROM t_wms_stock
                     GROUP BY materiel_openid
                 ) t2 ON t1.materiel_openid = t2.materiel_openid
             ) t3
        WHERE (
                t3.min_stock IS NOT NULL
                OR t3.min_warning IS NOT NULL
                OR t3.max_stock IS NOT NULL
                OR t3.max_warning IS NOT NULL
            )
          and (t3.min_stock >= stock or t3.min_warning >= stock or t3.max_stock &lt;= stock or
               t3.max_warning &lt;= stock)
    </select>
</mapper>
