package com.boyo.master.service;

import java.util.List;

import com.boyo.master.domain.BaseDictType;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 数据字典类型管理Service接口
 *
 * <AUTHOR>
 */
public interface IBaseDictTypeService extends IService<BaseDictType> {
    /**
     * 根据条件查询查询数据字典类型管理列表
     *
     * @param baseDictType 数据字典类型管理
     * @return 数据字典类型管理集合
     */
    List<BaseDictType> selectBaseDictTypeList(BaseDictType baseDictType);
}
