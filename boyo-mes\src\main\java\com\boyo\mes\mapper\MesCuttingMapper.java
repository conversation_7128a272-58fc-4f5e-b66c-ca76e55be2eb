package com.boyo.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.mes.entity.MesCutting;
import java.util.List;

/**
 * (MesCutting)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-10 15:46:44
 */
public interface MesCuttingMapper extends BaseMapper<MesCutting>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param mesCutting 实例对象
     * @return 对象列表
     */
    List<MesCutting> selectMesCuttingList(MesCutting mesCutting);


}

