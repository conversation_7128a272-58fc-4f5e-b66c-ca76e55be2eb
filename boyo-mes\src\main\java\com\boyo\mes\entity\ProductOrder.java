package com.boyo.mes.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 生产订单(ProductOrder)实体类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Data
@TableName(value = "t_product_order")
public class ProductOrder implements Serializable {
    private static final long serialVersionUID = 703841474125444899L;
            
    @TableId
    private Integer id;
    
    /**
    * 订单编号
    */
    @TableField(value="order_num")
    private String orderNum;
    /**
    * 产品id
    */
    @TableField(value="production_id")
    private Integer productionId;
    /**
    * 计划数量
    */
    @TableField(value="production_num")
    private Integer productionNum;
    /**
    * 交期
    */
    @TableField(value="delivery_date")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date deliveryDate;
    /**
    * 客户id
    */
    @TableField(value="customer_id")
    private Integer customerId;
    /**
    * 订单状态 0：新建 1：执行中 2：完工 3：作废
    */
    @TableField(value="order_status")
    private String orderStatus;
    /**
    * 开始执行时间
    */
    @TableField(value="execute_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date executeTime;
    /**
    * 完工时间
    */
    @TableField(value="complete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date completeTime;
    /**
    * 作废时间
    */
    @TableField(value="cancel_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;
    /**
    * 对应工序组id
    */
    @TableField(value="process_group_id")
    private Integer processGroupId;

    @TableField(value = "actual_num")
    private Integer actualNum;
    /**
    * 创建时间
    */
    @TableField(value="create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 订单id
     */
    @TableField(value="task_id")
    private Integer taskId;
    /**
     * 图片介绍
     */
    @TableField(value = "picture")
    private String picture;
    /**
    * 创建人
    */
    @TableField(value="create_user_id")
    private Integer createUserId;

    @TableField(exist = false)
    private String productionName;

    /**
     * 产品编码
     */
    @TableField(exist = false)
    private String productionCode;

    /**
     * 产品规格
     */
    @TableField(exist = false)
    private String productionNorms;

    @TableField(exist = false)
    private String customerName;

    /**
     * 设备id
     */
    @TableField(exist = false)
    private Long equipmentId;

    /**
     * 工序id
     */
    @TableField(exist = false)
    private Long processId;

    /**
     * 工序名称
     */
    @TableField(exist = false)
    private String processName;

    /**
     * 已报工数量
     */
    @TableField(exist = false)
    private Integer reportNum;

    /**
     * 废品量
     */
    @TableField(exist = false)
    private Integer wasteNum;

    /**
     * 工序状态 0：待执行 1：执行中
     */
    @TableField(exist = false)
    private String processStatus;
    /**
     * 顶单编码
     */
    private String taskNum;
    /**
     * 订单名称
     */
    private String taskName;
    /**
     * 单位
     */
    private  String unit;

    /**
     * 工单创建人
     */
    @TableField(value = "create_user_name")
    private String createUserName;

    @TableField(value = "order_finish_num")
    private Integer orderFinishNum;

    @TableField(value = "create_status")
    private String createStatus;

    @TableField(value = "work_shop")
    private String workShop;

    @TableField
    private Integer orderAmounts;

    @TableField(value = "delivery_cycle")
    private Integer deliveryCycle;

    @TableField(exist = false)
    private Date startTime;

    @TableField(exist = false)
    private Date endTime;

    @TableField(value = "order_number")
    private Integer orderNumber;

    @TableField(exist = false )
    private Date stopTime;

    @TableField(value = "order_name")
    private String orderName;
}
