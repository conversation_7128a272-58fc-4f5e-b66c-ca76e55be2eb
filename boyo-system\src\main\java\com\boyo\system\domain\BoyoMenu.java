package com.boyo.system.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import com.boyo.common.core.domain.entity.SysMenu;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 菜单管理
 * 表名 t_sys_menu
 *
 * <AUTHOR>
 */
@ApiModel("系统菜单表")
@Data
@TableName("t_sys_menu")
public class BoyoMenu extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID
     */
    @ApiModelProperty("菜单ID")
    @TableId
    private Long menuId;
    /**
     * 系统openid
     */
    @ApiModelProperty("系统openid")
    @TableField(value = "system_openid")
    private String systemOpenid;
    /**
     * 菜单名称
     */
    @ApiModelProperty("菜单名称")
    @TableField(value = "menu_name")
    private String menuName;
    /**
     * 父菜单ID
     */
    @ApiModelProperty("父菜单ID")
    @TableField(value = "parent_id")
    private Long parentId;
    /**
     * 显示顺序
     */
    @ApiModelProperty("显示顺序")
    @TableField(value = "order_num")
    private Integer orderNum;
    /**
     * 路由地址
     */
    @ApiModelProperty("路由地址")
    @TableField(value = "path")
    private String path;
    /**
     * 组件路径
     */
    @ApiModelProperty("组件路径")
    @TableField(value = "component")
    private String component;
    /**
     * 是否为外链（0是 1否）
     */
    @ApiModelProperty("是否为外链（0是 1否）")
    @TableField(value = "is_frame")
    private Integer isFrame;
    /**
     * 是否缓存（0缓存 1不缓存）
     */
    @ApiModelProperty("是否缓存（0缓存 1不缓存）")
    @TableField(value = "is_cache")
    private Integer isCache;
    /**
     * 菜单类型（M目录 C菜单 F按钮）
     */
    @ApiModelProperty("菜单类型（M目录 C菜单 F按钮）")
    @TableField(value = "menu_type")
    private String menuType;
    /**
     * 菜单状态（0显示 1隐藏）
     */
    @ApiModelProperty("菜单状态（0显示 1隐藏）")
    @TableField(value = "visible")
    private String visible;
    /**
     * 菜单状态（0正常 1停用）
     */
    @ApiModelProperty("菜单状态（0正常 1停用）")
    @TableField(value = "status")
    private String status;
    /**
     * 权限标识
     */
    @ApiModelProperty("权限标识")
    @TableField(value = "perms")
    private String perms;
    /**
     * 菜单图标
     */
    @ApiModelProperty("菜单图标")
    @TableField(value = "icon")
    private String icon;
    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    @TableField(value = "create_by")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    @TableField(value = "update_by")
    private String updateBy;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(value = "update_time")
    private Date updateTime;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private List<BoyoMenu> children = new ArrayList<>();
}
