package com.boyo.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.mes.entity.MesYield;
import java.util.List;

/**
 * 产量详情(MesYield)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-05 17:02:39
 */
public interface MesYieldMapper extends BaseMapper<MesYield>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param mesYield 实例对象
     * @return 对象列表
     */
    List<MesYield> selectMesYieldList(MesYield mesYield);

    List<MesYield> listLessYield(MesYield mesYield);
    List<MesYield> listYieldByDevice(String rq);


}

