<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.wms.mapper.WmsFlowMapper">

    <resultMap type="com.boyo.wms.vo.WmsFlowVO" id="WmsFlowResult">
        <result property="id" column="id"/>
        <result property="planOpenid" column="plan_openid"/>
        <result property="detailOpenid" column="detail_openid"/>
        <result property="materielNumber" column="materiel_number"/>
        <result property="materielBatch" column="materiel_batch"/>
        <result property="warehouseOpenid" column="warehouse_openid"/>
        <result property="areaOpenid" column="area_openid"/>
        <result property="allocationOpenid" column="allocation_openid"/>
        <result property="createdAt" column="created_at"/>
        <result property="createdUser" column="created_user"/>
        <result property="flowType" column="flow_type"/>

        <result property="materielName" column="materiel_name"></result>
        <result property="warehouseName" column="warehouse_name"></result>
        <result property="areaName" column="area_name"></result>
        <result property="allocationName" column="allocation_name"></result>
        <result property="qcReport" column="qc_report"></result>
    </resultMap>


    <select id="selectWmsFlowList" parameterType="com.boyo.wms.entity.WmsFlow" resultMap="WmsFlowResult">
        SELECT
        l1.*,t3.materiel_name ,t4.warehouse_name,t5.area_name,t6.allocation_name,t7.id as qc_report FROM
        (
            SELECT
            t1.* ,t2.detail_materiel_openid
            FROM
            t_wms_flow t1,
            t_wms_materiel t2
        <where>
            t1.detail_openid = t2.detail_openid
            <if test="planOpenid != null  and planOpenid != ''">
                and plan_openid = #{planOpenid}
            </if>
            <if test="flowType != null  and flowType != ''">
                and flow_type = #{flowType}
            </if>
            <if test="detailOpenid != null  and detailOpenid != ''">
                and detail_openid = #{detailOpenid}
            </if>
            <if test="warehouseOpenid != null  and warehouseOpenid != ''">
                and warehouse_openid = #{warehouseOpenid}
            </if>
            <if test="areaOpenid != null  and areaOpenid != ''">
                and area_openid = #{areaOpenid}
            </if>
            <if test="allocationOpenid != null  and allocationOpenid != ''">
                and allocation_openid = #{allocationOpenid}
            </if>
        </where>
        ) l1 LEFT JOIN t_material t3 ON l1.detail_materiel_openid = t3.materiel_openid
        LEFT JOIN t_model_warehouse t4 ON l1.warehouse_openid = t4.warehouse_openid
        LEFT JOIN t_model_area t5 ON l1.area_openid = t5.area_openid
        LEFT JOIN t_model_allocation t6 ON l1.allocation_openid = t6.allocation_openid
        left join t_wms_flowqc_index t7 on l1.id = t7.flow_id
        <where>
            <if test="materielName != null  and materielName != ''">
                and t3.materiel_name like concat('%', #{materielName}, '%')
            </if>
        </where>
        order by l1.id desc
    </select>

    <select id="selectDateFlow" resultType="com.boyo.wms.vo.DateFlowVO">
        SELECT
            t1.rq as date,
            max(
                    CASE
                        t1.flow_type
                        WHEN '1' THEN
                            t1.c ELSE 0
                        END
                ) 'in',
            max(
                    CASE
                        t1.flow_type
                        WHEN '2' THEN
                            t1.c ELSE 0
                        END
                ) 'out'
        FROM
            (
                SELECT
                    DATE_FORMAT( created_at, '%y-%m-%d' ) rq,
                    flow_type,
                    count(*) c
                FROM
                    t_wms_flow
                where created_at >= (DATE_FORMAT(DATE_ADD(created_at,INTERVAL -7 DAY),'%y-%m-%d') + ' 00:00:00')
                GROUP BY
                    DATE_FORMAT( created_at, '%y-%m-%d' ),
                    flow_type
            ) t1
        GROUP BY
            rq
    </select>
</mapper>
