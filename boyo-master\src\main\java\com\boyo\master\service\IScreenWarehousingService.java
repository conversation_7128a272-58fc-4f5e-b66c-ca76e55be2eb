package com.boyo.master.service;


import com.boyo.master.domain.ScreenWarehousing;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 大屏入库信息Service接口
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
public interface IScreenWarehousingService
{
    /**
     * 查询大屏入库信息
     *
     * @param id 大屏入库信息主键
     * @return 大屏入库信息
     */
    public ScreenWarehousing selectScreenWarehousingById(Long id);

    /**
     * 查询大屏入库信息列表
     *
     * @param screenWarehousing 大屏入库信息
     * @return 大屏入库信息集合
     */
    public List<ScreenWarehousing> selectScreenWarehousingBetween(ScreenWarehousing screenWarehousing, String start, String end);


    public List<ScreenWarehousing> selectScreenWarehousingList(ScreenWarehousing screenWarehousing);

    /**
     * 新增大屏入库信息
     *
     * @param screenWarehousing 大屏入库信息
     * @return 结果
     */
    public int insertScreenWarehousing(ScreenWarehousing screenWarehousing);

    /**
     * 修改大屏入库信息
     *
     * @param screenWarehousing 大屏入库信息
     * @return 结果
     */
    public int updateScreenWarehousing(ScreenWarehousing screenWarehousing);

    /**
     * 批量删除大屏入库信息
     *
     * @param ids 需要删除的大屏入库信息主键集合
     * @return 结果
     */
    public int deleteScreenWarehousingByIds(Long[] ids);

    /**
     * 删除大屏入库信息信息
     *
     * @param id 大屏入库信息主键
     * @return 结果
     */
    public int deleteScreenWarehousingById(Long id);

    public Long getWarehousingCount (String start, String end);

}
