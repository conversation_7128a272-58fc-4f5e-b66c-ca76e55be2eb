package com.boyo.project.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.framework.annotation.Tenant;
import com.boyo.project.entity.TeamTask;
import com.boyo.project.mapper.TeamTaskMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.project.entity.TeamTaskStages;
import com.boyo.project.mapper.TeamTaskStagesMapper;
import com.boyo.project.service.ITeamTaskStagesService;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 任务列表表(TeamTaskStages)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-10 11:03:15
 */
@Service("teamTaskStagesService")
@AllArgsConstructor
@Tenant
public class TeamTaskStagesServiceImpl extends ServiceImpl<TeamTaskStagesMapper, TeamTaskStages> implements ITeamTaskStagesService {
    private final TeamTaskStagesMapper teamTaskStagesMapper;
    private final TeamTaskMapper teamTaskMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<TeamTaskStages> selectTeamTaskStagesList(TeamTaskStages teamTaskStages) {
        List<TeamTaskStages> list = teamTaskStagesMapper.selectTeamTaskStagesList(teamTaskStages);
        if(list != null && list.size() > 0){
            for (int i = 0; i < list.size(); i++) {
                QueryWrapper<TeamTask> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("stage_code",list.get(i).getCode()).eq("project_code",list.get(i).getProjectCode()).orderByAsc("sort");
                list.get(i).setTaskList(teamTaskMapper.selectList(queryWrapper));
            }
        }
        return list;
    }

    @Override
    public boolean save(TeamTaskStages entity) {
        QueryWrapper<TeamTaskStages> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_code",entity.getProjectCode());
        entity.setSort(teamTaskStagesMapper.selectCount(queryWrapper) + 1);
        entity.setCode(IdUtil.fastSimpleUUID());
        entity.setCreateTime(DateUtil.formatDateTime(new Date()));
        return super.save(entity);
    }

    @Override
    public boolean updateBatchById(Collection<TeamTaskStages> entityList) {
        List<TeamTaskStages> list = new ArrayList<>(entityList);
        for (int i = 0; i < list.size(); i++) {
            TeamTaskStages stages = list.get(i);
            List<TeamTask> tasks = stages.getTaskList();
            if(tasks != null && tasks.size() > 0){
                for (int j = 0; j < tasks.size(); j++) {
                    tasks.get(j).setStageCode(stages.getCode());
                    tasks.get(j).setSort(j);
                    teamTaskMapper.updateById(tasks.get(j));
                }
            }
        }
        return super.updateBatchById(entityList);
    }
}
