package com.boyo.common.utils.oss;

import cn.hutool.core.date.DateTime;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.boyo.common.utils.uuid.UUID;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

@Component
@AllArgsConstructor
public class OssUtil {
    private final OSSConstantProperties ossConstantProperties;

    public String uploadFile(MultipartFile file) {

        String endpoint = ossConstantProperties.getEndPoint();
        String accessKeyId = ossConstantProperties.getAccessKeyId();
        String accessKeySecret = ossConstantProperties.getAccessKeySecret();
        String bucketName = ossConstantProperties.getBucketName();

        try {
            // 创建OSSClient实例。
            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            InputStream inputStream = file.getInputStream();
            //获取文件名称
            String fileName = file.getOriginalFilename();

            //1 在文件名称里面添加随机唯一值
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            fileName = uuid + fileName;

            //2 文件按照日期分类
            String datePath = new DateTime().toString("yyyy/MM/dd");

            //拼接
            fileName = datePath + "/" + fileName;

            //oss方法实现上传
            //第一个参数 bucket名称
            //第二个参数 上传到oss文件路径和名称 fileName
            //第三个参数 上传文件输入流
            ossClient.putObject(bucketName, fileName, inputStream);
            ossClient.shutdown();

            //把上传之后文件路径返回
            //需要把上传到阿里云oss路径手动拼接出来
            String url = "https://" + bucketName + "." + endpoint + "/" + fileName;

            return url;

        } catch (Exception e) {
            e.printStackTrace();
            return null;

        }
    }
}

