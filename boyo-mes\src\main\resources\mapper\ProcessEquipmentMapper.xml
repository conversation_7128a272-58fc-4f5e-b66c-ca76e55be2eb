<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.ProcessEquipmentMapper">

    <resultMap type="com.boyo.mes.entity.ProcessEquipment" id="ProcessEquipmentResult">
        <result property="id" column="id"/>
        <result property="processId" column="process_id"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="equipmentName" column="equipment_name"/>
        <result property="equipmentCode" column="equipment_code"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectProcessEquipmentList" parameterType="com.boyo.mes.entity.ProcessEquipment"
            resultMap="ProcessEquipmentResult">
        select
        t1.*,t2.equipment_name,t2.equipment_code
        from t_process_equipment t1,iot_equipment t2
        <where>
            t1.equipment_id = t2.id
            <if test="processId != null">
                and t1.process_id = #{processId}
            </if>
            <if test="equipmentId != null">
                and t1.equipment_id = #{equipmentId}
            </if>
        </where>
    </select>
</mapper>

