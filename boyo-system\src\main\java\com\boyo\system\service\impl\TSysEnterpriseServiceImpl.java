package com.boyo.system.service.impl;

import java.io.StringReader;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.UUID;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.rds20140815.Client;
import com.aliyun.rds20140815.models.CreateAccountRequest;
import com.aliyun.rds20140815.models.CreateDatabaseRequest;
import com.aliyun.rds20140815.models.GrantAccountPrivilegeRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.config.RdsConfig;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.system.domain.DatabaseLog;
import com.boyo.system.domain.EnterpriseDepartment;
import com.boyo.system.mapper.DatabaseLogMapper;
import com.boyo.system.mapper.EnterpriseDepartmentMapper;
import com.boyo.system.mapper.EnterpriseUserMapper;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.jdbc.ScriptRunner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.system.mapper.TSysEnterpriseMapper;
import com.boyo.system.domain.TSysEnterprise;
import com.boyo.system.service.ITSysEnterpriseService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 企业管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class TSysEnterpriseServiceImpl extends ServiceImpl<TSysEnterpriseMapper, TSysEnterprise> implements ITSysEnterpriseService {

    private final TSysEnterpriseMapper tSysEnterpriseMapper;

    private final DatabaseLogMapper databaseLogMapper;

    private final EnterpriseDepartmentMapper enterpriseDepartmentMapper;

    private final EnterpriseUserMapper enterpriseUserMapper;

    private final RdsConfig rdsConfig;
    private final JdbcTemplate jdbcTemplate;


    @Value("${boyo.tenant.database.init.driver-class}")
    private String tenantDbDriverClass;

    @Value("${boyo.tenant.database.init.url}")
    private String tenantDbUrl;

    private static final String DBINSTANCEID = "rm-m5e83ylqo6bdw8nq5";

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, propagation = Propagation.REQUIRED)
    public boolean save(TSysEnterprise entity) {
        // 业务主键
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setEnterpriseCode(entity.getEnterpriseAbbreviation());
        // 生成部门
        EnterpriseDepartment enterpriseDepartment = new EnterpriseDepartment();
        enterpriseDepartment.setEnterpriseOpenid(entity.getEnterpriseOpenid());
        enterpriseDepartment.setDepartmentOpenid(IdUtil.fastSimpleUUID());
        enterpriseDepartment.setParentOpenid("0");
        enterpriseDepartment.setDepartmentName(entity.getEnterpriseName());
        enterpriseDepartment.setDepartmentContacts(entity.getEnterpriseContacts());
        enterpriseDepartment.setDepartmentPhone(entity.getEnterprisePhone());
        enterpriseDepartment.setCreateTime(new Date());
        enterpriseDepartment.setUpdateTime(new Date());
        enterpriseDepartmentMapper.insert(enterpriseDepartment);

        // 生成管理员
        EnterpriseUser enterpriseUser = new EnterpriseUser();
        enterpriseUser.setEnterpriseOpenid(entity.getEnterpriseOpenid());
        enterpriseUser.setUserName(entity.getEnterpriseName());
        enterpriseUser.setUserAdmin(1L);
        enterpriseUser.setUserOpenid(IdUtil.fastSimpleUUID());
        enterpriseUser.setDepartmentOpenid(enterpriseDepartment.getDepartmentOpenid());
        enterpriseUser.setUserEmail(entity.getEnterpriseEmail());
        enterpriseUser.setUserPhone(entity.getEnterprisePhone());
//        String pass = UUID.randomUUID().toString().replaceAll("-", "");
        enterpriseUser.setUserPassword(SecurityUtils.encryptPassword("123456"));
        enterpriseUser.setNeedModifyPassword("1");
        enterpriseUser.setCreateTime(new Date());
        enterpriseUser.setUpdateTime(new Date());
        enterpriseUserMapper.insert(enterpriseUser);
        return super.save(entity);
    }

    /**
     * 查询企业管理列表
     *
     * @param tSysEnterprise 企业管理
     * @return tSysEnterprise 列表
     */
    @Override
    public List<TSysEnterprise> selectTSysEnterpriseList(TSysEnterprise tSysEnterprise) {
        return tSysEnterpriseMapper.selectTSysEnterpriseList(tSysEnterprise);
    }

    @Override
    public boolean checkExist(TSysEnterprise tSysEnterprise) {
        QueryWrapper<TSysEnterprise> queryWrapper = new QueryWrapper<>();
        queryWrapper.or(i -> i.eq("enterprise_name",tSysEnterprise.getEnterpriseName()).or().eq("enterprise_abbreviation", tSysEnterprise.getEnterpriseAbbreviation()));
        if (ObjectUtil.isNotNull(tSysEnterprise.getId())) {
            queryWrapper.and(i -> i.ne("id",  tSysEnterprise.getId()));
        }
        if (tSysEnterpriseMapper.selectCount(queryWrapper) > 0) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, propagation = Propagation.REQUIRED)
    public void initDatabase(String enterpriseOpenid) {
        enterpriseOpenid = enterpriseOpenid.toLowerCase();
        TSysEnterprise enterprise = this.getOne(Wrappers.<TSysEnterprise>query().eq("enterprise_openid", enterpriseOpenid));
        if (enterprise == null) {
            throw new CustomException("未找到企业信息");
        }
        if (!StrUtil.isAllBlank(
                enterprise.getEnterpriseDatabaseDriver(),
                enterprise.getEnterpriseDatabaseUrl(),
                enterprise.getEnterpriseDatabaseUsername(),
                enterprise.getEnterpriseDatabasePassword()
        )) {
            throw new CustomException("企业租户数据库连接信息已存在");
        }
        String database = "boyo_" + enterpriseOpenid.toLowerCase();
        String password = "By@#"+RandomUtil.randomString(20);
        Connection conn = null;
        try {
            String dataBaseUser = "db_" + RandomUtil.randomString(20);
            String sql = "create database if not exists " + database + " default charset utf8mb4";
            jdbcTemplate.execute(sql);
            sql = "create user '" + dataBaseUser + "'@'%' identified by '" + password + "'";
            jdbcTemplate.execute(sql);
            sql = "grant all on " + database.toLowerCase() + ".* to '" + dataBaseUser + "'";
            jdbcTemplate.execute(sql);
            sql = "flush  privileges";
            jdbcTemplate.execute(sql);

            enterprise.setEnterpriseDatabaseDriver(tenantDbDriverClass);
            enterprise.setEnterpriseDatabaseUrl(tenantDbUrl.replace("{{database}}", database));
            enterprise.setEnterpriseDatabaseUsername(dataBaseUser);
            enterprise.setEnterpriseDatabasePassword(password);
            // 使用新建企业租户数据库连接初始化表结构
            String url = enterprise.getEnterpriseDatabaseUrl();
            conn = DriverManager.getConnection(url, dataBaseUser, password);
            ScriptRunner runner = new ScriptRunner(conn);
            runner.setAutoCommit(false);
            runner.setErrorLogWriter(null);
            runner.setLogWriter(null);
            List<DatabaseLog> list = databaseLogMapper.selectList(null);
            if (list != null && list.size() > 0) {
                runner.runScript(new StringReader(list.get(0).getDatabaseFullSql()));
                for (int i = 1; i < list.size(); i++) {
                    runner.runScript(new StringReader(list.get(i).getDatabaseChangeSql()));
                }
                enterprise.setEnterpriseDatabaseVersion(list.get(list.size() - 1).getId());
            }
            // 初始化状态
            enterprise.setEnterpriseInit("1");
            // 更新企业租户连接信息入库
            this.updateById(enterprise);
        } catch (Exception e) {
            if (e.getCause() instanceof SQLException) {
                if (((SQLException) e.getCause()).getErrorCode() == 1007) {
                    throw new CustomException("企业租户数据库已存在");
                } else if (((SQLException) e.getCause()).getErrorCode() == 1396) {
                    throw new CustomException("企业租户数据库用户已存在");
                }
            }
            log.error("企业租户数据库初始化失败", e);
            throw new CustomException("企业租户数据库初始化失败");
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (Exception e) {
                }
            }
        }
    }

    @Override
    public String getEnterpriseScreen(String openid) {
        QueryWrapper<TSysEnterprise> enterpriseQueryWrapper = new QueryWrapper<>();
        enterpriseQueryWrapper.eq("enterprise_openid",openid);
        List<TSysEnterprise> enterpriseList = tSysEnterpriseMapper.selectList(enterpriseQueryWrapper);
        if(enterpriseList != null && enterpriseList.size() > 0){
            return enterpriseList.get(0).getEnterpriseScreen();
        }
        return null;
    }
}
