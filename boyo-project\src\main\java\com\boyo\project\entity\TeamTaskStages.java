package com.boyo.project.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务列表表(TeamTaskStages)实体类
 *
 * <AUTHOR>
 * @since 2022-02-10 11:03:15
 */
@Data
@TableName(value = "team_task_stages")
public class TeamTaskStages implements Serializable {
    private static final long serialVersionUID = 880498916608854241L;

    @TableId
    private Integer id;

    /**
    * 类型名称
    */
    @TableField(value="name")
    private String name;
    /**
    * 项目id
    */
    @TableField(value="project_code")
    private String projectCode;
    /**
    * 排序
    */
    @TableField(value="sort")
    private Integer sort;
    /**
    * 备注
    */
    @TableField(value="description")
    private String description;
    /**
    * 创建时间
    */
    @TableField(value="create_time")
    private String createTime;
    /**
    * 编号
    */
    @TableField(value="code")
    private String code;
    /**
    * 删除标记
    */
    @TableField(value="deleted")
    private Integer deleted;

    @TableField(exist = false)
    private boolean showTaskCard = false;

    @TableField(exist = false)
    private List<TeamTask> taskList;

}
