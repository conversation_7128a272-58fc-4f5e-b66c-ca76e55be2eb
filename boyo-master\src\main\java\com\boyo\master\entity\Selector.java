package com.boyo.master.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * @TableName bu_enterprise_data_asset_selector
 */
@TableName(value ="bu_enterprise_data_asset_selector")
@Data
public class Selector implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 企业id，因企业与用户是一对一的关系，所以此处是企业用户id
     */
    @TableField(value = "enterprise_id")
    private Long enterpriseId;

    /**
     * 数据资产类型
     */
    @TableField(value = "data_asset_type")
    private String dataAssetType;

    /**
     * 选择器类型
     */
    @TableField(value = "type")
    private String type;

    /**
     *
     */
    @TableField(value = "`key`")
    private String key;

    /**
     *
     */
    @TableField(value = "`value`")
    private String value;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
