package com.boyo.master.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 工厂模型-车间
 * 表名 t_model_workshop
 *
 * <AUTHOR>
 */
@ApiModel("工厂模型-车间")
@Data
@TableName("t_model_workshop")
public class ModelWorkshop extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "workshop_openid")
    private String workshopOpenid;
    /**
     * 所属工厂
     */
    @ApiModelProperty("所属工厂")
    @TableField(value = "workshop_factory")
    private String workshopFactory;
    /**
     * 车间名称
     */
    @ApiModelProperty("车间名称")
    @TableField(value = "workshop_name")
    private String workshopName;
    /**
     * 车间简称
     */
    @ApiModelProperty("车间简称")
    @TableField(value = "workshop_abbreviation")
    private String workshopAbbreviation;
    /**
     * 车间编码
     */
    @ApiModelProperty("车间编码")
    @TableField(value = "workshop_code")
    private String workshopCode;
    /**
     * 状态 1启用 0停用
     */
    @ApiModelProperty("状态 1启用 0停用")
    @TableField(value = "workshop_status")
    private String workshopStatus;
    /**
     * 车间logo
     */
    @ApiModelProperty("车间logo")
    @TableField(value = "workshop_img")
    private String workshopImg;
    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    @TableField(value = "workshop_contacts")
    private String workshopContacts;
    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    @TableField(value = "workshop_phone")
    private String workshopPhone;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "created_at")
    private Date createdAt;
    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    @TableField(value = "created_user")
    private String createdUser;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(value = "updated_at")
    private Date updatedAt;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "updated_user")
    private String updatedUser;

    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remark;

    /**
     * 所属工厂名称
     */
    @TableField(exist = false)
    private String factoryName;

    /**
     * 下属产线列表
     */
    @TableField(exist = false)
    private List<ModelLine> children;
    @TableField(exist = false)
    private String openid;
    @TableField(exist = false)
    private String name;
}
