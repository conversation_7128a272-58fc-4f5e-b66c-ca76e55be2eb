package com.boyo.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.iot.domain.IotEquipment;

import java.util.List;

/**
 * 物联网设备管理Service接口
 *
 * <AUTHOR>
 */
public interface IIotEquipmentService extends IService<IotEquipment> {
    /**
     * 根据条件查询查询物联网设备管理列表
     *
     * @param iotEquipment 物联网设备管理
     * @return 物联网设备管理集合
     */
    List<IotEquipment> selectIotEquipmentList(IotEquipment iotEquipment);

    List<IotEquipment> selectIotEquipmentListOnly(IotEquipment iotEquipment);

    /**
     * 获取物联网设备详情数据
     *
     * @param id 设备id
     * @return
     */
    IotEquipment getEquipmentDetail(Integer id);

    IotEquipment getEquipmentByCode(String code);

}
