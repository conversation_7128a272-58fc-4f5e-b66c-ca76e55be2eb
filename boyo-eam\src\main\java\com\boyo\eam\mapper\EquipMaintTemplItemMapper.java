package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipMaintTemplItem;

import java.util.List;

/**
 * 设备-维保模板-维保项目(EquipMaintTemplItem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:31
 */
public interface EquipMaintTemplItemMapper extends BaseMapper<EquipMaintTemplItem>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipMaintTemplItem 实例对象
     * @return 对象列表
     */
    List<EquipMaintTemplItem> selectEquipMaintTemplItemList(EquipMaintTemplItem equipMaintTemplItem);


}

