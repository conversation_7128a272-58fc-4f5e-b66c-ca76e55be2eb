package com.boyo.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.entity.Iofault;

import java.util.List;

/**
 * IoT故障清单(Iofault)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-07 15:16:28
 */
@Tenant
public interface IofaultMapper extends BaseMapper<Iofault> {

    /**
     * 通过实体作为筛选条件查询
     *
     * @param iofault 实例对象
     * @return 对象列表
     */
    List<Iofault> selectIofaultList(Iofault iofault);


}

