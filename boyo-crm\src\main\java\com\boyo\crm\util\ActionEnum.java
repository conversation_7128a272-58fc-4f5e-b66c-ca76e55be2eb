package com.boyo.crm.util;

import lombok.Getter;

@Getter
public enum ActionEnum {
    VISIT("VISIT","客户回访"),
    BUSINESS("BUSINESS","商机"),
    CLUE("CLUE","线索"),
    CONTACTS("CONTACTS","联系人"),
    CONTRACT("CONTRACT","合同"),
    CUSTOMER("CUSTOMER","客户"),
    PRODUCT("PRODUCT","产品"),
    RECEIVABLES("RECEIVABLES","回款"),
    INVOICE("INVOICE","发票");

    private String type;
    private String name;
    ActionEnum(String type, String name){
        this.type = type;
        this.name=name;
    }
}
