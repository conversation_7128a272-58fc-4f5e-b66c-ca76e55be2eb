package com.boyo.project.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.project.entity.TeamProjecmember;
import com.boyo.project.service.ITeamProjecmemberService;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 项目-成员表(TeamProjecmember)表控制层
 *
 * <AUTHOR>
 * @since 2022-02-10 16:43:19
 */
@Api("项目-成员表")
@RestController
@RequestMapping("/project/teamProjecmember")
@AllArgsConstructor
public class TeamProjecmemberController extends BaseController{
    /**
     * 服务对象
     */
    private final ITeamProjecmemberService teamProjecmemberService;

    private final IEnterpriseUserService enterpriseUserService;


    /**
     * 查询项目-成员表列表
     *
     */
    @ApiOperation("查询项目-成员表列表")
    @GetMapping("/list")
    public TableDataInfo list(TeamProjecmember teamProjecmember) {
        startPage();
        List<TeamProjecmember> list = teamProjecmemberService.selectTeamProjecmemberList(teamProjecmember);
        List<String> codes = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            codes.add(list.get(i).getMemberCode());
        }
        codes = codes.stream().distinct().collect(Collectors.toList());
        if(codes.size() > 0){
            QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("user_openid",codes);
            List<EnterpriseUser> userList = enterpriseUserService.list(queryWrapper);
            for (int i = 0; i < list.size(); i++) {
                for (int j = 0; j < userList.size(); j++) {
                    if(list.get(i).getMemberCode().equals(userList.get(j).getUserOpenid())){
                        list.get(i).setMemberName(userList.get(j).getUserFullName());
                        continue;
                    }
                }
            }
        }
        return getDataTable(list);
    }

    /**
     * 获取项目-成员表详情
     */
    @ApiOperation("获取项目-成员表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(teamProjecmemberService.getById(id));
    }

    /**
     * 新增项目-成员表
     */
    @ApiOperation("新增项目-成员表")
    @PostMapping
    public AjaxResult add(@RequestBody TeamProjecmember teamProjecmember) {
        return toBooleanAjax(teamProjecmemberService.save(teamProjecmember));
    }

    /**
     * 修改项目-成员表
     */
    @ApiOperation("修改项目-成员表")
    @PutMapping
    public AjaxResult edit(@RequestBody TeamProjecmember teamProjecmember) {
        return toBooleanAjax(teamProjecmemberService.updateById(teamProjecmember));
    }

    /**
     * 删除项目-成员表
     */
    @ApiOperation("删除项目-成员表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(teamProjecmemberService.removeByIds(Arrays.asList(ids)));
    }

}
