package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 点检-项目(EquipInspectionTemplItem)实体类
 *
 * <AUTHOR>
 * @since 2021-11-29 10:21:50
 */
@Data
@TableName(value = "equip_inspection_templ_item")
public class EquipInspectionTemplItem implements Serializable {
    private static final long serialVersionUID = -26599214645078531L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 关联equip_inspection_templ_openid表的openid
    */
    @TableField(value="equip_inspection_templ_openid")
    private String equipInspectionTemplOpenid;
    /**
    * 项目
    */
    @TableField(value="item")
    private String item;
    /**
    * 检验基准
    */
    @TableField(value="basis")
    private String basis;
    /**
    * 检查方法
    */
    @TableField(value="method")
    private String method;
    /**
    * 处理办法
    */
    @TableField(value="way")
    private String way;
    /**
    * 时长
    */
    @TableField(value="use_time")
    private String useTime = "0";

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

    /** 额外字段 */
    @TableField(exist = false)
    private Integer cycle; //周期
    @TableField(exist = false)
    private String cycleUnit; //周期单位
    @TableField(exist = false)
    private String spotCode;//点检任务单号
    @TableField(exist = false)
    private String type;//检验类型（0点检/1巡检）
    @TableField(exist = false)
    private String equipCode;//设备编号
    @TableField(exist = false)
    private String equipName;//设备名称
    @TableField(exist = false)
    private String deptName;//部门名称
    @TableField(exist = false)
    private String staffName;//点巡检人员
    @TableField(exist = false)
    private String lineOpenid;//产线openid
    @TableField(exist = false)
    private String lineName;//产线名
    @TableField(exist = false)
    private Integer spotId;//点巡检主表id
}
