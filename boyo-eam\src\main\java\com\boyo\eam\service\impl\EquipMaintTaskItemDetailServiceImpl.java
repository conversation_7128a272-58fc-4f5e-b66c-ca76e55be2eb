package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipMaintTaskItemDetail;
import com.boyo.eam.mapper.EquipMaintTaskItemDetailMapper;
import com.boyo.eam.service.IEquipMaintTaskItemDetailService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 维保任务管理-维保项目-明细(EquipMaintTaskItemDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:33
 */
@Service("EquipMaintTaskItemDetailService")
@AllArgsConstructor
@Tenant
public class EquipMaintTaskItemDetailServiceImpl extends ServiceImpl<EquipMaintTaskItemDetailMapper, EquipMaintTaskItemDetail> implements IEquipMaintTaskItemDetailService {
    private final EquipMaintTaskItemDetailMapper equipMaintTaskItemDetailMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipMaintTaskItemDetail> selectEquipMaintTaskItemDetailList(EquipMaintTaskItemDetail equipMaintTaskItemDetail) {
        return equipMaintTaskItemDetailMapper.selectEquipMaintTaskItemDetailList(equipMaintTaskItemDetail);
    }

    @Override
    public EquipMaintTaskItemDetail getDetailAndRecord(Integer id) {
        return equipMaintTaskItemDetailMapper.getDetailAndRecord(id);
    }

}
