<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.wms.mapper.WmsMaterielMapper">

    <resultMap type="com.boyo.wms.vo.WmsMaterielVO" id="WmsMaterielResult">
        <result property="id" column="id"/>
        <result property="detailOpenid" column="detail_openid"/>
        <result property="detailInoutOpenid" column="detail_inout_openid"/>
        <result property="detailWarehouseOpenid" column="detail_warehouse_openid"/>
        <result property="detailAreaOpenid" column="detail_area_openid"/>
        <result property="detailMaterielOpenid" column="detail_materiel_openid"/>
        <result property="detailQuantity" column="detail_quantity"/>
        <result property="completeQuantity" column="complete_quantity"/>
        <result property="surplusQuantity" column="surplus_quantity"/>
        <result property="detailMaterielOrder" column="detail_materiel_order"/>
        <result property="detailSupplierOpenid" column="detail_supplier_openid"/>
        <result property="detailAllocationOpenid" column="detail_allocation_openid"/>
        <result property="detailState" column="detail_state"/>
        <result property="createdAt" column="created_at"/>
        <result property="createdUser" column="created_user"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="updatedUser" column="updated_user"/>
        <result property="qcTemplate" column="qc_template"/>

        <result property="warehouseName" column="warehouse_name"></result>
        <result property="areaName" column="area_name"></result>
        <result property="materielName" column="materiel_name"></result>
        <result property="materielCode" column="materiel_code"></result>
        <result property="supplierName" column="supplier_name"></result>
        <result property="unitName" column="unit_name"></result>
    </resultMap>



    <select id="selectWmsMaterielList" parameterType="com.boyo.wms.entity.WmsMateriel" resultMap="WmsMaterielResult">
        select t1.*,
        t2.warehouse_name as warehouse_name ,
        t3.area_name as area_name ,
        t4.materiel_name as materiel_name ,
        t4.materiel_code as materiel_code,
        t5.supplier_name as supplier_name,
        t6.base_desc as unit_name
        from t_wms_materiel t1
        left join t_model_warehouse t2 on t1.detail_warehouse_openid = t2.warehouse_openid
        left join t_model_area t3 on t1.detail_area_openid = t3.area_warehouse_id
        left join t_material t4 on t1.detail_materiel_openid = t4.materiel_openid
        left join t_supplier t5 on t1.detail_supplier_openid = t5.supplier_openid
        left join t_base_dict t6 on t6.openid = t4.materiel_unit
        <where>
            detail_state &lt;&gt; '99'
            <if test="detailOpenid != null  and detailOpenid != ''">
                and t1.detail_openid = #{detailOpenid}
            </if>
            <if test="detailInoutOpenid != null  and detailInoutOpenid != ''">
                and t1.detail_inout_openid = #{detailInoutOpenid}
            </if>
            <if test="detailWarehouseOpenid != null  and detailWarehouseOpenid != ''">
                and t1.detail_warehouse_openid = #{detailWarehouseOpenid}
            </if>
            <if test="detailAreaOpenid != null  and detailAreaOpenid != ''">
                and t1.detail_area_openid = #{detailAreaOpenid}
            </if>
            <if test="detailMaterielOpenid != null  and detailMaterielOpenid != ''">
                and t1.detail_materiel_openid = #{detailMaterielOpenid}
            </if>
            <if test="detailMaterielOrder != null  and detailMaterielOrder != ''">
                and t1.detail_materiel_order = #{detailMaterielOrder}
            </if>
            <if test="detailSupplierOpenid != null  and detailSupplierOpenid != ''">
                and t1.detail_supplier_openid = #{detailSupplierOpenid}
            </if>
            <if test="detailAllocationOpenid != null  and detailAllocationOpenid != ''">
                and t1.detail_allocation_openid = #{detailAllocationOpenid}
            </if>
            <if test="detailState != null  and detailState != ''">
                and t1.detail_state = #{detailState}
            </if>
            <if test="createdAt != null ">
                and t1.created_at = #{createdAt}
            </if>
            <if test="createdUser != null  and createdUser != ''">
                and t1.created_user = #{createdUser}
            </if>
            <if test="updatedAt != null ">
                and t1.updated_at = #{updatedAt}
            </if>
            <if test="updatedUser != null  and updatedUser != ''">
                and t1.updated_user = #{updatedUser}
            </if>
        </where>
    </select>
</mapper>
