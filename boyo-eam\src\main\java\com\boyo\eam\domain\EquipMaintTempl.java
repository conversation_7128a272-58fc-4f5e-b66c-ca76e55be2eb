package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 设备-维保模板(EquipMaintTempl)实体类
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:29
 */
@Data
@TableName(value = "equip_maint_templ")
public class EquipMaintTempl implements Serializable {
    private static final long serialVersionUID = 392937354220102655L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 模板名称
    */
    @TableField(value="name")
    private String name;
    /**
    * 模板描述
    */
    @TableField(value="remark")
    private String remark;
    /**
    * 模板类型
    */
    @TableField(value="type")
    private String type;
    /**
    * 关联equip_ledger中的openid（用逗号隔开）
    */
    @TableField(value="equip_openid")
    private String equipOpenid;
    /**
    * 状态：0停用，1启用
    */
    @TableField(value="state")
    private String state;

    /**
     * 产线openid：关联t_model_line表的openid
     */
    @TableField(value="line_openid")
    private String lineOpenid;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

    /** 额外字段 */
    @TableField(exist = false)
    private List<EquipMaintTemplItem> equipMaintTemplItems;
    @TableField(exist = false)
    private String equipNames;
    @TableField(exist = false)
    private String lineName;
    /** 额外字段 */
}
