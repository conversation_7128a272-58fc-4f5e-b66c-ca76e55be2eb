package com.boyo.system.domain;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 企业部门管理
 * 表名 t_enterprise_department
 *
 * <AUTHOR>
 */
@ApiModel("部门架构表（企业部门关联表）")
@Data
@TableName("t_enterprise_department")
public class EnterpriseDepartment extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    @TableField(value = "enterprise_openid")
    private String enterpriseOpenid;
    /**
     * 部门id
     */
    @ApiModelProperty("部门id")
    @TableField(value = "department_openid")
    private String departmentOpenid;
    /**
     * 上级部门
     */
    @ApiModelProperty("上级部门")
    @TableField(value = "parent_openid")
    private String parentOpenid;
    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    @TableField(value = "department_name")
    private String departmentName;
    /**
     * 部门编码
     */
    @ApiModelProperty("部门编码")
    @TableField(value = "department_code")
    private String departmentCode;
    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    @TableField(value = "department_contacts")
    private String departmentContacts;
    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    @TableField(value = "department_phone")
    private String departmentPhone;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private List<EnterpriseDepartment> children;
}
