package com.boyo.iot.service.impl;

import com.boyo.common.annotation.DataScope;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.iot.entity.Iofault;
import com.boyo.iot.mapper.IofaultMapper;
import com.boyo.iot.service.IIofaultService;
import java.util.List;

/**
 * IoT故障清单(Iofault)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-07 15:16:28
 */
@Service("iofaultService")
@AllArgsConstructor
public class IofaultServiceImpl extends ServiceImpl<IofaultMapper, Iofault> implements IIofaultService {
    private final IofaultMapper iofaultMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    @DataScope(userAlias = "l1")
    public List<Iofault> selectIofaultList(Iofault iofault) {
        return iofaultMapper.selectIofaultList(iofault);
    }

}
