package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.SparePart;
import com.boyo.eam.mapper.SparePartMapper;
import com.boyo.eam.service.ISparePartService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 备件总账表(SparePart)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-04 11:12:31
 */
@Service("sparePartService")
@AllArgsConstructor
@Tenant
public class SparePartServiceImpl extends ServiceImpl<SparePartMapper, SparePart> implements ISparePartService {
    private final SparePartMapper sparePartMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<SparePart> selectSparePartList(SparePart sparePart) {
        return sparePartMapper.selectSparePartList(sparePart);
    }

}
