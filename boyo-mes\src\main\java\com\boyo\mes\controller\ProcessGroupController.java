package com.boyo.mes.controller;

import com.boyo.mes.entity.ProcessGroup;
import com.boyo.mes.service.IProcessGroupService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 工序组(ProcessGroup)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Api("工序组")
@RestController
@RequestMapping("/mes/processGroup")
@AllArgsConstructor
public class ProcessGroupController extends BaseController{
    /**
     * 服务对象
     */
    private final IProcessGroupService processGroupService;

    /**
     * 查询工序组列表
     *
     */
    @ApiOperation("查询工序组列表")
    @GetMapping("/list")
    public TableDataInfo list(ProcessGroup processGroup) {
        startPage();
        List<ProcessGroup> list = processGroupService.selectProcessGroupList(processGroup);
        return getDataTable(list);
    }
    
    /**
     * 获取工序组详情
     */
    @ApiOperation("获取工序组详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(processGroupService.getById(id));
    }

    /**
     * 新增工序组
     */
    @ApiOperation("新增工序组")
    @PostMapping
    public AjaxResult add(@RequestBody ProcessGroup processGroup) {
        return toBooleanAjax(processGroupService.save(processGroup));
    }

    /**
     * 修改工序组
     */
    @ApiOperation("修改工序组")
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessGroup processGroup) {
        return toBooleanAjax(processGroupService.updateById(processGroup));
    }

    /**
     * 删除工序组
     */
    @ApiOperation("删除工序组")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(processGroupService.removeByIds(Arrays.asList(ids)));
    }

}
