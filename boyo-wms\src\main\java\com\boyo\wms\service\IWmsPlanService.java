package com.boyo.wms.service;

import java.util.List;

import com.boyo.wms.entity.WmsPlan;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.wms.vo.WmsPlanVO;

/**
 * 出入库计划管理Service接口
 *
 * <AUTHOR>
 */
public interface IWmsPlanService extends IService<WmsPlan> {
    /**
     * 根据条件查询查询出入库计划管理列表
     *
     * @param wmsPlan 出入库计划管理
     * @return 出入库计划管理集合
     */
    List<WmsPlanVO> selectWmsPlanList(WmsPlan wmsPlan);

    /**
     * 出入库计划下发
     */
    boolean issuePlan(String id);

    /**
     * 出入库计划撤回
     */
    boolean withdrawPlan(String id);

    /**
     * 结束出入库计划
     * @param id
     * @return
     */
    boolean endPlan(String id);
}
