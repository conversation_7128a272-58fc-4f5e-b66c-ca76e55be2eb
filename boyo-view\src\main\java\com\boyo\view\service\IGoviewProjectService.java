package com.boyo.view.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.view.entity.GoviewProject;
import java.util.List;

/**
 * 项目表(GoviewProject)表服务接口
 *
 * <AUTHOR>
 * @since 2022-12-13 15:15:05
 */
public interface IGoviewProjectService extends IService<GoviewProject> {

    /**
     * 查询多条数据
     *
     * @param goviewProject 对象信息
     * @return 对象列表
     */
    List<GoviewProject> selectGoviewProjectList(GoviewProject goviewProject);


}
