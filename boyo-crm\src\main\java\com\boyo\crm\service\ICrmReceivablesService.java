package com.boyo.crm.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.crm.entity.CrmReceivables;
import java.util.List;

/**
 * 回款表(CrmReceivables)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-28 10:22:52
 */
public interface ICrmReceivablesService extends IService<CrmReceivables> {

    /**
     * 查询多条数据
     *
     * @param crmReceivables 对象信息
     * @return 对象列表
     */
    List<CrmReceivables> selectCrmReceivablesList(CrmReceivables crmReceivables);


}
