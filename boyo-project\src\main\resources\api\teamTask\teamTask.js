import request from '@/utils/request'

const prefix = ''

// 查询任务表(TeamTask)列表
export function listTeamTask(query) {
  return request({
    url: prefix + '/teamTask/list',
    method: 'get',
    params: query,
  })
}

// 查询任务表(TeamTask)详细
export function getTeamTask(id) {
  return request({
    url: prefix + '/teamTask/' + id,
    method: 'get',
  })
}

// 新增任务表(TeamTask)
export function addTeamTask(data) {
  return request({
    url: prefix + '/teamTask',
    method: 'post',
    data: data,
  })
}

// 修改任务表(TeamTask)
export function updateTeamTask(data) {
  return request({
    url: prefix + '/teamTask',
    method: 'put',
    data: data,
  })
}

// 删除任务表(TeamTask)
export function delTeamTask(id) {
  return request({
    url: prefix + '/teamTask/' + id,
    method: 'delete',
  })
}
