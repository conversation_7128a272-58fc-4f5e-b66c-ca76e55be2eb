package com.boyo.mes.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.ProcessGroup;
import com.boyo.mes.mapper.ProcessGroupMapper;
import com.boyo.mes.service.IProcessGroupService;
import java.util.List;

/**
 * 工序组(ProcessGroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Service("processGroupService")
@AllArgsConstructor
@Tenant
public class ProcessGroupServiceImpl extends ServiceImpl<ProcessGroupMapper, ProcessGroup> implements IProcessGroupService {
    private final ProcessGroupMapper processGroupMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<ProcessGroup> selectProcessGroupList(ProcessGroup processGroup) {
        return processGroupMapper.selectProcessGroupList(processGroup);
    }

}
