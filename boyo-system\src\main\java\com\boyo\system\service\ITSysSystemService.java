package com.boyo.system.service;

import java.util.List;

import com.boyo.system.domain.TSysSystem;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 系统管理Service接口
 *
 * <AUTHOR>
 */
public interface ITSysSystemService extends IService<TSysSystem> {
    /**
     * 根据条件查询查询系统管理列表
     *
     * @param tSysSystem 系统管理
     * @return 系统管理集合
     */
    List<TSysSystem> selectTSysSystemList(TSysSystem tSysSystem);

    List<TSysSystem> findEnterpriseSystem(String enterpriseOpenid);

    List<TSysSystem> findUserSystem(String enterpriseOpenid,String userOpenid);

}
