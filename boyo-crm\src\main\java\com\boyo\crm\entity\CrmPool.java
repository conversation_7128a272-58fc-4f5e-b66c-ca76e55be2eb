package com.boyo.crm.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 公海表(CrmPool)实体类
 *
 * <AUTHOR>
 * @since 2022-03-30 14:33:58
 */
@Data
@TableName(value = "t_crm_pool")
public class CrmPool extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = -41193310955792481L;
        /**
    * 公海id
    */
    @TableId
    private Integer id;

    /**
    * 公海名称
    */
    @TableField(value="pool_name")
    private String poolName;
    /**
    * 管理员 “,”分割
    */
    @TableField(value="admin_user_id")
    private String adminUserId;
    /**
    * 公海规则员工成员 “,”分割
    */
    @TableField(value="member_user_id")
    private String memberUserId;
    /**
    * 公海规则部门成员 “,”分割
    */
    @TableField(value="member_dept_id")
    private String memberDeptId;
    /**
    * 状态 0 停用 1启用
    */
    @TableField(value="status")
    private Integer status;
    /**
    * 前负责人领取规则 0不限制 1限制
    */
    @TableField(value="pre_owner_setting")
    private Integer preOwnerSetting;
    /**
    * 前负责人领取规则限制天数
    */
    @TableField(value="pre_owner_setting_day")
    private Integer preOwnerSettingDay;
    /**
    * 是否限制领取频率 0不限制 1限制
    */
    @TableField(value="receive_setting")
    private Integer receiveSetting;
    /**
    * 领取频率规则
    */
    @TableField(value="receive_num")
    private Integer receiveNum;
    /**
    * 收回规则 0不自动收回 1自动收回
    */
    @TableField(value="put_in_rule")
    private Integer putInRule;
    /**
    * 无跟进天数
    */
    @TableField(value="put_in_task")
    private Integer putInTask;
    /**
    * 无商机天数
    */
    @TableField(value="put_in_business")
    private Integer putInBusiness;
    /**
    * 未成交天数
    */
    @TableField(value="put_in_deal")
    private Integer putInDeal;

    @TableField(value="create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;

    @TableField(value="create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
