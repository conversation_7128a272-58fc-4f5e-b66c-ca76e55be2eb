package com.boyo.crm.controller;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.crm.entity.CrmProduct;
import com.boyo.crm.service.ICrmProductService;
import com.boyo.system.service.IEnterpriseUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * CRM产品表(CrmProduct)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-24 15:52:39
 */
@Api("CRM产品表")
@RestController
@RequestMapping("/crm/crmProduct")
@AllArgsConstructor
public class CrmProductController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmProductService crmProductService;

    private final IEnterpriseUserService enterpriseUserService;

    /**
     * 查询CRM产品表列表
     *
     */
    @ApiOperation("查询CRM产品表列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmProduct crmProduct) {
        startPage();
        List<CrmProduct> list = crmProductService.selectCrmProductList(crmProduct);
        if(list != null && list.size() > 0){
            List<Long> ids = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                ids.add(list.get(i).getOwnerUserId());
            }
            List<EnterpriseUser> userList = enterpriseUserService.selectByIds(ids);
            if(userList != null && userList.size() > 0){
                for (int i = 0; i < list.size(); i++) {
                    for (int j = 0; j < userList.size(); j++) {
                        if(list.get(i).getOwnerUserId().equals(userList.get(j).getId())){
                            list.get(i).setOwnerUserName(userList.get(j).getUserFullName());
                            break;
                        }
                    }
                }
            }
        }

        return getDataTable(list);
    }

    /**
     * 获取CRM产品表详情
     */
    @ApiOperation("获取CRM产品表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(crmProductService.getById(id));
    }

    /**
     * 新增CRM产品表
     */
    @ApiOperation("新增CRM产品表")
    @PostMapping
    public AjaxResult add(@RequestBody CrmProduct crmProduct) {
        return toBooleanAjax(crmProductService.save(crmProduct));
    }

    /**
     * 修改CRM产品表
     */
    @ApiOperation("修改CRM产品表")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmProduct crmProduct) {
        return toBooleanAjax(crmProductService.updateById(crmProduct));
    }

    /**
     * 删除CRM产品表
     */
    @ApiOperation("删除CRM产品表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmProductService.removeByIds(Arrays.asList(ids)));
    }

}
