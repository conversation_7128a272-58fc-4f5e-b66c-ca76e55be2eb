<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>boyo</artifactId>
        <groupId>com.boyo</groupId>
        <version>3.3.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>boyo-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>

        <!-- spring-boot-devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 表示依赖不会传递 -->
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- swagger2-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>

        <!--防止进入swagger页面报类型转换错误，排除2.9.2中的引用，手动增加1.5.21版本-->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.21</version>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.5.21</version>
        </dependency>

        <!-- swagger2-UI-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>

         <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- 公共组件 -->
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-common</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-framework</artifactId>
        </dependency>

        <!-- 定时任务-->
<!--        <dependency>-->
<!--            <groupId>com.boyo</groupId>-->
<!--            <artifactId>boyo-quartz</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-wms</artifactId>
        </dependency>
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-mes</artifactId>
        </dependency>
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-master</artifactId>
        </dependency>
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-iot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-crm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-eam</artifactId>
        </dependency>
        <!--管理-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.boyo</groupId>
            <artifactId>boyo-project</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <!--网关加解密依赖    开始    -->
        <dependency>
            <groupId>com.dhcc.openapi</groupId>
            <artifactId>openapi-gateway-client</artifactId>
            <systemPath>${pom.basedir}/lib/openapi-gateway-client-2.1.0.jar</systemPath>
            <version>2.1.0</version>
            <scope>system</scope>
        </dependency>

        <dependency>
            <groupId>com.lingyang</groupId>
            <artifactId>sdk</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.1.RELEASE</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
           </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>

</project>
