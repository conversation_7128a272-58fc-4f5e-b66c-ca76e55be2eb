package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipMaintTempl;

import java.util.List;

/**
 * 设备-维保模板(EquipMaintTempl)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:30
 */
public interface IEquipMaintTemplService extends IService<EquipMaintTempl> {

    /**
     * 查询多条数据
     *
     * @param equipMaintTempl 对象信息
     * @return 对象列表
     */
    List<EquipMaintTempl> selectEquipMaintTemplList(EquipMaintTempl equipMaintTempl);


}
