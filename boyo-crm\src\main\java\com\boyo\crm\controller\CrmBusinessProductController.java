package com.boyo.crm.controller;

import com.boyo.crm.entity.CrmBusinessProduct;
import com.boyo.crm.service.ICrmBusinessProductService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 商机产品关系表(CrmBusinessProduct)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-26 09:56:24
 */
@Api("商机产品关系表")
@RestController
@RequestMapping("/crm/crmBusinessProduct")
@AllArgsConstructor
public class CrmBusinessProductController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmBusinessProductService crmBusinessProductService;

    /**
     * 查询商机产品关系表列表
     *
     */
    @ApiOperation("查询商机产品关系表列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusinessProduct crmBusinessProduct) {
        startPage();
        List<CrmBusinessProduct> list = crmBusinessProductService.selectCrmBusinessProductList(crmBusinessProduct);
        return getDataTable(list);
    }
    
    /**
     * 获取商机产品关系表详情
     */
    @ApiOperation("获取商机产品关系表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(crmBusinessProductService.getById(id));
    }

    /**
     * 新增商机产品关系表
     */
    @ApiOperation("新增商机产品关系表")
    @PostMapping
    public AjaxResult add(@RequestBody CrmBusinessProduct crmBusinessProduct) {
        return toBooleanAjax(crmBusinessProductService.save(crmBusinessProduct));
    }

    /**
     * 修改商机产品关系表
     */
    @ApiOperation("修改商机产品关系表")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmBusinessProduct crmBusinessProduct) {
        return toBooleanAjax(crmBusinessProductService.updateById(crmBusinessProduct));
    }

    /**
     * 删除商机产品关系表
     */
    @ApiOperation("删除商机产品关系表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmBusinessProductService.removeByIds(Arrays.asList(ids)));
    }

}
