<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.wms.mapper.WmsQcItemMapper">

    <resultMap type="com.boyo.wms.entity.WmsQcItem" id="WmsQcItemResult">
        <result property="id" column="id"/>
        <result property="templateId" column="template_id"/>
        <result property="itemName" column="item_name"/>
        <result property="itemCode" column="item_code"/>
        <result property="itemType" column="item_type"/>
        <result property="itemResult" column="item_result"/>
        <result property="itemStr" column="item_str"/>
        <result property="itemOrder" column="item_order"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectWmsQcItemList" parameterType="com.boyo.wms.entity.WmsQcItem" resultMap="WmsQcItemResult">
        select
        id, template_id, item_name, item_code, item_type, item_result, item_str,item_order
        from t_wms_qc_item
        <where>
            <if test="templateId != null">
                and template_id = #{templateId}
            </if>
            <if test="itemName != null and itemName != ''">
                and item_name = #{itemName}
            </if>
            <if test="itemCode != null and itemCode != ''">
                and item_code = #{itemCode}
            </if>
            <if test="itemType != null and itemType != ''">
                and item_type = #{itemType}
            </if>
            <if test="itemResult != null and itemResult != ''">
                and item_result = #{itemResult}
            </if>
            <if test="itemStr != null and itemStr != ''">
                and item_str = #{itemStr}
            </if>
        </where>
        order by item_order asc
    </select>
</mapper>

