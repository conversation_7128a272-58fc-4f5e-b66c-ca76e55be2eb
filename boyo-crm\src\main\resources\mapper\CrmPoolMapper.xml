<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmPoolMapper">

    <resultMap type="com.boyo.crm.entity.CrmPool" id="CrmPoolResult">
        <result property="id" column="id" />
        <result property="poolName" column="pool_name" />
        <result property="adminUserId" column="admin_user_id" />
        <result property="memberUserId" column="member_user_id" />
        <result property="memberDeptId" column="member_dept_id" />
        <result property="status" column="status" />
        <result property="preOwnerSetting" column="pre_owner_setting" />
        <result property="preOwnerSettingDay" column="pre_owner_setting_day" />
        <result property="receiveSetting" column="receive_setting" />
        <result property="receiveNum" column="receive_num" />
        <result property="putInRule" column="put_in_rule" />
        <result property="putInTask" column="put_in_task" />
        <result property="putInBusiness" column="put_in_business" />
        <result property="putInDeal" column="put_in_deal" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmPoolList" parameterType="com.boyo.crm.entity.CrmPool" resultMap="CrmPoolResult">
        select
          id, pool_name, admin_user_id, member_user_id, member_dept_id, status, pre_owner_setting, pre_owner_setting_day, receive_setting, receive_num, put_in_rule, put_in_task, put_in_business, put_in_deal, create_user_id, create_time
        from t_crm_pool
        <where>
            <if test="poolName != null and poolName != ''">
                and pool_name = #{poolName}
            </if>
            <if test="adminUserId != null and adminUserId != ''">
                and admin_user_id = #{adminUserId}
            </if>
            <if test="memberUserId != null and memberUserId != ''">
                and member_user_id = #{memberUserId}
            </if>
            <if test="memberDeptId != null and memberDeptId != ''">
                and member_dept_id = #{memberDeptId}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="preOwnerSetting != null">
                and pre_owner_setting = #{preOwnerSetting}
            </if>
            <if test="preOwnerSettingDay != null">
                and pre_owner_setting_day = #{preOwnerSettingDay}
            </if>
            <if test="receiveSetting != null">
                and receive_setting = #{receiveSetting}
            </if>
            <if test="receiveNum != null">
                and receive_num = #{receiveNum}
            </if>
            <if test="putInRule != null">
                and put_in_rule = #{putInRule}
            </if>
            <if test="putInTask != null">
                and put_in_task = #{putInTask}
            </if>
            <if test="putInBusiness != null">
                and put_in_business = #{putInBusiness}
            </if>
            <if test="putInDeal != null">
                and put_in_deal = #{putInDeal}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
    </select>
</mapper>

