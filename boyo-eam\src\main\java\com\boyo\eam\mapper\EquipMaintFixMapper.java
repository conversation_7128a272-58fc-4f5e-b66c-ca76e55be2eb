package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipMaintFix;

import java.util.List;

/**
 * 维修任务管理(EquipMaintFix)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-17 16:13:53
 */
public interface EquipMaintFixMapper extends BaseMapper<EquipMaintFix>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipMaintFix 实例对象
     * @return 对象列表
     */
    List<EquipMaintFix> selectEquipMaintFixList(EquipMaintFix equipMaintFix);


}

