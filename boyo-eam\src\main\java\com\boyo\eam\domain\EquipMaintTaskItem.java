package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 维保任务管理-维保项目(EquipMaintTaskItem)实体类
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:32
 */
@Data
@TableName(value = "equip_maint_task_item")
public class EquipMaintTaskItem implements Serializable {
    private static final long serialVersionUID = 433281405542626929L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 关联equip_maint_task表的主键
    */
    @TableField(value="equip_maint_task_openid")
    private String equipMaintTaskOpenid;
    /**
    * 项目
    */
    @TableField(value="item")
    private String item;
    /**
    * 标准工时
    */
    @TableField(value="hour")
    private Integer hour;
    /**
    * 工时单位（分钟minutes/小时hours）
    */
    @TableField(value="hour_unit")
    private String hourUnit;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

    /** 额外字段 */
    @TableField(exist = false)
    private List<EquipMaintTaskItemDetail> detailList;
    @TableField(exist = false)
    private Integer taskId;
    @TableField(exist = false)
    private String taskPlanCode;
    @JsonFormat( pattern = "yyyy-MM-dd")
    @TableField(exist = false)
    private Date taskDate;
    @TableField(exist = false)
    private String equipCode;
    @TableField(exist = false)
    private String equipName;
    @TableField(exist = false)
    private String taskCycle;//周期：0仅此一次，1每周，2每月，3每隔N天（若从维保任务管理中进行添加，则默认为0仅此一次）
    @TableField(exist = false)
    private Integer taskDay;
    @TableField(exist = false)
    private String taskState;
    @TableField(exist = false)
    private String taskCode;
    @TableField(exist = false)
    private String staffName;
    @TableField(exist = false)
    private String deptName;

    @TableField(exist = false)
    private Integer staffId;
    @TableField(exist = false)
    private String lineName;
}
