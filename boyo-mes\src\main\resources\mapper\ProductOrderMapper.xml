<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.ProductOrderMapper">

    <resultMap type="com.boyo.mes.entity.ProductOrder" id="ProductOrderResult">
        <result property="id" column="id"/>
        <result property="orderNum" column="order_num"/>
        <result property="productionId" column="production_id"/>
        <result property="productionNum" column="production_num"/>
        <result property="deliveryDate" column="delivery_date"/>
        <result property="customerId" column="customer_id"/>
        <result property="orderStatus" column="order_status"/>
        <result property="executeTime" column="execute_time"/>
        <result property="completeTime" column="complete_time"/>
        <result property="cancelTime" column="cancel_time"/>
        <result property="processGroupId" column="process_group_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>

        <result property="productionName" column="production_name"/>
        <result property="productionCode" column="production_code"/>
        <result property="productionNorms" column="production_norms"/>
        <result property="customerName" column="customer_name"/>

        <result property="equipmentId" column="equipment_id"/>
        <result property="processId" column="process_id"/>
        <result property="processName" column="process_name"/>
        <result property="reportNum" column="report_num"/>
        <result property="wasteNum" column="waste_num"/>
        <result property="processStatus" column="process_status"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectProductOrderList" parameterType="com.boyo.mes.entity.ProductOrder" resultMap="ProductOrderResult">
        select t1.*,t2.materiel_name as production_name,t2.materiel_code as production_code,t2.materiel_norms as production_norms,t3.supplier_name as customer_name from (select
        * from t_product_order
        <where>
            <if test="orderNum != null and orderNum != ''">
                and order_num like concat('%', #{orderNum}, '%')
            </if>
            <if test="productionId != null">
                and production_id = #{productionId}
            </if>
            <if test="productionNum != null">
                and production_num = #{productionNum}
            </if>
            <if test="deliveryDate != null">
                and delivery_date = #{deliveryDate}
            </if>
            <if test="customerId != null">
                and customer_id = #{customerId}
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                and order_status = #{orderStatus}
            </if>
            <if test="executeTime != null">
                and execute_time = #{executeTime}
            </if>
            <if test="completeTime != null">
                and complete_time = #{completeTime}
            </if>
            <if test="cancelTime != null">
                and cancel_time = #{cancelTime}
            </if>
            <if test="processGroupId != null">
                and process_group_id = #{processGroupId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
        </where>
        ) t1 left join t_material t2 on t1.production_Id = t2.id
        left join t_supplier t3 on t1.customer_Id = t3.id
    </select>

    <select id="listExecuteOrder" resultMap="ProductOrderResult">
        SELECT *
        from (SELECT l1.*,
                     l2.materiel_name         AS production_name,
                     l3.supplier_name         AS customer_name,
                     IFNULL(l4.c, 0)          AS process_status,
                     IFNULL(l5.report_num, 0) as report_num,
                     IFNULL(l5.waste_num, 0)  as waste_num
              FROM (SELECT t3.*,
                           t1.equipment_id,
                           t4.process_name,
                           t4.id AS process_id
                    FROM t_process_equipment t1,
                         t_process_group_detail t2,
                         t_product_order t3,
                         t_product_process t4
                    WHERE t1.process_id = t2.process_id
                      AND t2.group_id = t3.process_group_id
                      AND t1.process_id = t4.id
                      and t3.order_status = '1'
                      and t1.equipment_id = #{equipmentId}) l1
                       LEFT JOIN t_material l2 ON l1.production_Id = l2.id
                       LEFT JOIN t_supplier l3 ON l1.customer_Id = l3.id
                       LEFT JOIN (SELECT count(*) c, order_id, process_id, equipment_id
                                  FROM t_product_order_detail where end_time is null and user_id = #{userId}
                                  GROUP BY order_id, process_id, equipment_id) l4 ON l1.id = l4.order_id
                  AND l1.process_id = l4.process_id
                  AND l1.equipment_id = l4.equipment_id
                       left join (select sum(report_num) as report_num,
                                         sum(waste_num)  as waste_num,
                                         order_id,
                                         process_id,
                                         equipment_id
                                  from t_work_report
                                  group by order_id, process_id, equipment_id) l5 on l1.id = l5.order_id
                  AND l1.process_id = l5.process_id
                  AND l1.equipment_id = l5.equipment_id) al
        order by al.process_status desc, al.delivery_date asc
    </select>
</mapper>

