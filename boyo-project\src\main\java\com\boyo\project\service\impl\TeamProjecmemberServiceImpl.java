package com.boyo.project.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.project.entity.TeamProjecmember;
import com.boyo.project.mapper.TeamProjecmemberMapper;
import com.boyo.project.service.ITeamProjecmemberService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 项目-成员表(TeamProjecmember)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-10 16:43:19
 */
@Service("teamProjecmemberService")
@AllArgsConstructor
@Tenant
public class TeamProjecmemberServiceImpl extends ServiceImpl<TeamProjecmemberMapper, TeamProjecmember> implements ITeamProjecmemberService {
    private final TeamProjecmemberMapper teamProjecmemberMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<TeamProjecmember> selectTeamProjecmemberList(TeamProjecmember teamProjecmember) {
        return teamProjecmemberMapper.selectTeamProjecmemberList(teamProjecmember);
    }

    @Override
    public boolean updateById(TeamProjecmember entity) {
        QueryWrapper<TeamProjecmember> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("project_code",entity.getProjectCode()).ne("is_owner","1");
        teamProjecmemberMapper.delete(deleteWrapper);
        String[] userList = entity.getMemberCode().split(",");
        QueryWrapper<TeamProjecmember> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_code",entity.getProjectCode()).eq("is_owner","1");
        TeamProjecmember owner = teamProjecmemberMapper.selectOne(queryWrapper);
        List<String> users = Arrays.asList(userList);
        List<TeamProjecmember> list = new ArrayList<>();
        if(users != null && users.size() > 0){
            for (int i = 0; i < users.size(); i++) {
                if(users.get(i).equals(owner.getMemberCode())){
                    continue;
                }
                TeamProjecmember obj = new TeamProjecmember();
                obj.setIsOwner(0);
                obj.setProjectCode(entity.getProjectCode());
                obj.setMemberCode(users.get(i));
                obj.setJoinTime(DateUtil.formatDateTime(new Date()));
                list.add(obj);
            }
        }
        if(list != null && list.size() > 0){
            return super.saveBatch(list);
        }else{
            return true;
        }
    }
}
