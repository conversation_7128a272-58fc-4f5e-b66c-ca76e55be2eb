import request from '@/utils/request'

const prefix = '/${module}'

// 查询(MesCutting)列表
export function listMesCutting(query) {
  return request({
    url: prefix + '/mesCutting/list',
    method: 'get',
    params: query,
  })
}

// 查询(MesCutting)详细
export function getMesCutting(id) {
  return request({
    url: prefix + '/mesCutting/' + id,
    method: 'get',
  })
}

// 新增(MesCutting)
export function addMesCutting(data) {
  return request({
    url: prefix + '/mesCutting',
    method: 'post',
    data: data,
  })
}

// 修改(MesCutting)
export function updateMesCutting(data) {
  return request({
    url: prefix + '/mesCutting',
    method: 'put',
    data: data,
  })
}

// 删除(MesCutting)
export function delMesCutting(id) {
  return request({
    url: prefix + '/mesCutting/' + id,
    method: 'delete',
  })
}
