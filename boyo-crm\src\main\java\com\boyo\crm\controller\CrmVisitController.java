package com.boyo.crm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.crm.entity.CrmCustomer;
import com.boyo.crm.entity.CrmVisit;
import com.boyo.crm.service.ICrmVisitService;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

/**
 * 回访表(CrmVisit)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-28 17:06:59
 */
@Api("回访表")
@RestController
@RequestMapping("/crm/crmVisit")
@AllArgsConstructor
public class CrmVisitController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmVisitService crmVisitService;
    private final IEnterpriseUserService enterpriseUserService;

    /**
     * 查询回访表列表
     *
     */
    @ApiOperation("查询回访表列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmVisit crmVisit) {
        startPage();
        List<CrmVisit> list = crmVisitService.selectCrmVisitList(crmVisit);
        if(list != null && list.size() > 0){
            List<Long> ids = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                ids.add(list.get(i).getOwnerUserId());
            }
            List<EnterpriseUser> userList = enterpriseUserService.selectByIds(ids);
            if(userList != null && userList.size() > 0){
                for (int i = 0; i < list.size(); i++) {
                    for (int j = 0; j < userList.size(); j++) {
                        if(list.get(i).getOwnerUserId().equals(userList.get(j).getId())){
                            list.get(i).setOwnerUserName(userList.get(j).getUserFullName());
                            break;
                        }
                    }
                }
            }
        }
        return getDataTable(list);
    }
    
    /**
     * 获取回访表详情
     */
    @ApiOperation("获取回访表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        CrmVisit visit = crmVisitService.getById(id);
        if(ObjectUtil.isNotNull(visit)){
            EnterpriseUser user = enterpriseUserService.getById(visit.getOwnerUserId());
            if(ObjectUtil.isNotNull(user)){
                visit.setOwnerUserName(user.getUserFullName());
            }
            user = enterpriseUserService.getById(visit.getCreateUserId());
            if(ObjectUtil.isNotNull(user)){
                visit.setCreateUserName(user.getUserFullName());
            }
        }
        return AjaxResult.success(visit);
    }

    /**
     * 新增回访表
     */
    @ApiOperation("新增回访表")
    @PostMapping
    public AjaxResult add(@RequestBody CrmVisit crmVisit) {
        return toBooleanAjax(crmVisitService.save(crmVisit));
    }

    /**
     * 修改回访表
     */
    @ApiOperation("修改回访表")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmVisit crmVisit) {
        return toBooleanAjax(crmVisitService.updateById(crmVisit));
    }

    /**
     * 删除回访表
     */
    @ApiOperation("删除回访表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmVisitService.removeByIds(Arrays.asList(ids)));
    }

}
