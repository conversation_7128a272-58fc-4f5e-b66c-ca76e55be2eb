package com.boyo.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.mes.entity.ProductOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产订单(ProductOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
public interface ProductOrderMapper extends BaseMapper<ProductOrder>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param productOrder 实例对象
     * @return 对象列表
     */
    List<ProductOrder> selectProductOrderList(ProductOrder productOrder);

    List<ProductOrder> listExecuteOrder(@Param("equipmentId") Long equipmentId,@Param("userId") Long userId);


}

