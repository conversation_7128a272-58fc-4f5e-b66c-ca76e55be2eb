import request from '@/utils/request'

const prefix = ''

// 查询项目表(TeamProject)列表
export function listTeamProject(query) {
  return request({
    url: prefix + '/teamProject/list',
    method: 'get',
    params: query,
  })
}

// 查询项目表(TeamProject)详细
export function getTeamProject(id) {
  return request({
    url: prefix + '/teamProject/' + id,
    method: 'get',
  })
}

// 新增项目表(TeamProject)
export function addTeamProject(data) {
  return request({
    url: prefix + '/teamProject',
    method: 'post',
    data: data,
  })
}

// 修改项目表(TeamProject)
export function updateTeamProject(data) {
  return request({
    url: prefix + '/teamProject',
    method: 'put',
    data: data,
  })
}

// 删除项目表(TeamProject)
export function delTeamProject(id) {
  return request({
    url: prefix + '/teamProject/' + id,
    method: 'delete',
  })
}
