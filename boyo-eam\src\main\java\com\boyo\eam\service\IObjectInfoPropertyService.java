package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.ObjectInfoProperty;

import java.util.List;

/**
 * 物模型属性表(ObjectInfoProperty)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
public interface IObjectInfoPropertyService extends IService<ObjectInfoProperty> {

    /**
     * 查询多条数据
     *
     * @param objectInfoProperty 对象信息
     * @return 对象列表
     */
    List<ObjectInfoProperty> selectObjectInfoPropertyList(ObjectInfoProperty objectInfoProperty);


}
