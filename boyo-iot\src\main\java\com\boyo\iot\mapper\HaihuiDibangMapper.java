package com.boyo.iot.mapper;

import com.boyo.iot.entity.HaihuiDibang;

import java.util.List;

public interface HaihuiDibangMapper {

    /**
     * 查询磅单信息列表
     */
    public List<HaihuiDibang> selectHaihuiDibangList(HaihuiDibang haihuiDibang);

    /**
     * 新增磅单信息
     */
    public int insertHaihuiDibang(HaihuiDibang haihuiDibang);

    /**
     * 修改磅单信息
     */
    public int updateHaihuiDibang(HaihuiDibang haihuiDibang);

    /**
     * 删除磅单信息
     */
    public int deleteHaihuiDibangByIds(Long[] ids);

    /**
     * 根据ID查询磅单信息
     */
    public HaihuiDibang selectHaihuiDibangById(Long id);

    public HaihuiDibang selectHaihuiDibangByticketNumber(String ticketNumber);
}
