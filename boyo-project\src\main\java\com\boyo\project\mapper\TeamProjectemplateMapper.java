package com.boyo.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.project.entity.TeamProjectemplate;
import java.util.List;

/**
 * 项目类型表(TeamProjectemplate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-08 20:50:45
 */
public interface TeamProjectemplateMapper extends BaseMapper<TeamProjectemplate>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param teamProjectemplate 实例对象
     * @return 对象列表
     */
    List<TeamProjectemplate> selectTeamProjectemplateList(TeamProjectemplate teamProjectemplate);


}

