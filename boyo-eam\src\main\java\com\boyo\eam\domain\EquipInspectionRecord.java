package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (EquipInspectionRecord)实体类
 *
 * <AUTHOR>
 * @since 2021-11-28 19:30:52
 */
@Data
@TableName(value = "equip_inspection_record")
public class EquipInspectionRecord implements Serializable {
    private static final long serialVersionUID = -98368402984405773L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 点巡检任务openid: 关联equip_inspection_spot表的openid
    */
    @TableField(value="equip_inspection_spot_openid")
    private String equipInspectionSpotOpenid;
    /**
    * 点巡检任务项目openid: 关联equip_inspection_spot_item表的openid
    */
    @TableField(value="equip_inspection_spot_item_openid")
    private String equipInspectionSpotItemOpenid;
    /**
    * 点巡检时间
    */
    @TableField(value="inspection_date")
    private Date inspectionDate;
    /**
    * 点巡检结果：0不通过，1通过
    */
    @TableField(value="pass")
    private String pass;
    /**
    * 点巡检说明
    */
    @TableField(value="remark")
    private String remark;
    /**
    * 附件id：关联t_media表的id，多个用逗号隔开
    */
    @TableField(value="media_id")
    private String mediaId;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

}
