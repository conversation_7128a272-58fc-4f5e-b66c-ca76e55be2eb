<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipMaintRecordMapper">

    <resultMap type="com.boyo.eam.domain.EquipMaintRecord" id="EquipMaintRecordResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="equipMaintTaskItemOpenid" column="equip_maint_task_item_openid" />
        <result property="equipMaintFixOpenid" column="equip_maint_fix_openid" />
        <result property="beginDate" column="begin_date" />
        <result property="endDate" column="end_date" />
        <result property="handleMinute" column="handle_minute" />
        <result property="pass" column="pass" />
        <result property="maintRemark" column="maint_remark" />
        <result property="responseMinute" column="response_minute" />
        <result property="actualValue" column="actual_value" />
        <result property="type" column="type" />
        <result property="mediaId" column="media_id" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipMaintRecordList" parameterType="com.boyo.eam.domain.EquipMaintRecord" resultType="com.boyo.eam.domain.EquipMaintRecord">
        select
        t2.taskPlanCode as planCode,
        t2.taskCode as taskCode,
        t2.equipTypeName as equipTypeName,
        t2.equipCode as equipCode,
        t2.equipName as equipName,
        t2.taskState as taskState,
        t2.staffName as staffName,

        t1.*,

        t3.create_by as fixCreateBy,
        t3.create_time as fixCreateTime,
        t3.remark as fixRemark,
        t3.location as fixLocation

        from
        (
        select
        id, openid, equip_maint_task_item_openid, equip_maint_fix_openid, begin_date, end_date, handle_minute,
        maint_remark, response_minute, type, create_by, create_time, update_by, update_time
        from equip_maint_record
        ) t1 left join
        (
            select
            EMTI.*,EMT.id as taskId,EMT.plan_code as taskPlanCode,EMT.date as taskDate,EL.`code` as equipCode,EL.`name` as equipName, ET.name as equipTypeName,EMT.cycle as taskCycle,EMT.day as taskDay,EMT.state as taskState,EMT.task_code as taskCode,TML.line_name as lineName
            from
            equip_maint_task_item EMTI left join
            equip_maint_task EMT on EMTI.equip_maint_task_openid=EMT.openid left join
            equip_ledger EL on EMT.equip_ledger_openid=EL.openid left join
            equip_type ET on EL.equip_type_openid=ET.openid left join
            t_model_line TML on EMT.line_openid=TML.line_openid
        )t2 on t1.equip_maint_task_item_openid=t2.openid left join
        (
            select
            EMF.*,EL.name as equipName,EL.code as equipCode
            from equip_maint_fix EMF left join equip_ledger EL on EMF.equip_ledger_openid=EL.openid
        )t3 on t1.equip_maint_fix_openid=t3.openid
        <where>
            <if test="equipTypeName!=null and equipTypeName!=''">
                and t1.equipTypeName = #{equipTypeName}
            </if>
            <if test="equipName!=null and equipName!=''">
                and t1.equipName like CONCAT('%',#{equipName},'%')
            </if>
            <if test="taskState!=null and taskState!=''">
                and t1.taskState = #{taskState}
            </if>
            <if test="actualValue!=null and actualValue!=''">
                and t1.actual_value = #{actualValue}
            </if>
            <if test="type!=null and type!=''">
                and t1.type =#{type}
            </if>
            <if test="staffName!=null and staffName!=''">
                and find_in_set(t1.type, #{staffName})
            </if>
            <if test="upSTime!=null and upETime!=''">
                and upSTime &lt;= t3.report_time
                and t3.report_time &lt;= upSTime
            </if>
            <if test="maintSTime!=null and maintETime!=''">
                and upSTime &lt;= t1.begin_date
                and t1.begin_date &lt;= upSTime
            </if>

        </where>
    </select>

    <select id="selectTask" resultType="EquipMaintRecordTaskVO">
        select
        EMT.*,
        TML.line_name as lineName,
        EL.name as equipName,
        EL.code as equipCode,
        ET.name as equipType,
        EMR.begin_date as recordBeginDate,
        EMR.end_date as recordEndDate,
        EMR.handle_minute as handleMinute
        from
        equip_maint_task EMT left join
        equip_ledger EL on EMT.equip_ledger_openid=EL.openid left join
        equip_type ET on EL.equip_type_openid=ET.openid left join
        t_model_line TML on EMT.line_openid=TML.line_openid left join
        (select * from equip_maint_record group by equip_maint_task_openid) EMR on EMT.openid = EMR.equip_maint_task_openid
        <where>
            and EMR.openid is not null
            and EMR.begin_date is not null
            and EMR.end_date is not null
            <if test="openid != null and openid != ''">
                and EMT.openid = #{openid}
            </if>
            <if test="planCode != null and planCode != ''">
                and EMT.plan_code = #{planCode}
            </if>
            <if test="date != null">
                and EMT.date = #{date}
            </if>
            <if test="equipLedgerOpenid != null and equipLedgerOpenid != ''">
                and EMT.equip_ledger_openid like CONCAT('%',#{equipLedgerOpenid},'%')
            </if>
            <if test="equipMaintTemplOpenid != null and equipMaintTemplOpenid != ''">
                and EMT.equip_maint_templ_openid = #{equipMaintTemplOpenid}
            </if>
            <if test="sysUserId != null">
                and find_in_set(EMT.sys_user_id,#{sysUserId})
            </if>
            <if test="cycle != null and cycle != ''">
                and EMT.cycle = #{cycle}
            </if>
            <if test="day != null">
                and EMT.day = #{day}
            </if>
            <if test="state != null and state != ''">
                and find_in_set(EMT.state,#{state})
            </if>
            <if test="taskCode != null and taskCode != ''">
                and EMT.task_code = #{taskCode}
            </if>
            <if test="lineOpenid != null and lineOpenid != ''">
                and EMT.line_openid = #{lineOpenid}
            </if>
            <if test="createBy != null and createBy != ''">
                and EMT.create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and EMT.create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and EMT.update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and EMT.update_time = #{updateTime}
            </if>
            <if test="equipType != null and equipType!=''">
                and ET.name = #{equipType}
            </if>
            <if test="equipName != null and equipName!=''">
                and EL.name like CONCAT('%',#{equipName},'%')
            </if>
            <if test="state != null and state!=''">
                and EMT.state = #{state}
            </if>

            <if test="upBeginDate!=null and upEndDate!=null">
                and #{upBeginDate} &lt;= EMT.date
                and EMT.date &lt;= #{upEndDate}
            </if>
            <if test="maintBeginDate!=null and maintEndDate!=null">
                and #{maintBeginDate} &lt;= EMR.begin_date
                and EMR.begin_date &lt;= #{maintEndDate}
            </if>
        </where>
    </select>

</mapper>

