<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmActionMapper">

    <resultMap type="com.boyo.crm.entity.CrmAction" id="CrmActionResult">
        <result property="id" column="id" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="types" column="types" />
        <result property="actionId" column="action_id" />
        <result property="detail" column="detail" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmActionList" parameterType="com.boyo.crm.entity.CrmAction" resultMap="CrmActionResult">
        select
          id, create_user_id, create_time, types, action_id, detail
        from t_crm_action
        <where>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="types != null and types != ''">
                and types = #{types}
            </if>
            <if test="actionId != null">
                and action_id = #{actionId}
            </if>
            <if test="detail != null and detail != ''">
                and detail = #{detail}
            </if>
        </where>
    </select>
</mapper>

