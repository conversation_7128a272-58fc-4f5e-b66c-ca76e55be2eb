package com.boyo.mes.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工序管理(ProductProcess)实体类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Data
@TableName(value = "t_product_process")
public class ProductProcess implements Serializable {
    private static final long serialVersionUID = 887522676343502015L;
            
    @TableId
    private Integer id;
    
    /**
    * 工序名称
    */
    @TableField(value="process_name")
    private String processName;
    /**
    * 工序状态 1：启用 0：停用
    */
    @TableField(value="process_status")
    private String processStatus;

}
