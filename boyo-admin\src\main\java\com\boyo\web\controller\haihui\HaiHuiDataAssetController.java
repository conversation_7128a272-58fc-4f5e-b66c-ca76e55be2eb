package com.boyo.web.controller.haihui;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.db.sql.Query;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.core.text.Convert;
import com.boyo.master.domain.HaihuiSale;
import com.boyo.master.domain.annotations.DataAsset;
import com.boyo.master.domain.enums.MethodType;
import com.boyo.master.entity.*;
import com.boyo.master.service.DataAssetService;
import com.boyo.master.service.IHaihuiSaleService;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.boyo.master.register.DataAssetRegister.DATA_ASSET_MAP;

@RestController
@RequestMapping("/haihui/data/asset/")
public class HaiHuiDataAssetController extends BaseController {

    @Autowired
    private IHaihuiSaleService haihuiSaleService;

    private HaihuiSaleController haihuiSaleController;


    Long enterpriseId = 1845014766829256705L;
//region 数据资产公有开放接口

    /**
     * 获取数据资产类型列表
     *
     * @param enterpriseId
     * @return
     */
//    @GetMapping("{enterpriseId}")
//    public Map<String, Object> getDataAssetTypeList(@PathVariable Long enterpriseId) throws Exception {
//        Map<String, String> dataAsset = new HashMap<>(16);
//        for (Map.Entry<String, DataAssetService> item : DATA_ASSET_MAP.entrySet()) {
//            if (item.getValue().currentDataAssetExist(enterpriseId)) {
//                dataAsset.put(item.getValue().getClass().getAnnotation(DataAsset.class).value(), item.getKey());
//            }
//        }
//        Map<String, Object> result = new HashMap<>(2);
//        result.put("row", dataAsset);
//        result.put("total", dataAsset.size());
//        return result;
//    }

    /**
     * 获取数据资产列表
     *
     * @param dataAssertType
     */
    @GetMapping("list/{dataAssertType}")
    public Object getDataAssetList(@PathVariable String dataAssertType, @RequestParam Map<String, String> params) {

        if ("FertInventory".equals(dataAssertType) || "PurchaseInventory".equals(dataAssertType) || "SaleDelivery".equals(dataAssertType)
                || "MaterialDelivery".equals(dataAssertType) || "SaleOrder".equals(dataAssertType) || "PurchaseOrders".equals(dataAssertType)) {
            if (!params.containsKey("childrenCompany")) {
                return AjaxResult.error("请选择子公司");
            }
            if (!params.containsKey("dbilldateStart")) {
                return AjaxResult.error("请选择开始时间");
            }
            if (!params.containsKey("dbilldateEnd")) {
                return AjaxResult.error("请选择结束时间");
            }
        } else if ("AccountBalance".equals(dataAssertType)) {
            if (!params.containsKey("childrenCompany")) {
                return AjaxResult.error("请选择子公司");
            }
            if (!params.containsKey("year")) {
                return AjaxResult.error("请选择年份");
            }
            if (!params.containsKey("period")) {
                return AjaxResult.error("请选择月份");
            }
        } else if ("AccountDetail".equals(dataAssertType)) {
            if (!params.containsKey("subjcode")) {
                return AjaxResult.error("请选择科目");
            }
            if (!params.containsKey("childrenCompany")) {
                return AjaxResult.error("请选择子公司");
            }
            if (!params.containsKey("year")) {
                return AjaxResult.error("请选择年份");
            }
            if (!params.containsKey("period")) {
                return AjaxResult.error("请选择月份");
            }
        } else {
            return AjaxResult.error("参数错误");
        }


        List<String> invalidParameters = Arrays.asList("null", "undefined");
        for (Map.Entry<String, String> item : params.entrySet()) {
            if (invalidParameters.contains(item.getValue())) {
                params.put(item.getKey(), "");
            }
        }
        int pageNum = 1; // 当前页码
        int pageSize = 10; // 每页显示的数据条数
        try {
            if (params.containsKey("pageNum")) {
                pageNum = Convert.toInt(params.get("pageNum"));
            }
            if (params.containsKey("pageSize")) {
                pageSize = Convert.toInt(params.get("pageSize"));
            }
        } catch (Exception e) {
//            e.printStackTrace();
        }
        DataAssetService dataAssetService = DATA_ASSET_MAP.get(dataAssertType);

        params.put("enterpriseId", Convert.toStr(enterpriseId));
//        IPage page = getPage(pageNum, pageSize, dataAssertType);
        IPage page = new Page<>(pageNum, pageSize);
        final Object dataAssetList = dataAssetService.getDataAssetList(page, params);
        final List records = page.getRecords();
        JSONObject ans = new JSONObject();
        ans.put("row", records);
        ans.put("total", page.getTotal());
        ans.put("pageNum", pageNum);
        ans.put("pageSize", pageSize);
        ans.put("pageTotal", page.getPages());
        return AjaxResult.success(ans);
    }


    /**
     * 获取crm合同数据
     *
     * @param
     */
    @GetMapping("list/crm/ht")
    public TableDataInfo list(HaihuiSale haihuiSale) {
        startPage();
        List<HaihuiSale> list = haihuiSaleService.selectHaihuiSaleList(haihuiSale);
        return getDataTable(list);
    }


    /**
     * 实时刷新，传递过来数据资产即可
     * 如何根据不同公司更新数据？数据库存储公司和数据资产链接的对应关系？有个Enterprise注解，根据他找到更新方法就行了
     */
//    @GetMapping("{dataAssertType}/{enterpriseId}/refresh")
//    public boolean refresh(@PathVariable String dataAssertType, @PathVariable Long enterpriseId) throws InvocationTargetException, IllegalAccessException, IoTDBConnectionException, ParseException, StatementExecutionException, NoSuchMethodException {
//        DataAssetService dataAssetService = DATA_ASSET_MAP.get(dataAssertType);
//        // 如果注解在接口上面，直接调用 getClass 不能获取注解，必须更进一步调用 getSuperclass 才行
//        Object resut = dataAssetService.enterpriseSpecialExecute(MethodType.TASK, enterpriseId, null);
//        return ObjUtil.isEmpty(resut);
//    }

    /**
     * 获取主表数据
     *
     * @param dataAssertType
     * @param enterpriseId
     * @param params
     * @return
     */
//    @GetMapping("{dataAssertType}/{enterpriseId}/main")
//    public Object getEquipmentsMainInfo(
//            @PathVariable String dataAssertType,
//            @PathVariable Long enterpriseId,
//            @RequestParam Map<String, String> params) {
//        List<String> invalidParameters = Arrays.asList("null", "undefined");
//        for (Map.Entry<String, String> item : params.entrySet()) {
//            if (invalidParameters.contains(item.getValue())) {
//                params.put(item.getKey(), "");
//            }
//        }
//        return DATA_ASSET_MAP.get(dataAssertType).getMainTableData(enterpriseId, params);
//    }

    /**
     * 获取选择器选项
     *
     * @param dataAssetType
     * @param enterpriseId
     * @return
     */
//    @GetMapping("{dataAssetType}/{enterpriseId}/selector")
//    public Map<String, Object> selectorList(
//            @PathVariable String dataAssetType,
//            @PathVariable Long enterpriseId) {
//        DataAssetService dataAssetService = DATA_ASSET_MAP.get(dataAssetType);
//        return dataAssetService.getSelectorList(dataAssetType, enterpriseId);
//    }

    /**
     * 获取大屏数据
     *
     * @param dataAssetType
     * @param enterpriseId
     * @return
     */
//    @GetMapping("{dataAssetType}/{enterpriseId}/screen")
//    public Object getScreenData(@PathVariable String dataAssetType, @PathVariable Long enterpriseId,
//                                @RequestParam Map<String, String> params)
//            throws InvocationTargetException, IllegalAccessException {
//        List<String> invalidParameters = Arrays.asList("null", "undefined");
//        for (Map.Entry<String, String> item : params.entrySet()) {
//            if (invalidParameters.contains(item.getValue())) {
//                params.put(item.getKey(), "");
//            }
//        }
//        DataAssetService dataAssetService = DATA_ASSET_MAP.get(dataAssetType);
//        params.put("enterpriseId", Convert.toStr(enterpriseId));
//        return dataAssetService.enterpriseSpecialExecute(MethodType.SCREEN, enterpriseId, params);
//    }


}
