package com.boyo.wms.controller;

import com.boyo.wms.entity.WmsQcItem;
import com.boyo.wms.service.IWmsQcItemService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * Wms质检项(WmsQcItem)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-08 15:20:57
 */
@Api("Wms质检项")
@RestController
@RequestMapping("/wms/wmsQcItem")
@AllArgsConstructor
public class WmsQcItemController extends BaseController{
    /**
     * 服务对象
     */
    private final IWmsQcItemService wmsQcItemService;

    /**
     * 查询Wms质检项列表
     *
     */
    @ApiOperation("查询Wms质检项列表")
    @GetMapping("/list")
    public TableDataInfo list(WmsQcItem wmsQcItem) {
        startPage();
        List<WmsQcItem> list = wmsQcItemService.selectWmsQcItemList(wmsQcItem);
        return getDataTable(list);
    }
    
    /**
     * 获取Wms质检项详情
     */
    @ApiOperation("获取Wms质检项详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(wmsQcItemService.getById(id));
    }

    /**
     * 新增Wms质检项
     */
    @ApiOperation("新增Wms质检项")
    @PostMapping
    public AjaxResult add(@RequestBody WmsQcItem wmsQcItem) {
        return toBooleanAjax(wmsQcItemService.save(wmsQcItem));
    }

    /**
     * 修改Wms质检项
     */
    @ApiOperation("修改Wms质检项")
    @PutMapping
    public AjaxResult edit(@RequestBody WmsQcItem wmsQcItem) {
        return toBooleanAjax(wmsQcItemService.updateById(wmsQcItem));
    }

    /**
     * 删除Wms质检项
     */
    @ApiOperation("删除Wms质检项")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(wmsQcItemService.removeByIds(Arrays.asList(ids)));
    }

}
