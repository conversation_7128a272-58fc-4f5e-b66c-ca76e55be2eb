package com.boyo.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.annotation.DataScope;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.crm.entity.CrmCustomer;
import com.boyo.crm.entity.CrmVisit;
import com.boyo.crm.mapper.CrmCustomerMapper;
import com.boyo.crm.util.ActionEnum;
import com.boyo.crm.util.ActionUtil;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.entity.CrmClue;
import com.boyo.crm.mapper.CrmClueMapper;
import com.boyo.crm.service.ICrmClueService;
import java.util.List;

/**
 * CRM线索主表(CrmClue)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-15 10:48:28
 */
@Service("crmClueService")
@AllArgsConstructor
public class CrmClueServiceImpl extends ServiceImpl<CrmClueMapper, CrmClue> implements ICrmClueService {
    private final CrmClueMapper crmClueMapper;
    private final CrmCustomerMapper customerMapper;
    private final ActionUtil actionUtil;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    @DataScope(columns = "owner_user_id,create_user_id")
    public List<CrmClue> selectCrmClueList(CrmClue crmClue) {
        return crmClueMapper.selectCrmClueList(crmClue);
    }

    @Override
    public void changeToCustomer(CrmClue crmClue) {
        crmClue = super.getById(crmClue.getId());
        QueryWrapper<CrmCustomer> customerQueryWrapper = new QueryWrapper<>();
        customerQueryWrapper.eq("customer_name",crmClue.getClueName());
        if(customerMapper.selectCount(customerQueryWrapper) > 0){
            throw new CustomException("企业名称已存在");
        }
        CrmCustomer customer = new CrmCustomer();
        customer.setCustomerName(crmClue.getClueName());
        customer.setFollowup("0");
        customer.setDealStatus("0");
        customer.setOwnerUserId(SecurityUtils.getUserOpenid());
        customer.setIndustry(crmClue.getClueIndustry());
        customer.setLevel(crmClue.getClueLevel());
        customer.setTelephone(crmClue.getCluePhone());
        customer.setMobile(crmClue.getCluePhone());
        customer.setDetailAddress(crmClue.getClueAddress());
        customerMapper.insert(customer);
    }

    @Override
    public boolean save(CrmClue entity) {
        super.save(entity);
        actionUtil.editRecord(null,null, ActionEnum.CLUE,entity.getId(), null);
        return true;
    }
    @Override
    public boolean updateById(CrmClue entity) {
        actionUtil.editRecord(super.getById(entity.getId()),entity,ActionEnum.CLUE,entity.getId(), CrmClue.class);
        return super.updateById(entity);
    }
}
