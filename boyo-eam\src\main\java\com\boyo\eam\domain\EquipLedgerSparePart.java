package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 台账和部件关联表(EquipLedgerSparePart)实体类
 *
 * <AUTHOR>
 * @since 2021-11-04 16:59:32
 */
@Data
@TableName(value = "equip_ledger_spare_part")
public class EquipLedgerSparePart implements Serializable {
    private static final long serialVersionUID = -60817965124930868L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 对应equip_ledger表的openid
    */
    @TableField(value="equip_ledger_openid")
    private String equipLedgerOpenid;
    /**
    * 对应spare_part表的openid
    */
    @TableField(value="spare_part_openid")
    private String sparePartOpenid;

    @TableField(value="create_by")
    private String createBy;
    /**
    * 创建日期
    */
    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

}
