package com.boyo.crm.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商机产品关系表(CrmBusinessProduct)实体类
 *
 * <AUTHOR>
 * @since 2022-03-26 09:56:24
 */
@Data
@TableName(value = "t_crm_business_product")
public class CrmBusinessProduct extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = 256538589408920765L;
            
    @TableId
    private Integer id;
    
    /**
    * 商机ID
    */
    @TableField(value="business_id")
    private Integer businessId;
    /**
    * 产品ID
    */
    @TableField(value="product_id")
    private Integer productId;

    /**
     * 产品名称
     */
    @TableField(exist = false)
    private String name;

    /**
     * 产品编码
     */
    @TableField(exist = false)
    private String num;

    /**
     * 产品类别
     */
    @TableField(exist = false)
    private String categoryName;
    /**
    * 产品单价
    */
    @TableField(value="price")
    private Double price;
    /**
    * 销售价格
    */
    @TableField(value="sales_price")
    private Double salesPrice;
    /**
    * 数量
    */
    @TableField(value="quantity")
    private Integer quantity;
    /**
    * 折扣
    */
    @TableField(value="discount")
    private Double discount;
    /**
    * 小计（折扣后价格）
    */
    @TableField(value="subtotal")
    private Double subtotal;
    /**
    * 单位
    */
    @TableField(value="unit")
    private String unit;

}
