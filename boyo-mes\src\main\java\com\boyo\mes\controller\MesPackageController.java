package com.boyo.mes.controller;

import com.boyo.mes.entity.MesPackage;
import com.boyo.mes.service.IMesPackageService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 打包管理(MesPackage)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-10 15:46:38
 */
@Api("打包管理")
@RestController
@RequestMapping("/mes/mesPackage")
@AllArgsConstructor
public class MesPackageController extends BaseController{
    /**
     * 服务对象
     */
    private final IMesPackageService mesPackageService;

    /**
     * 查询打包管理列表
     *
     */
    @ApiOperation("查询打包管理列表")
    @GetMapping("/list")
    public TableDataInfo list(MesPackage mesPackage) {
        startPage();
        List<MesPackage> list = mesPackageService.selectMesPackageList(mesPackage);
        return getDataTable(list);
    }
    
    /**
     * 获取打包管理详情
     */
    @ApiOperation("获取打包管理详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(mesPackageService.getById(id));
    }

    /**
     * 新增打包管理
     */
    @ApiOperation("新增打包管理")
    @PostMapping
    public AjaxResult add(@RequestBody MesPackage mesPackage) {
        return toBooleanAjax(mesPackageService.save(mesPackage));
    }

    /**
     * 修改打包管理
     */
    @ApiOperation("修改打包管理")
    @PutMapping
    public AjaxResult edit(@RequestBody MesPackage mesPackage) {
        return toBooleanAjax(mesPackageService.updateById(mesPackage));
    }

    /**
     * 删除打包管理
     */
    @ApiOperation("删除打包管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(mesPackageService.removeByIds(Arrays.asList(ids)));
    }

}
