package com.boyo.master.service.impl;

import java.util.List;

import com.boyo.framework.annotation.Tenant;
import com.boyo.master.vo.SupplierVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.master.mapper.TSupplierMapper;
import com.boyo.master.domain.TSupplier;
import com.boyo.master.service.ITSupplierService;

/**
 * 供应商管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class TSupplierServiceImpl extends ServiceImpl<TSupplierMapper, TSupplier> implements ITSupplierService {
    private final TSupplierMapper tSupplierMapper;


    /**
     * 查询供应商管理列表
     *
     * @param tSupplier 供应商管理
     * @return tSupplier 列表
     */
    @Override
    public List<SupplierVO> selectTSupplierList(TSupplier tSupplier) {
        return tSupplierMapper.selectTSupplierList(tSupplier);
    }
}
