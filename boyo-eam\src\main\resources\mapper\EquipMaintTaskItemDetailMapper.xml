<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipMaintTaskItemDetailMapper">

    <resultMap type="com.boyo.eam.domain.EquipMaintTaskItemDetail" id="EquipMaintTaskItemDetailResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="equipMaintTaskItemOpenid" column="equip_maint_task_item_openid" />
        <result property="detail" column="detail" />
        <result property="value" column="value" />
        <result property="type" column="type" />
        <result property="maxNum" column="max_num" />
        <result property="minNum" column="min_num" />
        <result property="unit" column="unit" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipMaintTaskItemDetailList" parameterType="com.boyo.eam.domain.EquipMaintTaskItemDetail" resultMap="EquipMaintTaskItemDetailResult">
        select
        EMTID.*,
        EMT.openid as taskOpenid,
        EMTI.item as item,
        EMR.openid as recordOpenid,
        EMR.pass as pass,
        EMR.maint_remark as maintRemark,
        EMR.actual_value as actualValue,
        EMR.media_id as mediaIds
        from equip_maint_task_item_detail EMTID left join
        equip_maint_task_item EMTI on EMTID.equip_maint_task_item_openid=EMTI.openid left join
        equip_maint_task EMT on EMTI.equip_maint_task_openid=EMT.openid left join
        equip_maint_record EMR on EMTID.openid = EMR.equip_maint_task_item_detail_openid

        <where>
            <if test="openid != null and openid != ''">
                and EMTID.openid = #{openid}
            </if>
            <if test="equipMaintTaskItemOpenid != null and equipMaintTaskItemOpenid != ''">
                and EMTID.equip_maint_task_item_openid = #{equipMaintTaskItemOpenid}
            </if>
            <if test="detail != null and detail != ''">
                and EMTID.detail = #{detail}
            </if>
            <if test="value != null and value != ''">
                and EMTID.value = #{value}
            </if>
            <if test="type != null and type != ''">
                and EMTID.type = #{type}
            </if>
            <if test="maxNum != null">
                and EMTID.max_num = #{maxNum}
            </if>
            <if test="minNum != null">
                and EMTID.min_num = #{minNum}
            </if>
            <if test="unit != null and unit != ''">
                and EMTID.unit = #{unit}
            </if>
            <if test="standard != null and standard != ''">
                and EMTID.standard = #{standard}
            </if>
            <if test="createBy != null and createBy != ''">
                and EMTID.create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and EMTID.create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and EMTID.update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and EMTID.update_time = #{updateTime}
            </if>
            <if test="taskOpenid != null">
                and EMT.openid = #{taskOpenid}
            </if>
        </where>
    </select>

    <select id="getDetailAndRecord" resultType="EquipMaintTaskItemDetail">
        select
        EMTID.*,
        EMR.openid as recordOpenid,
        EMR.pass as pass,
        EMR.maint_remark as maintRemark,
        EMR.actual_value as actualValue,
        EMR.media_id as mediaIds,
        EL.name as equipName,
        EL.location as equipLocation
        from
        equip_maint_task_item_detail EMTID left join
        equip_maint_record EMR on EMTID.openid = EMR.equip_maint_task_item_detail_openid left join
        equip_maint_task_item EMTI on EMTID.equip_maint_task_item_openid=EMTI.openid left join
        equip_maint_task EMT on EMTI.equip_maint_task_openid=EMT.openid left join
        equip_ledger EL on EMT.equip_ledger_openid=EL.openid
        where EMTID.id=#{id}
    </select>
</mapper>

