package com.boyo.master.schedule;


import cn.hutool.core.collection.CollUtil;
import com.boyo.master.domain.annotations.DataAsset;
import com.boyo.master.entity.AccountDetail;
import com.boyo.master.service.DataAssetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.boyo.master.register.DataAssetRegister.DATA_ASSET_MAP;
import static com.boyo.master.register.DataAssetRegister.getDataAssetType;

;


/**
 * 海汇定时任务
 */
@Slf4j
@Component
public class HaihuiTask {

    int a = 0;

    /**
     * 海汇同步数据资产
     * 次日凌晨执行定时任务，否则iot同步需要修改
     */
    @Scheduled(cron = "0 0 2 */1 * ?")
//    @Scheduled(cron = "0 25 14 */1 * ?")
    public void dataAssets() {

//        if (a++ % 100 != 0) {
//            return;
//        }
//        a=1;
        log.info("-------------海汇同步数据资产定时任务开始了-------------");
        long start = System.currentTimeMillis();
        // 待执行任务
        List<DataAssetService> waitTask = new ArrayList<>(DATA_ASSET_MAP.values());
        // 将AccountDetail任务放在最后执行
        int index = 0;
        for (int i = 0; i < waitTask.size(); i++) {
            if (waitTask.get(i).getEntityClass().equals(AccountDetail.class)){
                index=i;
                break;
            }
        }
        final DataAssetService dataAssetService = waitTask.get(index);
        waitTask.remove(index);
        waitTask.add(dataAssetService);
        // 已完成任务
        List<String> finishedTask = new ArrayList<>();

        while (CollUtil.isNotEmpty(waitTask)) {
            DataAssetService item = waitTask.remove(0);
            try {
                // 定时任务是否满足执行条件
//                Class<?> dependsDataAsset = item.getClass().getAnnotation(DataAsset.class).dependsOn();
//                if (!dependsDataAsset.equals(Object.class) && !finishedTask.contains(getDataAssetType(dependsDataAsset))) {
//                    waitTask.add(item);
//                    continue;
//                }
                log.info("类别【{}】同步开始", item.getClass().getAnnotation(DataAsset.class).value());
                // 执行定时任务
                item.syncDataAssetHaihui();
                finishedTask.add(getDataAssetType(item));
                log.info("类别【{}】同步成功", item.getClass().getAnnotation(DataAsset.class).value());
            } catch (Exception e) {
                e.printStackTrace();
                log.error("类别【{}】同步失败：{} - {} - {}", item.getClass().getAnnotation(DataAsset.class).value(), e.getClass(), e.getMessage(), e.getStackTrace());
            }
        }
        long end = System.currentTimeMillis();
        log.info("海汇同步数据资产定时任务结束了-----------消耗：{}s",(end-start)/1000);
    }
}
