package com.boyo.eam;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.eam.domain.EquipInspectionSpot;
import com.boyo.eam.domain.EquipInspectionSpotItem;
import com.boyo.eam.domain.EquipInspectionTempl;
import com.boyo.eam.domain.EquipInspectionTemplItem;
import com.boyo.eam.service.IEquipInspectionSpotItemService;
import com.boyo.eam.service.IEquipInspectionSpotService;
import com.boyo.eam.service.IEquipInspectionTemplItemService;
import com.boyo.eam.service.IEquipInspectionTemplService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 点巡检定时任务调度
 */
@Slf4j
@Component("equipInspectionSpotTask")
@RequiredArgsConstructor
public class EquipInspectionSpotTask {

    private final IEquipInspectionTemplService equipInspectionTemplService;
    private final IEquipInspectionTemplItemService equipInspectionTemplItemService;

    private final IEquipInspectionSpotService equipInspectionSpotService;
    private final IEquipInspectionSpotItemService equipInspectionSpotItemService;

    /**
     * 定时创建点巡检任务
     */
    public void createSpotTask(){
        log.info(">>>>>>>>>> 点巡检定时任务调度-定时创建点巡检任务 >>>>>>>>>>");
        try {
            List<EquipInspectionTempl> templList = equipInspectionTemplService.list(
                Wrappers.<EquipInspectionTempl>lambdaQuery()
                    .eq(EquipInspectionTempl::getType, 0)//查询出点检的所有项目
            );
            if (templList!=null && templList.size()>0){
                for (EquipInspectionTempl eit:templList){
                    // 获取当前任务最新创建的任务
                    EquipInspectionSpot spot = equipInspectionSpotService.getOne(
                        Wrappers.<EquipInspectionSpot>lambdaQuery()
                            .eq(EquipInspectionSpot::getCode, eit.getCode())
                            .orderByDesc(EquipInspectionSpot::getCycleEndDate)
                            .last("limit 1")
                    );
                    Date createTime = eit.getCreateTime();
                    if (spot==null){
                        Map<String, Date> beDate = getBEDate(createTime);
                        String cycleUnit = eit.getCycleUnit();
                        if ("2".equals(cycleUnit)){ //天
                            String openid = saveSopt(eit,beDate.get("dayBegin"),beDate.get("dayEnd"));
                            log.info("first_task-创建成功，任务单号："+eit.getCode()+",openid："+openid);
                        }else if ("1".equals(cycleUnit)){ //周
                            String openid = saveSopt(eit,beDate.get("weekBegin"),beDate.get("weekEnd"));
                            log.info("first_task-创建成功，任务单号："+eit.getCode()+",openid："+openid);
                        }else if ("0".equals(cycleUnit)){ //月
                            String openid = saveSopt(eit,beDate.get("monthBegin"),beDate.get("monthEnd"));
                            log.info("first_task-创建成功，任务单号："+eit.getCode()+",openid："+openid);
                        }else{
                            log.info("first_task-周期为空或不合法，不创建任务：" + eit.getCode());
                        }
                    }else{
                        Date cycleBeginDate = spot.getCycleBeginDate();
                        Date cycleEndDate = spot.getCycleEndDate();
                        Map<String, Date> beDate = getBEDate(new Date());

                        String cycleUnit = eit.getCycleUnit();
                        if ("2".equals(cycleUnit) && cycleEndDate.getTime()<beDate.get("dayBegin").getTime()){ //天
                            String openid = saveSopt(eit, beDate.get("dayBegin"), beDate.get("dayEnd"));
                            log.info("创建成功，任务单号："+eit.getCode()+",openid："+openid);
                        }else if ("1".equals(cycleUnit) && cycleEndDate.getTime()<beDate.get("weekBegin").getTime()){ //周
                            String openid = saveSopt(eit,beDate.get("weekBegin"),beDate.get("weekEnd"));
                            log.info("创建成功，任务单号："+eit.getCode()+",openid："+openid);
                        }else if ("0".equals(cycleUnit) && cycleEndDate.getTime()<beDate.get("monthBegin").getTime()){ //月
                            String openid = saveSopt(eit,beDate.get("monthBegin"),beDate.get("monthEnd"));
                            log.info("创建成功，任务单号："+eit.getCode()+",openid："+openid);
                        }else{
                            log.info("周期为空或不合法，不创建任务：" + eit.getCode());
                        }
                    }
                }
            } else{
                log.info("当前没有点检模板");
            }

            // 巡检
//            List<EquipInspectionSpot> spotList = equipInspectionSpotService.list(
//                    Wrappers.<EquipInspectionSpot>lambdaQuery()
//                            .eq(EquipInspectionSpot::getType, 1)
//            );
//            if (spotList!=null&&spotList.size()>0){
//
//                List<String> codeList = spotList.stream().map(s -> s.getCode()).collect(Collectors.toList());
//                List<EquipInspectionTempl> templList1 = equipInspectionTemplService.list(
//                        Wrappers.<EquipInspectionTempl>lambdaQuery()
//                                .eq(EquipInspectionTempl::getType, 1)//查询出巡检的所有项目
//                                .notIn(EquipInspectionTempl::getCode,codeList)
//                );
//                if (templList1!=null && templList1.size()>0){
//                    for (EquipInspectionTempl templ :templList1){
//
//                        EquipInspectionSpot spot = new EquipInspectionSpot();
//                        spot.setOpenid(generateOpenid());
//                        spot.setCode(templ.getCode());
//                        spot.setEquipLedgerOpenid(templ.getEquipLedgerOpenid());
//                        spot.setType(templ.getType());
//                        spot.setSpotType(templ.getSpotType());
//                        spot.setLineOpenid(templ.getLineOpenid());
//                        spot.setCycle(templ.getCycle());
//                        spot.setCycleUnit(templ.getCycleUnit());
//
//                    }
//                }
//            }

        } catch (Exception e) {
            log.error("点巡检定时任务调度-定时创建点巡检任务时出错：", e);
        } finally {
            log.info("<<<<<<<<<< 点巡检定时任务调度-定时创建点巡检任务 <<<<<<<<<<");
        }
    }

    private String saveSopt(EquipInspectionTempl eit,Date beginDate,Date endDate){
        EquipInspectionSpot spot = new EquipInspectionSpot();
        spot.setCycleBeginDate(beginDate);
        spot.setCycleEndDate(endDate);
        String spotOpenid = generateOpenid();
        spot.setOpenid(spotOpenid);
        spot.setCode(eit.getCode());
        spot.setEquipLedgerOpenid(eit.getEquipLedgerOpenid());
        spot.setSysUserId(eit.getSysUserId());
        spot.setReviewer(eit.getReviewer());
        spot.setType(eit.getType());
        spot.setSpotType(eit.getSpotType());
        spot.setLineOpenid(eit.getLineOpenid());
        spot.setSysUserId(null);
        spot.setCycle(eit.getCycle());
        spot.setCycleUnit(eit.getCycleUnit());
        spot.setState(eit.getState());
        spot.setCreateBy(eit.getCreateBy());
        spot.setCreateTime(eit.getCreateTime());
        equipInspectionSpotService.save(spot);
        List<EquipInspectionTemplItem> itemList = equipInspectionTemplItemService.list(
                Wrappers.<EquipInspectionTemplItem>lambdaQuery()
                        .eq(EquipInspectionTemplItem::getEquipInspectionTemplOpenid, eit.getOpenid())
        );
        List<EquipInspectionSpotItem> spotItemList = new ArrayList();
        for (EquipInspectionTemplItem item:itemList){
            EquipInspectionSpotItem spotItem = new EquipInspectionSpotItem();
            spotItem.setOpenid(generateOpenid());
            spotItem.setEquipInspectionSpotOpenid(spotOpenid);
            spotItem.setItem(item.getItem());
            spotItem.setBasis(item.getBasis());
            spotItem.setMethod(item.getMethod());
            spotItem.setWay(item.getWay());
            spotItem.setUseTime(item.getUseTime());
            spotItem.setCreateBy(item.getCreateBy());
            spotItem.setCreateTime(new Date());
            spotItemList.add(spotItem);
        }
        equipInspectionSpotItemService.saveBatch(spotItemList);
        return spotOpenid;
    }

    /**
     * 根据日期获取今日、本周、本月开始结束时间
     */
    private Map<String,Date> getBEDate(Date date){
        Map<String,Date> dateMap = new HashMap<>();
        Calendar instance = Calendar.getInstance();

        instance.setTime(date);
        instance.set(Calendar.HOUR_OF_DAY,0);
        instance.set(Calendar.MINUTE,0);
        instance.set(Calendar.SECOND,1);
        dateMap.put("dayBegin",instance.getTime());
        instance.setTime(date);
        instance.set(Calendar.HOUR_OF_DAY,23);
        instance.set(Calendar.MINUTE,59);
        instance.set(Calendar.SECOND,59);
        dateMap.put("dayEnd",instance.getTime());


        instance.setTime(date);
        instance.set(Calendar.HOUR_OF_DAY,0);
        instance.set(Calendar.MINUTE,0);
        instance.set(Calendar.SECOND,1);
        instance.set(Calendar.DAY_OF_WEEK,2);
        dateMap.put("weekBegin",instance.getTime());
        instance.setTime(date);
        instance.set(Calendar.HOUR_OF_DAY,23);
        instance.set(Calendar.MINUTE,59);
        instance.set(Calendar.SECOND,59);
        instance.set(Calendar.DAY_OF_WEEK,6);
        dateMap.put("weekEnd",instance.getTime());


        instance.setTime(date);
        instance.set(Calendar.HOUR_OF_DAY,0);
        instance.set(Calendar.MINUTE,0);
        instance.set(Calendar.SECOND,1);
        instance.set(Calendar.DAY_OF_MONTH,instance.getActualMinimum(Calendar.DAY_OF_MONTH));
        dateMap.put("monthBegin",instance.getTime());
        instance.setTime(date);
        instance.set(Calendar.HOUR_OF_DAY,23);
        instance.set(Calendar.MINUTE,59);
        instance.set(Calendar.SECOND,59);
        instance.set(Calendar.DAY_OF_MONTH,instance.getActualMaximum(Calendar.DAY_OF_MONTH));
        dateMap.put("monthEnd",instance.getTime());
        return dateMap;
    }

    /**
     * 获取openid
     * @return
     */
    protected String generateOpenid(){
        return UUID.randomUUID().toString().replaceAll("-","");
    }
}
