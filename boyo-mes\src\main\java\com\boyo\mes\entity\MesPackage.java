package com.boyo.mes.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 打包管理(MesPackage)实体类
 *
 * <AUTHOR>
 * @since 2023-04-10 15:46:38
 */
@Data
@TableName(value = "t_mes_package")
public class MesPackage implements Serializable {
    private static final long serialVersionUID = 789285352215945301L;

    @TableId
    private Long id;

    /**
     * 订单id
     */
    @TableField(value="order_id")
    private Integer orderId;
    /**
     * 打包数量
     */
    @TableField(value="package_num")
    private Double packageNum;
    /**
     * 生产日期
     */
    @TableField(value="scrq")
    private String scrq;
    /**
     * 捆号
     */
    @TableField(value="kunhao")
    private String kunhao;
    /**
     * 炉号
     */
    @TableField(value="luhao")
    private String luhao;
    /**
     * 钢种
     */
    @TableField(value="gangzhong")
    private String gangzhong;
    /**
     * 钢级
     */
    @TableField(value="gangji")
    private String gangji;
    /**
     * 外径
     */
    @TableField(value="waijing")
    private String waijing;
    /**
     * 壁厚
     */
    @TableField(value="bihou")
    private String bihou;
    /**
     * 长度
     */
    @TableField(value="changdu")
    private String changdu;
    /**
     * 管号
     */
    @TableField(value="guanhao")
    private String guanhao;
    /**
     * 管号代码
     */
    @TableField(value="guanhaodaima")
    private String guanhaodaima;
    /**
     * 重量

     */
    @TableField(value="zhongliang")
    private String zhongliang;
    /**
     * 批号

     */
    @TableField(value="pihao")
    private String pihao;
    /**
     * 移库单号

     */
    @TableField(value="ykdh")
    private String ykdh;
    /**
     * 长度FT

     */
    @TableField(value="cdFT")
    private String cdft;
    /**
     * 重量LB

     */
    @TableField(value="zlLB")
    private String zllb;
    /**
     * 理重

     */
    @TableField(value="lizhong")
    private String lizhong;
    /**
     * 结论

     */
    @TableField(value="jielun")
    private String jielun;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="create_by")
    private String createBy;
    /**
     * 订单名称
     */
    @TableField(exist = false)
    private String orderNum;
    /**
     * 订单名称
     */
    @TableField(exist = false)
    private String orderName;


}