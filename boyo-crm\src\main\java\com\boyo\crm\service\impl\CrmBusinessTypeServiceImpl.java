package com.boyo.crm.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.entity.CrmBusinessType;
import com.boyo.crm.mapper.CrmBusinessTypeMapper;
import com.boyo.crm.service.ICrmBusinessTypeService;
import java.util.List;

/**
 * 商机状态组类别(CrmBusinessType)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-25 14:53:11
 */
@Service("crmBusinessTypeService")
@AllArgsConstructor
public class CrmBusinessTypeServiceImpl extends ServiceImpl<CrmBusinessTypeMapper, CrmBusinessType> implements ICrmBusinessTypeService {
    private final CrmBusinessTypeMapper crmBusinessTypeMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<CrmBusinessType> selectCrmBusinessTypeList(CrmBusinessType crmBusinessType) {
        return crmBusinessTypeMapper.selectCrmBusinessTypeList(crmBusinessType);
    }

}
