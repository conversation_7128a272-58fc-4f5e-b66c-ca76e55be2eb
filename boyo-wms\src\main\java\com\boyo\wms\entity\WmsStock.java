package com.boyo.wms.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 库存管理
 * 表名 t_wms_stock
 *
 * <AUTHOR>
 */
@ApiModel("Wms库存信息")
@Data
@TableName("t_wms_stock")
public class WmsStock extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @TableId
    private Long id;
    /**
     * 物料id
     */
    @ApiModelProperty("物料id")
    @TableField(value = "materiel_openid")
    private String materielOpenid;
    /**
     * 批次号
     */
    @ApiModelProperty("批次号")
    @TableField(value = "materiel_batch")
    private String materielBatch;
    /**
     * 仓库id
     */
    @ApiModelProperty("仓库id")
    @TableField(value = "warehouse_openid")
    private String warehouseOpenid;
    /**
     * 区域id
     */
    @ApiModelProperty("区域id")
    @TableField(value = "area_openid")
    private String areaOpenid;
    /**
     * 货位id
     */
    @ApiModelProperty("货位id")
    @TableField(value = "allocation_openid")
    private String allocationOpenid;
    /**
     * 物料数量
     */
    @ApiModelProperty("物料数量")
    @TableField(value = "materiel_quantity")
    private BigDecimal materielQuantity = new BigDecimal(0);
    /**
     * 乐观锁
     */
    @ApiModelProperty("乐观锁")
    @TableField(value = "version")
    @Version
    private Long version;
    /**
     * 最后更新时间
     */
    @ApiModelProperty("最后更新时间")
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private String materielName;
}
