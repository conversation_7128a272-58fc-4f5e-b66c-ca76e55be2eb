package com.boyo.mes.controller;



import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.baomidou.mybatisplus.extension.api.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.mes.entity.TFactoryOrder;
import com.boyo.mes.entity.TTransportOrder;
import com.boyo.mes.service.TTransportOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * (TTransportOrder)表控制层
 *
 * <AUTHOR>
 * @since 2023-09-12 09:30:36
 */
@Api("发货订单管理")
@RestController
@RequestMapping("/mes/transportOrder")
public class TTransportOrderController extends BaseController {
    /**
     * 服务对象
     */
    @Resource
    private TTransportOrderService tTransportOrderService;

    @ApiOperation("发货订单列表")
    @GetMapping("/list")
    public TableDataInfo list(TTransportOrder tTransportOrder){
        startPage();
        List<TTransportOrder> list = tTransportOrderService.selectTransportOrderList(tTransportOrder);
        return getDataTable(list);
    }

    @ApiOperation("获取发货订单详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(tTransportOrderService.getById(id));
    }

    /**
     * 新增报工记录
     */
    @ApiOperation("新增订单")
    @PostMapping
    public AjaxResult add(@RequestBody TTransportOrder tTransportOrder) {
        return toBooleanAjax(tTransportOrderService.save(tTransportOrder));
    }

    @ApiOperation("修改订单")
    @PutMapping
    public AjaxResult edit(@RequestBody TTransportOrder tTransportOrder) {
        return toBooleanAjax(tTransportOrderService.updateById(tTransportOrder));
    }

    @ApiOperation("删除订单")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(tTransportOrderService.removeByIds(Arrays.asList(ids)));
    }


    @GetMapping("/transportList")
    public AjaxResult transportList(String startTime,String endTime){

        List<Object> list = new ArrayList<>();
        QueryWrapper<TTransportOrder> allList = new QueryWrapper<>();
        allList.select("count(id) as total").between("create_time",startTime,endTime);
        Map<String, Object> map = tTransportOrderService.getMap(allList);
        Object sumAll = null;
        if(map!=null){
            sumAll = map.get("total");
        }
        QueryWrapper<TTransportOrder> yesList = new QueryWrapper<>();
        yesList.select("count(id) as total").between("create_time",startTime,endTime).isNotNull("invoice_number");
        Map<String, Object> map1 = tTransportOrderService.getMap(yesList);
        Object sumYes = null;
        if(map1!=null){
            sumYes = map1.get("total");
        }
        QueryWrapper<TTransportOrder> noList = new QueryWrapper<>();
        noList.select("count(id) as total").between("create_time",startTime,endTime).isNull("invoice_number");
        Map<String, Object> map2 = tTransportOrderService.getMap(noList);
        Object sumNo = null;
        if(map2!=null){
            sumNo = map2.get("total");
        }
        QueryWrapper<TTransportOrder> receiveList = new QueryWrapper<>();
        receiveList.select("count(id) as total").between("create_time",startTime,endTime).isNotNull("receive_time");
        Map<String, Object> map3 = tTransportOrderService.getMap(receiveList);
        Object sumReceive = null;
        if(map3!=null){
            sumReceive = map3.get("total");
        }
        list.add(sumReceive);
        list.add(sumNo);
        list.add(sumYes);
        list.add(sumAll);
        return AjaxResult.success(list);
    }
}

