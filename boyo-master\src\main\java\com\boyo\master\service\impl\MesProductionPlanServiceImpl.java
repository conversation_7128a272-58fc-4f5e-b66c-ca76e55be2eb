package com.boyo.master.service.impl;

import com.boyo.master.domain.MesProductionPlan;
import com.boyo.master.mapper.MesProductionPlanMapper;
import com.boyo.master.service.MesProductionPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 生产计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
@Service
public class MesProductionPlanServiceImpl implements MesProductionPlanService {

    @Autowired
    private MesProductionPlanMapper mesProductionPlanMapper;

    @Override
    public List<MesProductionPlan> selectMesProductionPlanList(MesProductionPlan mesProductionPlan) {
        return mesProductionPlanMapper.selectMesProductionPlanList(mesProductionPlan);
    }

    @Override
    public MesProductionPlan selectMesProductionPlanById(String id) {
        return mesProductionPlanMapper.selectMesProductionPlanById(id);
    }

    @Override
    public int insertMesProductionPlan(MesProductionPlan mesProductionPlan) {
        return mesProductionPlanMapper.insertMesProductionPlan(mesProductionPlan);
    }

    @Override
    public int updateMesProductionPlan(MesProductionPlan mesProductionPlan) {
        return mesProductionPlanMapper.updateMesProductionPlan(mesProductionPlan);
    }

    @Override
    public int deleteMesProductionPlanById(Long id) {
        return mesProductionPlanMapper.deleteMesProductionPlanById(id);
    }
}