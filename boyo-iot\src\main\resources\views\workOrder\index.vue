<template>
  <base-page :config="config">
    <span slot="operation" slot-scope="text, record">
      <a @click="$refs.createForm.handleUpdate($event,record.id)">
      <a-icon type="edit" />{{ $t('app.global.edit') }}
      </a>
      <a-divider type="vertical" />
      <a @click="handleDelete(record.id)"> <a-icon type="delete" />{{ $t('app.global.delete') }}</a>
    </span>
    <!-- 弹窗 -->
    <create-form ref="createForm" :statusOptions="statusOptions" @ok="getList" />
  </base-page>
</template>

<script>
import CreateForm from './modules/CreateForm'
import { delWorkOrder, listWorkOrder } from '@/api/workOrder'

export default {
  components: {
    CreateForm,
  },
  data() {
    return {
      // 页面加载状态
      loading: false,
      // 数据列表
      list: [],
      // 表格数据总数
      total: 0,
      // 状态数据字典
      statusOptions: [],
    }
  },
  async created() {
    this.getList()
  },
  computed: {
    config() {
      return {
        loading: this.loading,
        query: {
          onQuery: this.getList,
          items: [
                        { label: '工单类型 如IoT', name: 'type' },
                        { label: '工单编号', name: 'num' },
                        { label: '对应故障', name: 'faultId' },
                        { label: '工单描述', name: 'orderMsg' },
                        { label: '负责人id', name: 'ownerUserId' },
                        { label: '接收时间', name: 'receiveTime' },
                        { label: '完成时间', name: 'completeTime' },
                        { label: '工单完工内容', name: 'remark' },
                      ],
        },
        action: {
          add: () => this.$refs.createForm.handleAdd(),
          delete: this.handleDelete,
        },
        table: {
          total: this.total,
          list: this.list,
          columns: [
               { 
                title: '工单类型 如IoT', 
                dataIndex: 'type',
                align: 'center',
            },
                { 
                title: '工单编号', 
                dataIndex: 'num',
                align: 'center',
            },
                { 
                title: '对应故障', 
                dataIndex: 'faultId',
                align: 'center',
            },
                { 
                title: '工单描述', 
                dataIndex: 'orderMsg',
                align: 'center',
            },
                { 
                title: '负责人id', 
                dataIndex: 'ownerUserId',
                align: 'center',
            },
                { 
                title: '接收时间', 
                dataIndex: 'receiveTime',
                align: 'center',
            },
                { 
                title: '完成时间', 
                dataIndex: 'completeTime',
                align: 'center',
            },
                { 
                title: '工单完工内容', 
                dataIndex: 'remark',
                align: 'center',
            },
                        {
              title: this.$t('app.global.operation'),
              dataIndex: 'operation',
              scopedSlots: { customRender: 'operation' },
              align: 'center',
            },
          ],
        },
      }
    },
  },
  methods: {
    /**
     * 查询系统工单列表
     */
    async getList(queryParam) {
      this.loading = true
      if (queryParam !== undefined) {
        this.queryParam = queryParam
      }
      this.loading = true
      const response = await listWorkOrder(this.queryParam)
      this.list = response.rows
      this.total = response.total
      this.loading = false
    },
    /**
     * 删除按钮操作
     */
    handleDelete(id) {
      
      const ids = id || this.ids
      this.$alert.confirm({
        content: this.$t('app.global.delete.content'),
        onOk: async () => {
        await delWorkOrder(ids)
        this.getList()
        this.$alert.success(this.$t('app.global.delete.success'))
        },
      })
    },
  },
}
</script>
}
