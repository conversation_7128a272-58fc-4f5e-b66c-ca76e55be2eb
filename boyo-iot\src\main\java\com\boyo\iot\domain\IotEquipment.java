package com.boyo.iot.domain;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.BoyoBaseEntity;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 物联网设备管理
 * 表名 iot_equipment
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("物联网设备明细")
@Data
@TableName("iot_equipment")
public class IotEquipment extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;
    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    @TableField(value = "equipment_name")
    private String equipmentName;
    /**
     * 设备编号
     */
    @ApiModelProperty("设备编号")
    @TableField(value = "equipment_code")
    private String equipmentCode;
    /**
     * 物模型id
     */
    @ApiModelProperty("物模型id")
    @TableField(value = "tsl_id")
    private Long tslId;

    @ApiModelProperty("设备图片")
    @TableField(value = "equipment_img")
    private String equipmentImg;

    @ApiModelProperty("序号")
    @TableField(value = "equipment_sort")
    private Integer equipmentSort;
    @TableField(value = "create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;
    @TableField(value = "dept_id",fill = FieldFill.INSERT)
    private String deptId;
    @TableField(value = "open_id")
    private String openId;

    @TableField(exist = false)
    private List<IotTslAttr> attrList;

}
