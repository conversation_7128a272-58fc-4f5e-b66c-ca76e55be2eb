package com.boyo.master.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 主数据-工厂管理
 * 表名 t_model_factory
 *
 * <AUTHOR>
 */
@ApiModel("主数据-工厂")
@Data
@TableName("t_model_factory")
public class TModelFactory extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "factory_openid")
    private String factoryOpenid;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @TableField(value = "factory_name")
    private String factoryName;
    /**
     * 工厂简称
     */
    @ApiModelProperty("工厂简称")
    @TableField(value = "factory_abbreviation")
    private String factoryAbbreviation;
    /**
     * 工厂编码
     */
    @ApiModelProperty("工厂编码")
    @TableField(value = "factory_code")
    private String factoryCode;
    /**
     * 工厂地址
     */
    @ApiModelProperty("工厂地址")
    @TableField(value = "factory_address")
    private String factoryAddress;
    /**
     * 状态 1启用 0停用
     */
    @ApiModelProperty("状态 1启用 0停用")
    @TableField(value = "factory_status")
    private String factoryStatus;
    /**
     * 经度
     */
    @ApiModelProperty("经度")
    @TableField(value = "factory_lng")
    private String factoryLng;
    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    @TableField(value = "factory_lat")
    private String factoryLat;
    /**
     * 工厂logo
     */
    @ApiModelProperty("工厂logo")
    @TableField(value = "factory_img")
    private String factoryImg;
    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    @TableField(value = "factory_contacts")
    private String factoryContacts;
    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    @TableField(value = "factory_phone")
    private String factoryPhone;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "created_at")
    private Date createdAt;
    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    @TableField(value = "created_user")
    private String createdUser;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(value = "updated_at")
    private Date updatedAt;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "updated_user")
    private String updatedUser;

}
