package com.boyo.system.service;

import java.util.List;

import com.boyo.system.domain.TSysEnterprise;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 企业管理Service接口
 *
 * <AUTHOR>
 */
public interface ITSysEnterpriseService extends IService<TSysEnterprise> {
    /**
     * 根据条件查询查询企业管理列表
     *
     * @param tSysEnterprise 企业管理
     * @return 企业管理集合
     */
    List<TSysEnterprise> selectTSysEnterpriseList(TSysEnterprise tSysEnterprise);

    /**
     * 校验企业信息是否已存在
     * @param tSysEnterprise
     * @return
     */
    boolean checkExist(TSysEnterprise tSysEnterprise);

    /**
     * 初始化企业数据库租户
     * @param openid
     * @return
     */
    void initDatabase(String openid);

    String getEnterpriseScreen(String openid);
}
