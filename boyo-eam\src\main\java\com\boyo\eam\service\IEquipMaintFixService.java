package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipMaintFix;

import java.util.List;

/**
 * 维修任务管理(EquipMaintFix)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-17 16:13:53
 */
public interface IEquipMaintFixService extends IService<EquipMaintFix> {

    /**
     * 查询多条数据
     *
     * @param equipMaintFix 对象信息
     * @return 对象列表
     */
    List<EquipMaintFix> selectEquipMaintFixList(EquipMaintFix equipMaintFix);


}
