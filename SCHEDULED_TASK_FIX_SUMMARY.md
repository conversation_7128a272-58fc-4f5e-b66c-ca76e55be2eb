# 定时任务多租户数据源切换修复总结

## 问题描述

定时任务 `HaiHuiScreenController.sendPowerData()` 在执行时出现数据库错误：
```
java.sql.SQLSyntaxErrorException: Table 'iot.iot_equipment' doesn't exist
```

## 根本原因

这是一个多租户项目，每个租户有独立的数据库。问题的根本原因是：

1. **定时任务缺少租户上下文**：定时任务在执行时没有HTTP请求上下文，无法通过请求头获取租户信息
2. **@Tenant注解在定时任务中无效**：`@Tenant` 注解依赖于 `TenantProcessor.doDetermineDatasource()` 方法，该方法在没有HTTP请求上下文时会返回 `DynamicDataSourceContextHolder.peek()`，但此时上下文为空
3. **数据源未正确切换**：定时任务访问的是默认数据源，而不是租户特定的数据源

## 解决方案

### 1. 添加必要的依赖注入

在 `HaiHuiScreenController` 中添加：
```java
@Autowired
private TenantProcessor tenantProcessor;
```

### 2. 手动设置租户数据源

在 `sendPowerData()` 方法开始时手动切换到租户数据源：
```java
// 手动设置租户数据源
String tenantId = TENANT; // 使用默认租户ID "2db4bfe2cbe14269b410e4747073ee47"
try {
    // 检查并设置租户数据源
    if (tenantProcessor.checkDataSource(tenantId)) {
        DynamicDataSourceContextHolder.push(tenantId);
        System.out.println("成功切换到租户数据源: " + tenantId);
    } else {
        System.err.println("租户数据源不存在或初始化失败: " + tenantId);
        return;
    }
} catch (Exception e) {
    System.err.println("设置租户数据源失败: " + e.getMessage());
    e.printStackTrace();
    return;
}
```

### 3. 使用 try-finally 确保资源清理

将整个业务逻辑包装在 try-finally 块中，确保数据源上下文总是被清理：
```java
try {
    // 业务逻辑...
} catch (Exception e) {
    System.err.println("定时任务执行过程中发生异常: " + e.getMessage());
    e.printStackTrace();
} finally {
    // 清理数据源上下文
    try {
        DynamicDataSourceContextHolder.clear();
        System.out.println("数据源上下文已清理");
    } catch (Exception e) {
        System.err.println("清理数据源上下文失败: " + e.getMessage());
    }
}
```

### 4. 添加安全的设备获取方法

创建了两个安全的方法来处理设备获取失败的情况：
```java
private IotEquipment getEquipmentSafely(Integer equipmentId) {
    try {
        return iotEquipmentService.getEquipmentDetail(equipmentId);
    } catch (Exception e) {
        System.err.println("获取设备详情失败，设备ID: " + equipmentId + ", 错误: " + e.getMessage());
        return null;
    }
}

private IotEquipment getEquipmentByCodeSafely(String equipmentCode) {
    try {
        return iotEquipmentService.getEquipmentByCode(equipmentCode);
    } catch (Exception e) {
        System.err.println("根据编码获取设备失败，设备编码: " + equipmentCode + ", 错误: " + e.getMessage());
        return null;
    }
}
```

## 修复效果

1. **解决了数据库表不存在的错误**：通过正确切换到租户数据源，确保访问正确的数据库
2. **提高了系统稳定性**：添加了完善的异常处理，防止定时任务崩溃
3. **确保资源清理**：使用 try-finally 块确保数据源上下文总是被正确清理
4. **增强了日志记录**：添加了详细的日志输出，便于问题排查

## 测试验证

创建了单元测试 `HaiHuiScreenControllerTest` 来验证：
- 租户数据源切换成功的情况
- 租户数据源切换失败的情况  
- 租户数据源切换异常的情况

## 注意事项

1. **租户ID配置**：当前使用硬编码的默认租户ID `"2db4bfe2cbe14269b410e4747073ee47"`，如需支持多租户定时任务，可考虑从配置文件读取或遍历所有租户
2. **性能考虑**：每次定时任务执行都会检查和切换数据源，对于高频定时任务需要考虑性能影响
3. **监控告警**：建议添加监控告警，当租户数据源切换失败时及时通知运维人员

## 相关文件

- `boyo-admin/src/main/java/com/boyo/web/controller/haihui/HaiHuiScreenController.java` - 主要修复文件
- `boyo-admin/src/test/java/com/boyo/web/controller/haihui/HaiHuiScreenControllerTest.java` - 单元测试
- `boyo-admin/src/main/java/com/boyo/processor/TenantProcessor.java` - 租户处理器
- `boyo-framework/src/main/java/com/boyo/framework/annotation/Tenant.java` - 租户注解定义
