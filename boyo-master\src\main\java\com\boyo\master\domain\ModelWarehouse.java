package com.boyo.master.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 主数据-仓库管理
 * 表名 t_model_warehouse
 *
 * <AUTHOR>
 */
@ApiModel("主数据-仓库")
@Data
@TableName("t_model_warehouse")
public class ModelWarehouse extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "warehouse_openid")
    private String warehouseOpenid;
    /**
     * 所属工厂
     */
    @ApiModelProperty("所属工厂")
    @TableField(value = "warehouse_factory")
    private String warehouseFactory;
    /**
     * 所属车间
     */
    @ApiModelProperty("所属车间")
    @TableField(value = "warehouse_workshop")
    private String warehouseWorkshop;
    /**
     * 所属产线
     */
    @ApiModelProperty("所属产线")
    @TableField(value = "warehouse_line")
    private String warehouseLine;
    /**
     * 仓库名称
     */
    @ApiModelProperty("仓库名称")
    @TableField(value = "warehouse_name")
    private String warehouseName;
    /**
     * 仓库简称
     */
    @ApiModelProperty("仓库简称")
    @TableField(value = "warehouse_abbreviation")
    private String warehouseAbbreviation;
    /**
     * 仓库编码
     */
    @ApiModelProperty("仓库编码")
    @TableField(value = "warehouse_code")
    private String warehouseCode;
    /**
     * 仓库类型
     */
    @ApiModelProperty("仓库类型")
    @TableField(value = "warehouse_type")
    private String warehouseType;
    /**
     * 状态 1启用 0停用
     */
    @ApiModelProperty("状态 1启用 0停用")
    @TableField(value = "warehouse_status")
    private String warehouseStatus;
    /**
     * 仓库logo
     */
    @ApiModelProperty("仓库logo")
    @TableField(value = "warehouse_img")
    private String warehouseImg;
    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    @TableField(value = "warehouse_contacts")
    private String warehouseContacts;
    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    @TableField(value = "warehouse_phone")
    private String warehousePhone;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "created_at")
    private Date createdAt;
    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    @TableField(value = "created_user")
    private String createdUser;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(value = "updated_at")
    private Date updatedAt;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "updated_user")
    private String updatedUser;
}
