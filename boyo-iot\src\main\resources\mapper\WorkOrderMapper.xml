<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.iot.mapper.WorkOrderMapper">

    <resultMap type="com.boyo.iot.entity.WorkOrder" id="WorkOrderResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="num" column="num"/>
        <result property="faultId" column="fault_id"/>
        <result property="orderMsg" column="order_msg"/>
        <result property="ownerUserId" column="owner_user_id"/>
        <result property="receiveTime" column="receive_time"/>
        <result property="completeTime" column="complete_time"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="reasonId" column="reason_id"/>
        <result property="closeTime" column="close_time"/>
        <result property="closeUserId" column="close_user_id"/>
        <result property="reasonStr" column="base_desc"/>
        <result property="orderImg" column="order_img"/>
        <result property="completeImg" column="complete_img"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="equipmentName" column="equipment_name"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectWorkOrderList" parameterType="com.boyo.iot.entity.WorkOrder" resultMap="WorkOrderResult">
        select t1.*,t2.base_desc,ie.equipment_name from (select
        id, type, num, fault_id, order_msg, owner_user_id, receive_time, complete_time,
        remark,create_time,create_user_id,dept_id,reason_id,equipment_id,
        close_time,close_user_id
        from t_work_order
        <where>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="num != null and num != ''">
                and num = #{num}
            </if>
            <if test="faultId != null">
                and fault_id = #{faultId}
            </if>
            <if test="orderMsg != null and orderMsg != ''">
                and order_msg = #{orderMsg}
            </if>
            <if test="ownerUserId != null">
                and owner_user_id = #{ownerUserId}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="receiveTime != null">
                and receive_time = #{receiveTime}
            </if>
            <if test="completeTime != null">
                and complete_time = #{completeTime}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            ${params.dataScope}
        </where>
        ) t1
        left join t_base_dict t2 on t1.reason_id = t2.id
        left join iot_equipment ie on t1.equipment_id = ie.id

        order by t1.id desc
    </select>

    <select id="listWaitReceive" parameterType="com.boyo.iot.entity.WorkOrder" resultMap="WorkOrderResult">
        select t1.*,t2.base_desc from (select
        id, type, num, fault_id, order_msg, owner_user_id, receive_time, complete_time,
        remark,create_time,create_user_id,dept_id,reason_id,
        close_time,close_user_id
        from t_work_order
        <where>
            owner_user_id is null
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="num != null and num != ''">
                and num = #{num}
            </if>
            <if test="faultId != null">
                and fault_id = #{faultId}
            </if>
            <if test="orderMsg != null and orderMsg != ''">
                and order_msg = #{orderMsg}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="receiveTime != null">
                and receive_time = #{receiveTime}
            </if>
            <if test="completeTime != null">
                and complete_time = #{completeTime}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            ${params.dataScope}
        </where>
        ) t1 left join t_base_dict t2 on t1.reason_id = t2.id
        order by t1.id desc
    </select>
    <select id="getWorkOrderById" resultType="com.boyo.iot.entity.WorkOrder">
        select t1.*, t2.base_desc, ie.equipment_name
        from (select id,
                     type,
                     num,
                     fault_id,
                     order_msg,
                     owner_user_id,
                     receive_time,
                     complete_time,
                     remark,
                     create_time,
                     create_user_id,
                     dept_id,
                     reason_id,
                     equipment_id,
                     close_time,
                     close_user_id
              from t_work_order
              where id = #{id}
             ) t1
                 left join t_base_dict t2 on t1.reason_id = t2.id
                 left join iot_equipment ie on t1.equipment_id = ie.id
    </select>
</mapper>

