package com.boyo.wms.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (WmsFlowqcDetail)实体类
 *
 * <AUTHOR>
 * @since 2022-03-10 15:12:10
 */
@Data
@TableName(value = "t_wms_flowqc_detail")
public class WmsFlowqcDetail implements Serializable {
    private static final long serialVersionUID = 969321662334483817L;
        
    /**
    * 主键
    */
    @TableField(value="id")
    private Integer id;
    /**
    * 索引id
    */
    @TableField(value="p_id")
    private Integer pId;
    /**
    * 质检项名称
    */
    @TableField(value="item_name")
    private String itemName;
    /**
    * 质检项code
    */
    @TableField(value="item_code")
    private String itemCode;
    /**
    * 质检结果
    */
    @TableField(value="item_result")
    private String itemResult;

}
