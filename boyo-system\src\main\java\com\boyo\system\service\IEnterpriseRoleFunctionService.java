package com.boyo.system.service;

import java.util.List;

import com.boyo.system.domain.EnterpriseRoleFunction;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 企业角色权限管理Service接口
 *
 * <AUTHOR>
 */
public interface IEnterpriseRoleFunctionService extends IService<EnterpriseRoleFunction> {
    /**
     * 根据条件查询查询企业角色权限管理列表
     *
     * @param enterpriseRoleFunction 企业角色权限管理
     * @return 企业角色权限管理集合
     */
    List<EnterpriseRoleFunction> selectEnterpriseRoleFunctionList(EnterpriseRoleFunction enterpriseRoleFunction);
}
