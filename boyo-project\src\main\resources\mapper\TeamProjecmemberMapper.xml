<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.project.mapper.TeamProjecmemberMapper">

    <resultMap type="com.boyo.project.entity.TeamProjecmember" id="TeamProjecmemberResult">
        <result property="id" column="id" />
        <result property="projectCode" column="project_code" />
        <result property="memberCode" column="member_code" />
        <result property="joinTime" column="join_time" />
        <result property="isOwner" column="is_owner" />
        <result property="authorize" column="authorize" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectTeamProjecmemberList" parameterType="com.boyo.project.entity.TeamProjecmember" resultMap="TeamProjecmemberResult">
        select
          id, project_code, member_code, join_time, is_owner, authorize
        from team_project_member
        <where>
            <if test="projectCode != null and projectCode != ''">
                and project_code = #{projectCode}
            </if>
            <if test="memberCode != null and memberCode != ''">
                and member_code = #{memberCode}
            </if>
            <if test="joinTime != null and joinTime != ''">
                and join_time = #{joinTime}
            </if>
            <if test="isOwner != null">
                and is_owner = #{isOwner}
            </if>
            <if test="authorize != null and authorize != ''">
                and authorize = #{authorize}
            </if>
        </where>
    </select>
</mapper>

