package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipInspectionTemplItem;

import java.util.List;

/**
 * 点检-项目(EquipInspectionTemplItem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-29 10:21:50
 */
public interface IEquipInspectionTemplItemService extends IService<EquipInspectionTemplItem> {

    /**
     * 查询多条数据
     *
     * @param equipInspectionTemplItem 对象信息
     * @return 对象列表
     */
    List<EquipInspectionTemplItem> selectEquipInspectionTemplItemList(EquipInspectionTemplItem equipInspectionTemplItem);


}
