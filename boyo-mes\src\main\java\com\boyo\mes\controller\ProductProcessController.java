package com.boyo.mes.controller;

import com.boyo.mes.entity.ProductProcess;
import com.boyo.mes.service.IProductProcessService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 工序管理(ProductProcess)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Api("工序管理")
@RestController
@RequestMapping("/mes/productProcess")
@AllArgsConstructor
public class ProductProcessController extends BaseController{
    /**
     * 服务对象
     */
    private final IProductProcessService productProcessService;

    /**
     * 查询工序管理列表
     *
     */
    @ApiOperation("查询工序管理列表")
    @GetMapping("/list")
    public TableDataInfo list(ProductProcess productProcess) {
        startPage();
        List<ProductProcess> list = productProcessService.selectProductProcessList(productProcess);
        return getDataTable(list);
    }
    
    /**
     * 获取工序管理详情
     */
    @ApiOperation("获取工序管理详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(productProcessService.getById(id));
    }

    /**
     * 新增工序管理
     */
    @ApiOperation("新增工序管理")
    @PostMapping
    public AjaxResult add(@RequestBody ProductProcess productProcess) {
        return toBooleanAjax(productProcessService.save(productProcess));
    }

    /**
     * 修改工序管理
     */
    @ApiOperation("修改工序管理")
    @PutMapping
    public AjaxResult edit(@RequestBody ProductProcess productProcess) {
        return toBooleanAjax(productProcessService.updateById(productProcess));
    }

    /**
     * 删除工序管理
     */
    @ApiOperation("删除工序管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(productProcessService.removeByIds(Arrays.asList(ids)));
    }

}
