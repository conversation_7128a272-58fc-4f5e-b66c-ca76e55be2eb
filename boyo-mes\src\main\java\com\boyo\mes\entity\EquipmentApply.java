package com.boyo.mes.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 上下班记录(EquipmentApply)实体类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:47:21
 */
@Data
@TableName(value = "t_equipment_apply")
public class EquipmentApply implements Serializable {
    private static final long serialVersionUID = -28795826313905970L;
            
    @TableId
    private Integer id;
    
    /**
    * 设备id
    */
    @TableField(value="equipment_id")
    private Long equipmentId;
    /**
    * 用户id
    */
    @TableField(value="user_id")
    private Long userId;
    /**
    * 用户名
    */
    @TableField(value="user_name")
    private String userName;
    /**
    * 上班时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(value="start_time")
    private Date startTime;
    /**
    * 下班时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(value="end_time")
    private Date endTime;

    /**
     * 设备名称
     */
    @TableField(exist = false)
    private String equipmentName;

    /**
     * 设备编码
     */
    @TableField(exist = false)
    private String equipmentCode;

    /**
     * 设备状态 0：可用 1：已被使用 2:当前用户正在使用
     */
    @TableField(exist = false)
    private String equipmentStatus;

    @TableField(exist = false)
    private String equipmentMsg;

    @TableField(exist = false)
    private String applyType;

}
