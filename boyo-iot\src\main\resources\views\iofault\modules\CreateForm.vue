<template>
  <a-modal width="30%" :maskClosable="false" :visible="open" @cancel="cancel">
    <template #title>
      <a-icon type="security-scan" />
      {{ formTitle }}
    </template>
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      layout="horizontal"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
           <a-form-model-item label="设备id">
        <a-input
          :size="formSize"
          v-model="form.equipmentId"
          :placeholder="$t('app.global.please.input') + '设备id'"
        />
      </a-form-model-item>
           <a-form-model-item label="故障属性ID">
        <a-input
          :size="formSize"
          v-model="form.faultId"
          :placeholder="$t('app.global.please.input') + '故障属性ID'"
        />
      </a-form-model-item>
           <a-form-model-item label="发生时间">
        <a-input
          :size="formSize"
          v-model="form.createTime"
          :placeholder="$t('app.global.please.input') + '发生时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="解除时间">
        <a-input
          :size="formSize"
          v-model="form.relieveTime"
          :placeholder="$t('app.global.please.input') + '解除时间'"
        />
      </a-form-model-item>
        </a-form-model>
    <template #footer>
      <a-space>
        <a-button :size="formSize" icon="close" type="danger" @click="cancel">
          {{ $t('app.global.close') }}
        </a-button>
        <a-button :size="formSize" icon="save" type="primary" @click="submitForm">
          {{ $t('app.global.save') }}
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script>
import { addIofault, updateIofault, getIofault } from '@/api/iofault'
export default {
  data() {
    return {
      //新增或修改
      updateState: false,
      formTitle: 'IoT故障清单',
      // 表单参数
      form: {
           id: '',
           equipmentId: '',
           faultId: '',
           createTime: '',
           relieveTime: '',
             },
      open: false,
      rules: {},
    }
  },
  created() {
    this.rules = {
    }
  },
  methods: {
    /**
     * 新增按钮操作
     * */
    handleAdd() {
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.add') + 'IoT故障清单'
      this.form = {}
      this.updateState = false
    },
    /**
     * 修改按钮操作
     * */
    async handleUpdate($event, id) {
      $event.stopPropagation()
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.edit') + 'IoT故障清单'
      const response = await getIofault(id)
      this.form = response.data
    },
    /**
     * 提交按钮
     * */
    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
            if (this.form.id) {
                await updateIofault(this.form)
                this.$alert.success(this.$t('app.global.edit.success'))
                this.open = false
                this.$emit('ok')
            } else {
                await addIofault(this.form)
                this.$alert.success(this.$t('app.global.add.success'))
                this.open = false
                this.$emit('ok')
            }
        } else {
            return false
        }
        })
    },
    /**
     * 取消按钮
     * */
    cancel() {
      this.open = false
      this.reset()
    },
    /**
     * 表单重置
     * */
    reset() {
      this.form = {}
      if (this.$refs.form) {
        this.$refs.form.resetFields()}
      }
    },
}
</script>

