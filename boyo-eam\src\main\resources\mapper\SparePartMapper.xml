<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.SparePartMapper">

    <resultMap type="com.boyo.eam.domain.SparePart" id="SparePartResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="type" column="type" />
        <result property="code" column="code" />
        <result property="name" column="name" />
        <result property="model" column="model" />
        <result property="unit" column="unit" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectSparePartList" parameterType="com.boyo.eam.domain.SparePart" resultMap="SparePartResult">
        select
          id, openid, type, code, name, model, unit, remark, create_by, create_time, update_by, update_time
        from spare_part
        <where>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="model != null and model != ''">
                and model = #{model}
            </if>
            <if test="unit != null and unit != ''">
                and unit = #{unit}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>
</mapper>

