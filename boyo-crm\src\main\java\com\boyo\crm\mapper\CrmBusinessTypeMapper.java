package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmBusinessType;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * 商机状态组类别(CrmBusinessType)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-25 14:53:11
 */
@Tenant
public interface CrmBusinessTypeMapper extends BaseMapper<CrmBusinessType>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmBusinessType 实例对象
     * @return 对象列表
     */
    List<CrmBusinessType> selectCrmBusinessTypeList(CrmBusinessType crmBusinessType);


}

