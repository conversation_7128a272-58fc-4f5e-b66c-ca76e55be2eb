<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.ModelAreaMapper">

    <resultMap type="com.boyo.master.vo.ModelAreaVO" id="ModelAreaResult">
        <result property="id" column="id"/>
        <result property="areaOpenid" column="area_openid"/>
        <result property="areaName" column="area_name"/>
        <result property="areaWarehouseId" column="area_warehouse_id"/>
        <result property="createdAt" column="created_at"/>
        <result property="createdUser" column="created_user"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="updatedUser" column="updated_user"/>
        <result property="areaCode" column="area_code"/>
        <result property="warehouseName" column="warehouse_name" />
    </resultMap>

    <sql id="selectModelAreaVo">
        select id,
               area_openid,
               area_name,
               area_warehouse_id,
               created_at,
               created_user,
               updated_at,
               updated_user,
               area_code
        from t_model_area
    </sql>

    <select id="selectModelAreaList" parameterType="com.boyo.master.domain.ModelArea" resultMap="ModelAreaResult">
        select t1.*,t2.warehouse_name as warehouse_name from t_model_area t1 left join t_model_warehouse t2
        on t1.area_warehouse_id = t2.warehouse_openid
        <where>
            <if test="areaOpenid != null  and areaOpenid != ''">
                and t1.area_openid = #{areaOpenid}
            </if>
            <if test="areaName != null  and areaName != ''">
                and t1.area_name like concat('%', #{areaName}, '%')
            </if>
            <if test="areaWarehouseId != null  and areaWarehouseId != ''">
                and t1.area_warehouse_id = #{areaWarehouseId}
            </if>
            <if test="createdAt != null ">
                and t1.created_at = #{createdAt}
            </if>
            <if test="areaCode != null  and areaCode != ''">
                and t1.area_code = #{areaCode}
            </if>
        </where>
    </select>
</mapper>
