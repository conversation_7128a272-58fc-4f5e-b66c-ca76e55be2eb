package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipInspectionSpotItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 点检-项目(EquipInspectionSpotItem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-15 16:16:30
 */
public interface EquipInspectionSpotItemMapper extends BaseMapper<EquipInspectionSpotItem>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipInspectionSpotItem 实例对象
     * @return 对象列表
     */
    List<EquipInspectionSpotItem> selectEquipInspectionSpotItemList(EquipInspectionSpotItem equipInspectionSpotItem);

    /**
     * 获取详情和记录
     * @param id
     * @return
     */
    EquipInspectionSpotItem getItemAndRecord(@Param("id") Integer id);
}

