<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.BaseDictMapper">

    <resultMap type="com.boyo.master.domain.BaseDict" id="BaseDictResult">
        <result property="id" column="id"/>
        <result property="openid" column="openid"/>
        <result property="baseType" column="base_type"/>
        <result property="baseCode" column="base_code"/>
        <result property="baseDesc" column="base_desc"/>
        <result property="baseStatus" column="base_status"/>
    </resultMap>

    <sql id="selectBaseDictVo">
        select id, openid, base_type, base_code, base_desc, base_status
        from t_base_dict
    </sql>

    <select id="selectBaseDictList" parameterType="com.boyo.master.domain.BaseDict" resultMap="BaseDictResult">
        <include refid="selectBaseDictVo"/>
        <where>
            <if test="openid != null  and openid != ''">
                and openid = #{openid}
            </if>
            <if test="baseType != null  and baseType != ''">
                and base_type = #{baseType}
            </if>
            <if test="baseCode != null  and baseCode != ''">
                and base_code = #{baseCode}
            </if>
            <if test="baseDesc != null  and baseDesc != ''">
                and base_desc = #{baseDesc}
            </if>
            <if test="baseStatus != null  and baseStatus != ''">
                and base_status = #{baseStatus}
            </if>
        </where>
    </select>
</mapper>
