package com.boyo.iot.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.core.redis.RedisCache;
import com.boyo.common.core.text.Convert;
import com.boyo.framework.annotation.Tenant;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.domain.IotEquipmentProp;
import com.boyo.iot.entity.FaultProp;
import com.boyo.iot.entity.Iofault;
import com.boyo.iot.entity.IorealData;
import com.boyo.iot.entity.WorkOrder;
import com.boyo.iot.mapper.IofaultMapper;
import com.boyo.iot.mapper.IotEquipmentMapper;
import com.boyo.iot.mapper.IotEquipmentPropMapper;
import com.boyo.iot.mapper.WorkOrderMapper;
import com.boyo.iot.service.IIotEquipmentPropService;
import com.boyo.iot.vo.IoTAttrVO;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 设备属性管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class IotEquipmentPropServiceImpl extends ServiceImpl<IotEquipmentPropMapper, IotEquipmentProp> implements IIotEquipmentPropService {
    private final IotEquipmentPropMapper iotEquipmentPropMapper;
    private final IotEquipmentMapper iotEquipmentMapper;
    private final IofaultMapper iofaultMapper;
    private final WorkOrderMapper workOrderMapper;
    private final RedisCache redisCache;
    private final IEnterpriseUserService enterpriseUserService;
    private final WxMpService wxService;


    /**
     * 查询设备属性管理列表
     *
     * @param iotEquipmentProp 设备属性管理
     * @return iotEquipmentProp 列表
     */
    @Override
    public List<IoTAttrVO> selectIotEquipmentPropList(IotEquipmentProp iotEquipmentProp) {
        return iotEquipmentPropMapper.selectIotEquipmentPropList(iotEquipmentProp);
    }

    @Override
    public void executeEquipmentFaultProp(String code, List<IorealData> realList) {
        List<FaultProp> faultPropList = iotEquipmentPropMapper.selectEquipmentFaultProp(code);
        StringBuffer str = new StringBuffer("");
        if (faultPropList != null && faultPropList.size() > 0) {
            for (FaultProp faultProp : faultPropList) {
                if (ObjectUtil.isNull(faultProp.getAutoFault()) || !faultProp.getAutoFault().equals(1)) {
                    continue;
                }
                for (IorealData data : realList) {
                    if (faultProp.getCode().equals(data.getTag()) && faultProp.getEquipmentCode().equals(data.getDeviceCode())) {
                        if ((faultProp.getAttrType().equalsIgnoreCase("Double") || faultProp.getAttrType().equalsIgnoreCase("Integer")) && !faultProp.getAttrMultiple().equals(1d)){
                            data.setVal(Convert.toStr(Convert.toDouble(data.getVal()) * faultProp.getAttrMultiple()));
                        }
                        str.setLength(0);
                        str.append(code).append(".").append(faultProp.getEquipmentCode()).append(".").append(faultProp.getCode()).append(".time");
                        if ((StrUtil.isNotEmpty(faultProp.getFaultVal()) && faultProp.getFaultVal().equals(data.getVal())) || (StrUtil.isNotEmpty(faultProp.getMaxVal()) && Convert.toDouble(faultProp.getMaxVal()) < Convert.toDouble(data.getVal()))
                                || (StrUtil.isNotEmpty(faultProp.getMinVal()) && Convert.toDouble(faultProp.getMinVal()) > Convert.toDouble(data.getVal()))) {
                            if (ObjectUtil.isNotNull(faultProp.getFaultTime()) && faultProp.getFaultTime() > 0) {
                                if (ObjectUtil.isNull(redisCache.getCacheObject(str.toString()))) {
                                    redisCache.setCacheObject(str.toString(), System.currentTimeMillis() / 1000);
                                    continue;
                                }
                                long continued = System.currentTimeMillis() / 1000 - Convert.toLong(redisCache.getCacheObject(str.toString()));
                                if (continued < faultProp.getFaultTime()) {
                                    continue;
                                }
                            } else {
                                redisCache.setCacheObject(str.toString(), System.currentTimeMillis() / 1000);
                            }
                            QueryWrapper<Iofault> queryWrapper = new QueryWrapper<>();
                            queryWrapper.eq("equipment_id", faultProp.getEquipmentId()).eq("fault_id", faultProp.getId()).isNull("relieve_time")
                                    .orderByDesc("id").last("limit 1");
                            if (iofaultMapper.selectCount(queryWrapper) == 0) {
//                            新故障发生
                                Iofault fault = new Iofault();
                                fault.setEquipmentId(faultProp.getEquipmentId());
                                fault.setCreateTime(new Date(Convert.toLong(redisCache.getCacheObject(str.toString())) * 1000));
                                fault.setFaultId(faultProp.getId());
                                IotEquipment equipment = iotEquipmentMapper.selectById(faultProp.getEquipmentId());
                                fault.setDeptId(equipment.getDeptId());
                                if (StrUtil.isNotEmpty(faultProp.getFaultVal()) && faultProp.getFaultVal().equals(data.getVal())) {
                                    fault.setFaultMsg("故障点报警");
                                } else if (StrUtil.isNotEmpty(faultProp.getMaxVal()) && Convert.toDouble(faultProp.getMaxVal()) < Convert.toDouble(data.getVal())) {
                                    fault.setFaultMsg("【"+faultProp.getAttrName()+ "】数据值【"+ NumberUtil.round(Convert.toDouble(data.getVal()),2)+"】大于设定上限值【" + faultProp.getMaxVal() + "】");
                                } else {
                                    fault.setFaultMsg("【"+faultProp.getAttrName()+ "】数据值【"+ NumberUtil.round(Convert.toDouble(data.getVal()),2)+"】小于设定下限值【" + faultProp.getMinVal() + "】");
                                }
                                iofaultMapper.insert(fault);
                                if (faultProp.getAutoBill().equals(1)) {
//                                    需要自动生成工单
                                    WorkOrder workOrder = new WorkOrder();
                                    workOrder.setFaultId(fault.getId());
                                    workOrder.setType("iot");
                                    workOrder.setOrderMsg(fault.getFaultMsg());
                                    workOrder.setCreateTime(new Date());
                                    workOrder.setDeptId(equipment.getDeptId());
                                    workOrderMapper.insert(workOrder);
                                    workOrder.setNum("WO-IOT-" + DateUtil.format(new Date(), "yyyyMMdd") + "-" + StrUtil.fillBefore(Convert.toStr(workOrder.getId()), '0', 6));
                                    workOrderMapper.updateById(workOrder);
                                }
                                // 获取需要推送的用户
                                String source = DynamicDataSourceContextHolder.peek();
                                DynamicDataSourceContextHolder.push("master");
                                EnterpriseUser user = new EnterpriseUser();
                                user.setEnterpriseOpenid(source);
                                user.setDepartmentOpenid(equipment.getDeptId());
                                List<EnterpriseUser> userList = enterpriseUserService.selectEnterpriseUserList(user);
                                for (EnterpriseUser temp:userList) {
                                    if(StrUtil.isNotEmpty(temp.getUserWechat())){
//                                        WxMpTemplateMessage message = new WxMpTemplateMessage();
//                                        message.setToUser(temp.getUserWechat());
//                                        message.setTemplateId("KANsVELebKSPCTAnFHvrNbSALRc_uU24UYMhJEVlIJ4");
//                                        message.setData();
                                        WxMpTemplateMessage message = WxMpTemplateMessage.builder().toUser(temp.getUserWechat())
                                                .templateId("KANsVELebKSPCTAnFHvrNbSALRc_uU24UYMhJEVlIJ4")
                                                .build();
                                        message.addData(new WxMpTemplateData("first", "尊敬的用户，您的设备发生如下报警", "#000000"))
                                                .addData(new WxMpTemplateData("keyword1", equipment.getEquipmentName(), "#000000"))
                                                .addData(new WxMpTemplateData("keyword2", DateUtil.formatDateTime(new Date()), "#000000"))
                                                .addData(new WxMpTemplateData("keyword3", fault.getFaultMsg(), "#000000"))
                                                .addData(new WxMpTemplateData("remark", "请及时处理!若有疑问请及时联系我们", "#000000"));
                                        try {
                                            wxService.getTemplateMsgService().sendTemplateMsg(message);
                                        } catch (WxErrorException e) {
                                            throw new RuntimeException(e);
                                        }
                                    }
                                }
                                DynamicDataSourceContextHolder.poll();
                            }
                        } else {
//                            故障结束
                            QueryWrapper<Iofault> queryWrapper = new QueryWrapper<>();
                            queryWrapper.eq("equipment_id", faultProp.getEquipmentId()).eq("fault_id", faultProp.getId()).isNull("relieve_time")
                                    .orderByDesc("id").last("limit 1");
                            Iofault fault = iofaultMapper.selectOne(queryWrapper);
                            redisCache.deleteObject(str.toString());
                            if (ObjectUtil.isNotNull(fault)) {
                                fault.setRelieveTime(new Date());
                                iofaultMapper.updateById(fault);
                            }
                        }
                    }
                }
            }
        }
    }
}
