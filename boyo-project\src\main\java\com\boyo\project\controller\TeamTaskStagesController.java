package com.boyo.project.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.project.entity.TeamTask;
import com.boyo.project.entity.TeamTaskStages;
import com.boyo.project.service.ITeamTaskStagesService;
import com.boyo.project.util.ExecuteStatus;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 任务列表表(TeamTaskStages)表控制层
 *
 * <AUTHOR>
 * @since 2022-02-10 20:15:00
 */
@Api("任务列表表")
@RestController
@RequestMapping("/project/teamTaskStages")
@AllArgsConstructor
public class TeamTaskStagesController extends BaseController{
    /**
     * 服务对象
     */
    private final ITeamTaskStagesService teamTaskStagesService;
    private final IEnterpriseUserService enterpriseUserService;

    /**
     * 查询任务列表表列表
     *
     */
    @ApiOperation("查询任务列表表列表")
    @GetMapping("/list")
    public TableDataInfo list(TeamTaskStages teamTaskStages) {
        startPage();
        List<TeamTaskStages> list = teamTaskStagesService.selectTeamTaskStagesList(teamTaskStages);
        List<String> codes = new ArrayList<>();
        List<String> finalCodes = codes;
        list.forEach(item ->{
            List<TeamTask> taskList = item.getTaskList();
            taskList.forEach(task ->{
                task.setExecuteName(ExecuteStatus.getText(task.getExecuteStatus()));
                finalCodes.add(task.getAssignTo());
            });
        });
        codes = finalCodes.stream().distinct().collect(Collectors.toList());
        if(codes.size() > 0){
            QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("user_openid",codes);
            List<EnterpriseUser> userList = enterpriseUserService.list(queryWrapper);
            list.forEach(item ->{
                List<TeamTask> taskList = item.getTaskList();
                taskList.forEach(task ->{
                    if(StrUtil.isNotEmpty(task.getAssignTo())){
                        userList.forEach(user -> {
                            if(user.getUserOpenid().equals(task.getAssignTo())){
                                task.setAssignName(user.getUserFullName());
                            }
                        });
                    }
                });
            });
        }
        return getDataTable(list);
    }

    /**
     * 获取任务列表表详情
     */
    @ApiOperation("获取任务列表表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(teamTaskStagesService.getById(id));
    }

    /**
     * 新增任务列表表
     */
    @ApiOperation("新增任务列表表")
    @PostMapping
    public AjaxResult add(@RequestBody TeamTaskStages teamTaskStages) {
        return toBooleanAjax(teamTaskStagesService.save(teamTaskStages));
    }

    /**
     * 修改任务列表表
     */
    @ApiOperation("修改任务列表表")
    @PutMapping
    public AjaxResult edit(@RequestBody List<TeamTaskStages> teamTaskStages) {
        return toBooleanAjax(teamTaskStagesService.updateBatchById(teamTaskStages));
    }

    /**
     * 删除任务列表表
     */
    @ApiOperation("删除任务列表表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(teamTaskStagesService.removeByIds(Arrays.asList(ids)));
    }

}
