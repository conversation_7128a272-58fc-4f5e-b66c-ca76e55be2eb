package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipMaintTaskItemDetail;

import java.util.List;

/**
 * 维保任务管理-维保项目-明细(EquipMaintTaskItemDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:33
 */
public interface IEquipMaintTaskItemDetailService extends IService<EquipMaintTaskItemDetail> {

    /**
     * 查询多条数据
     *
     * @param equipMaintTaskItemDetail 对象信息
     * @return 对象列表
     */
    List<EquipMaintTaskItemDetail> selectEquipMaintTaskItemDetailList(EquipMaintTaskItemDetail equipMaintTaskItemDetail);


    EquipMaintTaskItemDetail getDetailAndRecord(Integer id);
}
