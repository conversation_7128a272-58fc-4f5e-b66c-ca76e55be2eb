<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.MesYieldMapper">

    <resultMap type="com.boyo.mes.entity.MesYield" id="MesYieldResult">
        <result property="id" column="id"/>
        <result property="rq" column="rq"/>
        <result property="recordId" column="record_id"/>
        <result property="shift" column="shift"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="productionId" column="production_id"/>
        <result property="yieldCount" column="yield_count"/>
        <result property="inCount" column="in_count"/>
        <result property="scrapCount" column="scrap_count"/>
        <result property="produtionName" column="prodution_name"/>
        <result property="productionCode" column="production_code"/>
        <result property="equipmentName" column="equipment_name"/>
        <result property="uuid" column="uuid"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectMesYieldList" parameterType="com.boyo.mes.entity.MesYield" resultMap="MesYieldResult">
        select
        id, rq, record_id, shift, equipment_id, production_id, yield_count
        from t_mes_yield
        <where>
            <if test="rq != null and rq != ''">
                and rq = #{rq}
            </if>
            <if test="recordId != null">
                and record_id = #{recordId}
            </if>
            <if test="shift != null and shift != ''">
                and shift = #{shift}
            </if>
            <if test="equipmentId != null">
                and equipment_id = #{equipmentId}
            </if>
            <if test="productionId != null">
                and production_id = #{productionId}
            </if>
            <if test="yieldCount != null">
                and yield_count = #{yieldCount}
            </if>
        </where>
    </select>

    <select id="listLessYield" resultMap="MesYieldResult">
        select *
        from (select p.materiel_name as prodution_name,p.materiel_code as production_code,
        p.id as production_id,
        p.id,
        t1.yield_count,
        IFNULL(t2.in_count, 0) as in_count,
        IFNULL(t2.scrap_count, 0) as scrap_count
        from (select * from t_material where materiel_type = 'type_product') p
        left join (select production_id, sum(yield_count) as yield_count
        from t_mes_yield
        group by production_id) t1
        on p.id = t1.production_id
        left join (select production_id,
        sum(in_count) as in_count,
        sum(scrap_count) as scrap_count
        from t_mes_warehousing
        group by production_id) t2 on t1.production_id = t2.production_id) p1
        <where>
            <if test="produtionName != null and produtionName != ''">
                and p1.prodution_name like CONCAT('%',#{produtionName},'%')
            </if>
            <if test="productionCode != null and productionCode != ''">
                and p1.production_code like CONCAT('%',#{productionCode},'%')
            </if>
        </where>
        order by p1.yield_count desc,p1.prodution_name
    </select>
    <select id="listYieldByDevice" resultMap="MesYieldResult">
        select t1.*, t2.materiel_name as prodution_name, t2.materiel_code as production_code, t3.equipment_name
        from (select UUID() as uuid, sum(yield_count) as yield_count, production_id, rq, shift, equipment_id
              from t_mes_yield
              where rq = #{rq}
              group by production_id, rq, shift, equipment_id) t1
                 left join (select * from t_material where materiel_type = 'type_product') t2
                           on t1.production_id = t2.id
                 left join iot_equipment t3 on t1.equipment_id = t3.id
    </select>
</mapper>

