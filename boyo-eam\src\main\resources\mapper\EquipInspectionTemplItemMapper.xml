<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipInspectionTemplItemMapper">

    <resultMap type="com.boyo.eam.domain.EquipInspectionTemplItem" id="EquipInspectionTemplItemResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="equipInspectionTemplOpenid" column="equip_inspection_templ_openid" />
        <result property="item" column="item" />
        <result property="basis" column="basis" />
        <result property="method" column="method" />
        <result property="way" column="way" />
        <result property="useTime" column="use_time" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipInspectionTemplItemList" parameterType="com.boyo.eam.domain.EquipInspectionTemplItem" resultMap="EquipInspectionTemplItemResult">
        select
            EITI.*,
            EIT.cycle as cycle,
            EIT.cycle_unit as cycleUnit,
            EIT.code as spotCode,
            EIT.type as type,
            EL.code as equipCode,
            EL.name as equipName,
            TML.line_openid as lineOpenid,
            TML.line_name as lineName,
            EIT.id as spotId
        from equip_inspection_templ_item EITI left join
        equip_inspection_templ EIT on EITI.equip_inspection_templ_openid=EIT.openid left join
        equip_ledger EL on EIT.equip_ledger_openid=El.openid left join
        t_model_line TML on EIT.line_openid=TML.line_openid
        <where>
            <if test="openid != null and openid != ''">
                and EITI.openid = #{openid}
            </if>
            <if test="equipInspectionTemplOpenid != null and equipInspectionTemplOpenid != ''">
                and EITI.equip_inspection_templ_openid = #{equipInspectionTemplOpenid}
            </if>
            <if test="item != null and item != ''">
                and EITI.item = #{item}
            </if>
            <if test="basis != null and basis != ''">
                and EITI.basis = #{basis}
            </if>
            <if test="method != null and method != ''">
                and EITI.method = #{method}
            </if>
            <if test="way != null and way != ''">
                and EITI.way = #{way}
            </if>
            <if test="createBy != null and createBy != ''">
                and EITI.create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and EITI.create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and EITI.update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and EITI.update_time = #{updateTime}
            </if>
            <if test="equipName != null and equipName != ''">
                and EL.name like CONCAT('%',#{equipName},'%')
            </if>
            <if test="equipCode != null and equipCode != ''">
                and EL.code like CONCAT('%',#{equipCode},'%')
            </if>
            <if test="lineOpenid != null and lineOpenid != ''">
                and TML.line_openid = #{lineOpenid}
            </if>
            <if test="lineName != null and lineName != ''">
                and TML.line_name like CONCAT('%',#{lineName},'%')
            </if>
            <if test="type != null and type != ''">
                and EIT.type = #{type}
            </if>
        </where>
    </select>
</mapper>

