package com.boyo.project.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务列表模板表(TeamTaskStagesTemplate)实体类
 *
 * <AUTHOR>
 * @since 2022-02-08 20:50:50
 */
@Data
@TableName(value = "team_task_stages_template")
public class TeamTaskStagesTemplate implements Serializable {
    private static final long serialVersionUID = 128922291913667864L;

    @TableId
    private Integer id;

    /**
    * 类型名称
    */
    @TableField(value="name")
    private String name;
    /**
    * 项目id
    */
    @TableField(value="project_template_code")
    private String projectTemplateCode;

    @TableField(value="create_time")
    private String createTime;

    @TableField(value="sort")
    private Integer sort;
    /**
    * 编号
    */
    @TableField(value="code")
    private String code;

}
