package com.boyo.master.domain;

import com.boyo.common.annotation.Excel;
import com.boyo.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 人员工资对象 mes_person_wage
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
public class MesPersonWage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 月份 */
    @Excel(name = "月份")
    private String monthTime;

    /** 人员编码 */
    @Excel(name = "人员编码")
    private String personCode;

    /** 人员名称 */
    @Excel(name = "人员名称")
    private String personName;

    /** 班组 */
    @Excel(name = "班组")
    private String teamName;

    /** 计价工资 */
    @Excel(name = "计价工资")
    private Long pieceCountWage;

    /** 工时工资 */
    @Excel(name = "工时工资")
    private Long pieceHourWage;

    /** 奖罚 */
    @Excel(name = "奖罚")
    private Long shouldGrantWage;

    /** 应发工资合计 */
    @Excel(name = "应发工资合计")
    private Long confirmWage;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }
    public void setMonthTime(String monthTime) 
    {
        this.monthTime = monthTime;
    }

    public String getMonthTime() 
    {
        return monthTime;
    }
    public void setPersonCode(String personCode) 
    {
        this.personCode = personCode;
    }

    public String getPersonCode() 
    {
        return personCode;
    }
    public void setPersonName(String personName) 
    {
        this.personName = personName;
    }

    public String getPersonName() 
    {
        return personName;
    }
    public void setTeamName(String teamName) 
    {
        this.teamName = teamName;
    }

    public String getTeamName() 
    {
        return teamName;
    }
    public void setPieceCountWage(Long pieceCountWage) 
    {
        this.pieceCountWage = pieceCountWage;
    }

    public Long getPieceCountWage() 
    {
        return pieceCountWage;
    }
    public void setPieceHourWage(Long pieceHourWage) 
    {
        this.pieceHourWage = pieceHourWage;
    }

    public Long getPieceHourWage() 
    {
        return pieceHourWage;
    }
    public void setShouldGrantWage(Long shouldGrantWage) 
    {
        this.shouldGrantWage = shouldGrantWage;
    }

    public Long getShouldGrantWage() 
    {
        return shouldGrantWage;
    }
    public void setConfirmWage(Long confirmWage) 
    {
        this.confirmWage = confirmWage;
    }

    public Long getConfirmWage() 
    {
        return confirmWage;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("monthTime", getMonthTime())
            .append("personCode", getPersonCode())
            .append("personName", getPersonName())
            .append("teamName", getTeamName())
            .append("pieceCountWage", getPieceCountWage())
            .append("pieceHourWage", getPieceHourWage())
            .append("shouldGrantWage", getShouldGrantWage())
            .append("confirmWage", getConfirmWage())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
