<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.project.mapper.TeamTaskStagesMapper">

    <resultMap type="com.boyo.project.entity.TeamTaskStages" id="TeamTaskStagesResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="projectCode" column="project_code"/>
        <result property="sort" column="sort"/>
        <result property="description" column="description"/>
        <result property="createTime" column="create_time"/>
        <result property="code" column="code"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectTeamTaskStagesList" parameterType="com.boyo.project.entity.TeamTaskStages"
            resultMap="TeamTaskStagesResult">
        select
        id, name, project_code, sort, description, create_time, code, deleted
        from team_task_stages
        <where>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="projectCode != null and projectCode != ''">
                and project_code = #{projectCode}
            </if>
            <if test="sort != null">
                and sort = #{sort}
            </if>
            <if test="description != null and description != ''">
                and description = #{description}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
        </where>
        order by sort asc
    </select>
</mapper>

