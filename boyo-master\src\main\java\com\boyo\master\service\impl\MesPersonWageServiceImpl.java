package com.boyo.master.service.impl;

import com.boyo.common.utils.DateUtils;
import com.boyo.master.domain.MesPersonWage;
import com.boyo.master.mapper.MesPersonWageMapper;
import com.boyo.master.service.IMesPersonWageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 人员工资Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
@Service
public class MesPersonWageServiceImpl implements IMesPersonWageService
{
    @Autowired
    private MesPersonWageMapper mesPersonWageMapper;

    /**
     * 查询人员工资
     * 
     * @param id 人员工资主键
     * @return 人员工资
     */
    @Override
    public MesPersonWage selectMesPersonWageById(String id)
    {
        return mesPersonWageMapper.selectMesPersonWageById(id);
    }

    /**
     * 查询人员工资列表
     * 
     * @param mesPersonWage 人员工资
     * @return 人员工资
     */
    @Override
    public List<MesPersonWage> selectMesPersonWageList(MesPersonWage mesPersonWage)
    {
        return mesPersonWageMapper.selectMesPersonWageList(mesPersonWage);
    }

    /**
     * 新增人员工资
     * 
     * @param mesPersonWage 人员工资
     * @return 结果
     */
    @Override
    public int insertMesPersonWage(MesPersonWage mesPersonWage)
    {
        mesPersonWage.setCreateTime(DateUtils.getNowDate());
        return mesPersonWageMapper.insertMesPersonWage(mesPersonWage);
    }

    /**
     * 修改人员工资
     * 
     * @param mesPersonWage 人员工资
     * @return 结果
     */
    @Override
    public int updateMesPersonWage(MesPersonWage mesPersonWage)
    {
        mesPersonWage.setUpdateTime(DateUtils.getNowDate());
        return mesPersonWageMapper.updateMesPersonWage(mesPersonWage);
    }

    /**
     * 批量删除人员工资
     * 
     * @param ids 需要删除的人员工资主键
     * @return 结果
     */
    @Override
    public int deleteMesPersonWageByIds(Long[] ids)
    {
        return mesPersonWageMapper.deleteMesPersonWageByIds(ids);
    }

    /**
     * 删除人员工资信息
     * 
     * @param id 人员工资主键
     * @return 结果
     */
    @Override
    public int deleteMesPersonWageById(Long id)
    {
        return mesPersonWageMapper.deleteMesPersonWageById(id);
    }
}
