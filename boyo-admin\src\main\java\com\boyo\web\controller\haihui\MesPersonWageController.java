//package com.boyo.web.controller.haihui;
//
//import com.alibaba.fastjson.JSONObject;
//import com.boyo.common.core.controller.BaseController;
//import com.boyo.common.core.domain.AjaxResult;
//import com.boyo.common.core.page.TableDataInfo;
//import com.boyo.master.domain.MesPersonWage;
//import com.boyo.master.service.IMesPersonWageService;
//import com.boyo.web.lysso.controller.HttpRequest;
//import com.boyo.web.lysso.controller.HttpResponse;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//
///**
// * 人员工资Controller
// *
// * <AUTHOR>
// * @date 2025-02-24
// */
//@RestController
//@RequestMapping("/haihui/haihui/wage")
//public class MesPersonWageController extends BaseController
//{
//    private static final String MES_BASEURL = "192.168.222.33:9114/api/outApi/listMesProductionSchedule";
//    @Autowired
//    private IMesPersonWageService mesPersonWageService;
//
//    /**
//     * 查询人员工资列表
//     */
//    @GetMapping("/list")
//    public TableDataInfo list(MesPersonWage mesPersonWage)
//    {
//        startPage();
//        List<MesPersonWage> list = mesPersonWageService.selectMesPersonWageList(mesPersonWage);
//        return getDataTable(list);
//    }
//
////    /**
////     * 导出人员工资列表
////     */
////    @PreAuthorize("@ss.hasPermi('haihui:haihui:export')")
////    @PostMapping("/export")
////    public void export(HttpServletResponse response, MesPersonWage mesPersonWage)
////    {
////        List<MesPersonWage> list = mesPersonWageService.selectMesPersonWageList(mesPersonWage);
////        ExcelUtil<MesPersonWage> util = new ExcelUtil<MesPersonWage>(MesPersonWage.class);
////        util.exportExcel(response, list, "人员工资数据");
////    }
//
//    /**
//     * 获取人员工资详细信息
//     */
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return AjaxResult.success(mesPersonWageService.selectMesPersonWageById(id));
//    }
//
//    /**
//     * 新增人员工资
//     */
//    @PostMapping
//    public AjaxResult add(@RequestBody MesPersonWage mesPersonWage)
//    {
//        return toAjax(mesPersonWageService.insertMesPersonWage(mesPersonWage));
//    }
//
//    /**
//     * 修改人员工资
//     */
//    @PutMapping
//    public AjaxResult edit(@RequestBody MesPersonWage mesPersonWage)
//    {
//        return toAjax(mesPersonWageService.updateMesPersonWage(mesPersonWage));
//    }
//
//    /**
//     * 删除人员工资
//     */
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(mesPersonWageService.deleteMesPersonWageByIds(ids));
//    }
//
//
//
////    @Scheduled(cron = "0 30 12,00 * * ?")//每天12：30和00：30执行各一次
//    public AjaxResult getWageData() {
//        String url =  MES_BASEURL;
//        try {
//            HttpRequest request1 = HttpRequest.builder()
//                    .setUrl(url)
//                    .setMethod(HttpRequest.Method.GET)
//                    .addHeader("Content-Type", "application/json; charset=UTF-8")
//                    .addHeader("Accept", "*/*")
//                    .addHeader("Connection", "keep-alive");
//            HttpResponse response = request1.get();
//            JSONObject jsonObject = JSONObject.parseObject(response.getText());
//
//            return AjaxResult.success(jsonObject);
//
//        } catch (Exception e) {
//            AjaxResult.error(e.getMessage());
//        }
//        return null;
//
//
//    }
//
//
//    private int saveData(JSONObject jsonObject){
//
//        MesPersonWage mesPersonWage = new MesPersonWage();
//        mesPersonWage.setPersonCode(jsonObject.getString("personCode"));
//        return 0;
//    }
//
//}
