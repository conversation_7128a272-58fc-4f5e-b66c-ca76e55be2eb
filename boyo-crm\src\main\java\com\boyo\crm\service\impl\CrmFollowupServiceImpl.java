package com.boyo.crm.service.impl;

import com.boyo.common.annotation.DataScope;
import com.boyo.crm.entity.*;
import com.boyo.crm.mapper.CrmClueMapper;
import com.boyo.crm.service.*;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.mapper.CrmFollowupMapper;

import java.util.List;

/**
 * (CrmFollowup)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-30 10:24:43
 */
@Service("crmFollowupService")
@AllArgsConstructor
public class CrmFollowupServiceImpl extends ServiceImpl<CrmFollowupMapper, CrmFollowup> implements ICrmFollowupService {
    private final CrmFollowupMapper crmFollowupMapper;
    private final ICrmClueService crmClueService;
    private final ICrmCustomerService customerService;
    private final ICrmBusinessService businessService;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
//    @DataScope(columns = "owner_user_id,create_user_id")
    public List<CrmFollowup> selectCrmFollowupList(CrmFollowup crmFollowup) {
        return crmFollowupMapper.selectCrmFollowupList(crmFollowup);
    }

    @Override
    public boolean save(CrmFollowup entity) {
        switch (entity.getActionType()){
//            线索
            case "CLUE":
                CrmClue clue = crmClueService.getById(entity.getActionId());
                clue.setNextTime(entity.getNextTime());
                crmClueService.updateById(clue);
                break;
            case "CUSTOMER":
                CrmCustomer customer = customerService.getById(entity.getActionId());
                customer.setNextTime(entity.getNextTime());
                customerService.updateById(customer);
                break;
            case "BUSINESS":
                CrmBusiness business = businessService.getById(entity.getActionId());
                business.setNextTime(entity.getNextTime());
                businessService.updateById(business);
                break;

            default:
                break;
        }
        return super.save(entity);
    }
}
