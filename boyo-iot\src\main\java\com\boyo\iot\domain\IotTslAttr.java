package com.boyo.iot.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * IoT物模型属性
 * 表名 iot_tsl_attr
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("物模型属性")
@Data
@TableName("iot_tsl_attr")
public class IotTslAttr extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *主键
     */
    @TableId
    private Long id;
    /**
     * 物模型id
     */
    @ApiModelProperty("物模型id")
    @TableField(value = "tsl_id")
    private Long tslId;
    /**
     * 属性中文名称
     */
    @ApiModelProperty("属性中文名称")
    @TableField(value = "attr_name")
    private String attrName;
    /**
     * 属性编码
     */
    @ApiModelProperty("属性编码")
    @TableField(value = "attr_code")
    private String attrCode;
    /**
     * 属性描述
     */
    @ApiModelProperty("属性描述")
    @TableField(value = "attr_desc")
    private String attrDesc;
    /**
     * 数据类型
     */
    @ApiModelProperty("数据类型")
    @TableField(value = "attr_type")
    private String attrType;
    /**
     * 数据单位
     */
    @ApiModelProperty("数据单位")
    @TableField(value = "attr_unit")
    private String attrUnit;

    @TableField(value = "attr_class")
    private String attrClass;

    /**
     * 属性图标
     */
    @TableField(value = "attr_icon")
    private String attrIcon;

    @TableField(value = "attr_multiple")
    private Double attrMultiple;

    /**
     * 序号
     */
    @ApiModelProperty("序号")
    @TableField(value = "attr_order")
    private Integer attrOrder;
    /**
     * 最小值
     */
    @TableField(exist = false)
    private String minVal;
    /**
     * 最大值
     */
    @TableField(exist = false)
    private String maxVal;
    /**
     * 枚举数据类型
     */
    @TableField(exist = false)
    private String enumList;
    /**
     * 数据展示方式 num 数值  line 折线
     */
    @TableField(exist = false)
    private String showType;
    /**
     * 历史数据记录
     */
    @TableField(exist = false)
    private List<HistoryData> historyDataList;

    @TableField(exist = false)
    private String lastVal;

    @TableField(exist = false)
    private String faultVal;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateTime;
}
