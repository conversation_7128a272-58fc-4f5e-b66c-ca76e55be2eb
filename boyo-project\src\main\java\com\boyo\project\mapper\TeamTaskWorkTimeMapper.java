package com.boyo.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.project.entity.TeamTaskWorkTime;
import java.util.List;

/**
 * 任务工时表(TeamTaskWorkTime)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-18 11:23:24
 */
public interface TeamTaskWorkTimeMapper extends BaseMapper<TeamTaskWorkTime>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param teamTaskWorkTime 实例对象
     * @return 对象列表
     */
    List<TeamTaskWorkTime> selectTeamTaskWorkTimeList(TeamTaskWorkTime teamTaskWorkTime);


}

