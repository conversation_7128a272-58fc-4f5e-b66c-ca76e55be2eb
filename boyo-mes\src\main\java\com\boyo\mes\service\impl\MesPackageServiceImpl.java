package com.boyo.mes.service.impl;

import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.MesPackage;
import com.boyo.mes.mapper.MesPackageMapper;
import com.boyo.mes.service.IMesPackageService;

import java.util.Date;
import java.util.List;

/**
 * 打包管理(MesPackage)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-10 15:46:38
 */
@Service("mesPackageService")
@AllArgsConstructor
@Tenant
public class MesPackageServiceImpl extends ServiceImpl<MesPackageMapper, MesPackage> implements IMesPackageService {
    private final MesPackageMapper mesPackageMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<MesPackage> selectMesPackageList(MesPackage mesPackage) {
        return mesPackageMapper.selectMesPackageList(mesPackage);
    }

    @Override
    public boolean save(MesPackage entity) {
        entity.setCreateTime(new Date());
        entity.setCreateBy(SecurityUtils.getUsername());
        return super.save(entity);
    }
}
