package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipLedger;
import com.boyo.eam.domain.EquipMaintTask;
import com.boyo.eam.mapper.EquipLedgerMapper;
import com.boyo.eam.mapper.EquipMaintTaskMapper;
import com.boyo.eam.service.IEquipMaintTaskService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * 维保任务管理(EquipMaintTask)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:32
 */
@Service("EquipMaintTaskService")
@AllArgsConstructor
@Tenant
public class EquipMaintTaskServiceImpl extends ServiceImpl<EquipMaintTaskMapper, EquipMaintTask> implements IEquipMaintTaskService {
    private final EquipMaintTaskMapper equipMaintTaskMapper;
    private final EquipLedgerMapper equipLedgerMapper;
    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipMaintTask> selectEquipMaintTaskList(EquipMaintTask equipMaintTask) {
        List<EquipMaintTask> equipMaintTaskList = equipMaintTaskMapper.selectEquipMaintTaskList(equipMaintTask);

        // 设置设备名称
        List<EquipLedger> equipLedgerList = equipLedgerMapper.selectList(Wrappers.<EquipLedger>lambdaQuery());
        HashMap<Object, EquipLedger> equipMap = new HashMap<>();
        for (EquipLedger equip :equipLedgerList){
            equipMap.put(equip.getOpenid(),equip);
        }
        for (EquipMaintTask emt:equipMaintTaskList){
            String equipOpenids = emt.getEquipLedgerOpenid();
            if(equipOpenids!=null&& !"".equals(equipOpenids)){
                String[] split = equipOpenids.split(",");
                StringBuffer buffer = new StringBuffer();
                for (String equipOpenid :split){
                    EquipLedger equipLedger = equipMap.get(equipOpenid);
                    if (equipLedger!=null){
                        buffer.append(equipLedger.getName()).append(",");
                    }
                }
                buffer.deleteCharAt(buffer.length()-1);
                emt.setEquipNames(buffer.toString());
            }
        }
        return equipMaintTaskList;
    }

}
