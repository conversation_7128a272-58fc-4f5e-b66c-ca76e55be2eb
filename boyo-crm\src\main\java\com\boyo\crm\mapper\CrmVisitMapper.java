package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmVisit;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * 回访表(CrmVisit)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-28 17:06:59
 */
@Tenant
public interface CrmVisitMapper extends BaseMapper<CrmVisit>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmVisit 实例对象
     * @return 对象列表
     */
    List<CrmVisit> selectCrmVisitList(CrmVisit crmVisit);


}

