<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.system.mapper.EnterpriseUserRoleMapper">

    <resultMap type="com.boyo.system.domain.EnterpriseUserRole" id="EnterpriseUserRoleResult">
        <result property="id" column="id"/>
        <result property="userOpenid" column="user_openid"/>
        <result property="roleOpenid" column="role_openid"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="dataScope" column="data_scope" ></result>
    </resultMap>

    <sql id="selectEnterpriseUserRoleVo">
        select id, user_openid, role_openid, create_time, update_time
        from t_enterprise_user_role
    </sql>

    <select id="selectEnterpriseUserRoleList" parameterType="com.boyo.system.domain.EnterpriseUserRole"
            resultMap="EnterpriseUserRoleResult">
        select t1.*,t2.data_scope from(SELECT * from t_enterprise_user_role
        <where>
            <if test="userOpenid != null  and userOpenid != ''">
                and user_openid = #{userOpenid}
            </if>
            <if test="roleOpenid != null  and roleOpenid != ''">
                and role_openid = #{roleOpenid}
            </if>
        </where>
        ) t1 left join t_enterprise_role t2 on t1.role_openid = t2.role_openid
    </select>
</mapper>
