package com.boyo.project.entity;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目类型表(TeamProjectemplate)实体类
 *
 * <AUTHOR>
 * @since 2022-02-08 20:50:45
 */
@Data
@TableName(value = "team_project_template")
public class TeamProjectemplate implements Serializable {
    private static final long serialVersionUID = -73112184044257584L;

    @TableId
    private Integer id;

    /**
    * 类型名称
    */
    @TableField(value="name")
    private String name;
    /**
    * 备注
    */
    @TableField(value="description")
    private String description;

    @TableField(value="sort")
    private Integer sort;

    @TableField(value="create_time",fill = FieldFill.INSERT)
    private String createTime;
    /**
    * 编号
    */
    @TableField(value="code")
    private String code;
    /**
    * 组织id
    */
    @TableField(value="organization_code")
    private String organizationCode;
    /**
    * 封面
    */
    @TableField(value="cover")
    private String cover;
    /**
    * 创建人
    */
    @TableField(value="member_code")
    private String memberCode;
    /**
    * 系统默认
    */
    @TableField(value="is_system")
    private Integer isSystem;

}
