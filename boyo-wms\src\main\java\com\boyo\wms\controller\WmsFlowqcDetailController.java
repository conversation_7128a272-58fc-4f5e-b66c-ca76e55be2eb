package com.boyo.wms.controller;

import com.boyo.wms.entity.WmsFlowqcDetail;
import com.boyo.wms.service.IWmsFlowqcDetailService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * (WmsFlowqcDetail)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-10 15:12:10
 */
@Api("")
@RestController
@RequestMapping("/wms/wmsFlowqcDetail")
@AllArgsConstructor
public class WmsFlowqcDetailController extends BaseController{
    /**
     * 服务对象
     */
    private final IWmsFlowqcDetailService wmsFlowqcDetailService;

    /**
     * 查询列表
     *
     */
    @ApiOperation("查询列表")
    @GetMapping("/list")
    public TableDataInfo list(WmsFlowqcDetail wmsFlowqcDetail) {
        startPage();
        List<WmsFlowqcDetail> list = wmsFlowqcDetailService.selectWmsFlowqcDetailList(wmsFlowqcDetail);
        return getDataTable(list);
    }
    
    /**
     * 获取详情
     */
    @ApiOperation("获取详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(wmsFlowqcDetailService.getById(id));
    }

    /**
     * 新增
     */
    @ApiOperation("新增")
    @PostMapping
    public AjaxResult add(@RequestBody WmsFlowqcDetail wmsFlowqcDetail) {
        return toBooleanAjax(wmsFlowqcDetailService.save(wmsFlowqcDetail));
    }

    /**
     * 修改
     */
    @ApiOperation("修改")
    @PutMapping
    public AjaxResult edit(@RequestBody WmsFlowqcDetail wmsFlowqcDetail) {
        return toBooleanAjax(wmsFlowqcDetailService.updateById(wmsFlowqcDetail));
    }

    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(wmsFlowqcDetailService.removeByIds(Arrays.asList(ids)));
    }

}
