package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备台账属性表(EquipLedgerProperty)实体类
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Data
@TableName(value = "equip_ledger_property")
public class EquipLedgerProperty implements Serializable {
    private static final long serialVersionUID = -64025489143211508L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 设备台账openid：对应t_equip_ledger中的openid
    */
    @TableField(value="equip_ledger_openid")
    private String equipLedgerOpenid;
    /**
    * 属性名称
    */
    @TableField(value="name")
    private String name;
    /**
    * 属性值
    */
    @TableField(value="value")
    private String value;
    /**
    * 属性描述
    */
    @TableField(value="remark")
    private String remark;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

}
