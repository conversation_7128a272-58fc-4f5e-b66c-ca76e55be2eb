import request from '@/utils/request'

const prefix = ''

// 查询项目日志表(TeamProjeclog)列表
export function listTeamProjeclog(query) {
  return request({
    url: prefix + '/teamProjeclog/list',
    method: 'get',
    params: query,
  })
}

// 查询项目日志表(TeamProjeclog)详细
export function getTeamProjeclog(id) {
  return request({
    url: prefix + '/teamProjeclog/' + id,
    method: 'get',
  })
}

// 新增项目日志表(TeamProjeclog)
export function addTeamProjeclog(data) {
  return request({
    url: prefix + '/teamProjeclog',
    method: 'post',
    data: data,
  })
}

// 修改项目日志表(TeamProjeclog)
export function updateTeamProjeclog(data) {
  return request({
    url: prefix + '/teamProjeclog',
    method: 'put',
    data: data,
  })
}

// 删除项目日志表(TeamProjeclog)
export function delTeamProjeclog(id) {
  return request({
    url: prefix + '/teamProjeclog/' + id,
    method: 'delete',
  })
}
