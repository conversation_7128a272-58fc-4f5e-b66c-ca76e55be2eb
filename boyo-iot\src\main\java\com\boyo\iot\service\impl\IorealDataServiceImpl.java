package com.boyo.iot.service.impl;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.iot.entity.IorealData;
import com.boyo.iot.mapper.IorealDataMapper;
import com.boyo.iot.service.IIorealDataService;
import java.util.List;

/**
 * IoT实时数据(IorealData)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-31 17:07:23
 */
@Service("iorealDataService")
@AllArgsConstructor
public class IorealDataServiceImpl extends ServiceImpl<IorealDataMapper, IorealData> implements IIorealDataService {
    private final IorealDataMapper iorealDataMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<IorealData> selectIorealDataList(IorealData iorealData) {
        return iorealDataMapper.selectIorealDataList(iorealData);
    }

}
