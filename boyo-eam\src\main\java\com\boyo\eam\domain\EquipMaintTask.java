package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 维保任务管理(EquipMaintTask)实体类
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:31
 */
@Data
@TableName(value = "equip_maint_task")
public class EquipMaintTask implements Serializable {
    private static final long serialVersionUID = 485530094907439125L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 维保计划单号
    */
    @TableField(value="plan_code")
    private String planCode;
    /**
    * 计划日期
    */
    @TableField(value="date")
    private Date date;
    /**
    * 设备,关联equip_ledger表的opnenid
    */
    @TableField(value="equip_ledger_openid")
    private String equipLedgerOpenid;
    /**
    * 维保模板：关联equip_maint_templ表的openid
    */
    @TableField(value="equip_maint_templ_openid")
    private String equipMaintTemplOpenid;
    /**
    * 维修人员：关联sys_user表的id
    */
    @TableField(value="sys_user_id")
    private String sysUserId;
    /**
    * 周期：0仅此一次，1每周，2每月，3每隔N天（若从维保任务管理中进行添加，则默认为0仅此一次）
    */
    @TableField(value="cycle")
    private String cycle;
    /**
    * 间隔天数
    */
    @TableField(value="day")
    private Integer day;
    /**
    * 任务状态
    */
    @TableField(value="state")
    private String state;
    /**
    * 派工单号
    */
    @TableField(value="task_code")
    private String taskCode;
    /**
     * 产线
     */
    @TableField(value="line_openid")
    private String lineOpenid;
    /**
     * 任务开始时间
     */
    @TableField(value="task_begin_date")
    private Date taskBeginDate;
    /**
     * 任务结束时间
     */
    @TableField(value="task_end_date")
    private Date taskEndDate;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

    /** 额外字段 */
    //子模块
    @TableField(exist = false)
    private List<EquipMaintTaskItem>  itemList;

    //返回字段用
    @TableField(exist = false)
    private String equipNames;
    @TableField(exist = false)
    private String staffName;
    @TableField(exist = false)
    private String lineName;

    //查询条件和新增时用
    @TableField(exist = false)
    private Date beginDate;//开始日期
    @TableField(exist = false)
    private Date endDate;//结束日期
    @TableField(exist = false)
    private Date createEndDate;//和上面的 date 字段一起使用 ，此处为日期上限

    //移动端搜索条件
    @TableField(exist = false)
    private String taskContent;//任务内容：设备名称+设备编码+派工单号
}
