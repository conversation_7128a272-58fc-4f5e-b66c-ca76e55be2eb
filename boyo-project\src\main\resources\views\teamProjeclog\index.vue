<template>
  <base-page :config="config">
    <span slot="operation" slot-scope="text, record">
      <a @click="$refs.createForm.handleUpdate($event,record.id)">
      <a-icon type="edit" />{{ $t('app.global.edit') }}
      </a>
      <a-divider type="vertical" />
      <a @click="handleDelete(record.id)"> <a-icon type="delete" />{{ $t('app.global.delete') }}</a>
    </span>
    <!-- 弹窗 -->
    <create-form ref="createForm" :statusOptions="statusOptions" @ok="getList" />
  </base-page>
</template>

<script>
import CreateForm from './modules/CreateForm'
import { delTeamProjeclog, listTeamProjeclog } from '@/api/teamProjeclog'

export default {
  components: {
    CreateForm,
  },
  data() {
    return {
      // 页面加载状态
      loading: false,
      // 数据列表
      list: [],
      // 表格数据总数
      total: 0,
      // 状态数据字典
      statusOptions: [],
    }
  },
  async created() {
    this.getList()
  },
  computed: {
    config() {
      return {
        loading: this.loading,
        query: {
          onQuery: this.getList,
          items: [
                        { label: '', name: 'code' },
                        { label: '操作人id', name: 'memberCode' },
                        { label: '操作内容', name: 'content' },
                        { label: '', name: 'remark' },
                        { label: '操作类型', name: 'type' },
                        { label: '添加时间', name: 'createTime' },
                        { label: '任务id', name: 'sourceCode' },
                        { label: '场景类型', name: 'actionType' },
                        { label: '', name: 'toMemberCode' },
                        { label: '是否评论，0：否', name: 'isComment' },
                        { label: '', name: 'projectCode' },
                        { label: '', name: 'icon' },
                        { label: '是否机器人', name: 'isRobot' },
                      ],
        },
        action: {
          add: () => this.$refs.createForm.handleAdd(),
          delete: this.handleDelete,
        },
        table: {
          total: this.total,
          list: this.list,
          columns: [
               { 
                title: '', 
                dataIndex: 'code',
                align: 'center',
            },
                { 
                title: '操作人id', 
                dataIndex: 'memberCode',
                align: 'center',
            },
                { 
                title: '操作内容', 
                dataIndex: 'content',
                align: 'center',
            },
                { 
                title: '', 
                dataIndex: 'remark',
                align: 'center',
            },
                { 
                title: '操作类型', 
                dataIndex: 'type',
                align: 'center',
            },
                { 
                title: '添加时间', 
                dataIndex: 'createTime',
                align: 'center',
            },
                { 
                title: '任务id', 
                dataIndex: 'sourceCode',
                align: 'center',
            },
                { 
                title: '场景类型', 
                dataIndex: 'actionType',
                align: 'center',
            },
                { 
                title: '', 
                dataIndex: 'toMemberCode',
                align: 'center',
            },
                { 
                title: '是否评论，0：否', 
                dataIndex: 'isComment',
                align: 'center',
            },
                { 
                title: '', 
                dataIndex: 'projectCode',
                align: 'center',
            },
                { 
                title: '', 
                dataIndex: 'icon',
                align: 'center',
            },
                { 
                title: '是否机器人', 
                dataIndex: 'isRobot',
                align: 'center',
            },
                        {
              title: this.$t('app.global.operation'),
              dataIndex: 'operation',
              scopedSlots: { customRender: 'operation' },
              align: 'center',
            },
          ],
        },
      }
    },
  },
  methods: {
    /**
     * 查询项目日志表列表
     */
    async getList(queryParam) {
      this.loading = true
      if (queryParam !== undefined) {
        this.queryParam = queryParam
      }
      this.loading = true
      const response = await listTeamProjeclog(this.queryParam)
      this.list = response.rows
      this.total = response.total
      this.loading = false
    },
    /**
     * 删除按钮操作
     */
    handleDelete(id) {
      
      const ids = id || this.ids
      this.$alert.confirm({
        content: this.$t('app.global.delete.content'),
        onOk: async () => {
        await delTeamProjeclog(ids)
        this.getList()
        this.$alert.success(this.$t('app.global.delete.success'))
        },
      })
    },
  },
}
</script>
}
