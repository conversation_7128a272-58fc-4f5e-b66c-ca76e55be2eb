package com.boyo.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.mes.entity.MesModulRecord;
import com.boyo.mes.vo.ModuleRecordVO;

import java.util.List;

/**
 * (MesModulRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
public interface MesModulRecordMapper extends BaseMapper<MesModulRecord>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param mesModulRecord 实例对象
     * @return 对象列表
     */
    List<MesModulRecord> selectMesModulRecordList(MesModulRecord mesModulRecord);

    List<ModuleRecordVO> listCurrentModule();

    List<MesModulRecord> listCurrentModule1(MesModulRecord mesModul);
}

