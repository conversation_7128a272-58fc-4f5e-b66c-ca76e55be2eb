package com.boyo.iot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.domain.IotTslAttr;
import com.boyo.iot.mapper.IotTslAttrMapper;
import com.boyo.iot.service.IIotTslAttrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * IoT物模型属性Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@Tenant
public class IotTslAttrServiceImpl extends ServiceImpl<IotTslAttrMapper, IotTslAttr> implements IIotTslAttrService {
    private final IotTslAttrMapper iotTslAttrMapper;

    @Autowired
    public IotTslAttrServiceImpl(IotTslAttrMapper iotTslAttrMapper) {
        this.iotTslAttrMapper = iotTslAttrMapper;
    }


    /**
     * 查询IoT物模型属性列表
     *
     * @param iotTslAttr IoT物模型属性
     * @return IotTslAttr 列表
     */
    @Override
    public List<IotTslAttr> selectIotTslAttrList(IotTslAttr iotTslAttr) {
        return iotTslAttrMapper.selectIotTslAttrList(iotTslAttr);
    }

    @Override
    public List<IotTslAttr> listAttr(Long tslId) {
        QueryWrapper<IotTslAttr> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tsl_id", tslId);
        return iotTslAttrMapper.selectList(queryWrapper);
    }
}
