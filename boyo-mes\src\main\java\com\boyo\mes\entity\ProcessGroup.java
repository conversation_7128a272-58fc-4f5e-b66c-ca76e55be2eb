package com.boyo.mes.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工序组(ProcessGroup)实体类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Data
@TableName(value = "t_process_group")
public class ProcessGroup implements Serializable {
    private static final long serialVersionUID = 553172601903099776L;
            
    @TableId
    private Integer id;
    
    /**
    * 工序组名称
    */
    @TableField(value="group_name")
    private String groupName;
    /**
    * 工序组状态 0：停用 1：启用
    */
    @TableField(value="group_status")
    private String groupStatus;

}
