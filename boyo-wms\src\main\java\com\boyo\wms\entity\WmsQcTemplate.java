package com.boyo.wms.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Wms质检模板(WmsQcTemplate)实体类
 *
 * <AUTHOR>
 * @since 2022-03-08 15:21:00
 */
@Data
@TableName(value = "t_wms_qc_template")
public class WmsQcTemplate implements Serializable {
    private static final long serialVersionUID = -26421051822654871L;
        /**
    * 主键
    */    
    @TableId
    private Integer id;
    
    /**
    * 模板编码
    */
    @TableField(value="template_code")
    private String templateCode;
    /**
    * 模板名称
    */
    @TableField(value="template_name")
    private String templateName;
    /**
    * 模板状态 0：停用 1：启用
    */
    @TableField(value="status")
    private String status;
    /**
    * 创建时间
    */
    @TableField(value="create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 创建人
    */
    @TableField(value="create_user",fill = FieldFill.INSERT)
    private String createUser;

}
