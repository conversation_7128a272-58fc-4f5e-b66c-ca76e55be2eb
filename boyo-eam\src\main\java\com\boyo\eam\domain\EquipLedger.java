package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备台账(EquipLedger)实体类
 *
 * <AUTHOR>
 * @since 2021-11-02 18:07:57
 */
@Data
@TableName(value = "equip_ledger")
public class EquipLedger implements Serializable {
    private static final long serialVersionUID = 482048003535317914L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 父设备openid
    */
    @TableField(value="parent_openid")
    private String parentOpenid;
    /**
    * 设备编码
    */
    @TableField(value="code")
    private String code;
    /**
    * 设备类型openid：对应t_equip_type中的openid
    */
    @TableField(value="equip_type_openid")
    private String equipTypeOpenid;
    /**
    * 设备类型openid：对应t_equip_type中的openid
    */
    @TableField(value="type")
    private String type;
    /**
    * 设备类型openid：对应t_equip_type中的openid
    */
    @TableField(value="workshop_section_code")
    private String workshopSectionCode;
    /**
    * 设备名称
    */
    @TableField(value="name")
    private String name;
    /**
    * 设备状态openid：对应t_equip_state中的opneid
    */
    @TableField(value="equip_state_openid")
    private String equipStateOpenid;
    /**
    * 规格型号
    */
    @TableField(value="specifications")
    private String specifications;
    /**
    * 工厂
    */
    @TableField(value="factory_openid")
    private String factoryOpenid;
    /**
    * 车间
    */
    @TableField(value="workshop_openid")
    private String workshopOpenid;
    /**
    * 安装位置
    */
    @TableField(value="location")
    private String location;
    /**
    * 投用时间
    */
    @TableField(value="use_time")
    private Date useTime;
    /**
    * 设备图片
    */
    @TableField(value="img_url")
    private String imgUrl;
    /**
    * 是否定检，0否，1是
    */
    @TableField(value="is_check")
    private Integer isCheck;
    /**
    * 物模型
    */
    @TableField(value="model_openid")
    private String modelOpenid;
    /**
    * 生产厂家
    */
    @TableField(value="supplier_openid")
    private String supplierOpenid;
    /**
    * 出厂编码
    */
    @TableField(value="out_factory_code")
    private String outFactoryCode;
    /**
    * 出厂时间
    */
    @TableField(value="out_factory_time")
    private Date outFactoryTime;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;


    /** 额外字段 */
    @TableField(exist = false)
    private List<EquipLedgerProperty> properties;//属性

    @TableField(exist = false)
    private String sparePartStr;//部件，逗号隔开

    @TableField(exist = false)
    private String equipTypeName;//设备类型

    @TableField(exist = false)
    private String equipStateName;//设备状态

    @TableField(exist = false)
    private String factoryName;//工厂

    @TableField(exist = false)
    private String workshopName;//车间

    @TableField(exist = false)
    private String modelName;//物模型

    @TableField(exist = false)
    private List<EquipLedger> children;

    @TableField(exist = false)
    private List<Map<String,Object>> statistics;//统计数据
    /** 额外字段 */
}
