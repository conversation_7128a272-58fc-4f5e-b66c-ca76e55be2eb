package com.boyo.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.mes.entity.ProcessGroup;
import java.util.List;

/**
 * 工序组(ProcessGroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
public interface ProcessGroupMapper extends BaseMapper<ProcessGroup>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param processGroup 实例对象
     * @return 对象列表
     */
    List<ProcessGroup> selectProcessGroupList(ProcessGroup processGroup);


}

