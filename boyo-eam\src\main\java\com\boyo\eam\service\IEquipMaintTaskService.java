package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipMaintTask;

import java.util.List;

/**
 * 维保任务管理(EquipMaintTask)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:32
 */
public interface IEquipMaintTaskService extends IService<EquipMaintTask> {

    /**
     * 查询多条数据
     *
     * @param equipMaintTask 对象信息
     * @return 对象列表
     */
    List<EquipMaintTask> selectEquipMaintTaskList(EquipMaintTask equipMaintTask);


}
