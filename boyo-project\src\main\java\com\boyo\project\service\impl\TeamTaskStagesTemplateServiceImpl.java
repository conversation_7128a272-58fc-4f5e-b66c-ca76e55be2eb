package com.boyo.project.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.project.entity.TeamTaskStagesTemplate;
import com.boyo.project.mapper.TeamTaskStagesTemplateMapper;
import com.boyo.project.service.ITeamTaskStagesTemplateService;
import java.util.List;

/**
 * 任务列表模板表(TeamTaskStagesTemplate)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-08 20:50:50
 */
@Service("teamTaskStagesTemplateService")
@AllArgsConstructor
@Tenant
public class TeamTaskStagesTemplateServiceImpl extends ServiceImpl<TeamTaskStagesTemplateMapper, TeamTaskStagesTemplate> implements ITeamTaskStagesTemplateService {
    private final TeamTaskStagesTemplateMapper teamTaskStagesTemplateMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<TeamTaskStagesTemplate> selectTeamTaskStagesTemplateList(TeamTaskStagesTemplate teamTaskStagesTemplate) {
        return teamTaskStagesTemplateMapper.selectTeamTaskStagesTemplateList(teamTaskStagesTemplate);
    }

}
