package com.boyo.system.service;

import java.util.List;

import com.boyo.system.domain.SysEnterpriseAuthority;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 企业权限管理Service接口
 *
 * <AUTHOR>
 */
public interface ISysEnterpriseAuthorityService extends IService<SysEnterpriseAuthority> {
    /**
     * 根据条件查询查询企业权限管理列表
     *
     * @param sysEnterpriseAuthority 企业权限管理
     * @return 企业权限管理集合
     */
    List<SysEnterpriseAuthority> selectSysEnterpriseAuthorityList(SysEnterpriseAuthority sysEnterpriseAuthority);
}
