package com.boyo.master.mapper;

import java.util.List;

import com.boyo.master.domain.BaseDictType;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;


/**
 * 数据字典类型管理Mapper接口
 *
 * <AUTHOR>
 */
public interface BaseDictTypeMapper extends BaseMapper<BaseDictType> {

    /**
     * 查询数据字典类型管理列表
     *
     * @param baseDictType 数据字典类型管理
     * @return BaseDictType集合
     */
    List<BaseDictType> selectBaseDictTypeList(BaseDictType baseDictType);

}
