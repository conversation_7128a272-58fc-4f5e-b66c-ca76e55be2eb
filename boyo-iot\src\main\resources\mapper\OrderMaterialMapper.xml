<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.iot.mapper.OrderMaterialMapper">

    <resultMap type="com.boyo.iot.entity.OrderMaterial" id="OrderMaterialResult">
        <result property="id" column="id" />
        <result property="orderId" column="order_id" />
        <result property="materialId" column="material_id" />
        <result property="materialCount" column="material_count" />
        <result property="materialName" column="material_name" />
    </resultMap>
    <!--通过实体作为筛选条件查询-->
    <select id="selectOrderMaterialList" parameterType="com.boyo.iot.entity.OrderMaterial" resultMap="OrderMaterialResult">
        select
          t1.*,t2.materiel_name as material_name
        from t_order_material t1,t_material t2
        <where>
        t1.material_id = t2.id
            <if test="orderId != null">
                and order_id = #{orderId}
            </if>
            <if test="materialId != null">
                and material_id = #{materialId}
            </if>
            <if test="materialCount != null">
                and material_count = #{materialCount}
            </if>
        </where>
    </select>
</mapper>

