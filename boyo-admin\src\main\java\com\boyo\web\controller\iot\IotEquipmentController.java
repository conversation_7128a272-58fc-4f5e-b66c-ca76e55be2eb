package com.boyo.web.controller.iot;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.core.text.Convert;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.domain.IotTslAttr;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.system.service.IEnterpriseDepartmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 物联网设备管理Controller
 *
 * <AUTHOR>
 */
@Api("物联网设备管理")
@RestController
@RequestMapping("/iot/equipment")
@AllArgsConstructor
public class IotEquipmentController extends BaseController {
    private final IIotEquipmentService iotEquipmentService;

    private final IEnterpriseDepartmentService enterpriseDepartmentService;


    /**
     * 查询物联网设备管理列表
     */
    @ApiOperation("查询物联网设备管理列表")
    @GetMapping("/list")
    public TableDataInfo list(IotEquipment iotEquipment) {
        Long userAdmin = SecurityUtils.getLoginUser().getEnterpriseUser().getUserAdmin();
        if (userAdmin != 1) {
            iotEquipment.setDeptId(SecurityUtils.getLoginUser().getEnterpriseUser().getDepartmentOpenid());
        }
        startPage();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
        if (list != null && list.size() > 0) {
            for (IotEquipment temp : list) {
                for (IotTslAttr attr : temp.getAttrList()) {
                    if ((attr.getAttrType().equalsIgnoreCase("Double") || attr.getAttrType().equalsIgnoreCase("Integer")) && StrUtil.isNotEmpty(attr.getLastVal())) {
                        attr.setLastVal(Convert.toStr(NumberUtil.round(Convert.toDouble(attr.getLastVal()) * attr.getAttrMultiple(), 2)));
                    }
                    if ("工作状态".equals(attr.getAttrName())&&attr.getLastUpdateTime()!=null) {
                        final Date lastUpdateTime = attr.getLastUpdateTime();
                        final long between = DateUtil.between(lastUpdateTime, new Date(), DateUnit.MINUTE);
                        if (between > 5L) {
                            attr.setLastVal("0");
                        }
                    }
                }
            }
        }
        return getDataTable(list);
    }

    @ApiOperation("查询物联网设备管理列表")
    @GetMapping("/listOnly")
    public TableDataInfo listOnly(IotEquipment iotEquipment) {
        Long userAdmin = SecurityUtils.getLoginUser().getEnterpriseUser().getUserAdmin();
        if (userAdmin != 1) {
            iotEquipment.setDeptId(SecurityUtils.getLoginUser().getEnterpriseUser().getDepartmentOpenid());
        }
        startPage();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
        return getDataTable(list);
    }

    /**
     * 获取物联网设备管理详细信息
     */
    @ApiOperation("获取物联网设备管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(iotEquipmentService.getById(id));
    }

    /**
     * 新增物联网设备管理
     */
    @ApiOperation("新增物联网设备管理")
    @PostMapping
    public AjaxResult add(@RequestBody IotEquipment iotEquipment) {
        iotEquipment.setOpenId(IdUtil.fastSimpleUUID());
        return toBooleanAjax(iotEquipmentService.save(iotEquipment));
    }

    /**
     * 修改物联网设备管理
     */
    @ApiOperation("修改物联网设备管理")
    @PutMapping
    public AjaxResult edit(@RequestBody IotEquipment iotEquipment) {
        return toBooleanAjax(iotEquipmentService.updateById(iotEquipment));
    }

    /**
     * 删除物联网设备管理
     */
    @ApiOperation("删除物联网设备管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(iotEquipmentService.removeByIds(Arrays.asList(ids)));
    }

    @ApiOperation("查询物联网设备数据详情")
    @GetMapping("/getEquipmentDetail")
    public AjaxResult getEquipmentDetail(Integer id) {
        return AjaxResult.success(iotEquipmentService.getEquipmentDetail(id));
    }
}
