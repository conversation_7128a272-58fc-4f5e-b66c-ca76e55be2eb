package com.boyo.master.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.system.domain.IotLvZhou;
import com.boyo.system.service.IIotLvZhouService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/iot/lvzhou")
public class IotLvZhouController extends BaseController {
    @Resource
    private IIotLvZhouService lvZhouService;

    @GetMapping("/getInfo")
    public AjaxResult getInfo(Integer id){
        QueryWrapper<IotLvZhou> queryWrapper= new QueryWrapper<>();
        queryWrapper.eq("id",id);
        List<IotLvZhou> list = lvZhouService.list(queryWrapper);
        return AjaxResult.success(list);
    }
}
