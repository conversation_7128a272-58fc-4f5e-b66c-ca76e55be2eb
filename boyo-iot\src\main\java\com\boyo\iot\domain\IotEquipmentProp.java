package com.boyo.iot.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备属性管理
 * 表名 iot_equipment_prop
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("设备属性特殊配置")
@Data
@TableName("iot_equipment_prop")
public class IotEquipmentProp extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId
    private Long id;
    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    @TableField(value = "equipment_id")
    private Long equipmentId;
    /**
     * 属性id
     */
    @ApiModelProperty("属性id")
    @TableField(value = "attr_id")
    private Long attrId;
    /**
     * 故障值
     */
    @TableField(value = "fault_val")
    private String faultVal;
    /**
     * 最小值
     */
    @ApiModelProperty("最小值")
    @TableField(value = "min_val")
    private String minVal;
    /**
     * 最小值
     */
    @ApiModelProperty("最小值")
    @TableField(value = "max_val")
    private String maxVal;
    /**
     * 枚举数据类型
     */
    @ApiModelProperty("枚举数据类型")
    @TableField(value = "enum_list")
    private String enumList;
    /**
     * 显示方式：num 数值  line 折线
     */
    @ApiModelProperty("显示方式：num 数值  line 折线")
    @TableField(value = "show_type")
    private String showType;

    @TableField(value = "auto_fault")
    private Integer autoFault;
    @TableField(value = "auto_bill")
    private Integer autoBill;
    @TableField(value = "fault_time")
    private Integer faultTime;
    @TableField(value = "bill_time")
    private Integer billTime;

    @TableField(value = "bill_user")
    private String billUser;

    @TableField(value = "custom_multiple")
    private Double customMultiple;
}
