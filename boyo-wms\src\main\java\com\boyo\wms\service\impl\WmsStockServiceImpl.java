package com.boyo.wms.service.impl;

import java.util.Date;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.wms.entity.WmsFlow;
import com.boyo.wms.entity.WmsMateriel;
import com.boyo.wms.dto.WmsAllotDTO;
import com.boyo.wms.mapper.WmsFlowMapper;
import com.boyo.wms.mapper.WmsMaterielMapper;
import com.boyo.wms.vo.StockWarnVO;
import com.boyo.wms.vo.WmsStockVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.wms.mapper.WmsStockMapper;
import com.boyo.wms.entity.WmsStock;
import com.boyo.wms.service.IWmsStockService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 库存管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class WmsStockServiceImpl extends ServiceImpl<WmsStockMapper, WmsStock> implements IWmsStockService {
    private final WmsStockMapper wmsStockMapper;

    private final WmsMaterielMapper wmsMaterielMapper;

    private final WmsFlowMapper wmsFlowMapper;


    /**
     * 查询库存管理列表
     *
     * @param wmsStock 库存管理
     * @return wmsStock 列表
     */
    @Override
    public List<WmsStockVO> selectWmsStockList(WmsStockVO wmsStock) {
        return wmsStockMapper.selectWmsStockList(wmsStock);
    }

    @Override
    public List<WmsStockVO> selectWmsStockByMateriel(WmsStock wmsStock) {
        return wmsStockMapper.selectWmsStockByMateriel(wmsStock);
    }

    @Override
    public List<StockWarnVO> selectStockWarn() {
        return wmsStockMapper.selectStockWarn();
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, propagation = Propagation.REQUIRED)
    public boolean allotMaterial(WmsAllotDTO allotDTO) {
        WmsStock stock = wmsStockMapper.selectById(allotDTO.getStockId());
        if (ObjectUtil.isNull(stock) || !stock.getMaterielOpenid().equals(allotDTO.getMaterielOpenid())
                || !stock.getMaterielBatch().equals(allotDTO.getMaterielBatch())
                || !stock.getWarehouseOpenid().equals(allotDTO.getWarehouseOpenid())
                || !stock.getAreaOpenid().equals(allotDTO.getAreaOpenid())
                || !stock.getAllocationOpenid().equals(allotDTO.getAllocationOpenid())) {
            throw new CustomException("库存信息错误！");
        }
        if (stock.getMaterielQuantity().compareTo(allotDTO.getQuantity()) < 0) {
            throw new CustomException("物料库存量不足！");
        }
        if (stock.getWarehouseOpenid().equals(allotDTO.getAimWarehouseOpenid())
                && stock.getAreaOpenid().equals(allotDTO.getAimAreaOpenid())
                && stock.getAllocationOpenid().equals(allotDTO.getAimAllocationOpenid())) {
            throw new CustomException("调拨货位不能与原货位一样！");
        }
        if (stock.getMaterielQuantity().compareTo(allotDTO.getQuantity()) == 0) {
//            stock.setWarehouseOpenid(allotDTO.getAimWarehouseOpenid());
//            stock.setAreaOpenid(allotDTO.getAimAreaOpenid());
//            stock.setAllocationOpenid(allotDTO.getAimAllocationOpenid());
            wmsStockMapper.deleteById(stock.getId());
        }else{
            stock.setMaterielQuantity(stock.getMaterielQuantity().subtract(allotDTO.getQuantity()));
            wmsStockMapper.updateById(stock);
        }
        WmsStockVO aim = new WmsStockVO();
        aim.setMaterielBatch(allotDTO.getMaterielBatch());
        aim.setMaterielOpenid(allotDTO.getMaterielOpenid());
        aim.setWarehouseOpenid(allotDTO.getAimWarehouseOpenid());
        aim.setAreaOpenid(allotDTO.getAimAreaOpenid());
        aim.setAllocationOpenid(allotDTO.getAimAllocationOpenid());
        List<WmsStockVO> stockList = wmsStockMapper.selectWmsStockList(aim);
        if (stockList != null && stockList.size() > 0) {
            aim = stockList.get(0);
            aim.setMaterielQuantity(aim.getMaterielQuantity().add(allotDTO.getQuantity()));
            aim.setUpdateTime(new Date());
            wmsStockMapper.updateById(aim);
        } else {
            aim.setMaterielQuantity(allotDTO.getQuantity());
            aim.setVersion(1L);
            aim.setUpdateTime(new Date());
            wmsStockMapper.insert(aim);
        }
        QueryWrapper<WmsMateriel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("detail_materiel_order", allotDTO.getMaterielBatch());
        WmsMateriel wmsMateriel = wmsMaterielMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNull(wmsMateriel)) {
            throw new CustomException("物料信息异常0001");
        }
        /**
         * 调拨出库流水
         */
        WmsFlow outFlow = new WmsFlow();
        outFlow.setFlowType("3");
        outFlow.setMaterielBatch(allotDTO.getMaterielBatch());
        outFlow.setDetailOpenid(wmsMateriel.getDetailOpenid());
        outFlow.setMaterielNumber(allotDTO.getQuantity());
        outFlow.setWarehouseOpenid(allotDTO.getWarehouseOpenid());
        outFlow.setAreaOpenid(allotDTO.getAreaOpenid());
        outFlow.setAllocationOpenid(allotDTO.getAllocationOpenid());
        outFlow.setCreatedAt(new Date());
        outFlow.setCreatedUser(SecurityUtils.getUserOpenid());
        /**
         * 调拨入库流水
         */
        WmsFlow inFlow = new WmsFlow();
        inFlow.setFlowType("4");
        inFlow.setMaterielBatch(allotDTO.getMaterielBatch());
        inFlow.setDetailOpenid(wmsMateriel.getDetailOpenid());
        inFlow.setMaterielNumber(allotDTO.getQuantity());
        inFlow.setWarehouseOpenid(allotDTO.getAimWarehouseOpenid());
        inFlow.setAreaOpenid(allotDTO.getAimAreaOpenid());
        inFlow.setAllocationOpenid(allotDTO.getAllocationOpenid());
        inFlow.setCreatedAt(new Date());
        inFlow.setCreatedUser(SecurityUtils.getUserOpenid());
        wmsFlowMapper.insert(outFlow);
        wmsFlowMapper.insert(inFlow);
        return true;
    }
}
