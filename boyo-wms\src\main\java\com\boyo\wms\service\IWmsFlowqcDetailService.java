package com.boyo.wms.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.wms.entity.WmsFlowqcDetail;
import java.util.List;

/**
 * (WmsFlowqcDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-10 15:12:10
 */
public interface IWmsFlowqcDetailService extends IService<WmsFlowqcDetail> {

    /**
     * 查询多条数据
     *
     * @param wmsFlowqcDetail 对象信息
     * @return 对象列表
     */
    List<WmsFlowqcDetail> selectWmsFlowqcDetailList(WmsFlowqcDetail wmsFlowqcDetail);


}
