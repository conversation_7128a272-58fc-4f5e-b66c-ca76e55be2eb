package com.boyo.mes.controller;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.text.Convert;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.master.domain.TMaterial;
import com.boyo.master.service.ITMaterialService;
import com.boyo.master.vo.MaterialVO;
import com.boyo.mes.entity.MesMould;
import com.boyo.mes.service.IMesMouldService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import java.util.Map;

/**
 * 模具基本信息(MesMould)表控制层
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
@Api("模具基本信息")
@RestController
@RequestMapping("/mes/mesMould")
@AllArgsConstructor
public class MesMouldController extends BaseController {
    /**
     * 服务对象
     */
    private final IMesMouldService mesMouldService;

    @Resource
    private final ITMaterialService tMaterialService;

    /**
     * 查询模具基本信息列表
     */
    @ApiOperation("查询模具基本信息列表")
    @GetMapping("/list")
    public TableDataInfo list(MesMould mesMould) {
//        Long userAdmin = SecurityUtils.getLoginUser().getEnterpriseUser().getUserAdmin();
//        if (userAdmin != 1) {
//            mesMould.setDeptId(SecurityUtils.getLoginUser().getEnterpriseUser().getDepartmentOpenid());
//        }
        startPage();
        List<MesMould> list = mesMouldService.selectMesMouldList(mesMould);
        return getDataTable(list);
    }

    /**
     * 获取模具基本信息详情
     */
    @ApiOperation("获取模具基本信息详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(mesMouldService.getById(id));
    }

    /**
     * 新增模具基本信息
     */
    @ApiOperation("新增模具基本信息")
    @PostMapping
    public AjaxResult add(@RequestBody MesMould mesMould) {
        QueryWrapper<MesMould> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mouldc_code", mesMould.getMouldCode());
        if (mesMouldService.count(queryWrapper) > 0) {
            throw new CustomException("模具编码已存在");
        }
        return toBooleanAjax(mesMouldService.save(mesMould));
    }

    /**
     * 修改模具基本信息
     */
    @ApiOperation("修改模具基本信息")
    @PutMapping
    public AjaxResult edit(@RequestBody MesMould mesMould) {
        QueryWrapper<MesMould> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mouldc_code", mesMould.getMouldCode()).ne("id", mesMould.getId());
        if (mesMouldService.count(queryWrapper) > 0) {
            throw new CustomException("模具编码已存在");
        }
        return toBooleanAjax(mesMouldService.updateById(mesMould));
    }

    /**
     * 删除模具基本信息
     */
    @ApiOperation("删除模具基本信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(mesMouldService.removeByIds(Arrays.asList(ids)));
    }

    @PostMapping("/upload")
    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file) throws Exception {
        if (!file.getContentType().startsWith("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")) {
            return AjaxResult.error("仅支持.xlsx格式的Excel文件");
        }
        int successCount = 0;
        int failCount = 0;
        try {
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
            List<Map<String, Object>> readAll = reader.readAll();
            List<MesMould> mouldList = new ArrayList<>();
            for (int i = 0; i < readAll.size(); i++) {
                boolean checkExist = true;
                MesMould mould = new MesMould();
                mould.setMouldName(Convert.toStr(readAll.get(i).get("模具名称")));
                mould.setMouldCode(Convert.toStr(readAll.get(i).get("模具编码")));
                mould.setMouldLife(Convert.toDouble(readAll.get(i).get("理论寿命")));
                mould.setMaintainFrequency(Convert.toDouble(readAll.get(i).get("保养频率")));
                mould.setUsedLife(Convert.toDouble(readAll.get(i).get("已用时长")));

                String toStr = Convert.toStr(readAll.get(i).get("关联产品"));
                if (toStr != null && !"".equals(toStr)) {
                    String[] split = toStr.split("/");
                    String productionId = "";
                    for (int j = 0; j < split.length; j++) {
                        TMaterial tMaterial = new TMaterial();
                        tMaterial.setMaterielCode(split[j]);
                        List<MaterialVO> list = tMaterialService.selectTMaterialList(tMaterial);
                        if (list == null || list.isEmpty()) {
                            checkExist = false;
                            break;
                        }
                        productionId += list.get(0).getId().toString() + ",";
                    }
                    if (!checkExist) {
                        failCount++;
                        continue;
                    }
                    productionId = productionId.substring(0, productionId.length() - 1); // 去掉最后一个逗号
                    mould.setProductionIdStr(productionId);
                }
                successCount++;
                mouldList.add(mould);
            }
            if (mouldList.size() > 0) {
                mesMouldService.saveBatch(mouldList);
            }
            logger.info("成功导入" + successCount + "条数据，失败" + failCount + "条数据");
            if (failCount > 0) {
                return AjaxResult.error("成功导入" + successCount + "条数据，失败" + failCount + "条数据");
            }
            return AjaxResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }

}
