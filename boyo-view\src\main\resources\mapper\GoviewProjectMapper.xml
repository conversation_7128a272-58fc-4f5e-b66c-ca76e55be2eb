<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.view.mapper.GoviewProjectMapper">

    <resultMap type="com.boyo.view.entity.GoviewProject" id="GoviewProjectResult">
        <result property="id" column="id" />
        <result property="projectName" column="project_name" />
        <result property="state" column="state" />
        <result property="createTime" column="create_time" />
        <result property="createUserId" column="create_user_id" />
        <result property="isDelete" column="is_delete" />
        <result property="indexImage" column="index_image" />
        <result property="remarks" column="remarks" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectGoviewProjectList" parameterType="com.boyo.view.entity.GoviewProject" resultMap="GoviewProjectResult">
        select
          id, project_name, state, create_time, create_user_id, is_delete, index_image, remarks
        from t_goview_project
        <where>
            <if test="projectName != null and projectName != ''">
                and project_name = #{projectName}
            </if>
            <if test="state != null">
                and state = #{state}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUserId != null and createUserId != ''">
                and create_user_id = #{createUserId}
            </if>
            <if test="isDelete != null">
                and is_delete = #{isDelete}
            </if>
            <if test="indexImage != null and indexImage != ''">
                and index_image = #{indexImage}
            </if>
            <if test="remarks != null and remarks != ''">
                and remarks = #{remarks}
            </if>
        </where>
    </select>
</mapper>

