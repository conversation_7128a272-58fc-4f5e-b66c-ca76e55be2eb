package com.boyo.master.domain;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 数据字典类型管理
 * 表名 t_base_dict_type
 *
 * <AUTHOR>
 */
@ApiModel("数据字典类型-不允许客户修改")
@Data
@TableName("t_base_dict_type")
public class BaseDictType extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "openid")
    private String openid;
    /**
     * 类型编码
     */
    @ApiModelProperty("类型编码")
    @TableField(value = "type_code")
    private String typeCode;
    /**
     * 类型名称
     */
    @ApiModelProperty("类型名称")
    @TableField(value = "type_desc")
    private String typeDesc;
}
