package com.boyo.master.service;

import com.boyo.master.domain.MesPersonWage;

import java.util.List;


/**
 * 人员工资Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
public interface IMesPersonWageService 
{
    /**
     * 查询人员工资
     * 
     * @param id 人员工资主键
     * @return 人员工资
     */
    public MesPersonWage selectMesPersonWageById(String id);

    /**
     * 查询人员工资列表
     * 
     * @param mesPersonWage 人员工资
     * @return 人员工资集合
     */
    public List<MesPersonWage> selectMesPersonWageList(MesPersonWage mesPersonWage);

    /**
     * 新增人员工资
     * 
     * @param mesPersonWage 人员工资
     * @return 结果
     */
    public int insertMesPersonWage(MesPersonWage mesPersonWage);

    /**
     * 修改人员工资
     * 
     * @param mesPersonWage 人员工资
     * @return 结果
     */
    public int updateMesPersonWage(MesPersonWage mesPersonWage);

    /**
     * 批量删除人员工资
     * 
     * @param ids 需要删除的人员工资主键集合
     * @return 结果
     */
    public int deleteMesPersonWageByIds(Long[] ids);

    /**
     * 删除人员工资信息
     * 
     * @param id 人员工资主键
     * @return 结果
     */
    public int deleteMesPersonWageById(Long id);
}
