package com.boyo.master.service;

import java.util.List;

import com.boyo.master.domain.BaseDict;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 租户数据字典管理Service接口
 *
 * <AUTHOR>
 */
public interface IBaseDictService extends IService<BaseDict> {
    /**
     * 根据条件查询查询租户数据字典管理列表
     *
     * @param baseDict 租户数据字典管理
     * @return 租户数据字典管理集合
     */
    List<BaseDict> selectBaseDictList(BaseDict baseDict);
}
