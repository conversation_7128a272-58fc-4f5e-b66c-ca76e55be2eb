package com.boyo.mes.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Value;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * (TFactoryOrder)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-06 16:18:20
 */
@Data
@TableName("t_factory_order")
public class TFactoryOrder  {

    @TableId(type = IdType.AUTO)
    private Long id;
    //订单编号
    private String orderNum;
    //订单名称
    private String orderName;
    //客户名称
    private String customerName;
    //订单数量
    private Integer number;
    //交付时间
    @TableField(value="delivery_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;
    //0:创建 1：执行 2：完成 3：发货
    private Integer orderStatus;
    //订单负责人
    private String orderManager;
    /**
     * 图片介绍
     */
    private String imageIntroduction;
    //附件
    /**
     * 附件
     */
    private String attachments;
    /**
     * 完成数量
     */
    private Integer finishNum;

    @TableField("unit")
    private String unit;

    @TableField(value="start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @TableField(value="end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @TableField(value="create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 交付周期
     */
    @TableField(value = "delivery_cycle")
    private Integer deliveryCycle;

    /**
     * 订单来源
     */
    @TableField(value = "order_source")
    private String orderSource;

    private String overdue;

    @TableField(value="update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField(value = "pay_method")
    private String payMethod;

    @TableField(value = "notes")
    private String notes;

    @TableField(value = "work_order_num")
    private Integer workOrderNum;

    @TableField(value = "customer_id")
    private Integer customerId;

}