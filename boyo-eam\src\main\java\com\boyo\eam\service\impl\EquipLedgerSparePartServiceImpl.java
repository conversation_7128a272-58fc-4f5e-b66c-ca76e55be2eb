package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipLedger;
import com.boyo.eam.domain.EquipLedgerSparePart;
import com.boyo.eam.mapper.EquipLedgerSparePartMapper;
import com.boyo.eam.service.IEquipLedgerSparePartService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 台账和部件关联表(EquipLedgerSparePart)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-04 16:59:34
 */
@Service("equipLedgerSparePartService")
@AllArgsConstructor
@Tenant
public class EquipLedgerSparePartServiceImpl extends ServiceImpl<EquipLedgerSparePartMapper, EquipLedgerSparePart> implements IEquipLedgerSparePartService {
    private final EquipLedgerSparePartMapper equipLedgerSparePartMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipLedgerSparePart> selectEquipLedgerSparePartList(EquipLedgerSparePart equipLedgerSparePart) {
        return equipLedgerSparePartMapper.selectEquipLedgerSparePartList(equipLedgerSparePart);
    }

    @Override
    public void removeEquipLedgerSparePart(EquipLedger equipLedger) {
        equipLedgerSparePartMapper.delete(
                Wrappers.<EquipLedgerSparePart>lambdaQuery()
                        .eq(EquipLedgerSparePart::getEquipLedgerOpenid,equipLedger.getOpenid())
        );
    }

}
