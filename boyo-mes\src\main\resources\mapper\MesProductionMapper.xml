<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.MesProductionMapper">

    <resultMap type="com.boyo.mes.entity.MesProduction" id="MesProductionResult">
        <result property="id" column="id" />
        <result property="produtionName" column="prodution_name" />
        <result property="productionCode" column="production_code" />
        <result property="createTime" column="create_time" />
        <result property="createUserId" column="create_user_id" />
        <result property="deptId" column="dept_id" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectMesProductionList" parameterType="com.boyo.mes.entity.MesProduction" resultMap="MesProductionResult">
        select
          id, prodution_name, production_code, create_time, create_user_id, dept_id
        from t_mes_production
        <where>
            <if test="produtionName != null and produtionName != ''">
                and prodution_name like concat('%', #{produtionName}, '%')
            </if>
            <if test="productionCode != null and productionCode != ''">
                and production_code like concat('%', #{productionCode}, '%')
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="deptId != null and deptId != ''">
                and dept_id = #{deptId}
            </if>
        </where>
    </select>
</mapper>

