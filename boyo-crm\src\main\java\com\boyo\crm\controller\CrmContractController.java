package com.boyo.crm.controller;

import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.crm.entity.CrmContract;
import com.boyo.crm.service.ICrmContractService;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

/**
 * 合同表(CrmContract)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-27 17:04:54
 */
@Api("合同表")
@RestController
@RequestMapping("/crm/crmContract")
@AllArgsConstructor
public class CrmContractController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmContractService crmContractService;
    private final IEnterpriseUserService enterpriseUserService;

    /**
     * 查询合同表列表
     *
     */
    @ApiOperation("查询合同表列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmContract crmContract) {
        startPage();
        List<CrmContract> list = crmContractService.selectCrmContractList(crmContract);
        if(list != null && list.size() > 0){
            List<Long> ids = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                ids.add(list.get(i).getOwnerUserId());
            }
            List<EnterpriseUser> userList = enterpriseUserService.selectByIds(ids);
            if(userList != null && userList.size() > 0){
                for (int i = 0; i < list.size(); i++) {
                    for (int j = 0; j < userList.size(); j++) {
                        if(list.get(i).getOwnerUserId().equals(userList.get(j).getId())){
                            list.get(i).setOwnerUserName(userList.get(j).getUserFullName());
                            break;
                        }
                    }
                }
            }
        }
        return getDataTable(list);
    }
    
    /**
     * 获取合同表详情
     */
    @ApiOperation("获取合同表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        CrmContract contract = crmContractService.getById(id);
        EnterpriseUser user = enterpriseUserService.getById(contract.getOwnerUserId());
        contract.setOwnerUserName(user.getUserFullName());
        return AjaxResult.success(contract);
    }

    /**
     * 新增合同表
     */
    @ApiOperation("新增合同表")
    @PostMapping
    public AjaxResult add(@RequestBody CrmContract crmContract) {
        return toBooleanAjax(crmContractService.save(crmContract));
    }

    /**
     * 修改合同表
     */
    @ApiOperation("修改合同表")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmContract crmContract) {
        return toBooleanAjax(crmContractService.updateById(crmContract));
    }
    @ApiOperation("修改合同主表")
    @PutMapping("/update")
    public AjaxResult update(@RequestBody CrmContract crmContract){
        crmContractService.update(crmContract);
        return AjaxResult.success();
    }

    /**
     * 删除合同表
     */
    @ApiOperation("删除合同表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmContractService.removeByIds(Arrays.asList(ids)));
    }

}
