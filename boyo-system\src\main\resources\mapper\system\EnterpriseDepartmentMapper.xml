<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.system.mapper.EnterpriseDepartmentMapper">

    <resultMap type="com.boyo.system.domain.EnterpriseDepartment" id="EnterpriseDepartmentResult">
        <result property="id" column="id"/>
        <result property="enterpriseOpenid" column="enterprise_openid"/>
        <result property="departmentOpenid" column="department_openid"/>
        <result property="parentOpenid" column="parent_openid"/>
        <result property="departmentName" column="department_name"/>
        <result property="departmentCode" column="department_code"/>
        <result property="departmentContacts" column="department_contacts"/>
        <result property="departmentPhone" column="department_phone"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectEnterpriseDepartmentVo">
        select id,
               enterprise_openid,
               department_openid,
               parent_openid,
               department_name,
               department_code,
               department_contacts,
               department_phone,
               create_time,
               update_time
        from t_enterprise_department
    </sql>

    <select id="selectEnterpriseDepartmentList" parameterType="com.boyo.system.domain.EnterpriseDepartment"
            resultMap="EnterpriseDepartmentResult">
        <include refid="selectEnterpriseDepartmentVo"/>
        <where>
            <if test="enterpriseOpenid != null  and enterpriseOpenid != ''">
                and enterprise_openid = #{enterpriseOpenid}
            </if>
            <if test="departmentOpenid != null  and departmentOpenid != ''">
                and department_openid = #{departmentOpenid}
            </if>
            <if test="parentOpenid != null  and parentOpenid != ''">
                and parent_openid = #{parentOpenid}
            </if>
            <if test="departmentName != null  and departmentName != ''">
                and department_name like concat('%', #{departmentName}, '%')
            </if>
            <if test="departmentCode != null  and departmentCode != ''">
                and department_code = #{departmentCode}
            </if>
            <if test="departmentContacts != null  and departmentContacts != ''">
                and department_contacts = #{departmentContacts}
            </if>
            <if test="departmentPhone != null  and departmentPhone != ''">
                and department_phone = #{departmentPhone}
            </if>
        </where>
    </select>
    <select id="getDeptAndChildren" resultType="java.lang.String">
        select department_openid
        from (select t1.department_openid,
                     if(find_in_set(parent_openid, @pids) > 0, @pids := concat(@pids, ',', department_openid), 0) as ischild
              from (select department_openid, parent_openid
                    from t_enterprise_department t
                    order by parent_openid, department_openid) t1,
                   (select @pids := #{openid}) t2) t3
        where ischild != 0 OR department_openid = #{openid}
    </select>
</mapper>
