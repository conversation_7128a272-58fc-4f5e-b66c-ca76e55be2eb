<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmBusinessStatusMapper">

    <resultMap type="com.boyo.crm.entity.CrmBusinessStatus" id="CrmBusinessStatusResult">
        <result property="id" column="id"/>
        <result property="typeId" column="type_id"/>
        <result property="name" column="name"/>
        <result property="rate" column="rate"/>
        <result property="orderNum" column="order_num"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmBusinessStatusList" parameterType="com.boyo.crm.entity.CrmBusinessStatus"
            resultMap="CrmBusinessStatusResult">
        select
        id, type_id, name, rate, order_num
        from t_crm_business_status
        <where>
            <if test="typeId != null">
                and type_id = #{typeId}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="rate != null and rate != ''">
                and rate = #{rate}
            </if>
            <if test="orderNum != null">
                and order_num = #{orderNum}
            </if>
        </where>
        order by order_num asc,id desc
    </select>
</mapper>

