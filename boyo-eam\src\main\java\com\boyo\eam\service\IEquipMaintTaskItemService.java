package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipMaintTaskItem;

import java.util.List;

/**
 * 维保任务管理-维保项目(EquipMaintTaskItem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:32
 */
public interface IEquipMaintTaskItemService extends IService<EquipMaintTaskItem> {

    /**
     * 查询多条数据
     *
     * @param equipMaintTaskItem 对象信息
     * @return 对象列表
     */
    List<EquipMaintTaskItem> selectEquipMaintTaskItemList(EquipMaintTaskItem equipMaintTaskItem);


}
