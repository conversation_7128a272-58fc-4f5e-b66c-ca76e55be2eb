package com.boyo.master.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 通知公告表(Notice)实体类
 *
 * <AUTHOR>
 * @since 2022-03-11 14:17:15
 */
@Data
@TableName(value = "t_notice")
public class Notice implements Serializable {
    private static final long serialVersionUID = -53759091833754838L;
        /**
    * 公告ID
    */    
    @TableId
    private Integer id;
    
    /**
    * 公告标题
    */
    @TableField(value="notice_title")
    private String noticeTitle;
    /**
    * 公告内容
    */
    @TableField(value="notice_content")
    private String noticeContent;
    /**
    * 创建者
    */
    @TableField(value="create_by")
    private String createBy;
    /**
    * 创建时间
    */
    @TableField(value="create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
