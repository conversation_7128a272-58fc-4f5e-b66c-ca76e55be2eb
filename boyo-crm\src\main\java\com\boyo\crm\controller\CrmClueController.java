package com.boyo.crm.controller;

import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.crm.entity.CrmClue;
import com.boyo.crm.service.ICrmClueService;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

/**
 * CRM线索主表(CrmClue)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 10:48:28
 */
@Api("CRM线索主表")
@RestController
@RequestMapping("/crm/crmClue")
@AllArgsConstructor
public class CrmClueController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmClueService crmClueService;
    private final IEnterpriseUserService enterpriseUserService;


    /**
     * 查询CRM线索主表列表
     *
     */
    @ApiOperation("查询CRM线索主表列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmClue crmClue) {
        startPage();
        List<CrmClue> list = crmClueService.selectCrmClueList(crmClue);
        if(list != null && list.size() > 0){
            List<Long> ids = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                ids.add(list.get(i).getOwnerUserId());
            }
            List<EnterpriseUser> userList = enterpriseUserService.selectByIds(ids);
            if(userList != null && userList.size() > 0){
                for (int i = 0; i < list.size(); i++) {
                    for (int j = 0; j < userList.size(); j++) {
                        if(list.get(i).getOwnerUserId().equals(userList.get(j).getId())){
                            list.get(i).setOwnerUserName(userList.get(j).getUserFullName());
                            break;
                        }
                    }
                }
            }
        }
        return getDataTable(list);
    }

    /**
     * 获取CRM线索主表详情
     */
    @ApiOperation("获取CRM线索主表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(crmClueService.getById(id));
    }

    /**
     * 新增CRM线索主表
     */
    @ApiOperation("新增CRM线索主表")
    @PostMapping
    public AjaxResult add(@RequestBody CrmClue crmClue) {
        return toBooleanAjax(crmClueService.save(crmClue));
    }

    /**
     * 修改CRM线索主表
     */
    @ApiOperation("修改CRM线索主表")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmClue crmClue) {
        return toBooleanAjax(crmClueService.updateById(crmClue));
    }

    /**
     * 删除CRM线索主表
     */
    @ApiOperation("删除CRM线索主表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmClueService.removeByIds(Arrays.asList(ids)));
    }

    @ApiOperation("线索转换为客户")
    @PostMapping("/changeToCustomer")
    public AjaxResult changeToCustomer(@RequestBody CrmClue crmClue){
        crmClueService.changeToCustomer(crmClue);
     return AjaxResult.success();
    }

}
