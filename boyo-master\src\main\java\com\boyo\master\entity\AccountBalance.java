package com.boyo.master.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 科目余额表
 * @TableName bu_enterprise_data_asset_account_balance
 */
@TableName(value ="bu_enterprise_data_asset_account_balance")
@Data
public class AccountBalance extends DataAssetBase implements Serializable {
    /**
     * 科目编码
     */
    @JSONField(name = "SUBJCODE")
    @TableField(value = "subjcode")
    private String subjcode;

    /**
     * 科目名称
     */

    @JSONField(name = "SUBJNAME")
    @TableField(value = "subjname")
    private String subjname;

    /**
     * 余额方向
     */

    @JSONField(name = "BALANORIENT")
    @TableField(value = "balanorient")
    private Integer balanorient;

    /**
     * 本期贷方
     */
    @JSONField(name = "CREDITAMOUNT")
    @TableField(value = "creditamount")
    private BigDecimal creditamount;

    /**
     * 本期借方
     */
    @JSONField(name = "DEBITAMOUNT")
    @TableField(value = "debitamount")
    private BigDecimal debitamount;

    /**
     * 贷方累计
     */
    @JSONField(name = "SUMCREDITAMOUNT")
    @TableField(value = "sumcreditamount")
    private BigDecimal sumcreditamount;

    /**
     * 借方累计
     */
    @JSONField(name = "SUMDEBITAMOUNT")
    @TableField(value = "sumdebitamount")
    private BigDecimal sumdebitamount;

    /**
     * 期初金额
     */
    @JSONField(name = "QICHU")
    @TableField(value = "qichu")
    private BigDecimal qichu;

    @TableField(value = "children_company")
    private String childrenCompany;

    @TableField(value = "year")
    private String year;

    @TableField(value = "period")
    private String period;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
