package com.boyo.master.service.impl;

import java.util.List;
import java.util.Arrays;

import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.master.mapper.BaseDictTypeMapper;
import com.boyo.master.domain.BaseDictType;
import com.boyo.master.service.IBaseDictTypeService;

/**
 * 数据字典类型管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class BaseDictTypeServiceImpl extends ServiceImpl<BaseDictTypeMapper, BaseDictType> implements IBaseDictTypeService {
    private final BaseDictTypeMapper baseDictTypeMapper;


    /**
     * 查询数据字典类型管理列表
     *
     * @param baseDictType 数据字典类型管理
     * @return baseDictType 列表
     */
    @Override
    public List<BaseDictType> selectBaseDictTypeList(BaseDictType baseDictType) {
        return baseDictTypeMapper.selectBaseDictTypeList(baseDictType);
    }
}
