package com.boyo.project.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 项目表(TeamProject)实体类
 *
 * <AUTHOR>
 * @since 2022-02-09 11:07:52
 */
@Data
@TableName(value = "team_project")
public class TeamProject implements Serializable {
    private static final long serialVersionUID = -45385405504348096L;

    @TableId
    private Long id;

    /**
    * 封面
    */
    @TableField(value="cover")
    private String cover;
    /**
    * 名称
    */
    @TableField(value="name")
    private String name;
    /**
    * 编号
    */
    @TableField(value="code")
    private String code;
    /**
    * 描述
    */
    @TableField(value="description")
    private String description;
    /**
    * 访问控制l类型
    */
    @TableField(value="access_control_type")
    private String accessControlType;
    /**
    * 可以访问项目的权限组（白名单）
    */
    @TableField(value="white_list")
    private String whiteList;
    /**
    * 排序
    */
    @TableField(value="`order`")
    private Integer order;
    /**
    * 删除标记
    */
    @TableField(value="deleted")
    private Integer deleted;
    /**
    * 项目类型
    */
    @TableField(value="template_code")
    private String templateCode;

    @TableField(exist = false)
    private String templateName;
    /**
    * 进度
    */
    @TableField(value="schedule")
    private Double schedule;
    /**
    * 创建时间
    */
    @TableField(value="create_time")
    private String createTime;
    /**
    * 组织id
    */
    @TableField(value="organization_code")
    private String organizationCode;
    /**
    * 删除时间
    */
    @TableField(value="deleted_time")
    private String deletedTime;
    /**
    * 项目前缀
    */
    @TableField(value="prefix")
    private String prefix;
    /**
    * 是否开启项目前缀
    */
    @TableField(value="open_prefix")
    private Integer openPrefix;
    /**
    * 是否归档
    */
    @TableField(value="archive")
    private Integer archive;
    /**
    * 归档时间
    */
    @TableField(value="archive_time")
    private String archiveTime;
    /**
    * 是否开启任务开始时间
    */
    @TableField(value="open_begin_time")
    private Integer openBeginTime;
    /**
    * 是否开启新任务默认开启隐私模式
    */
    @TableField(value="open_task_private")
    private Integer openTaskPrivate;
    /**
    * 看板风格
    */
    @TableField(value="task_board_theme")
    private String taskBoardTheme;
    /**
    * 项目开始日期
    */
    @TableField(value="begin_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginTime;
    /**
    * 项目截止日期
    */
    @TableField(value="end_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    /**
    * 自动更新项目进度
    */
    @TableField(value="auto_update_schedule")
    private Integer autoUpdateSchedule;

    /**
     * 任务总数
     */
    @TableField(exist = false)
    private Integer taskCount;
    /**
     * 已完成任务数
     */
    @TableField(exist = false)
    private Integer completeCount;

    @TableField(exist = false)
    private String currentUser;

}
