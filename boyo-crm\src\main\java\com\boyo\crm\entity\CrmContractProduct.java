package com.boyo.crm.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同产品关系表(CrmContractProduct)实体类
 *
 * <AUTHOR>
 * @since 2022-03-27 17:04:54
 */
@Data
@TableName(value = "t_crm_contract_product")
public class CrmContractProduct extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = 899969625445316973L;
            
    @TableId
    private Integer id;
    
    /**
    * 合同ID
    */
    @TableField(value="contract_id")
    private Integer contractId;
    /**
    * 产品ID
    */
    @TableField(value="product_id")
    private Integer productId;
    /**
    * 产品单价
    */
    @TableField(value="price")
    private Double price;
    /**
    * 数量
    */
    @TableField(value="quantity")
    private Integer quantity;
    /**
    * 折扣
    */
    @TableField(value="discount")
    private Double discount;
    /**
    * 小计（折扣后价格）
    */
    @TableField(value="subtotal")
    private Double subtotal;
    /**
    * 单位
    */
    @TableField(value="unit")
    private String unit;

    @TableField(exist = false)
    private String categoryName;
    @TableField(exist = false)
    private String name;
    @TableField(exist = false)
    private String num;

}
