package com.boyo.master.controller;

import com.boyo.master.entity.Notice;
import com.boyo.master.service.INoticeService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.Date;
import java.util.List;
import java.util.Arrays;

/**
 * 通知公告表(Notice)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-11 14:17:15
 */
@Api("通知公告表")
@RestController
@RequestMapping("/master/notice")
@AllArgsConstructor
public class NoticeController extends BaseController{
    /**
     * 服务对象
     */
    private final INoticeService noticeService;

    /**
     * 查询通知公告表列表
     *
     */
    @ApiOperation("查询通知公告表列表")
    @GetMapping("/list")
    public TableDataInfo list(Notice notice) {
        startPage();
        List<Notice> list = noticeService.selectNoticeList(notice);
        return getDataTable(list);
    }
    
    /**
     * 获取通知公告表详情
     */
    @ApiOperation("获取通知公告表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(noticeService.getById(id));
    }

    /**
     * 新增通知公告表
     */
    @ApiOperation("新增通知公告表")
    @PostMapping
    public AjaxResult add(@RequestBody Notice notice) {
        notice.setCreateTime(new Date());
        return toBooleanAjax(noticeService.save(notice));
    }

    /**
     * 修改通知公告表
     */
    @ApiOperation("修改通知公告表")
    @PutMapping
    public AjaxResult edit(@RequestBody Notice notice) {
        return toBooleanAjax(noticeService.updateById(notice));
    }

    /**
     * 删除通知公告表
     */
    @ApiOperation("删除通知公告表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(noticeService.removeByIds(Arrays.asList(ids)));
    }

}
