package com.boyo.master.domain.annotations;


import com.boyo.master.domain.enums.MethodType;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * 企业端用户个性化扩展专用注解
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface Enterprise {
    /**
     * 企业用户id
     * @return
     */
    String value() default "";

    /**
     * 作用类型
     * @return
     */
    MethodType type() default MethodType.TASK;
}
