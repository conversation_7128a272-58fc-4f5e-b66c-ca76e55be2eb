<template>
  <base-page :config="config">
    <span slot="operation" slot-scope="text, record">
      <a @click="$refs.createForm.handleUpdate($event,record.id)">
      <a-icon type="edit" />{{ $t('app.global.edit') }}
      </a>
      <a-divider type="vertical" />
      <a @click="handleDelete(record.id)"> <a-icon type="delete" />{{ $t('app.global.delete') }}</a>
    </span>
    <!-- 弹窗 -->
    <create-form ref="createForm" :statusOptions="statusOptions" @ok="getList" />
  </base-page>
</template>

<script>
import CreateForm from './modules/CreateForm'
import { delMesCutting, listMesCutting } from '@/api/mesCutting'

export default {
  components: {
    CreateForm,
  },
  data() {
    return {
      // 页面加载状态
      loading: false,
      // 数据列表
      list: [],
      // 表格数据总数
      total: 0,
      // 状态数据字典
      statusOptions: [],
    }
  },
  async created() {
    this.getList()
  },
  computed: {
    config() {
      return {
        loading: this.loading,
        query: {
          onQuery: this.getList,
          items: [
                        { label: '订单id', name: 'orderId' },
                        { label: '下料长度', name: 'allLength' },
                        { label: '切割长度', name: 'cuttingLength' },
                        { label: '数量', name: 'cuttingCount' },
                        { label: '余量', name: 'surplus' },
                        { label: '', name: 'createTime' },
                        { label: '', name: 'createBy' },
                      ],
        },
        action: {
          add: () => this.$refs.createForm.handleAdd(),
          delete: this.handleDelete,
        },
        table: {
          total: this.total,
          list: this.list,
          columns: [
               { 
                title: '订单id', 
                dataIndex: 'orderId',
                align: 'center',
            },
                { 
                title: '下料长度', 
                dataIndex: 'allLength',
                align: 'center',
            },
                { 
                title: '切割长度', 
                dataIndex: 'cuttingLength',
                align: 'center',
            },
                { 
                title: '数量', 
                dataIndex: 'cuttingCount',
                align: 'center',
            },
                { 
                title: '余量', 
                dataIndex: 'surplus',
                align: 'center',
            },
                { 
                title: '', 
                dataIndex: 'createTime',
                align: 'center',
            },
                { 
                title: '', 
                dataIndex: 'createBy',
                align: 'center',
            },
                        {
              title: this.$t('app.global.operation'),
              dataIndex: 'operation',
              scopedSlots: { customRender: 'operation' },
              align: 'center',
            },
          ],
        },
      }
    },
  },
  methods: {
    /**
     * 查询${tableInfo.comment}列表
     */
    async getList(queryParam) {
      this.loading = true
      if (queryParam !== undefined) {
        this.queryParam = queryParam
      }
      this.loading = true
      const response = await listMesCutting(this.queryParam)
      this.list = response.rows
      this.total = response.total
      this.loading = false
    },
    /**
     * 删除按钮操作
     */
    handleDelete(id) {
      
      const ids = id || this.ids
      this.$alert.confirm({
        content: this.$t('app.global.delete.content'),
        onOk: async () => {
        await delMesCutting(ids)
        this.getList()
        this.$alert.success(this.$t('app.global.delete.success'))
        },
      })
    },
  },
}
</script>
}
