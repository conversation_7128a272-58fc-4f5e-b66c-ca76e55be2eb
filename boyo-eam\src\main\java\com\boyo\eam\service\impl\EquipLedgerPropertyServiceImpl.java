package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipLedger;
import com.boyo.eam.domain.EquipLedgerProperty;
import com.boyo.eam.mapper.EquipLedgerPropertyMapper;
import com.boyo.eam.service.IEquipLedgerPropertyService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备台账属性表(EquipLedgerProperty)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Service("equipLedgerPropertyService")
@AllArgsConstructor
@Tenant
public class EquipLedgerPropertyServiceImpl extends ServiceImpl<EquipLedgerPropertyMapper, EquipLedgerProperty> implements IEquipLedgerPropertyService {
    private final EquipLedgerPropertyMapper equipLedgerPropertyMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipLedgerProperty> selectEquipLedgerPropertyList(EquipLedgerProperty equipLedgerProperty) {
        return equipLedgerPropertyMapper.selectEquipLedgerPropertyList(equipLedgerProperty);
    }

    @Override
    public void removeEquipLedgerProperty(EquipLedger equipLedger) {
        equipLedgerPropertyMapper.delete(
                Wrappers.<EquipLedgerProperty>lambdaQuery()
                        .eq(EquipLedgerProperty::getEquipLedgerOpenid,equipLedger.getOpenid())
        );
    }

}
