package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipRelation;
import com.boyo.eam.domain.EquipWorkshopSection;
import com.boyo.eam.mapper.EquipRelationMapper;
import com.boyo.eam.mapper.EquipWorkshopSectionMapper;
import com.boyo.eam.service.IEquipRelationService;
import com.boyo.framework.annotation.Tenant;
import com.boyo.master.domain.ModelLine;
import com.boyo.master.domain.ModelWorkshop;
import com.boyo.master.mapper.ModelLineMapper;
import com.boyo.master.mapper.ModelWorkshopMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 车间、产线、工段关联表(EquipRelation)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-21 15:23:24
 */
@Service("equipRelationService")
@AllArgsConstructor
@Tenant
public class EquipRelationServiceImpl extends ServiceImpl<EquipRelationMapper, EquipRelation> implements IEquipRelationService {
    private final EquipRelationMapper equipRelationMapper;
    private final ModelWorkshopMapper modelWorkshopMapper;
    private final ModelLineMapper modelLineMapper;
    private final EquipWorkshopSectionMapper equipWorkshopSectionMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipRelation> selectEquipRelationList(EquipRelation equipRelation) {
        List<EquipRelation> equipRelations = equipRelationMapper.selectEquipRelationList(equipRelation);
        if (equipRelations!=null&&equipRelations.size()>0) {
            // 车间、产线、工段 的 opendid-名称
            Map allKeyValue = new HashMap<>();
            List<ModelWorkshop> modelWorkshopList = modelWorkshopMapper.selectList(Wrappers.lambdaQuery());
            for (ModelWorkshop workshop:modelWorkshopList){
                allKeyValue.put(workshop.getWorkshopOpenid(),workshop.getWorkshopName());
            }
            List<ModelLine> modelLineList = modelLineMapper.selectList(Wrappers.lambdaQuery());
            for (ModelLine line:modelLineList){
                allKeyValue.put(line.getLineOpenid(),line.getLineName());
            }
            List<EquipWorkshopSection> workshopSectionList = equipWorkshopSectionMapper.selectList(Wrappers.lambdaQuery());
            for (EquipWorkshopSection workshopSection:workshopSectionList){
                allKeyValue.put(workshopSection.getOpenid(),workshopSection.getName());
            }
            for (EquipRelation er : equipRelations) {
                String relationType = er.getRelationType();
                String relation = er.getRelation();
                // 0生产非生产，1车间，2产线，3工段
                if ("0".equals(relationType)) {
                    if ("production".equals(relation)) {
                        er.setName("生产");
                    } else if ("non-production".equals(relation)) {
                        er.setName("非生产");
                    }
                }else if ("4".equals(relationType)) {
                    if ("total".equals(relation)) {
                        er.setName("总");
                    }
                } else {
                    er.setName(String.valueOf(allKeyValue.get(relation)));
                }
            }
        }
        return equipRelations;
    }

}
