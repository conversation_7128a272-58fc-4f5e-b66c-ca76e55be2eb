package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipInspectionSpot;
import com.boyo.eam.mapper.EquipInspectionSpotMapper;
import com.boyo.eam.mapper.EquipLedgerMapper;
import com.boyo.eam.service.IEquipInspectionSpotService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 点检表(EquipInspectionSpot)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-15 16:16:30
 */
@Service("equipInspectionSpotService")
@AllArgsConstructor
@Tenant
public class EquipInspectionSpotServiceImpl extends ServiceImpl<EquipInspectionSpotMapper, EquipInspectionSpot> implements IEquipInspectionSpotService {
    private final EquipInspectionSpotMapper equipInspectionSpotMapper;
    private final EquipLedgerMapper equipLedgerMapper;
    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipInspectionSpot> selectEquipInspectionSpotList(EquipInspectionSpot equipInspectionSpot) {
        List<EquipInspectionSpot> equipInspectionSpots = equipInspectionSpotMapper.selectEquipInspectionSpotList(equipInspectionSpot);
        return equipInspectionSpots;
    }

}
