package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmReceivables;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * 回款表(CrmReceivables)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-28 10:22:52
 */
@Tenant
public interface CrmReceivablesMapper extends BaseMapper<CrmReceivables>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmReceivables 实例对象
     * @return 对象列表
     */
    List<CrmReceivables> selectCrmReceivablesList(CrmReceivables crmReceivables);


}

