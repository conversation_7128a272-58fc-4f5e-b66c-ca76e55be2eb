package com.boyo.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.mes.entity.ProductOrderDetail;
import com.boyo.mes.mapper.ProductOrderDetailMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.ProductOrder;
import com.boyo.mes.mapper.ProductOrderMapper;
import com.boyo.mes.service.IProductOrderService;

import java.io.Serializable;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 生产订单(ProductOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Service("productOrderService")
@AllArgsConstructor
@Tenant
public class ProductOrderServiceImpl extends ServiceImpl<ProductOrderMapper, ProductOrder> implements IProductOrderService {
    private final ProductOrderMapper productOrderMapper;
    private final ProductOrderDetailMapper productOrderDetailMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<ProductOrder> selectProductOrderList(ProductOrder productOrder) {
        return productOrderMapper.selectProductOrderList(productOrder);
    }

    /**
     * 获取设备可执行的工单
     * @param equipmentId
     * @return
     */
    @Override
    public List<ProductOrder> listExecuteOrder(Long equipmentId) {
        return productOrderMapper.listExecuteOrder(equipmentId, SecurityUtils.getUserId());
    }

    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        QueryWrapper<ProductOrderDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("order_id",idList);
        if(productOrderDetailMapper.selectCount(queryWrapper) > 0){
            throw new CustomException("要删除的工单已在执行，不能删除");
        }
        return super.removeByIds(idList);
    }

//    @Override
//    public boolean save(ProductOrder productOrder){
//        QueryWrapper<ProductOrder> productOrderQueryWrapper = new QueryWrapper<>();
//        productOrderQueryWrapper.eq("order_name",productOrder.getOrderName());
//        List<ProductOrder> list = productOrderMapper.selectList(productOrderQueryWrapper);
//        if(list.size() > 0){
//            throw  new CustomException("工单名称重复");
//        }
//        QueryWrapper<ProductOrder> orderQueryWrapper = new QueryWrapper<>();
//        orderQueryWrapper.eq("task_id", productOrder.getTaskId());
//        List<ProductOrder> orderList = productOrderMapper.selectList(orderQueryWrapper);
//        int totalNum = 0;
//        for (ProductOrder productOrder1 : orderList) {
//            totalNum += productOrder1.getProductionNum();
//        }
//        int leaveNum = productOrder.getOrderAmounts() - totalNum;
//        int totalAllNum = totalNum + productOrder.getProductionNum();
//        if(totalAllNum > productOrder.getOrderAmounts()){
//            throw new CustomException("工单生产数量超过订单剩余数量，订单剩余"+leaveNum);
//        }else if (totalNum == productOrder.getOrderAmounts()) {
//            UpdateWrapper<ProductOrder> updateWrapper = new UpdateWrapper<>();
//            updateWrapper.set("create_status", "Y").eq("task_id", productOrder.getTaskId());
//            productOrderMapper.update(null,updateWrapper);
//        } else {
//            UpdateWrapper<ProductOrder> updateWrapper = new UpdateWrapper<>();
//            updateWrapper.set("create_status", "N").eq("task_id", productOrder.getTaskId());
//            productOrderMapper.update(null,updateWrapper);
//        }
//        SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
//        QueryWrapper<ProductOrder> productOrderQueryWrapper1 = new QueryWrapper<>();
//        productOrderQueryWrapper1.like("order_num",simpleDateFormat.format(new Date())).orderByDesc("create_time");
//        List<ProductOrder> list1 = productOrderMapper.selectList(productOrderQueryWrapper1);
//        if(list1.size()>0){
//            String[] s = list1.get(0).getOrderNum().split("_");
//            DecimalFormat decimalFormat = new DecimalFormat("0000");
//            String maxSerialNumber = decimalFormat.format( Integer.parseInt(s[1])+ 1);
//            String id = s[0]+"_"+maxSerialNumber;
//            productOrder.setOrderNum(id);
//        }else {
//            productOrder.setOrderNum("GD"+simpleDateFormat.format(new Date())+"_"+"0001");
//        }
//        try {
//            productOrder.setCreateTime(simpleDateFormat1.parse(simpleDateFormat1.format(new Date())));
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//        productOrder.setCreateUserName(SecurityUtils.getUsername());
//        return super.save(productOrder);
//    }
}
