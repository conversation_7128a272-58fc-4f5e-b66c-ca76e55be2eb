package com.boyo.wms.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 出入库流水管理
 * 表名 t_wms_flow
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("出入库流水记录表")
@Data
@TableName("t_wms_flow")
public class WmsFlow extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @ApiModelProperty("自增主键")
    @TableId
    private Long id;
    /**
     * 出入库计划
     */
    @ApiModelProperty("出入库计划")
    @TableField(value = "plan_openid")
    private String planOpenid;


    @ApiModelProperty("出入库类型")
    @TableField(value = "flow_type")
    private String flowType;
    /**
     * 出入库明细
     */
    @ApiModelProperty("出入库明细")
    @TableField(value = "detail_openid")
    private String detailOpenid;

    /**
     * 出入库数量
     */
    @ApiModelProperty("出入库数量")
    @TableField(value = "materiel_number")
    private BigDecimal materielNumber;

    @ApiModelProperty("出入库物料批次")
    @TableField(value = "materiel_batch")
    private String materielBatch;
    /**
     * 仓库openid
     */
    @ApiModelProperty("仓库openid")
    @TableField(value = "warehouse_openid")
    private String warehouseOpenid;
    /**
     * 区域openid
     */
    @ApiModelProperty("区域openid")
    @TableField(value = "area_openid")
    private String areaOpenid;
    /**
     * 货位openid
     */
    @ApiModelProperty("货位openid")
    @TableField(value = "allocation_openid")
    private String allocationOpenid;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "created_at",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createdAt;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(value = "created_user",fill = FieldFill.INSERT)
    private String createdUser;


    /**
     * 物料名称
     */
    @TableField(exist = false)
    private String materielName;

    @TableField(exist = false)
    private Integer qcReport;
}
