<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.system.mapper.TSysEnterpriseMapper">

    <resultMap type="com.boyo.system.domain.TSysEnterprise" id="TSysEnterpriseResult">
        <result property="id" column="id"/>
        <result property="enterpriseOpenid" column="enterprise_openid"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="enterpriseAbbreviation" column="enterprise_abbreviation"/>
        <result property="enterpriseCode" column="enterprise_code"/>
        <result property="enterpriseLogincode" column="enterprise_logincode"/>
        <result property="enterpriseMaxRegister" column="enterprise_max_register"/>
        <result property="enterpriseContacts" column="enterprise_contacts"/>
        <result property="enterpriseTelephone" column="enterprise_telephone"/>
        <result property="enterprisePhone" column="enterprise_phone"/>
        <result property="enterpriseEmail" column="enterprise_email"/>
        <result property="enterpriseLogo" column="enterprise_logo"/>
        <result property="enterpriseProvince" column="enterprise_province"/>
        <result property="enterpriseCity" column="enterprise_city"/>
        <result property="enterpriseDistrict" column="enterprise_district"/>
        <result property="enterpriseAddress" column="enterprise_address"/>
        <result property="enterpriseLng" column="enterprise_lng"/>
        <result property="enterpriseLat" column="enterprise_lat"/>
        <result property="enterpriseInit" column="enterprise_init"/>
        <result property="enterpriseSysLogo" column="enterprise_sys_logo"/>
        <result property="enterpriseSysName" column="enterprise_sys_name"/>
        <result property="enterpriseAuthorization" column="enterprise_authorization"/>
        <result property="enterpriseValidity" column="enterprise_validity"/>
        <result property="enterpriseDomainName" column="enterprise_domain_name"/>
        <result property="enterpriseDatabaseVersion" column="enterprise_database_version"/>
        <result property="enterpriseDatabaseDriver" column="enterprise_database_driver"/>
        <result property="enterpriseDatabaseUrl" column="enterprise_database_url"/>
        <result property="enterpriseDatabaseUsername" column="enterprise_database_username"/>
        <result property="enterpriseDatabasePassword" column="enterprise_database_password"/>
        <result property="enterpriseStatus" column="enterprise_status"/>
        <result property="enterpriseScreen" column="enterprise_screen"/>
        <result property="creatUserDep" column="creat_user_dep"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTSysEnterpriseVo">
        select id,
               enterprise_openid,
               enterprise_name,
               enterprise_abbreviation,
               enterprise_code,
               enterprise_logincode,
               enterprise_max_register,
               enterprise_contacts,
               enterprise_telephone,
               enterprise_phone,
               enterprise_email,
               enterprise_logo,
               enterprise_province,
               enterprise_city,
               enterprise_district,
               enterprise_address,
               enterprise_lng,
               enterprise_lat,
               enterprise_init,
               enterprise_sys_logo,
               enterprise_sys_name,
               enterprise_authorization,
               enterprise_validity,
               enterprise_domain_name,
               enterprise_database_version,
               enterprise_database_driver,
               enterprise_database_url,
               enterprise_database_username,
               enterprise_database_password,
               enterprise_status,
               creat_user_dep,
               create_time,
               enterprise_screen,
               update_time
        from t_sys_enterprise
    </sql>

    <select id="selectTSysEnterpriseList" parameterType="com.boyo.system.domain.TSysEnterprise"
            resultMap="TSysEnterpriseResult">
        <include refid="selectTSysEnterpriseVo"/>
        <where>
            <if test="enterpriseOpenid != null  and enterpriseOpenid != ''">
                and enterprise_openid = #{enterpriseOpenid}
            </if>
            <if test="enterpriseName != null  and enterpriseName != ''">
                and enterprise_name like concat('%', #{enterpriseName}, '%')
            </if>
            <if test="enterpriseAbbreviation != null  and enterpriseAbbreviation != ''">
                and enterprise_abbreviation = #{enterpriseAbbreviation}
            </if>
            <if test="enterpriseCode != null  and enterpriseCode != ''">
                and enterprise_code = #{enterpriseCode}
            </if>
            <if test="enterpriseLogincode != null  and enterpriseLogincode != ''">
                and enterprise_logincode = #{enterpriseLogincode}
            </if>
            <if test="enterpriseMaxRegister != null ">
                and enterprise_max_register = #{enterpriseMaxRegister}
            </if>
            <if test="enterpriseContacts != null  and enterpriseContacts != ''">
                and enterprise_contacts = #{enterpriseContacts}
            </if>
            <if test="enterpriseTelephone != null  and enterpriseTelephone != ''">
                and enterprise_telephone = #{enterpriseTelephone}
            </if>
            <if test="enterprisePhone != null  and enterprisePhone != ''">
                and enterprise_phone = #{enterprisePhone}
            </if>
            <if test="enterpriseEmail != null  and enterpriseEmail != ''">
                and enterprise_email = #{enterpriseEmail}
            </if>
            <if test="enterpriseLogo != null  and enterpriseLogo != ''">
                and enterprise_logo = #{enterpriseLogo}
            </if>
            <if test="enterpriseProvince != null  and enterpriseProvince != ''">
                and enterprise_province = #{enterpriseProvince}
            </if>
            <if test="enterpriseCity != null  and enterpriseCity != ''">
                and enterprise_city = #{enterpriseCity}
            </if>
            <if test="enterpriseDistrict != null  and enterpriseDistrict != ''">
                and enterprise_district = #{enterpriseDistrict}
            </if>
            <if test="enterpriseAddress != null  and enterpriseAddress != ''">
                and enterprise_address = #{enterpriseAddress}
            </if>
            <if test="enterpriseLng != null  and enterpriseLng != ''">
                and enterprise_lng = #{enterpriseLng}
            </if>
            <if test="enterpriseLat != null  and enterpriseLat != ''">
                and enterprise_lat = #{enterpriseLat}
            </if>
            <if test="enterpriseInit != null  and enterpriseInit != ''">
                and enterprise_init = #{enterpriseInit}
            </if>
            <if test="enterpriseSysLogo != null  and enterpriseSysLogo != ''">
                and enterprise_sys_logo = #{enterpriseSysLogo}
            </if>
            <if test="enterpriseSysName != null  and enterpriseSysName != ''">
                and enterprise_sys_name like concat('%', #{enterpriseSysName}, '%')
            </if>
            <if test="enterpriseAuthorization != null  and enterpriseAuthorization != ''">
                and enterprise_authorization = #{enterpriseAuthorization}
            </if>
            <if test="enterpriseValidity != null ">
                and enterprise_validity = #{enterpriseValidity}
            </if>
            <if test="enterpriseDomainName != null  and enterpriseDomainName != ''">
                and enterprise_domain_name like concat('%', #{enterpriseDomainName}, '%')
            </if>
            <if test="enterpriseDatabaseVersion != null ">
                and enterprise_database_version = #{enterpriseDatabaseVersion}
            </if>
            <if test="enterpriseDatabaseDriver != null  and enterpriseDatabaseDriver != ''">
                and enterprise_database_driver = #{enterpriseDatabaseDriver}
            </if>
            <if test="enterpriseDatabaseUrl != null  and enterpriseDatabaseUrl != ''">
                and enterprise_database_url = #{enterpriseDatabaseUrl}
            </if>
            <if test="enterpriseDatabaseUsername != null  and enterpriseDatabaseUsername != ''">
                and enterprise_database_username like concat('%', #{enterpriseDatabaseUsername}, '%')
            </if>
            <if test="enterpriseDatabasePassword != null  and enterpriseDatabasePassword != ''">
                and enterprise_database_password = #{enterpriseDatabasePassword}
            </if>
            <if test="enterpriseStatus != null  and enterpriseStatus != ''">
                and enterprise_status = #{enterpriseStatus}
            </if>
            <if test="creatUserDep != null  and creatUserDep != ''">
                and creat_user_dep = #{creatUserDep}
            </if>
        </where>
    </select>
</mapper>
