package com.boyo.master.service.impl;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.master.mapper.TModelFactoryMapper;
import com.boyo.master.domain.TModelFactory;
import com.boyo.master.service.ITModelFactoryService;

/**
 * 主数据-工厂管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class TModelFactoryServiceImpl extends ServiceImpl<TModelFactoryMapper, TModelFactory> implements ITModelFactoryService {
    private final TModelFactoryMapper tModelFactoryMapper;


    /**
     * 查询主数据-工厂管理列表
     *
     * @param tModelFactory 主数据-工厂管理
     * @return tModelFactory 列表
     */
    @Override
    public List<TModelFactory> selectTModelFactoryList(TModelFactory tModelFactory) {
        return tModelFactoryMapper.selectTModelFactoryList(tModelFactory);
    }

    @Override
    public boolean checkExist(TModelFactory tModelFactory) {
        QueryWrapper<TModelFactory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("factory_code", tModelFactory.getFactoryCode());
        if(ObjectUtil.isNotNull(tModelFactory.getId())){
            queryWrapper.ne("id",tModelFactory.getId());
        }
        if (tModelFactoryMapper.selectCount(queryWrapper) > 0) {
            return true;
        } else {
            return false;
        }
    }
}
