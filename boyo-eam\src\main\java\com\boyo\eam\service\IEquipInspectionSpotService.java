package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipInspectionSpot;

import java.util.List;

/**
 * 点检表(EquipInspectionSpot)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-15 16:16:30
 */
public interface IEquipInspectionSpotService extends IService<EquipInspectionSpot> {

    /**
     * 查询多条数据
     *
     * @param equipInspectionSpot 对象信息
     * @return 对象列表
     */
    List<EquipInspectionSpot> selectEquipInspectionSpotList(EquipInspectionSpot equipInspectionSpot);


}
