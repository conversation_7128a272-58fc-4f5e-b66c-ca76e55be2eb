package com.boyo.crm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.annotation.DataScope;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.crm.util.ActionEnum;
import com.boyo.crm.util.ActionUtil;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.entity.CrmCustomer;
import com.boyo.crm.mapper.CrmCustomerMapper;
import com.boyo.crm.service.ICrmCustomerService;

import java.util.List;

/**
 * CRM客户表(CrmCustomer)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-25 16:07:33
 */
@Service("crmCustomerService")
@AllArgsConstructor
public class CrmCustomerServiceImpl extends ServiceImpl<CrmCustomerMapper, CrmCustomer> implements ICrmCustomerService {
    private final CrmCustomerMapper crmCustomerMapper;
    private final ActionUtil actionUtil;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    @DataScope(columns = "owner_user_id,create_user_id")
    public List<CrmCustomer> selectCrmCustomerList(CrmCustomer crmCustomer) {
        return crmCustomerMapper.selectCrmCustomerList(crmCustomer);
    }

    @Override
    public List<CrmCustomer> selectPoolCustomerList(CrmCustomer crmCustomer) {
        return crmCustomerMapper.selectPoolCustomerList(crmCustomer);
    }

    @Override
    public void moveToPool(Integer id) {
        crmCustomerMapper.update(null,
                Wrappers.<CrmCustomer>lambdaUpdate()
                        .set(CrmCustomer::getOwnerUserId, null)
                        .eq(CrmCustomer::getId, id));
    }

    @Override
    public void claimCustomer(Integer id) {
        crmCustomerMapper.update(null,
                Wrappers.<CrmCustomer>lambdaUpdate()
                        .set(CrmCustomer::getOwnerUserId, SecurityUtils.getUserOpenid())
                        .eq(CrmCustomer::getId, id));
    }

    @Override
    public boolean save(CrmCustomer entity) {
        if (ObjectUtil.isNull(entity.getOwnerUserId())) {
            entity.setOwnerUserId(SecurityUtils.getUserOpenid());
        }
        super.save(entity);
        actionUtil.editRecord(null, null, ActionEnum.CUSTOMER, entity.getId(), null);
        return true;
    }

    @Override
    public boolean updateById(CrmCustomer entity) {
        actionUtil.editRecord(super.getById(entity.getId()), entity, ActionEnum.CUSTOMER, entity.getId(), CrmCustomer.class);
        return super.updateById(entity);
    }
}
