package com.boyo.wms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.wms.entity.WmsFlowqcDetail;
import java.util.List;

/**
 * (WmsFlowqcDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-10 15:12:10
 */
public interface WmsFlowqcDetailMapper extends BaseMapper<WmsFlowqcDetail>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param wmsFlowqcDetail 实例对象
     * @return 对象列表
     */
    List<WmsFlowqcDetail> selectWmsFlowqcDetailList(WmsFlowqcDetail wmsFlowqcDetail);


}

