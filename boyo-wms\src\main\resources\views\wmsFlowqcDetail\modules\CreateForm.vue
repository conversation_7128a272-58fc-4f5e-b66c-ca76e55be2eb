<template>
  <a-modal width="30%" :maskClosable="false" :visible="open" @cancel="cancel">
    <template #title>
      <a-icon type="security-scan" />
      {{ formTitle }}
    </template>
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      layout="horizontal"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
           <a-form-model-item label="主键">
        <a-input
          :size="formSize"
          v-model="form.id"
          :placeholder="$t('app.global.please.input') + '主键'"
        />
      </a-form-model-item>
           <a-form-model-item label="索引id">
        <a-input
          :size="formSize"
          v-model="form.pId"
          :placeholder="$t('app.global.please.input') + '索引id'"
        />
      </a-form-model-item>
           <a-form-model-item label="质检项名称">
        <a-input
          :size="formSize"
          v-model="form.itemName"
          :placeholder="$t('app.global.please.input') + '质检项名称'"
        />
      </a-form-model-item>
           <a-form-model-item label="质检项code">
        <a-input
          :size="formSize"
          v-model="form.itemCode"
          :placeholder="$t('app.global.please.input') + '质检项code'"
        />
      </a-form-model-item>
           <a-form-model-item label="质检结果">
        <a-input
          :size="formSize"
          v-model="form.itemResult"
          :placeholder="$t('app.global.please.input') + '质检结果'"
        />
      </a-form-model-item>
        </a-form-model>
    <template #footer>
      <a-space>
        <a-button :size="formSize" icon="close" type="danger" @click="cancel">
          {{ $t('app.global.close') }}
        </a-button>
        <a-button :size="formSize" icon="save" type="primary" @click="submitForm">
          {{ $t('app.global.save') }}
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script>
import { addWmsFlowqcDetail, updateWmsFlowqcDetail, getWmsFlowqcDetail } from '@/api/wmsFlowqcDetail'
export default {
  data() {
    return {
      //新增或修改
      updateState: false,
      formTitle: '$tableInfo.comment',
      // 表单参数
      form: {
           id: '',
           pId: '',
           itemName: '',
           itemCode: '',
           itemResult: '',
             },
      open: false,
      rules: {},
    }
  },
  created() {
    this.rules = {
    }
  },
  methods: {
    /**
     * 新增按钮操作
     * */
    handleAdd() {
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.add') + '$tableInfo.comment'
      this.form = {}
      this.updateState = false
    },
    /**
     * 修改按钮操作
     * */
    async handleUpdate($event, id) {
      $event.stopPropagation()
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.edit') + '$tableInfo.comment'
      const response = await getWmsFlowqcDetail(id)
      this.form = response.data
    },
    /**
     * 提交按钮
     * */
    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
            if (this.form.id) {
                await updateWmsFlowqcDetail(this.form)
                this.$alert.success(this.$t('app.global.edit.success'))
                this.open = false
                this.$emit('ok')
            } else {
                await addWmsFlowqcDetail(this.form)
                this.$alert.success(this.$t('app.global.add.success'))
                this.open = false
                this.$emit('ok')
            }
        } else {
            return false
        }
        })
    },
    /**
     * 取消按钮
     * */
    cancel() {
      this.open = false
      this.reset()
    },
    /**
     * 表单重置
     * */
    reset() {
      this.form = {}
      if (this.$refs.form) {
        this.$refs.form.resetFields()}
      }
    },
}
</script>

