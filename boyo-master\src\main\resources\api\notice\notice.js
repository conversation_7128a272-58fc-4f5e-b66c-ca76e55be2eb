import request from '@/utils/request'

const prefix = '/${module}'

// 查询通知公告表(Notice)列表
export function listNotice(query) {
  return request({
    url: prefix + '/notice/list',
    method: 'get',
    params: query,
  })
}

// 查询通知公告表(Notice)详细
export function getNotice(id) {
  return request({
    url: prefix + '/notice/' + id,
    method: 'get',
  })
}

// 新增通知公告表(Notice)
export function addNotice(data) {
  return request({
    url: prefix + '/notice',
    method: 'post',
    data: data,
  })
}

// 修改通知公告表(Notice)
export function updateNotice(data) {
  return request({
    url: prefix + '/notice',
    method: 'put',
    data: data,
  })
}

// 删除通知公告表(Notice)
export function delNotice(id) {
  return request({
    url: prefix + '/notice/' + id,
    method: 'delete',
  })
}
