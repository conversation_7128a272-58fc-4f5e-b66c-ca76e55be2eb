package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.ObjectInfoProperty;

import java.util.List;

/**
 * 物模型属性表(ObjectInfoProperty)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
public interface ObjectInfoPropertyMapper extends BaseMapper<ObjectInfoProperty>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param objectInfoProperty 实例对象
     * @return 对象列表
     */
    List<ObjectInfoProperty> selectObjectInfoPropertyList(ObjectInfoProperty objectInfoProperty);


}

