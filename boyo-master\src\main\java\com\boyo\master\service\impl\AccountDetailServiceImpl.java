package com.boyo.master.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.master.domain.EnterpriseConstant;
import com.boyo.master.domain.annotations.DataAsset;
import com.boyo.master.domain.annotations.Enterprise;
import com.boyo.master.entity.AccountBalance;
import com.boyo.master.entity.AccountDetail;
import com.boyo.master.mapper.AccountDetailMapper;
import com.boyo.master.service.AccountBalanceService;
import com.boyo.master.service.AccountDetailService;
import com.boyo.master.service.DataAssetService;
import com.boyo.master.utils.HttpUtil;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.boyo.master.register.DataAssetRegister.getDataAssetType;

/**
 * <AUTHOR>
 * @description 针对表【bu_enterprise_data_asset_account_detail】的数据库操作Service实现
 * @createDate 2024-11-16 14:16:13
 */
@Slf4j
@DataAsset(value = "科目明细表", dependsOn = AccountDetailServiceImpl.class)
@Service
public class AccountDetailServiceImpl extends ServiceImpl<AccountDetailMapper, AccountDetail> implements AccountDetailService, DataAssetService {

    @Autowired
    private AccountBalanceService accountBalanceService;


    private String ncUrl = EnterpriseConstant.NCURL;

    @Override
    public Object getDataAssetList(IPage page, Map<String, String> params) {
       PageHelper.clearPage();
        return this.page(page, new LambdaQueryWrapper<AccountDetail>()
                .eq(AccountDetail::getEnterpriseId, params.get(EnterpriseConstant.ENTERPRISE_ID))
                .eq(StrUtil.isNotEmpty(params.get("childrenCompany")), AccountDetail::getChildrenCompany, params.get("childrenCompany"))
                .eq(StrUtil.isNotEmpty(params.get("year")), AccountDetail::getYear, params.get("year"))
                .eq(StrUtil.isNotEmpty(params.get("period")), AccountDetail::getPeriod, params.get("period"))
                .like(StrUtil.isNotEmpty(params.get("subjcode")), AccountDetail::getSubjcode, params.get("subjcode"))
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncDataAssetHaihui() throws Exception {
        Enterprise annotation = getCurrentMethodAnnotation(Enterprise.class);
        Long enterpriseId = Convert.toLong(annotation.value());

        int period = LocalDate.now().minusDays(1).getMonthValue();
        int year = LocalDate.now().minusDays(1).getYear();
        List<AccountBalance> accountBalanceList = accountBalanceService.list(new LambdaQueryWrapper<AccountBalance>()
                .eq(AccountBalance::getYear, year)
                .eq(AccountBalance::getPeriod, period)
        );
        final String dataAssetType = getDataAssetType(this);
        Map<String, String> company = (Map<String, String>) getSelectorList(dataAssetType, enterpriseId)
                .getOrDefault("company", new HashMap<>());
        for (String companyItem : company.keySet()) {
            List<String> subjcodeList = accountBalanceList.stream().filter(item -> companyItem.equals(item.getChildrenCompany())).map(AccountBalance::getSubjcode).collect(Collectors.toList());
            for (String subjcode : subjcodeList) {
                // 1. 拼装查询参数
                Map<String, Object> params = new HashMap<>();
                params.put("period", period);
                params.put("year", year);
                params.put("url", "api/nc57/index/accountDetail");
                params.put("unitname", companyItem);
                params.put("subjcode", subjcode);
                log.info("科目明细表同步数据开始，查询参数【{}】", params.toString());
                // 2. 发送请求获取数据
                final ResponseEntity<?> responseEntity = HttpUtil.postData(params);
                String post = responseEntity.getBody().toString();
                // 3. 解析响应数据
                JSONObject response = null;
                try {
                    response = JSON.parseObject(post);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("科目明细表请求失败，查询参数【{}】返回数据【{}】", params.toString(), post);
                    return;
                }
                String data = response.getString("data");
                if (StrUtil.isEmpty(data)) {
                    continue;
                }
                AccountDetail[] dataArr = JSON.parseObject(data).getObject("data", AccountDetail[].class);
                // 4. 循环处理数据
                List<String> remoteIdList = new ArrayList<>();
                List<AccountDetail> dataList = new ArrayList<>();
                for (AccountDetail item : dataArr) {
                    item.setChildrenCompany(companyItem);
                    item.setEnterpriseId(enterpriseId);
                    item.setRemoteId(StrUtil.format("{}-{}-{}-{}", item.getYear(), item.getPeriod(), item.getChildrenCompany(), item.getSubjcode()));
                    remoteIdList.add(item.getRemoteId());
                    dataList.add(item);
                }
                // 5. 更新数据
                this.remove(new LambdaQueryWrapper<AccountDetail>()
                        .eq(AccountDetail::getEnterpriseId, enterpriseId)
                        .in(AccountDetail::getRemoteId, remoteIdList)
                );
                if (!this.saveBatch(dataList)) {
                    log.error("科目明细表同步数据失败，查询参数【{}】", params.toString());
                }
            }
        }
    }
}




