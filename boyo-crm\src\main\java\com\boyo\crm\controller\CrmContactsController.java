package com.boyo.crm.controller;

import com.boyo.crm.entity.CrmContacts;
import com.boyo.crm.service.ICrmContactsService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 联系人表(CrmContacts)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-25 17:57:52
 */
@Api("联系人表")
@RestController
@RequestMapping("/crm/crmContacts")
@AllArgsConstructor
public class CrmContactsController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmContactsService crmContactsService;

    /**
     * 查询联系人表列表
     *
     */
    @ApiOperation("查询联系人表列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmContacts crmContacts) {
        startPage();
        List<CrmContacts> list = crmContactsService.selectCrmContactsList(crmContacts);
        return getDataTable(list);
    }
    
    /**
     * 获取联系人表详情
     */
    @ApiOperation("获取联系人表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(crmContactsService.getById(id));
    }

    /**
     * 新增联系人表
     */
    @ApiOperation("新增联系人表")
    @PostMapping
    public AjaxResult add(@RequestBody CrmContacts crmContacts) {
        return toBooleanAjax(crmContactsService.save(crmContacts));
    }

    /**
     * 修改联系人表
     */
    @ApiOperation("修改联系人表")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmContacts crmContacts) {
        return toBooleanAjax(crmContactsService.updateById(crmContacts));
    }

    /**
     * 删除联系人表
     */
    @ApiOperation("删除联系人表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmContactsService.removeByIds(Arrays.asList(ids)));
    }

}
