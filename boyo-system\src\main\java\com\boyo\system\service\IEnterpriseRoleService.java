package com.boyo.system.service;

import java.util.List;

import com.boyo.system.domain.EnterpriseRole;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 企业角色管理Service接口
 *
 * <AUTHOR>
 */
public interface IEnterpriseRoleService extends IService<EnterpriseRole> {
    /**
     * 根据条件查询查询企业角色管理列表
     *
     * @param enterpriseRole 企业角色管理
     * @return 企业角色管理集合
     */
    List<EnterpriseRole> selectEnterpriseRoleList(EnterpriseRole enterpriseRole);

    void saveRoleAdmin(EnterpriseRole enterpriseRole);
}
