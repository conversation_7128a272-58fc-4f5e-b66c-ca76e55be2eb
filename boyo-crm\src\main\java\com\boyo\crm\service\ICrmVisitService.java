package com.boyo.crm.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.crm.entity.CrmVisit;
import java.util.List;

/**
 * 回访表(CrmVisit)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-28 17:06:59
 */
public interface ICrmVisitService extends IService<CrmVisit> {

    /**
     * 查询多条数据
     *
     * @param crmVisit 对象信息
     * @return 对象列表
     */
    List<CrmVisit> selectCrmVisitList(CrmVisit crmVisit);


}
