package com.boyo.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.mes.entity.ProcessEquipment;
import java.util.List;

/**
 * 工序设备关联关系(ProcessEquipment)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
public interface ProcessEquipmentMapper extends BaseMapper<ProcessEquipment>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param processEquipment 实例对象
     * @return 对象列表
     */
    List<ProcessEquipment> selectProcessEquipmentList(ProcessEquipment processEquipment);


}

