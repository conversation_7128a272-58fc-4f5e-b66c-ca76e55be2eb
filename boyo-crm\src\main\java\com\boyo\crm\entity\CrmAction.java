package com.boyo.crm.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 操作记录表(CrmAction)实体类
 *
 * <AUTHOR>
 * @since 2022-03-28 18:36:06
 */
@Data
@TableName(value = "t_crm_action")
public class CrmAction extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = -38679168705682413L;
            
    @TableId
    private Integer id;
    
    /**
    * 操作人ID
    */
    @TableField(value="create_user_id",fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 创建时间
    */
    @TableField(value="create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 模块类型
    */
    @TableField(value="types")
    private String types;
    /**
    * 被操作对象ID
    */
    @TableField(value="action_id")
    private Integer actionId;
    /**
    * 详情
    */
    @TableField(value="detail")
    private String detail;

    @TableField(exist = false)
    private String createUserName;

}
