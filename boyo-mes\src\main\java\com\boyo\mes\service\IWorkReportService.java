package com.boyo.mes.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.mes.entity.WorkReport;
import java.util.List;

/**
 * 报工记录(WorkReport)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
public interface IWorkReportService extends IService<WorkReport> {

    /**
     * 查询多条数据
     *
     * @param workReport 对象信息
     * @return 对象列表
     */
    List<WorkReport> selectWorkReportList(WorkReport workReport);

    List<WorkReport> getOrderReportDetail(Integer orderId);


}
