package com.boyo.master.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.thoughtworks.xstream.converters.time.LocalDateConverter;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 销售出库单
 * @TableName bu_enterprise_data_asset_sale_delivery
 */
@TableName(value ="bu_enterprise_data_asset_sale_delivery")
@Data
public class SaleDelivery extends DataAssetBase implements Serializable {
    /**
     * 库存组织编号
     */
    @JSONField(name = "BODYCODE")
    @TableField(value = "bodycode")
    private String bodycode;

    /**
     * 库存组织名称
     */
    @JSONField(name = "BODYNAME")
    @TableField(value = "bodyname")
    private String bodyname;

    /**
     * 单据类型名称
     */
    @JSONField(name = "VBILLTYPENAME")
    @TableField(value = "vbilltypename")
    private String vbilltypename;

    /**
     * 存货编码
     */
    @JSONField(name = "INVCODE")
    @TableField(value = "invcode")
    private String invcode;

    /**
     * 存货名称
     */
    @JSONField(name = "INVNAME")
    @TableField(value = "invname")
    private String invname;

    /**
     * 规格
     */
    @JSONField(name = "INVSPEC")
    @TableField(value = "invspec")
    private String invspec;

    /**
     * 型号
     */
    @JSONField(name = "INVTYPE")
    @TableField(value = "invtype")
    private String invtype;

    /**
     * 实出数量
     */
    @JSONField(name = "NOUTNUM")
    @TableField(value = "noutnum")
    private BigDecimal noutnum;

    /**
     * 单据日期
     */
    @JSONField(format="yyyy-MM-dd", name = "DBILLDATE")
    @TableField(value = "dbilldate")
    private Date dbilldate;

    /**
     * 含税单价
     */
    @JSONField(name = "NTAXPRICE")
    @TableField(value = "ntaxprice")
    private BigDecimal ntaxprice;

    /**
     * 含税金额
     */
    @JSONField(name = "NTAXMNY")
    @TableField(value = "ntaxmny")
    private BigDecimal ntaxmny;

    /**
     * 单据编码
     */
    @JSONField(name = "VBILLCODE")
    @TableField(value = "vbillcode")
    private String vbillcode;

    /**
     * 源头单据编码
     */
    @JSONField(name = "VFIRSTBILLCODE")
    @TableField(value = "vfirstbillcode")
    private String vfirstbillcode;

    @TableField(value = "children_company")
    private String childrenCompany;

    @JSONField(name = "DACCOUNTDATE", format="yyyy-MM-dd")
    @TableField(value = "daccountdate")
    private LocalDate daccountdate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
