package com.boyo.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.crm.entity.CrmContacts;
import com.boyo.framework.annotation.Tenant;

import java.util.List;

/**
 * 联系人表(CrmContacts)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-25 17:57:52
 */
@Tenant
public interface CrmContactsMapper extends BaseMapper<CrmContacts>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param crmContacts 实例对象
     * @return 对象列表
     */
    List<CrmContacts> selectCrmContactsList(CrmContacts crmContacts);


}

