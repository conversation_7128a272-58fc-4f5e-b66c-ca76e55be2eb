<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipInspectionSpotItemMapper">

    <resultMap type="com.boyo.eam.domain.EquipInspectionSpotItem" id="EquipInspectionSpotItemResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="equipInspectionSpotOpenid" column="equip_inspection_spot_openid" />
        <result property="item" column="item" />
        <result property="basis" column="basis" />
        <result property="method" column="method" />
        <result property="way" column="way" />
        <result property="useTime" column="use_time" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipInspectionSpotItemList" parameterType="com.boyo.eam.domain.EquipInspectionSpotItem" resultType="com.boyo.eam.domain.EquipInspectionSpotItem">
        select
            EISI.*,
            EIS.code as spotCode,
            EIS.type as type,
            EL.code as equipCode,
            EL.name as equipName,
            ET.name as equipType,
            TML.line_openid as lineOpenid,
            TML.line_name as lineName,
            EIS.id as spotId,
            EIR.inspection_date as inspectionDate,
            EIR.pass as pass,
            EIR.remark as recordRemark,
            EIR.media_id as mediaId,
            EIR.openid as recordOpenid
        from
        equip_inspection_spot_item EISI left join
        equip_inspection_spot EIS on EISI.equip_inspection_spot_openid=EIS.openid left join
        equip_ledger EL on EIS.equip_ledger_openid=El.openid left join
        equip_type ET on EL.equip_type_openid=ET.openid left join
        t_model_line TML on EIS.line_openid=TML.line_openid left join
        equip_inspection_record EIR on EISI.openid=EIR.equip_inspection_spot_item_openid

        <where>
            <if test="openid != null and openid != ''">
                and EISI.openid = #{openid}
            </if>
            <if test="equipInspectionSpotOpenid != null and equipInspectionSpotOpenid != ''">
                and EISI.equip_inspection_spot_openid = #{equipInspectionSpotOpenid}
            </if>
            <if test="item != null and item != ''">
                and EISI.item = #{item}
            </if>
            <if test="basis != null and basis != ''">
                and EISI.basis = #{basis}
            </if>
            <if test="method != null and method != ''">
                and EISI.method = #{method}
            </if>
            <if test="way != null and way != ''">
                and EISI.way = #{way}
            </if>
            <if test="createBy != null and createBy != ''">
                and EISI.create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and EISI.create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and EISI.update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and EISI.update_time = #{updateTime}
            </if>
            <if test="equipName != null and equipName != ''">
                and EL.name like CONCAT('%',#{equipName},'%')
            </if>
            <if test="equipCode != null and equipCode != ''">
                and EL.code like CONCAT('%',#{equipCode},'%')
            </if>
            <if test="lineOpenid != null and lineOpenid != ''">
                and TML.line_openid = #{lineOpenid}
            </if>
            <if test="lineName != null and lineName != ''">
                and TML.line_name like CONCAT('%',#{lineName},'%')
            </if>
            <if test="type != null and type != ''">
                and EIS.type = #{type}
            </if>
            <if test="beginDate!=null and endDate!=null">
                and #{beginDate} &lt;= EIR.inspection_date
                and EIR.inspection_date &lt;= #{endDate}
            </if>
        </where>
    </select>

    <select id="getItemAndRecord" resultType="EquipInspectionSpotItem">
        select
            EISI.*,
            EIS.code as spotCode,
            EIS.type as type,
            EL.code as equipCode,
            EL.name as equipName,
            EL.location as equipLocation,
            TML.line_openid as lineOpenid,
            TML.line_name as lineName,
            EIS.id as spotId,
            EIR.inspection_date as inspectionDate,
            EIR.pass as pass,
            EIR.remark as recordRemark,
            EIR.media_id as mediaId
        from
        equip_inspection_spot_item EISI left join
        equip_inspection_spot EIS on EISI.equip_inspection_spot_openid=EIS.openid left join
        equip_ledger EL on EIS.equip_ledger_openid=El.openid left join
        t_model_line TML on EIS.line_openid=TML.line_openid left join
        equip_inspection_record EIR on EISI.openid=EIR.equip_inspection_spot_item_openid
        where
        EISI.id = #{id}
    </select>
</mapper>

