package com.boyo.mes.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.boyo.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * (TTransportOrder)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-12 09:30:39
 */
@TableName("t_transport_order")
@Data
public class TTransportOrder extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    //订单编号
    private String orderNum;
    //订单名称
    private String orderName;
    //客户名称
    private int customerId;
    //客户名称
    private String customerName;
    //订单数量
    private Integer number;
    //单位
    private String unit;
    //发货方式
    private String deliveryWay;
    //发货数量
    private Integer deliveryNum;
    //发货数量
    private String invoiceNumber;
    //发货状态
    private Integer shippingStatus;
    //发货人
    private String consigner;
    //发货报告
    private String deliveryReport;
    //发货时间
    @TableField(value="delivery_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;
    //到货时间
    @TableField(value="receive_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;


    @TableField(value="create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField(exist = false)
    private Date startTime;

    @TableField(exist = false)
    private Date endTime;

}

