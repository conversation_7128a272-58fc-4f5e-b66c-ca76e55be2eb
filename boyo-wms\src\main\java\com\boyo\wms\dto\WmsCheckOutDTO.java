package com.boyo.wms.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 快速出库传输对象
 * <AUTHOR>
 */
@Data
public class WmsCheckOutDTO {
    /**
     * 物料openid
     */
    private String materielOpenid;

    /**
     * 出库类型
     */
    private String planCategory;

    /**
     * 出库量
     */
    private BigDecimal quantity;

    /**
     * 仓库
     */
    private String warehouseOpenid;

    /**
     * 区域
     */
    private String areaOpenid;

    /**
     * 货位
     */
    private String allocationOpenid;

    /**
     * 物料批次
     */
    private String materielBatch;
}
