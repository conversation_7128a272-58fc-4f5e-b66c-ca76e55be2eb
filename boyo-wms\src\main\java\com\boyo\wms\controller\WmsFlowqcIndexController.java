package com.boyo.wms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.system.service.IEnterpriseUserService;
import com.boyo.wms.entity.WmsFlowqcIndex;
import com.boyo.wms.service.IWmsFlowqcIndexService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * (WmsFlowqcIndex)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-10 15:12:17
 */
@Api("")
@RestController
@RequestMapping("/wms/wmsFlowqcIndex")
@AllArgsConstructor
public class WmsFlowqcIndexController extends BaseController{
    /**
     * 服务对象
     */
    private final IWmsFlowqcIndexService wmsFlowqcIndexService;

    private final IEnterpriseUserService enterpriseUserService;


    /**
     * 查询列表
     *
     */
    @ApiOperation("查询列表")
    @GetMapping("/list")
    public TableDataInfo list(WmsFlowqcIndex wmsFlowqcIndex) {
        startPage();
        List<WmsFlowqcIndex> list = wmsFlowqcIndexService.selectWmsFlowqcIndexList(wmsFlowqcIndex);
        return getDataTable(list);
    }
    
    /**
     * 获取详情
     */
    @ApiOperation("获取详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        WmsFlowqcIndex obj = wmsFlowqcIndexService.getById(id);
        QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_openid",obj.getCreateUser());
        EnterpriseUser user = enterpriseUserService.getOne(queryWrapper);
        obj.setUserName(user.getUserFullName());
        return AjaxResult.success(obj);
    }

    /**
     * 新增
     */
    @ApiOperation("新增")
    @PostMapping
    public AjaxResult add(@RequestBody WmsFlowqcIndex wmsFlowqcIndex) {
        return toBooleanAjax(wmsFlowqcIndexService.save(wmsFlowqcIndex));
    }

    /**
     * 修改
     */
    @ApiOperation("修改")
    @PutMapping
    public AjaxResult edit(@RequestBody WmsFlowqcIndex wmsFlowqcIndex) {
        return toBooleanAjax(wmsFlowqcIndexService.updateById(wmsFlowqcIndex));
    }

    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(wmsFlowqcIndexService.removeByIds(Arrays.asList(ids)));
    }

}
