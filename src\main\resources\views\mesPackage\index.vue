<template>
  <base-page :config="config">
    <span slot="operation" slot-scope="text, record">
      <a @click="$refs.createForm.handleUpdate($event,record.id)">
      <a-icon type="edit" />{{ $t('app.global.edit') }}
      </a>
      <a-divider type="vertical" />
      <a @click="handleDelete(record.id)"> <a-icon type="delete" />{{ $t('app.global.delete') }}</a>
    </span>
    <!-- 弹窗 -->
    <create-form ref="createForm" :statusOptions="statusOptions" @ok="getList" />
  </base-page>
</template>

<script>
import CreateForm from './modules/CreateForm'
import { delMesPackage, listMesPackage } from '@/api/mesPackage'

export default {
  components: {
    CreateForm,
  },
  data() {
    return {
      // 页面加载状态
      loading: false,
      // 数据列表
      list: [],
      // 表格数据总数
      total: 0,
      // 状态数据字典
      statusOptions: [],
    }
  },
  async created() {
    this.getList()
  },
  computed: {
    config() {
      return {
        loading: this.loading,
        query: {
          onQuery: this.getList,
          items: [
                        { label: '订单id', name: 'orderId' },
                        { label: '打包数量', name: 'packageNum' },
                        { label: '', name: 'createTime' },
                        { label: '', name: 'createBy' },
                      ],
        },
        action: {
          add: () => this.$refs.createForm.handleAdd(),
          delete: this.handleDelete,
        },
        table: {
          total: this.total,
          list: this.list,
          columns: [
               { 
                title: '订单id', 
                dataIndex: 'orderId',
                align: 'center',
            },
                { 
                title: '打包数量', 
                dataIndex: 'packageNum',
                align: 'center',
            },
                { 
                title: '', 
                dataIndex: 'createTime',
                align: 'center',
            },
                { 
                title: '', 
                dataIndex: 'createBy',
                align: 'center',
            },
                        {
              title: this.$t('app.global.operation'),
              dataIndex: 'operation',
              scopedSlots: { customRender: 'operation' },
              align: 'center',
            },
          ],
        },
      }
    },
  },
  methods: {
    /**
     * 查询打包管理列表
     */
    async getList(queryParam) {
      this.loading = true
      if (queryParam !== undefined) {
        this.queryParam = queryParam
      }
      this.loading = true
      const response = await listMesPackage(this.queryParam)
      this.list = response.rows
      this.total = response.total
      this.loading = false
    },
    /**
     * 删除按钮操作
     */
    handleDelete(id) {
      
      const ids = id || this.ids
      this.$alert.confirm({
        content: this.$t('app.global.delete.content'),
        onOk: async () => {
        await delMesPackage(ids)
        this.getList()
        this.$alert.success(this.$t('app.global.delete.success'))
        },
      })
    },
  },
}
</script>
}
