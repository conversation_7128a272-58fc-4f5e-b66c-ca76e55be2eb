package com.boyo.master.utils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class DateUtil2 {

    public static DateTimeFormatter YYYY_FORMATTER = DateTimeFormatter.ofPattern("yyyy");
    public static DateTimeFormatter YYYY_MM_DD_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static DateTimeFormatter YYYY_MM_DD_HH_MM_SS_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取指定天数之前的日期字符串
     * 此方法用于计算从当前日期向前推算指定天数的日期，并以字符串形式返回
     * 主要用于需要根据当前日期计算过去某一天的日期的场景
     *
     * @param days 前推的天数，不能为负数
     * @return 日期字符串，格式为YYYY-MM-DD
     */
    public static String getDateLate(Integer days) {
        // 从当前日期时间中减去指定天数，并格式化为YYYY-MM-DD格式的字符串
        return LocalDateTime.now().minusDays(days).format(YYYY_MM_DD_FORMATTER);
    }

    public static String getCurrentYear() {
        return LocalDateTime.now().format(YYYY_FORMATTER);
    }
}
