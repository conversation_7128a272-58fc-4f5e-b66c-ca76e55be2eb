package com.boyo.system.domain;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 企业用户角色管理
 * 表名 t_enterprise_user_role
 *
 * <AUTHOR>
 */
@ApiModel("用户角色表（用户角色关联表）")
@Data
@TableName("t_enterprise_user_role")
public class EnterpriseUserRole extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    @TableField(value = "user_openid")
    private String userOpenid;
    /**
     * 角色id
     */
    @ApiModelProperty("角色id")
    @TableField(value = "role_openid")
    private String roleOpenid;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private String dataScope;
}
