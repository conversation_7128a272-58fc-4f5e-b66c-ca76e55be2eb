package com.boyo.project.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 任务工时表(TeamTaskWorkTime)实体类
 *
 * <AUTHOR>
 * @since 2022-02-18 11:23:24
 */
@Data
@TableName(value = "team_task_work_time")
public class TeamTaskWorkTime implements Serializable {
    private static final long serialVersionUID = -68607104709159432L;
            
    @TableId
    private Integer id;
    
    /**
    * 任务ID
    */
    @TableField(value="task_code")
    private String taskCode;
    /**
    * 成员id
    */
    @TableField(value="member_code")
    private String memberCode;

    @TableField(exist = false)
    private String memberName;
    
    @TableField(value="create_time")
    private String createTime;
    /**
    * 描述
    */
    @TableField(value="content")
    private String content;
    /**
    * 开始时间
    */
    @TableField(value="begin_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String beginTime;
    /**
    * 工时
    */
    @TableField(value="num")
    private Integer num;
    /**
    * id
    */
    @TableField(value="code")
    private String code;

}
