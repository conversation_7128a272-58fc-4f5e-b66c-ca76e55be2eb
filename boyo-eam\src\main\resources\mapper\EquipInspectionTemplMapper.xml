<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipInspectionTemplMapper">

    <resultMap type="com.boyo.eam.domain.EquipInspectionTempl" id="EquipInspectionTemplResult">
        <result property="id" column="id"/>
        <result property="openid" column="openid"/>
        <result property="code" column="code"/>
        <result property="equipLedgerOpenid" column="equip_ledger_openid"/>
        <result property="sysUserId" column="sys_user_id"/>
        <result property="reviewer" column="reviewer"/>
        <result property="type" column="type"/>
        <result property="spotType" column="spot_type"/>
        <result property="lineOpenid" column="line_openid"/>
        <result property="state" column="state"/>
        <result property="cycle" column="cycle"/>
        <result property="cycleUnit" column="cycle_unit"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipInspectionTemplList" parameterType="com.boyo.eam.domain.EquipInspectionTempl"
            resultMap="EquipInspectionTemplResult">
        select
        EIT.*,EL.equipment_code as equipCode,EL.equipment_name as equipName
        from equip_inspection_templ EIT left join
        iot_equipment EL on EIT.equip_ledger_openid=EL.id
        <where>
            <if test="openid != null and openid != ''">
                and EIT.openid = #{openid}
            </if>
            <if test="code != null and code != ''">
                and EIT.code = #{code}
            </if>
            <if test="equipLedgerOpenid != null and equipLedgerOpenid != ''">
                and EIT.equip_ledger_openid = #{equipLedgerOpenid}
            </if>
            <if test="type != null and type != ''">
                and EIT.type = #{type}
            </if>
            <if test="spotType != null and spotType != ''">
                and EIT.spot_type = #{spotType}
            </if>
            <if test="lineOpenid != null and lineOpenid != ''">
                and EIT.line_openid = #{lineOpenid}
            </if>
            <if test="state != null and state != ''">
                and EIT.state = #{state}
            </if>
            <if test="cycle != null">
                and EIT.cycle = #{cycle}
            </if>
            <if test="cycleUnit != null and cycleUnit != ''">
                and EIT.cycle_unit = #{cycleUnit}
            </if>
            <if test="createBy != null and createBy != ''">
                and EIT.create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and EIT.create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and EIT.update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and EIT.update_time = #{updateTime}
            </if>
        </where>
    </select>
</mapper>

