package com.boyo.wms.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.wms.entity.*;
import com.boyo.wms.dto.WmsCheckOutDTO;
import com.boyo.wms.dto.WmsFlowDTO;
import com.boyo.wms.dto.WmsWarehousingDTO;
import com.boyo.wms.mapper.*;
import com.boyo.wms.vo.DateFlowVO;
import com.boyo.wms.vo.WmsFlowVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.wms.service.IWmsFlowService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 出入库流水管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class WmsFlowServiceImpl extends ServiceImpl<WmsFlowMapper, WmsFlow> implements IWmsFlowService {

    private final WmsPlanMapper planMapper;

    private final WmsFlowMapper wmsFlowMapper;

    private final WmsMaterielMapper wmsMaterielMapper;

    private final WmsStockMapper wmsStockMapper;

    private final WmsPlanMapper wmsPlanMapper;

    private final WmsQcTemplateMapper qcTemplateMapper;

    private final WmsFlowqcIndexMapper flowqcIndexMapper;

    private final WmsFlowqcDetailMapper flowqcDetailMapper;

    /**
     * 查询出入库流水管理列表
     *
     * @param wmsFlow 出入库流水管理
     * @return wmsFlow 列表
     */
    @Override
    public List<WmsFlowVO> selectWmsFlowList(WmsFlow wmsFlow) {
        return wmsFlowMapper.selectWmsFlowList(wmsFlow);
    }

    /**
     * 快速入库
     * @param warehousingDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, propagation = Propagation.REQUIRED)
    public boolean quickWarehousing(WmsWarehousingDTO warehousingDTO) {
//        1、创建入库计划，默认下发
        WmsPlan wmsPlan = new WmsPlan();
        wmsPlan.setPlanOpenid(IdUtil.fastSimpleUUID());
        wmsPlan.setPlanType("1");
        wmsPlan.setPlanCategory(warehousingDTO.getPlanCategory());
        QueryWrapper<WmsPlan> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("created_at", DateUtil.format(new Date(), "yyyy-MM-dd") + " 00:00:00", DateUtil.format(new Date(), "yyyy-MM-dd") + " 23:59:59");
        Integer planCount = wmsPlanMapper.selectCount(queryWrapper) + 1;
        String order ="QI-" + DateUtil.format(new Date(), "yyMMddHHmmss");
        order += "-" + StrUtil.fillBefore(Convert.toStr(planCount), '0', 3);
        wmsPlan.setPlanOrder(order);
        wmsPlan.setPlanState("4");
        wmsPlan.setCreatedAt(new Date());
        wmsPlan.setCreatedUser(SecurityUtils.getUserOpenid());
        wmsPlan.setPlanStarttime(DateUtil.parseDateTime(DateUtil.format(new Date(), "yyyy-MM-dd") + " 00:00:00"));
        wmsPlan.setPlanEndtime(DateUtil.parseDateTime(DateUtil.format(new Date(), "yyyy-MM-dd") + " 23:59:59"));
        wmsPlan.setPlanEndreason("快速入库，自动结束！");
        wmsPlanMapper.insert(wmsPlan);
//        2、创建入库计划明细，自动生成批次号
        WmsMateriel materiel = new WmsMateriel();
        materiel.setDetailOpenid(IdUtil.fastSimpleUUID());
        materiel.setDetailType("1");
        materiel.setDetailInoutOpenid(wmsPlan.getPlanOpenid());
        materiel.setDetailWarehouseOpenid(warehousingDTO.getWarehouseOpenid());
        materiel.setDetailAreaOpenid(warehousingDTO.getAreaOpenid());
        materiel.setDetailAllocationOpenid(warehousingDTO.getAllocationOpenid());
        materiel.setDetailMaterielOpenid(warehousingDTO.getMaterialOpenid());
        materiel.setDetailQuantity(warehousingDTO.getQuantity());
        materiel.setCompleteQuantity(warehousingDTO.getQuantity());
        materiel.setSurplusQuantity(warehousingDTO.getQuantity());
        QueryWrapper<WmsMateriel> materielQueryWrapper = new QueryWrapper<>();
        materielQueryWrapper.and(o -> o.between("created_at", DateUtil.format(new Date(), "yyyy") + "-01-01 00:00:00", DateUtil.format(new Date(), "yyyy") + "-12-31 23:59:59"));
        Integer materielCount = wmsMaterielMapper.selectCount(materielQueryWrapper) + 1;
        String materielNO = "QI-" + DateUtil.format(new Date(), "yyMMdd");
        materielNO += "-" + StrUtil.fillBefore(Convert.toStr(materielCount), '0', 5);
        materiel.setDetailMaterielOrder(materielNO);
        materiel.setCreatedAt(new Date());
        materiel.setCreatedUser(SecurityUtils.getUserOpenid());
        wmsMaterielMapper.insert(materiel);
//        3、生成入库流水
        WmsFlow flow = new WmsFlow();
        flow.setFlowType("1");
        flow.setPlanOpenid(wmsPlan.getPlanOpenid());
        flow.setDetailOpenid(materiel.getDetailOpenid());
        flow.setMaterielNumber(materiel.getDetailQuantity());
        flow.setMaterielBatch(materiel.getDetailMaterielOrder());
        flow.setWarehouseOpenid(warehousingDTO.getWarehouseOpenid());
        flow.setAreaOpenid(warehousingDTO.getAreaOpenid());
        flow.setAllocationOpenid(warehousingDTO.getAllocationOpenid());
        flow.setCreatedAt(new Date());
        flow.setCreatedUser(SecurityUtils.getUserOpenid());
        wmsFlowMapper.insert(flow);
//        4、更新库存信息
        WmsStock stock = new WmsStock();
        stock.setMaterielOpenid(warehousingDTO.getMaterialOpenid());
        stock.setMaterielBatch(flow.getMaterielBatch());
        stock.setWarehouseOpenid(warehousingDTO.getWarehouseOpenid());
        stock.setAreaOpenid(warehousingDTO.getAreaOpenid());
        stock.setAllocationOpenid(warehousingDTO.getAllocationOpenid());
        stock.setMaterielQuantity(warehousingDTO.getQuantity());
        stock.setUpdateTime(new Date());
        stock.setVersion(1L);
        wmsStockMapper.insert(stock);
        return true;
    }

    /**
     * 快速出库
     * @param checkOutDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, propagation = Propagation.REQUIRED)
    public boolean quickCheckOut(WmsCheckOutDTO checkOutDTO) {
//        1、校验库存是否满足
        QueryWrapper<WmsStock> stockQueryWrapper = new QueryWrapper<>();
        stockQueryWrapper.eq("materiel_openid", checkOutDTO.getMaterielOpenid()).eq("materiel_batch", checkOutDTO.getMaterielBatch());
        if (ObjectUtil.isNull(checkOutDTO.getWarehouseOpenid())) {
            stockQueryWrapper.isNull("warehouse_openid");
        } else {
            stockQueryWrapper.eq("warehouse_openid", checkOutDTO.getWarehouseOpenid());
        }
        if (ObjectUtil.isNull(checkOutDTO.getAreaOpenid())) {
            stockQueryWrapper.isNull("area_openid");
        } else {
            stockQueryWrapper.eq("area_openid", checkOutDTO.getAreaOpenid());
        }
        if (ObjectUtil.isNull(checkOutDTO.getAllocationOpenid())) {
            stockQueryWrapper.isNull("allocation_openid");
        } else {
            stockQueryWrapper.eq("allocation_openid", checkOutDTO.getAllocationOpenid());
        }
        WmsStock stock = wmsStockMapper.selectOne(stockQueryWrapper);
        if (ObjectUtil.isNull(stock)) {
            throw new CustomException("库存信息不存在，不能完成出库操作！");
        }
        if (stock.getMaterielQuantity().compareTo(checkOutDTO.getQuantity()) < 0) {
            throw new CustomException("库存不足，不能完成出库操作!");
        }
//        2、创建出库计划，默认下发
        WmsPlan wmsPlan = new WmsPlan();
        wmsPlan.setPlanOpenid(IdUtil.fastSimpleUUID());
        wmsPlan.setPlanType("2");
        wmsPlan.setPlanCategory(checkOutDTO.getPlanCategory());
        QueryWrapper<WmsPlan> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("created_at", DateUtil.format(new Date(), "yyyy-MM-dd") + " 00:00:00", DateUtil.format(new Date(), "yyyy-MM-dd") + " 23:59:59");
        Integer planCount = wmsPlanMapper.selectCount(queryWrapper) + 1;
        String order ="QO-" + DateUtil.format(new Date(), "yyMMddHHmmss");
        order += "-" + StrUtil.fillBefore(Convert.toStr(planCount), '0', 3);
        wmsPlan.setPlanOrder(order);
        wmsPlan.setPlanState("4");
        wmsPlan.setCreatedAt(new Date());
        wmsPlan.setCreatedUser(SecurityUtils.getUserOpenid());
        wmsPlan.setPlanStarttime(DateUtil.parseDateTime(DateUtil.format(new Date(), "yyyy-MM-dd") + " 00:00:00"));
        wmsPlan.setPlanEndtime(DateUtil.parseDateTime(DateUtil.format(new Date(), "yyyy-MM-dd") + " 23:59:59"));
        wmsPlan.setPlanEndreason("快速入库，自动结束！");
        wmsPlanMapper.insert(wmsPlan);
//        3、创建出库明细
        WmsMateriel materiel = new WmsMateriel();
        materiel.setDetailOpenid(IdUtil.fastSimpleUUID());
        materiel.setDetailType("2");
        materiel.setDetailInoutOpenid(wmsPlan.getPlanOpenid());
        materiel.setDetailWarehouseOpenid(checkOutDTO.getWarehouseOpenid());
        materiel.setDetailAreaOpenid(checkOutDTO.getAreaOpenid());
        materiel.setDetailAllocationOpenid(checkOutDTO.getAllocationOpenid());
        materiel.setDetailQuantity(checkOutDTO.getQuantity());
        materiel.setCompleteQuantity(checkOutDTO.getQuantity());
        materiel.setSurplusQuantity(checkOutDTO.getQuantity());
        materiel.setDetailMaterielOrder(checkOutDTO.getMaterielBatch());
        materiel.setDetailMaterielOpenid(checkOutDTO.getMaterielOpenid());
        materiel.setCreatedAt(new Date());
        materiel.setCreatedUser(SecurityUtils.getUserOpenid());
        wmsMaterielMapper.insert(materiel);
//        4、生成出库流水
        WmsFlow flow = new WmsFlow();
        flow.setFlowType("2");
        flow.setPlanOpenid(wmsPlan.getPlanOpenid());
        flow.setDetailOpenid(materiel.getDetailOpenid());
        flow.setMaterielNumber(materiel.getDetailQuantity());
        flow.setMaterielBatch(materiel.getDetailMaterielOrder());
        flow.setWarehouseOpenid(checkOutDTO.getWarehouseOpenid());
        flow.setAreaOpenid(checkOutDTO.getAreaOpenid());
        flow.setAllocationOpenid(checkOutDTO.getAllocationOpenid());
        flow.setCreatedAt(new Date());
        flow.setCreatedUser(SecurityUtils.getUserOpenid());
        wmsFlowMapper.insert(flow);
//        5、更新库存信息
        stock.setMaterielQuantity(stock.getMaterielQuantity().subtract(checkOutDTO.getQuantity()));
        if (stock.getMaterielQuantity().compareTo(new BigDecimal(0)) < 0) {
            throw new CustomException("库存不足，不能完成出库操作!");
        }
        if (stock.getMaterielQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            wmsStockMapper.deleteById(stock);
        }else{
            wmsStockMapper.updateById(stock);
        }
//      6、更新物料明细表库存
        QueryWrapper<WmsMateriel> materielQueryWrapper = new QueryWrapper<>();
        materielQueryWrapper.eq("detail_materiel_order",checkOutDTO.getMaterielBatch()).eq("detail_type","1");
        WmsMateriel wmsMateriel = wmsMaterielMapper.selectOne(materielQueryWrapper);
        wmsMateriel.setSurplusQuantity(wmsMateriel.getSurplusQuantity().subtract(checkOutDTO.getQuantity()));
        wmsMaterielMapper.updateById(wmsMateriel);
        return true;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, propagation = Propagation.REQUIRED)
    public boolean saveFlow(WmsFlowDTO entity) {
        QueryWrapper<WmsMateriel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("detail_openid", entity.getDetailOpenid());
        WmsMateriel materiel = wmsMaterielMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNotNull(materiel)) {
            entity.setFlowType(materiel.getDetailType());
            if (ObjectUtil.isNull(materiel.getCompleteQuantity())) {
                materiel.setCompleteQuantity(entity.getMaterielNumber());
            } else {
                materiel.setCompleteQuantity(materiel.getCompleteQuantity().add(entity.getMaterielNumber()));
            }
            wmsMaterielMapper.updateById(materiel);
            QueryWrapper<WmsPlan> planQueryWrapper = new QueryWrapper<>();
            planQueryWrapper.eq("plan_openid", materiel.getDetailInoutOpenid());
            WmsPlan plan = planMapper.selectOne(planQueryWrapper);
            plan.setPlanState("3");
            planMapper.updateById(plan);

            QueryWrapper<WmsStock> stockQueryWrapper = new QueryWrapper<>();
            stockQueryWrapper.eq("materiel_openid", materiel.getDetailMaterielOpenid());
            if(materiel.getDetailType().equals("1")){
                stockQueryWrapper.eq("materiel_batch", materiel.getDetailMaterielOrder());
            }else{
                stockQueryWrapper.eq("materiel_batch", entity.getMaterielBatch());
            }
            if (ObjectUtil.isNull(entity.getWarehouseOpenid())) {
                stockQueryWrapper.isNull("warehouse_openid");
            } else {
                stockQueryWrapper.eq("warehouse_openid", entity.getWarehouseOpenid());
            }
            if (ObjectUtil.isNull(entity.getAreaOpenid())) {
                stockQueryWrapper.isNull("area_openid");
            } else {
                stockQueryWrapper.eq("area_openid", entity.getAreaOpenid());
            }
            if (ObjectUtil.isNull(entity.getAllocationOpenid())) {
                stockQueryWrapper.isNull("allocation_openid");
            } else {
                stockQueryWrapper.eq("allocation_openid", entity.getAllocationOpenid());
            }
            WmsStock stock = wmsStockMapper.selectOne(stockQueryWrapper);
            if (ObjectUtil.isNull(stock)) {
                if (materiel.getDetailType().equals("2")) {
                    throw new CustomException("库存信息不存在，不能完成出库操作！");
                }
                stock = new WmsStock();
                stock.setMaterielOpenid(materiel.getDetailMaterielOpenid());
                stock.setMaterielBatch(materiel.getDetailMaterielOrder());
                stock.setWarehouseOpenid(entity.getWarehouseOpenid());
                stock.setAreaOpenid(entity.getAreaOpenid());
                stock.setAllocationOpenid(entity.getAllocationOpenid());
                stock.setVersion(1L);
            }
            if (materiel.getDetailType().equals("1")) {
                entity.setMaterielBatch(materiel.getDetailMaterielOrder());
                stock.setMaterielQuantity(stock.getMaterielQuantity().add(entity.getMaterielNumber()));
            } else if (materiel.getDetailType().equals("2")) {
                stock.setMaterielQuantity(stock.getMaterielQuantity().subtract(entity.getMaterielNumber()));
                if (stock.getMaterielQuantity().compareTo(new BigDecimal(0)) < 0) {
                    throw new CustomException("库存不足，不能完成出库操作!");
                }
            }
            if (ObjectUtil.isNull(stock.getId())) {
                wmsStockMapper.insert(stock);
            } else {
                if (stock.getMaterielQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    wmsStockMapper.deleteById(stock);
                }else{
                    wmsStockMapper.updateById(stock);
                }
            }
        } else {
            throw new CustomException("出入库计划不存在");
        }
        super.save(entity);
        if(ObjectUtil.isNotNull(materiel.getQcTemplate()) && materiel.getQcTemplate() > 0){
//            需要进行质检
            WmsQcTemplate qcTemplate = qcTemplateMapper.selectById(materiel.getQcTemplate());
            if(ObjectUtil.isNotNull(qcTemplate)){
                WmsFlowqcIndex flowqcIndex = new WmsFlowqcIndex();
                flowqcIndex.setFlowId(entity.getId());
                flowqcIndex.setQcName(qcTemplate.getTemplateName());
                flowqcIndex.setCreateTime(new Date());
                flowqcIndex.setCreateUser(SecurityUtils.getUserOpenid());
                flowqcIndexMapper.insert(flowqcIndex);
                if(ObjectUtil.isNotNull(entity.getQcItemList()) && entity.getQcItemList().size() > 0){
                    for (WmsQcItem item:entity.getQcItemList()) {
                        WmsFlowqcDetail detail = new WmsFlowqcDetail();
                        detail.setPId(flowqcIndex.getId());
                        detail.setItemCode(item.getItemCode());
                        detail.setItemName(item.getItemName());
                        detail.setItemResult(item.getResult());
                        flowqcDetailMapper.insert(detail);
                    }
                }
            }
        }
        return true;
    }

    /**
     * 按天统计出入库记录数
     * @return
     */
    @Override
    public List<DateFlowVO> listDateFlow() {
        List<DateFlowVO> list = new ArrayList<>();
        Date current = new Date();
        for (int i = 6; i >= 0; i--) {
            DateFlowVO obj = new DateFlowVO();
            obj.setDate(DateUtil.format(DateUtil.offset(current, DateField.DAY_OF_YEAR,-i),"yy-MM-dd"));
            list.add(obj);
        }
        List<DateFlowVO> tempList = wmsFlowMapper.selectDateFlow();
        if(tempList != null && tempList.size() > 0){
            for (int i = 0; i < list.size(); i++) {
                DateFlowVO obj = list.get(i);
                for (int j = 0; j < tempList.size(); j++) {
                    DateFlowVO temp = tempList.get(j);
                    if(obj.getDate().equals(temp.getDate())){
                        obj.setIn(temp.getIn());
                        obj.setOut(temp.getOut());
                    }
                }
            }
        }
        return list;
    }
}
