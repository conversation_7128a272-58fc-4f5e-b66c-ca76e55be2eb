<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.EnterpriseCustomMapper">

    <resultMap type="com.boyo.master.entity.EnterpriseCustom" id="EnterpriseCustomResult">
        <result property="id" column="id" />
        <result property="customName" column="custom_name" />
        <result property="customLogo" column="custom_logo" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEnterpriseCustomList" parameterType="com.boyo.master.entity.EnterpriseCustom" resultMap="EnterpriseCustomResult">
        select
          id, custom_name, custom_logo
        from t_enterprise_custom
        <where>
            <if test="customName != null and customName != ''">
                and custom_name = #{customName}
            </if>
            <if test="customLogo != null and customLogo != ''">
                and custom_logo = #{customLogo}
            </if>
        </where>
    </select>
</mapper>

