<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.WorkReportMapper">

    <resultMap type="com.boyo.mes.entity.WorkReport" id="WorkReportResult">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="processId" column="process_id"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="reportNum" column="report_num"/>
        <result property="wasteNum" column="waste_num"/>
        <result property="reportTime" column="report_time"/>

        <result property="equipmentName" column="equipment_name"/>
        <result property="processName" column="process_name"/>
        <result property="productionName" column="production_name"/>
        <result property="orderNum" column="order_num"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectWorkReportList" parameterType="com.boyo.mes.entity.WorkReport" resultMap="WorkReportResult">
        -- select t1.*,t2.equipment_name,t3.process_name,t4.production_name,t4.order_num from (select
        select tt.* from (select t1.*,t2.equipment_name,t3.process_name,t4.production_name,t4.order_num from (select
        *
        from t_work_report
        <where>
            <if test="orderId != null">
                and order_id = #{orderId}
            </if>
            <if test="processId != null">
                and process_id = #{processId}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="reportNum != null">
                and report_num = #{reportNum}
            </if>
            <if test="wasteNum != null">
                and waste_num = #{wasteNum}
            </if>
            <if test="reportTime != null">and report_time = #{reportTime}</if>
        </where>
        ) t1 left join iot_equipment t2 on t1.equipment_id = t2.id
        left join t_product_process t3 on t1.process_id = t3.id
        left join (select a.id,a.order_num,b.materiel_name as production_name from t_product_order a,t_material b where
        a.production_id = b.id) t4 on t4.id = t1.order_id
        )tt
        <where>
            <if test="orderNum != null">
                and tt.order_num like CONCAT('%',#{orderNum},'%')
            </if>
            <if test="productionName != null">
                and tt.production_name like CONCAT('%',#{productionName},'%')
            </if>
            <if test="processName != null">
                and tt.process_name like CONCAT('%',#{processName},'%')
            </if>
            <if test="equipmentName != null">
                and tt.equipment_name like CONCAT('%',#{equipmentName},'%')
            </if>
        </where>
        order by tt.id desc
    </select>

    <select id="getOrderReportSum" resultMap="WorkReportResult">
        SELECT t1.report_num,
               t1.waste_num,
               t1.process_id,
               t3.process_name
        FROM (SELECT SUM(report_num) AS report_num, SUM(waste_num) AS waste_num, process_id
              FROM t_work_report
              WHERE order_id = #{orderId}
              GROUP BY process_id) AS t1
                 LEFT JOIN t_product_process AS t3 ON t1.process_id = t3.id;
    </select>
</mapper>


