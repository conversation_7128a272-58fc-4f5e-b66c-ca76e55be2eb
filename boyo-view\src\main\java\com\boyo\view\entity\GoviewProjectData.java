package com.boyo.view.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目数据关联表(GoviewProjectData)实体类
 *
 * <AUTHOR>
 * @since 2022-12-13 15:15:05
 */
@Data
@TableName(value = "t_goview_project_data")
public class GoviewProjectData implements Serializable {
    private static final long serialVersionUID = 669327538093639705L;
        /**
    * 主键
    */    
    @TableId
    private String id;
    
    /**
    * 项目id
    */
    @TableField(value="project_id")
    private String projectId;
    /**
    * 创建时间
    */
    @TableField(value="create_time")
    private Date createTime;
    /**
    * 创建人id
    */
    @TableField(value="create_user_id")
    private String createUserId;
    /**
    * 存储数据
    */
    @TableField(value="content")
    private String content;

}
