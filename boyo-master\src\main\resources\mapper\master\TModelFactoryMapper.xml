<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.TModelFactoryMapper">

    <resultMap type="com.boyo.master.domain.TModelFactory" id="TModelFactoryResult">
        <result property="id" column="id"/>
        <result property="factoryOpenid" column="factory_openid"/>
        <result property="factoryName" column="factory_name"/>
        <result property="factoryAbbreviation" column="factory_abbreviation"/>
        <result property="factoryCode" column="factory_code"/>
        <result property="factoryAddress" column="factory_address"/>
        <result property="factoryStatus" column="factory_status"/>
        <result property="factoryLng" column="factory_lng"/>
        <result property="factoryLat" column="factory_lat"/>
        <result property="factoryImg" column="factory_img"/>
        <result property="factoryContacts" column="factory_contacts"/>
        <result property="factoryPhone" column="factory_phone"/>
        <result property="createdAt" column="created_at"/>
        <result property="createdUser" column="created_user"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="updatedUser" column="updated_user"/>
    </resultMap>

    <sql id="selectTModelFactoryVo">
        select id,
               factory_openid,
               factory_name,
               factory_abbreviation,
               factory_code,
               factory_address,
               factory_status,
               factory_lng,
               factory_lat,
               factory_img,
               factory_contacts,
               factory_phone,
               created_at,
               created_user,
               updated_at,
               updated_user
        from t_model_factory
    </sql>

    <select id="selectTModelFactoryList" parameterType="com.boyo.master.domain.TModelFactory"
            resultMap="TModelFactoryResult">
        <include refid="selectTModelFactoryVo"/>
        <where>
            <if test="factoryOpenid != null  and factoryOpenid != ''">
                and factory_openid = #{factoryOpenid}
            </if>
            <if test="factoryName != null  and factoryName != ''">
                and factory_name like concat('%', #{factoryName}, '%')
            </if>
            <if test="factoryAbbreviation != null  and factoryAbbreviation != ''">
                and factory_abbreviation = #{factoryAbbreviation}
            </if>
            <if test="factoryCode != null  and factoryCode != ''">
                and factory_code = #{factoryCode}
            </if>
            <if test="factoryAddress != null  and factoryAddress != ''">
                and factory_address = #{factoryAddress}
            </if>
            <if test="factoryStatus != null  and factoryStatus != ''">
                and factory_status = #{factoryStatus}
            </if>
            <if test="factoryLng != null  and factoryLng != ''">
                and factory_lng = #{factoryLng}
            </if>
            <if test="factoryLat != null  and factoryLat != ''">
                and factory_lat = #{factoryLat}
            </if>
            <if test="factoryImg != null  and factoryImg != ''">
                and factory_img = #{factoryImg}
            </if>
            <if test="factoryContacts != null  and factoryContacts != ''">
                and factory_contacts = #{factoryContacts}
            </if>
            <if test="factoryPhone != null  and factoryPhone != ''">
                and factory_phone = #{factoryPhone}
            </if>
            <if test="createdAt != null ">
                and created_at = #{createdAt}
            </if>
            <if test="createdUser != null  and createdUser != ''">
                and created_user = #{createdUser}
            </if>
            <if test="updatedAt != null ">
                and updated_at = #{updatedAt}
            </if>
            <if test="updatedUser != null  and updatedUser != ''">
                and updated_user = #{updatedUser}
            </if>
        </where>
    </select>
</mapper>
