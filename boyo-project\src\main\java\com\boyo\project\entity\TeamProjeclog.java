package com.boyo.project.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 项目日志表(TeamProjeclog)实体类
 *
 * <AUTHOR>
 * @since 2022-02-17 19:34:43
 */
@Data
@TableName(value = "team_project_log")
public class TeamProjeclog implements Serializable {
    private static final long serialVersionUID = -21048867367872855L;
            
    @TableId
    private Integer id;
    
    
    @TableField(value="code")
    private String code;
    /**
    * 操作人id
    */
    @TableField(value="member_code")
    private String memberCode;

    @TableField(exist = false)
    private String memberName;
    /**
    * 操作内容
    */
    @TableField(value="content")
    private String content;
    
    @TableField(value="remark")
    private String remark;
    /**
    * 操作类型
    */
    @TableField(value="type")
    private String type;
    /**
    * 添加时间
    */
    @TableField(value="create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 任务id
    */
    @TableField(value="source_code")
    private String sourceCode;
    /**
    * 场景类型
    */
    @TableField(value="action_type")
    private String actionType;
    
    @TableField(value="to_member_code")
    private String toMemberCode;
    /**
    * 是否评论，0：否
    */
    @TableField(value="is_comment")
    private Integer isComment;
    
    @TableField(value="project_code")
    private String projectCode;
    
    @TableField(value="icon")
    private String icon;
    /**
    * 是否机器人
    */
    @TableField(value="is_robot")
    private Integer isRobot;

}
