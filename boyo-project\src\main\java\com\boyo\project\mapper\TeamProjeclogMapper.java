package com.boyo.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.project.entity.TeamProjeclog;
import java.util.List;

/**
 * 项目日志表(TeamProjeclog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-17 19:34:43
 */
public interface TeamProjeclogMapper extends BaseMapper<TeamProjeclog>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param teamProjeclog 实例对象
     * @return 对象列表
     */
    List<TeamProjeclog> selectTeamProjeclogList(TeamProjeclog teamProjeclog);


}

