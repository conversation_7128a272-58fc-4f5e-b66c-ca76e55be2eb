package com.boyo.system.mapper;

import java.util.List;

import com.boyo.system.domain.BoyoMenu;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;


/**
 * 菜单管理Mapper接口
 *
 * <AUTHOR>
 */
public interface BoyoMenuMapper extends BaseMapper<BoyoMenu> {

    /**
     * 查询菜单管理列表
     *
     * @param boyoMenu 菜单管理
     * @return BoyoMenu集合
     */
    List<BoyoMenu> selectBoyoMenuList(BoyoMenu boyoMenu);

    List<BoyoMenu> selectMenuTreeByUserId(@Param("systemOpenid") String systemOpenid, @Param("userOpenid")String userOpenid);

    List<BoyoMenu> selectMenuByUserId(@Param("userOpenid")String userOpenid);

}
