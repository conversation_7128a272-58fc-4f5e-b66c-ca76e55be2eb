<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.MesMouldMapper">

    <resultMap type="com.boyo.mes.entity.MesMould" id="MesMouldResult">
        <result property="id" column="id"/>
        <result property="mouldName" column="mould_name"/>
        <result property="mouldCode" column="mouldc_code"/>
        <result property="mouldLife" column="mould_life"/>
        <result property="maintainFrequency" column="maintain_frequency"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="produtionName" column="prodution_name"/>
        <result property="produtionCode" column="prodution_code"/>
        <result property="productionIdStr" column="productionIdStr"/>
        <result property="usedLife" column="used_life"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectMesMouldList" parameterType="com.boyo.mes.entity.MesMould" resultMap="MesMouldResult">
        SELECT
        id, mould_name, mouldc_code, mould_life, maintain_frequency,GROUP_CONCAT(pid) as
        productionIdStr,GROUP_CONCAT(prodution_name) as prodution_name,GROUP_CONCAT(prodution_code) as prodution_code,max(used) as used_life
        FROM
        (
        SELECT
        t1.*,
        t3.id AS pid,
        t3.materiel_name as prodution_name,
        t3.materiel_code as prodution_code,
        t5.used
        FROM
        t_mes_mould t1
        LEFT JOIN t_mes_modulproduction t2 ON t1.id = t2.modul_id
        LEFT JOIN t_material t3 ON t2.production_id = t3.id
        left join (select sum(function_time) used,modul_id from t_mes_modul_record group by modul_id) t5 on t1.id =
        t5.modul_id
        ) t4
        <where>
            <if test="id != null and id != ''">
                and t4.id = #{id}
            </if>
            <if test="mouldName != null and mouldName != ''">
                and t4.mould_name like concat('%', #{mouldName}, '%')
            </if>
            <if test="mouldCode != null and mouldCode != ''">
                and t4.mouldc_code like concat('%', #{mouldCode}, '%')
            </if>
            <if test="mouldLife != null">
                and t4.mould_life = #{mouldLife}
            </if>
            <if test="maintainFrequency != null">
                and t4.maintain_frequency = #{maintainFrequency}
            </if>
            <if test="createTime != null">
                and t4.create_time = #{createTime}
            </if>
            <if test="createUserId != null">
                and t4.create_user_id = #{createUserId}
            </if>
            <if test="deptId != null and deptId != ''">
                and t4.dept_id = #{deptId}
            </if>
        </where>
        group by t4.id,t4.mould_name,t4.mouldc_code,mould_life,maintain_frequency
    </select>
</mapper>

