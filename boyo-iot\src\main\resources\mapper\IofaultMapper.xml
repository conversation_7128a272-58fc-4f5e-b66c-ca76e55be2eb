<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.iot.mapper.IofaultMapper">

    <resultMap type="com.boyo.iot.entity.Iofault" id="IofaultResult">
        <result property="id" column="id"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="faultId" column="fault_id"/>
        <result property="faultMsg" column="fault_msg"/>
        <result property="createTime" column="create_time"/>
        <result property="relieveTime" column="relieve_time"/>
        <result property="equipmentName" column="equipment_name"></result>
        <result property="faultName" column="fault_name"></result>
        <result property="createUserId" column="create_user_id"/>
        <result property="deptId" column="dept_id"/>
        <association property="workOrder" resultMap="WorkOrderResult"></association>
    </resultMap>
    <resultMap type="com.boyo.iot.entity.WorkOrder" id="WorkOrderResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="num" column="num"/>
        <result property="faultId" column="fault_id"/>
        <result property="orderMsg" column="order_msg"/>
        <result property="ownerUserId" column="owner_user_id"/>
        <result property="receiveTime" column="receive_time"/>
        <result property="completeTime" column="complete_time"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="deptId" column="dept_id"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectIofaultList" parameterType="com.boyo.iot.entity.Iofault" resultMap="IofaultResult">
        select l1.*,l2.*  from (select t1.*,t2.attr_name as fault_name from (
        select
        l1.id, l1.equipment_id, l1.fault_id, l1.create_time, l1.relieve_time,l1.fault_msg,
        l2.equipment_name as equipment_name
        from iot_fault l1,iot_equipment l2
        <where>
            l1.equipment_id = l2.id
            <if test="equipmentName != null">
                and l2.equipment_name like concat('%', #{equipmentName}, '%')
            </if>
            <if test="equipmentId != null">
                and equipment_id = #{equipmentId}
            </if>
            <if test="faultId != null">
                and fault_id = #{faultId}
            </if>
            <if test="createTime != null">
                and DATE_FORMAT(create_time,'%Y-%m-%d') = DATE_FORMAT(#{createTime},'%Y-%m-%d')
            </if>
            <if test="relieveTime != null">
                and relieve_time = #{relieveTime}
            </if>
            ${params.dataScope}
        </where>
        ) t1 left join iot_tsl_attr t2 on t1.fault_id = t2.id
        order by t1.relieve_time is not null,id desc
        ) l1 left join t_work_order l2 on l1.id = l2.fault_id
    </select>
</mapper>

