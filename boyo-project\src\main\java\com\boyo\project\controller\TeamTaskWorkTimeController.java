package com.boyo.project.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.project.entity.TeamTaskWorkTime;
import com.boyo.project.service.ITeamTaskWorkTimeService;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 任务工时表(TeamTaskWorkTime)表控制层
 *
 * <AUTHOR>
 * @since 2022-02-18 11:23:24
 */
@Api("任务工时表")
@RestController
@RequestMapping("/project/teamTaskWorkTime")
@AllArgsConstructor
public class TeamTaskWorkTimeController extends BaseController{
    /**
     * 服务对象
     */
    private final ITeamTaskWorkTimeService teamTaskWorkTimeService;

    private final IEnterpriseUserService enterpriseUserService;
    /**
     * 查询任务工时表列表
     *
     */
    @ApiOperation("查询任务工时表列表")
    @GetMapping("/list")
    public TableDataInfo list(TeamTaskWorkTime teamTaskWorkTime) {
        startPage();
        List<TeamTaskWorkTime> list = teamTaskWorkTimeService.selectTeamTaskWorkTimeList(teamTaskWorkTime);
        List<String> codes = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            codes.add(list.get(i).getMemberCode());
        }
        codes = codes.stream().distinct().collect(Collectors.toList());
        if(codes.size() > 0){
            QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("user_openid",codes);
            List<EnterpriseUser> userList = enterpriseUserService.list(queryWrapper);
            for (int i = 0; i < list.size(); i++) {
                for (int j = 0; j < userList.size(); j++) {
                    if(list.get(i).getMemberCode().equals(userList.get(j).getUserOpenid())){
                        list.get(i).setMemberName(userList.get(j).getUserFullName());
                        continue;
                    }
                }
            }
        }
        return getDataTable(list);
    }
    
    /**
     * 获取任务工时表详情
     */
    @ApiOperation("获取任务工时表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(teamTaskWorkTimeService.getById(id));
    }

    /**
     * 新增任务工时表
     */
    @ApiOperation("新增任务工时表")
    @PostMapping
    public AjaxResult add(@RequestBody TeamTaskWorkTime teamTaskWorkTime) {
        return toBooleanAjax(teamTaskWorkTimeService.save(teamTaskWorkTime));
    }

    /**
     * 修改任务工时表
     */
    @ApiOperation("修改任务工时表")
    @PutMapping
    public AjaxResult edit(@RequestBody TeamTaskWorkTime teamTaskWorkTime) {
        return toBooleanAjax(teamTaskWorkTimeService.updateById(teamTaskWorkTime));
    }

    /**
     * 删除任务工时表
     */
    @ApiOperation("删除任务工时表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(teamTaskWorkTimeService.removeByIds(Arrays.asList(ids)));
    }

}
