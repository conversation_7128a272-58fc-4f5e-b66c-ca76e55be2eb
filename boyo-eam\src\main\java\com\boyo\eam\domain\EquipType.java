package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备类型表(EquipType)实体类
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Data
@TableName(value = "equip_type")
public class EquipType implements Serializable {
    private static final long serialVersionUID = 631897785459000905L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 类型编号
    */
    @TableField(value="code")
    private String code;
    /**
    * 类型名称
    */
    @TableField(value="name")
    private String name;
    /**
    * 说明
    */
    @TableField(value="remark")
    private String remark;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

}
