<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmReceivablesMapper">

    <resultMap type="com.boyo.crm.entity.CrmReceivables" id="CrmReceivablesResult">
        <result property="id" column="id"/>
        <result property="number" column="number"/>
        <result property="planId" column="plan_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="contractId" column="contract_id"/>
        <result property="checkStatus" column="check_status"/>
        <result property="examineRecordId" column="examine_record_id"/>
        <result property="returnTime" column="return_time"/>
        <result property="returnType" column="return_type"/>
        <result property="money" column="money"/>
        <result property="remark" column="remark"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="ownerUserId" column="owner_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remarks" column="remarks"/>
        <result property="customerName" column="customer_name"></result>
        <result property="contractName" column="contract_name"></result>
        <result property="returnTypeName" column="type_name"></result>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmReceivablesList" parameterType="com.boyo.crm.entity.CrmReceivables"
            resultMap="CrmReceivablesResult">
        select t1.*,t2.customer_name,t3.name as contract_name,t4.base_desc as type_name from (select
        id, number, plan_id, customer_id, contract_id, check_status, examine_record_id, return_time, return_type, money,
        remark, create_user_id, owner_user_id, create_time, update_time, remarks
        from t_crm_receivables
        <where>
            <if test="number != null and number != ''">
                and number = #{number}
            </if>
            <if test="planId != null">
                and plan_id = #{planId}
            </if>
            <if test="customerId != null">
                and customer_id = #{customerId}
            </if>
            <if test="contractId != null">
                and contract_id = #{contractId}
            </if>
            <if test="checkStatus != null">
                and check_status = #{checkStatus}
            </if>
            <if test="examineRecordId != null">
                and examine_record_id = #{examineRecordId}
            </if>
            <if test="returnTime != null">
                and return_time = #{returnTime}
            </if>
            <if test="returnType != null and returnType != ''">
                and return_type = #{returnType}
            </if>
            <if test="money != null">
                and money = #{money}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="ownerUserId != null">
                and owner_user_id = #{ownerUserId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="remarks != null and remarks != ''">
                and remarks = #{remarks}
            </if>
            ${params.dataScope}

        </where>
        ) t1 left join t_crm_customer t2 on t1.customer_id = t2.id
        left join t_crm_contract t3 on t1.contract_id = t3.id
        left join (select * from t_base_dict where base_type = 'PAYMENT_TYPE') t4 on t1.return_type = t4.id
        order by t1.id desc
    </select>
    <select id="selectById" resultMap="CrmReceivablesResult">
        select t1.*, t2.customer_name, t3.name as contract_name, t4.base_desc as type_name
        from (select id,
                     number,
                     plan_id,
                     customer_id,
                     contract_id,
                     check_status,
                     examine_record_id,
                     return_time,
                     return_type,
                     money,
                     remark,
                     create_user_id,
                     owner_user_id,
                     create_time,
                     update_time,
                     remarks
              from t_crm_receivables
              where id = #{id}
             ) t1
                 left join t_crm_customer t2 on t1.customer_id = t2.id
                 left join t_crm_contract t3 on t1.contract_id = t3.id
                 left join (select * from t_base_dict where base_type = 'PAYMENT_TYPE') t4 on t1.return_type = t4.id
        order by t1.id desc
    </select>
</mapper>

