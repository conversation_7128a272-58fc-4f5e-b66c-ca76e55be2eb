<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.ModelAllocationMapper">

    <resultMap type="com.boyo.master.vo.ModelAllocatonVO" id="ModelAllocationResult">
        <result property="id" column="id"/>
        <result property="allocationOpenid" column="allocation_openid"/>
        <result property="allocationFactory" column="allocation_factory"/>
        <result property="allocationWarehouse" column="allocation_warehouse"/>
        <result property="allocationArea" column="allocation_area"/>
        <result property="allocationName" column="allocation_name"/>
        <result property="allocationAbbreviation" column="allocation_abbreviation"/>
        <result property="allocationCode" column="allocation_code"/>
        <result property="allocationType" column="allocation_type"/>
        <result property="allocationDesc" column="allocation_desc"/>
        <result property="allocationStatus" column="allocation_status"/>
        <result property="allocationImg" column="allocation_img"/>
        <result property="allocationContacts" column="allocation_contacts"/>
        <result property="allocationPhone" column="allocation_phone"/>
        <result property="createdAt" column="created_at"/>
        <result property="createdUser" column="created_user"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="updatedUser" column="updated_user"/>
        <result property="factoryName" column="factory_name"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="areaName" column="area_name"/>
        <result property="batchCount" column="batch_count"/>
    </resultMap>


    <select id="selectModelAllocationList" parameterType="com.boyo.master.domain.ModelAllocation"
            resultMap="ModelAllocationResult">
        select t1.*,t2.factory_name,t3.warehouse_name,t4.area_name,t5.c as batch_count from t_model_allocation t1
        left join t_model_factory t2 on t1.allocation_factory = t2.factory_openid
        left join t_model_warehouse t3 on t1.allocation_warehouse = t3.warehouse_openid
        left join t_model_area t4 on t1.allocation_area = t4.area_openid
        left join (select count(DISTINCT(materiel_openid)) c,allocation_openid from t_wms_stock group by allocation_openid) t5 on t1.allocation_openid = t5.allocation_openid
        <where>
            <if test="allocationOpenid != null  and allocationOpenid != ''">
                and t1.allocation_openid = #{allocationOpenid}
            </if>
            <if test="allocationFactory != null  and allocationFactory != ''">
                and t1.allocation_factory = #{allocationFactory}
            </if>
            <if test="allocationWarehouse != null  and allocationWarehouse != ''">
                and t1.allocation_warehouse = #{allocationWarehouse}
            </if>
            <if test="allocationArea != null  and allocationArea != ''">
                and t1.allocation_area = #{allocationArea}
            </if>
            <if test="allocationName != null  and allocationName != ''">
                and t1.allocation_name like concat('%', #{allocationName}, '%')
            </if>
            <if test="allocationAbbreviation != null  and allocationAbbreviation != ''">
                and t1.allocation_abbreviation = #{allocationAbbreviation}
            </if>
            <if test="allocationCode != null  and allocationCode != ''">
                and t1.allocation_code = #{allocationCode}
            </if>
            <if test="allocationType != null  and allocationType != ''">
                and t1.allocation_type = #{allocationType}
            </if>
            <if test="allocationDesc != null  and allocationDesc != ''">
                and t1.allocation_desc = #{allocationDesc}
            </if>
            <if test="allocationStatus != null  and allocationStatus != ''">
                and t1.allocation_status = #{allocationStatus}
            </if>
            <if test="allocationContacts != null  and allocationContacts != ''">
                and t1.allocation_contacts = #{allocationContacts}
            </if>
            <if test="allocationPhone != null  and allocationPhone != ''">
                and t1.allocation_phone = #{allocationPhone}
            </if>
        </where>
    </select>
</mapper>
