package com.boyo.project.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.project.entity.TeamProjectemplate;
import java.util.List;

/**
 * 项目类型表(TeamProjectemplate)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-08 20:50:45
 */
public interface ITeamProjectemplateService extends IService<TeamProjectemplate> {

    /**
     * 查询多条数据
     *
     * @param teamProjectemplate 对象信息
     * @return 对象列表
     */
    List<TeamProjectemplate> selectTeamProjectemplateList(TeamProjectemplate teamProjectemplate);


}
