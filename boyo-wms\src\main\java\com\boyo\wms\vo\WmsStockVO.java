package com.boyo.wms.vo;

import com.boyo.wms.entity.WmsStock;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WmsStockVO extends WmsStock {
    /**
     * 物料名称
     */
    private String materielName;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 货位名称
     */
    private String allocationName;

    /**
     * 物料编码
     */
    private String materielCode;

    /**
     * 物料单位
     */
    private String materielUnit;

    /**
     * 物料规格
     */
    private String materielNorms;
    /**
     * 最大库存
     */
    private BigDecimal max;
    /**
     * 最大库存预警
     */
    private BigDecimal maxWarn;
    /**
     * 最小库存
     */
    private BigDecimal min;
    /**
     * 最小库存预警
     */
    private BigDecimal minWarn;
}
