package com.boyo.wms.mapper;

import java.util.List;

import com.boyo.wms.entity.WmsFlow;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.wms.vo.DateFlowVO;
import com.boyo.wms.vo.WmsFlowVO;


/**
 * 出入库流水管理Mapper接口
 *
 * <AUTHOR>
 */
public interface WmsFlowMapper extends BaseMapper<WmsFlow> {

    /**
     * 查询出入库流水管理列表
     *
     * @param wmsFlow 出入库流水管理
     * @return WmsFlow集合
     */
    List<WmsFlowVO> selectWmsFlowList(WmsFlow wmsFlow);

    /**
     * 根据日期统计出入库流水信息
     * @return
     */
    List<DateFlowVO> selectDateFlow();
}
