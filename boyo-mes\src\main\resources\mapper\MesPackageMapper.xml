<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.MesPackageMapper">

    <resultMap type="com.boyo.mes.entity.MesPackage" id="MesPackageResult">
        <result property="id" column="id" />
        <result property="orderId" column="order_id" />
        <result property="packageNum" column="package_num" />
        <result property="scrq" column="scrq" />
        <result property="kunhao" column="kunhao" />
        <result property="luhao" column="luhao" />
        <result property="gangzhong" column="gangzhong" />
        <result property="gangji" column="gangji" />
        <result property="waijing" column="waijing" />
        <result property="bihou" column="bihou" />
        <result property="changdu" column="changdu" />
        <result property="guanhao" column="guanhao" />
        <result property="guanhaodaima" column="guanhaodaima" />
        <result property="zhongliang" column="zhongliang" />
        <result property="pihao" column="pihao" />
        <result property="ykdh" column="ykdh" />
        <result property="cdft" column="cdFT" />
        <result property="zllb" column="zlLB" />
        <result property="lizhong" column="lizhong" />
        <result property="jielun" column="jielun" />
        <result property="createTime" column="create_time" />
        <result property="createBy" column="create_by" />

        <result property="orderName" column="order_name" />
        <result property="orderNum" column="order_num" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectMesPackageList" parameterType="com.boyo.mes.entity.MesPackage" resultMap="MesPackageResult">
        select t1.*,t2.order_num from (select *
        from t_mes_package
        <where>
            <if test="orderId != null">
                and order_id = #{orderId}
            </if>
            <if test="packageNum != null">
                and package_num = #{packageNum}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
        </where>
        ) t1 left join t_product_order t2 on t1.order_id = t2.id
    </select>
</mapper>

