<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.mes.mapper.ProductProcessMapper">

    <resultMap type="com.boyo.mes.entity.ProductProcess" id="ProductProcessResult">
        <result property="id" column="id" />
        <result property="processName" column="process_name" />
        <result property="processStatus" column="process_status" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectProductProcessList" parameterType="com.boyo.mes.entity.ProductProcess" resultMap="ProductProcessResult">
        select
          id, process_name, process_status
        from t_product_process
        <where>
            <if test="processName != null and processName != ''">
                and process_name like concat('%', #{processName}, '%')
            </if>
            <if test="processStatus != null and processStatus != ''">
                and process_status = #{processStatus}
            </if>
        </where>
    </select>
</mapper>

