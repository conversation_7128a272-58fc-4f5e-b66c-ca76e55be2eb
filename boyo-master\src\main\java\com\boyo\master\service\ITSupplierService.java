package com.boyo.master.service;

import java.util.List;

import com.boyo.master.domain.TSupplier;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.master.vo.SupplierVO;

/**
 * 供应商管理Service接口
 *
 * <AUTHOR>
 */
public interface ITSupplierService extends IService<TSupplier> {
    /**
     * 根据条件查询查询供应商管理列表
     *
     * @param tSupplier 供应商管理
     * @return 供应商管理集合
     */
    List<SupplierVO> selectTSupplierList(TSupplier tSupplier);
}
