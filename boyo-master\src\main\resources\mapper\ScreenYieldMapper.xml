<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.ScreenYieldMapper">

    <resultMap type="com.boyo.master.domain.ScreenYield" id="ScreenYieldResult">
        <result property="id" column="id"/>
        <result property="workshopId" column="workshop_id"/>
        <result property="workshopName" column="workshop_name"/>
        <result property="yield" column="yield"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="yieldTime" column="yield_time"/>
    </resultMap>

    <sql id="selectScreenYieldVo">
        select id, screen_workshop.workshop_id, workshop_name, yield, yield_time
        from screen_yield
                 left join screen_workshop on screen_yield.workshop_id = screen_workshop.workshop_id
    </sql>

    <select id="selectScreenYieldList" parameterType="com.boyo.master.domain.ScreenYield" resultMap="ScreenYieldResult">
        <include refid="selectScreenYieldVo"/>
        <where>
            <if test="workshopId != null ">and screen_yield.workshop_id = #{workshopId}</if>
            <if test="yield != null ">and yield = #{yield}</if>
            <if test="yieldTime != null ">and yield_time = DATE(#{yieldTime})</if>
        </where>

        order by yield_time
    </select>

    <select id="selectScreenYieldById" parameterType="Long" resultMap="ScreenYieldResult">
        <include refid="selectScreenYieldVo"/>
        where id = #{id}
    </select>

    <select id="getYieldCount" resultType="java.lang.Long">
        select COALESCE(SUM(yield), 0)
        from screen_yield
        WHERE yield_time BETWEEN DATE (#{start})
          AND DATE (#{end})
    </select>

    <insert id="insertScreenYield" parameterType="com.boyo.master.domain.ScreenYield" useGeneratedKeys="true"
            keyProperty="id">
        insert into screen_yield
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workshopId != null">workshop_id,</if>
            <if test="yield != null">yield,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="yieldTime != null">yield_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workshopId != null">#{workshopId},</if>
            <if test="yield != null">#{yield},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="yieldTime != null">#{yieldTime},</if>
        </trim>
    </insert>

    <update id="updateScreenYield" parameterType="com.boyo.master.domain.ScreenYield">
        update screen_yield
        <trim prefix="SET" suffixOverrides=",">
            <if test="workshopId != null">workshop_id = #{workshopId},</if>
            <if test="yield != null">yield = #{yield},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="yieldTime != null">yield_time = #{yieldTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateScreenYieldByYieldTime" parameterType="com.boyo.master.domain.ScreenYield">
        update screen_yield
        <trim prefix="SET" suffixOverrides=",">
            <if test="yield != null">yield = #{yield},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where yield_time = #{yieldTime} and workshop_id = #{workshopId}
    </update>


    <delete id="deleteScreenYieldById" parameterType="Long">
        delete
        from screen_yield
        where id = #{id}
    </delete>

    <delete id="deleteScreenYieldByIds" parameterType="String">
        delete from screen_yield where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteScreenYieldByyieldTime">
        delete
        from screen_yield
        where yield_time = #{yieldTime}
    </delete>
    <delete id="deleteScreenYieldByWorkshopId">
        delete
        from screen_yield
        where workshop_id = #{workshopId}
    </delete>
</mapper>