package com.boyo.master.service;

import com.boyo.master.domain.ScreenWorkshop;

import java.util.List;

/**
 * 大屏车间Service接口
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
public interface IScreenWorkshopService
{
    /**
     * 查询大屏车间
     *
     * @param workshopId 大屏车间主键
     * @return 大屏车间
     */
    public ScreenWorkshop selectScreenWorkshopByWorkshopId(Long workshopId);

    /**
     * 查询大屏车间列表
     *
     * @param screenWorkshop 大屏车间
     * @return 大屏车间集合
     */
    public List<ScreenWorkshop> selectScreenWorkshopList(ScreenWorkshop screenWorkshop);

    /**
     * 新增大屏车间
     *
     * @param screenWorkshop 大屏车间
     * @return 结果
     */
    public int insertScreenWorkshop(ScreenWorkshop screenWorkshop);

    /**
     * 修改大屏车间
     *
     * @param screenWorkshop 大屏车间
     * @return 结果
     */
    public int updateScreenWorkshop(ScreenWorkshop screenWorkshop);

    /**
     * 批量删除大屏车间
     *
     * @param workshopIds 需要删除的大屏车间主键集合
     * @return 结果
     */
    public int deleteScreenWorkshopByWorkshopIds(Long[] workshopIds);

    /**
     * 删除大屏车间信息
     *
     * @param workshopId 大屏车间主键
     * @return 结果
     */
    public int deleteScreenWorkshopByWorkshopId(Long workshopId);
}