package com.boyo.mes.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.core.text.Convert;
import com.boyo.mes.entity.*;
import com.boyo.mes.mapper.ProductOrderMapper;
import com.boyo.mes.mapper.TFactoryOrderMapper;
import com.boyo.mes.mapper.WorkReportMapper;
import com.boyo.mes.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import lombok.Data;
import org.apache.poi.ss.formula.functions.T;
import org.aspectj.weaver.loadtime.Aj;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * (TFactoryOrder)表控制层
 *
 * <AUTHOR>
 * @since 2023-09-06 16:18:17
 */
@Api("订单管理")
@RestController
@RequestMapping("/factory/order")
public class TFactoryOrderController extends BaseController {
    /**
     * 服务对象
     */
    @Autowired
    private TFactoryOrderService tFactoryOrderService;

    @Autowired
    private TTransportOrderService tTransportOrderService;

    @Autowired
    private IProductOrderDetailService detailService;

    @Autowired
    private IWorkReportService workReportService;


    /**
     * 查询订单列表
     */
    @ApiOperation("获取订单列表")
    @GetMapping("/list")
    public TableDataInfo list(TFactoryOrder factoryOrder){
        startPage();
        List<TFactoryOrder> list = tFactoryOrderService.selectList(factoryOrder);
        return getDataTable(list);
    }

    @ApiOperation("获取订单名称")
    @GetMapping("/listName")
    public AjaxResult listName(){
        QueryWrapper<TFactoryOrder> factoryOrderQueryWrapper = new QueryWrapper<>();
        factoryOrderQueryWrapper.eq("order_status",1);
        factoryOrderQueryWrapper.or().eq("order_status",0);
        factoryOrderQueryWrapper.or().eq("order_status",2);
        List<TFactoryOrder> list = tFactoryOrderService.list(factoryOrderQueryWrapper);
        return AjaxResult.success(list);
    }

    @ApiOperation("获取生产任务详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(tFactoryOrderService.getById(id));
    }
    /**
     * 新增订单记录
     */
    @ApiOperation("新增订单")
    @PostMapping
    public AjaxResult add(@RequestBody TFactoryOrder workReport) {
//        QueryWrapper<TFactoryOrder > factoryOrderQueryWrapper = new QueryWrapper<>();
//        factoryOrderQueryWrapper.eq("order_name",workReport.getOrderName());
//        List<TFactoryOrder> list = tFactoryOrderService.list(factoryOrderQueryWrapper);
//        if(list.size() > 0){
//            return AjaxResult.error("订单名称重复");
//        }
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
//        SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
//        QueryWrapper<TFactoryOrder> factoryOrderQueryWrapper1 = new QueryWrapper<>();
//        factoryOrderQueryWrapper1.like("order_num",simpleDateFormat.format(new Date())).orderByDesc("create_time");
//        List<TFactoryOrder> list1 = tFactoryOrderService.list(factoryOrderQueryWrapper1);
//        System.out.println(list1);
//        if(list1.size()>0){
//            String[] s = list1.get(0).getOrderNum().split("_");
//            DecimalFormat decimalFormat = new DecimalFormat("0000");
//            String maxSerialNumber = decimalFormat.format( Integer.parseInt(s[1])+ 1);
//            System.out.println(maxSerialNumber);
//            String id = s[0]+"_"+maxSerialNumber;
//            workReport.setOrderNum(id);
//        }else {
//            workReport.setOrderNum("DD"+simpleDateFormat.format(new Date())+"_"+"0001");
//        }
//        workReport.setOrderStatus(0);
//        workReport.setCreateTime(new Date());
//        LocalDate date = LocalDate.parse(simpleDateFormat1.format(workReport.getDeliveryTime()));
//        LocalDate date1 =LocalDate.parse(simpleDateFormat1.format(workReport.getCreateTime()));
//        int days = (int) ChronoUnit.DAYS.between(date1, date);
//        workReport.setDeliveryCycle(days);
        return toBooleanAjax(tFactoryOrderService.save(workReport));
    }

    /**
     * 修改订单
     */
    @ApiOperation("修改订单")
    @PutMapping
    public AjaxResult edit(@RequestBody TFactoryOrder workReport) throws ParseException {
        return toBooleanAjax(tFactoryOrderService.updateById(workReport));
    }

    @ApiOperation("删除订单")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(tFactoryOrderService.removeByIds(Arrays.asList(ids)));
    }

    @GetMapping("/orderCount")
    public AjaxResult countList(){
        List<OrderType> list = new ArrayList<>();
        OrderType orderType = new OrderType();
        QueryWrapper<TFactoryOrder> allList = new QueryWrapper<>();
        allList.select("count(id) as total");
        Map<String, Object> map = tFactoryOrderService.getMap(allList);
        Object sumAll = null;
        if(map!=null){
            sumAll = map.get("total");
        }
        orderType.setSumAll(Convert.toInt(sumAll));
        QueryWrapper<TFactoryOrder> workList = new QueryWrapper<>();
        workList.select("count(id) as total");
        workList.eq("order_status",1);
        Map<String, Object> map1 = tFactoryOrderService.getMap(workList);
        Object sumWork = null;
        if(map1!=null){
            sumWork = map1.get("total");
        }
        orderType.setSumWork(Convert.toInt(sumWork));
        QueryWrapper<TFactoryOrder> overTimeList = new QueryWrapper<>();
        overTimeList.select("count(id) as total");
        overTimeList.like("overdue","逾期");
        Map<String, Object> map2 = tFactoryOrderService.getMap(overTimeList);
        Object sumOverTime = null;
        if(map2!=null){
            sumOverTime = map2.get("total");
        }
        orderType.setSumOverTime(Convert.toInt(sumOverTime));


        QueryWrapper<TTransportOrder> tTransportOrderQueryWrapper = new QueryWrapper<>();
        tTransportOrderQueryWrapper.select("count(id) as total");
        Map<String, Object> map3 = tTransportOrderService.getMap(tTransportOrderQueryWrapper);
        Object sumTransport = null;
        if(map3!=null){
            sumTransport = map3.get("total");
        }
        orderType.setSumTransport(Convert.toInt(sumTransport));
        list.add(orderType);
        return AjaxResult.success(orderType);
    }
    @GetMapping("/workOrderCount")
    public AjaxResult workCountList(String startTime,String endTime){
        List<Object> list = new ArrayList<>();
        OrderType orderType = new OrderType();
        QueryWrapper<TFactoryOrder> allList = new QueryWrapper<>();
        allList.select("count(id) as total").between("start_time",startTime,endTime);
        Map<String, Object> map = tFactoryOrderService.getMap(allList);
        Object sumAll = null;
        if(map!=null){
            sumAll = map.get("total");
        }
        QueryWrapper<TFactoryOrder> workList = new QueryWrapper<>();
        workList.select("count(id) as total");
        workList.eq("order_status",1).between("start_time",startTime,endTime);
        Map<String, Object> map1 = tFactoryOrderService.getMap(workList);
        Object sumWork = null;
        if(map1!=null){
            sumWork = map1.get("total");
        }

        QueryWrapper<TFactoryOrder> unworkList = new QueryWrapper<>();
        unworkList.select("count(id) as total");
        unworkList.eq("order_status",0).between("start_time",startTime,endTime);
        Map<String, Object> map2 = tFactoryOrderService.getMap(unworkList);
        Object sumNo = null;
        if(map2!=null){
            sumNo = map2.get("total");
        }
        QueryWrapper<TFactoryOrder> overTimeList = new QueryWrapper<>();
        overTimeList.select("count(id) as total");
        overTimeList.like("overdue","逾期").between("start_time",startTime,endTime);
        Map<String, Object> map3 = tFactoryOrderService.getMap(overTimeList);
        Object sumOverTime = null;
        if(map3!=null){
            sumOverTime = map3.get("total");
        }
        list.add(sumAll);
        list.add(sumWork);
        list.add(sumNo);
        list.add(sumOverTime);
        return AjaxResult.success(list);
    }

    @GetMapping("/chart")
    public AjaxResult chart(String startTime,String endTime){
        List<Chart> list =new ArrayList<>();
        Chart chart =new Chart();
        Chart chart1 =new Chart();
        Chart chart2 =new Chart();
        QueryWrapper<ProductOrderDetail> detailQueryWrapper = new QueryWrapper<>();
        detailQueryWrapper.select("count(status)").eq("status",0);
        detailQueryWrapper.or().eq("status",1).between("start_time",startTime,endTime);
        Map<String, Object> map3 = detailService.getMap(detailQueryWrapper);
        Object sumNo = null;
        if(map3!=null){
            sumNo = map3.get("total");
        }
        if(sumNo == null){
            sumNo=0;
        }
        chart.setName("未质检工单");
        chart.setValue(Integer.valueOf((Integer) sumNo));
        QueryWrapper<ProductOrderDetail> detailQueryWrapper1 = new QueryWrapper<>();
        detailQueryWrapper1.select("count(status)").eq("status",2).between("start_time",startTime,endTime);
        Map<String, Object> map = detailService.getMap(detailQueryWrapper1);
        Object sumOk = null;
        if(map!=null){
            sumOk = map.get("total");
        }
        if(sumOk==null){
            sumOk=0;
        }
        chart1.setName("已质检工单");
        chart1.setValue((Integer) sumOk);
        QueryWrapper<WorkReport> workReportQueryWrapper = new QueryWrapper<>();
        workReportQueryWrapper.select("count(id) as total").ne("waste_num",0).between("report_time",startTime,endTime);
        Map<String, Object> map1 = workReportService.getMap(workReportQueryWrapper);
        Object sumBad = null;
        if(map1!=null){
            sumBad = map1.get("total");
        }
        if(sumBad==null){
            sumBad=0;
        }
        chart2.setName("质检不合格工单");
        chart2.setValue(Integer.valueOf(String.valueOf(sumBad)));
        list.add(chart);
        list.add(chart1);
        list.add(chart2);
        return AjaxResult.success(list);
    }
}

@Data
class OrderType{
    /**
     * 总数
     */
    private Integer sumAll;
    /**
     * 工作中的
     */
    private Integer sumWork;
    /**
     * 逾期
     */
    private Integer sumOverTime;
    /**
     * 发货订单
     */
    private Integer sumTransport;
}
@Data
class Chart{
    private Integer value;

    private String name;

}