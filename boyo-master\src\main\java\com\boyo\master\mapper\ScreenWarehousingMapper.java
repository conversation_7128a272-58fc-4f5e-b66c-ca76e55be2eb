package com.boyo.master.mapper;


import com.boyo.master.domain.ScreenWarehousing;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 大屏入库信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
public interface ScreenWarehousingMapper
{
    /**
     * 查询大屏入库信息
     *
     * @param id 大屏入库信息主键
     * @return 大屏入库信息
     */
    public ScreenWarehousing selectScreenWarehousingById(Long id);

    /**
     * 查询大屏入库信息列表
     *
     * @param screenWarehousing 大屏入库信息
     * @return 大屏入库信息集合
     */
    public List<ScreenWarehousing> selectScreenWarehousingList(@RequestBody ScreenWarehousing screenWarehousing);

    public List<ScreenWarehousing> selectScreenWarehousingBetween(@RequestBody ScreenWarehousing screenWarehousing, @Param("start")String start, @Param("end")String end);

    /**
     * 新增大屏入库信息
     *
     * @param screenWarehousing 大屏入库信息
     * @return 结果
     */
    public int insertScreenWarehousing(ScreenWarehousing screenWarehousing);

    /**
     * 修改大屏入库信息
     *
     * @param screenWarehousing 大屏入库信息
     * @return 结果
     */
    public int updateScreenWarehousing(ScreenWarehousing screenWarehousing);

    /**
     * 删除大屏入库信息
     *
     * @param id 大屏入库信息主键
     * @return 结果
     */
    public int deleteScreenWarehousingById(Long id);

    /**
     * 批量删除大屏入库信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScreenWarehousingByIds(Long[] ids);

    public Long getWarehousingCount (@Param("start")String start, @Param("end")String end);

}

