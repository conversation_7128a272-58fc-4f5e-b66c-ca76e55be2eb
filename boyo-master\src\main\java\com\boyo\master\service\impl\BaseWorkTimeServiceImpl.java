package com.boyo.master.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.framework.annotation.Tenant;
import com.boyo.master.domain.BaseDictType;
import com.boyo.master.domain.WorkTime;
import com.boyo.master.mapper.BaseDictTypeMapper;
import com.boyo.master.mapper.BaseWorkTimeMapper;
import com.boyo.master.service.IBaseWorkTimeService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;


@Service
@AllArgsConstructor
@Tenant
public class BaseWorkTimeServiceImpl extends ServiceImpl<BaseWorkTimeMapper, WorkTime> implements IBaseWorkTimeService {



}
