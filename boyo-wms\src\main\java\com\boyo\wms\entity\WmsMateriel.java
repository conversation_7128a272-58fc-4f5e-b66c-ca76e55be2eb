package com.boyo.wms.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 出入库计划明细
 * 表名 t_wms_materiel
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("出入库详情表-计划物料表")
@Data
@TableName("t_wms_materiel")
public class WmsMateriel extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "detail_openid")
    private String detailOpenid;
    /**
     * 出入库计划主键id
     */
    @ApiModelProperty("出入库计划主键id")
    @TableField(value = "detail_inout_openid")
    private String detailInoutOpenid;

    @ApiModelProperty("出入库类型 1入库 2出库")
    @TableField(value = "detail_type")
    private String detailType;
    /**
     * 仓库业务主键
     */
    @ApiModelProperty("仓库业务主键")
    @TableField(value = "detail_warehouse_openid")
    private String detailWarehouseOpenid;
    /**
     * 区域业务主键
     */
    @ApiModelProperty("区域业务主键")
    @TableField(value = "detail_area_openid")
    private String detailAreaOpenid;
    /**
     * 物料业务主键
     */
    @ApiModelProperty("物料业务主键")
    @TableField(value = "detail_materiel_openid")
    private String detailMaterielOpenid;
    /**
     * 计划数量
     */
    @ApiModelProperty("计划数量")
    @TableField(value = "detail_quantity")
    private BigDecimal detailQuantity;
    /**
     * 已完成数量
     */
    @ApiModelProperty("已完成数量")
    @TableField(value = "complete_quantity")
    private BigDecimal completeQuantity = new BigDecimal(0);

    @ApiModelProperty("剩余数量")
    @TableField(value = "surplus_quantity")
    private BigDecimal surplusQuantity = new BigDecimal(0);
    /**
     * 物料批次号
     */
    @ApiModelProperty("物料批次号")
    @TableField(value = "detail_materiel_order")
    private String detailMaterielOrder;
    /**
     * 供应商业务主键
     */
    @ApiModelProperty("供应商业务主键")
    @TableField(value = "detail_supplier_openid")
    private String detailSupplierOpenid;
    /**
     * 货位业务主键
     */
    @ApiModelProperty("货位业务主键")
    @TableField(value = "detail_allocation_openid")
    private String detailAllocationOpenid;
    /**
     * 物料状态
     */
    @ApiModelProperty("物料状态")
    @TableField(value = "detail_state")
    private String detailState;

    @ApiModelProperty("质检模板")
    @TableField(value = "qc_template")
    private Integer qcTemplate;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "created_at",fill = FieldFill.INSERT)
    private Date createdAt;
    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    @TableField(value = "created_user",fill = FieldFill.INSERT)
    private String createdUser;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(value = "updated_at",fill = FieldFill.UPDATE)
    private Date updatedAt;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "updated_user",fill = FieldFill.UPDATE)
    private String updatedUser;
}
