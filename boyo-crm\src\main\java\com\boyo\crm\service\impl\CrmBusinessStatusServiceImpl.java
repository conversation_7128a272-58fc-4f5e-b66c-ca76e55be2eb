package com.boyo.crm.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.entity.CrmBusinessStatus;
import com.boyo.crm.mapper.CrmBusinessStatusMapper;
import com.boyo.crm.service.ICrmBusinessStatusService;
import java.util.List;

/**
 * 商机状态(CrmBusinessStatus)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-25 14:53:11
 */
@Service("crmBusinessStatusService")
@AllArgsConstructor
public class CrmBusinessStatusServiceImpl extends ServiceImpl<CrmBusinessStatusMapper, CrmBusinessStatus> implements ICrmBusinessStatusService {
    private final CrmBusinessStatusMapper crmBusinessStatusMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<CrmBusinessStatus> selectCrmBusinessStatusList(CrmBusinessStatus crmBusinessStatus) {
        return crmBusinessStatusMapper.selectCrmBusinessStatusList(crmBusinessStatus);
    }


}
