package com.boyo.master.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 科目余额明细
 *
 * @TableName bu_enterprise_data_asset_account_detail
 */
@TableName(value = "bu_enterprise_data_asset_account_detail")
@Data
public class AccountDetail extends DataAssetBase implements Serializable {
    /**
     * 年份
     */
    @JSONField(name = "YEAR")
    @TableField(value = "year")
    private String year;

    /**
     * 会计区间
     */
    @JSONField(name = "PERIOD")
    @TableField(value = "period")
    private String period;

    /**
     * 科目编码
     */
    @JSONField(name = "SUBJCODE")
    @TableField(value = "subjcode")
    private String subjcode;

    /**
     * 科目名称
     */
    @JSONField(name = "SUBJNAME")
    @TableField(value = "subjname")
    private String subjname;

    /**
     * 显示名称
     */
    @JSONField(name = "DISPNAME")
    @TableField(value = "dispname")
    private String dispname;

    /**
     * 制单日期
     */
    @JSONField(name = "PREPAREDDATE", format = "yyyy-MM-dd")
    @TableField(value = "prepareddate")
    private Date prepareddate;

    /**
     * 摘要内容
     */
    @JSONField(name = "EXPLANATION")
    @TableField(value = "explanation")
    private String explanation;

    /**
     * 余额方向
     */
    @JSONField(name = "BALANORIENT")
    @TableField(value = "balanorient")
    private Integer balanorient;

    /**
     * 原币贷发生额
     */
    @JSONField(name = "CREDITAMOUNT")
    @TableField(value = "creditamount")
    private BigDecimal creditamount;

    /**
     * 原币借发生额
     */
    @JSONField(name = "DEBITAMOUNT")
    @TableField(value = "debitamount")
    private BigDecimal debitamount;

    /**
     * 本币借发生额
     */
    @JSONField(name = "LOCALDEBITAMOUNT")
    @TableField(value = "localdebitamount")
    private BigDecimal localdebitamount;

    /**
     * 本币贷发生额
     */
    @JSONField(name = "LOCALCREDITAMOUNT")
    @TableField(value = "localcreditamount")
    private BigDecimal localcreditamount;

    /**
     * 贷方累计
     */
    @JSONField(name = "SUMCREDITAMOUNT")
    @TableField(value = "sumcreditamount")
    private BigDecimal sumcreditamount;

    /**
     * 借方累计
     */
    @JSONField(name = "SUMDEBITAMOUNT")
    @TableField(value = "sumdebitamount")
    private BigDecimal sumdebitamount;

    /**
     * 余额
     */
    @JSONField(name = "YUE")
    @TableField(value = "yue")
    private BigDecimal yue;

    @TableField(value = "children_company")
    private String childrenCompany;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
