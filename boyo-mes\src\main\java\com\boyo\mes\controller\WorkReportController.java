package com.boyo.mes.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.mes.entity.ProductOrderDetail;
import com.boyo.mes.entity.WorkReport;
import com.boyo.mes.mapper.ProductOrderDetailMapper;
import com.boyo.mes.service.IWorkReportService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 报工记录(WorkReport)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Api("报工记录")
@RestController
@RequestMapping("/mes/workReport")
@AllArgsConstructor
public class WorkReportController extends BaseController{
    /**
     * 服务对象
     */
    private final IWorkReportService workReportService;

    @Autowired
    private final ProductOrderDetailMapper detailMapper;

    /**
     * 查询报工记录列表
     *
     */
    @ApiOperation("查询报工记录列表")
    @GetMapping("/list")
    public TableDataInfo list(WorkReport workReport) {
        startPage();
        List<WorkReport> list = workReportService.selectWorkReportList(workReport);
        return getDataTable(list);
    }
    
    /**
     * 获取报工记录详情
     */
    @ApiOperation("获取报工记录详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(workReportService.getById(id));
    }

    /**
     * 新增报工记录
     */
    @ApiOperation("新增报工记录")
    @PostMapping
    public AjaxResult add(@RequestBody WorkReport workReport) {
        QueryWrapper<ProductOrderDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_id",workReport.getOrderId());
        List<ProductOrderDetail> productOrderDetails = detailMapper.selectList(queryWrapper);
        workReport.setTaskId(productOrderDetails.get(0).getTaskId());
        return toBooleanAjax(workReportService.save(workReport));
    }

    /**
     * 修改报工记录
     */
    @ApiOperation("修改报工记录")
    @PutMapping
    public AjaxResult edit(@RequestBody WorkReport workReport) {
        return toBooleanAjax(workReportService.updateById(workReport));
    }

    /**
     * 删除报工记录
     */
    @ApiOperation("删除报工记录")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(workReportService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 获取订单报工详情
     * @return
     */
    @GetMapping("/getOrderReportDetail")
    public AjaxResult getOrderReportDetail(Integer id){
        return AjaxResult.success(workReportService.getOrderReportDetail(id));
    }

}
