package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipInspectionTempl;
import com.boyo.eam.mapper.EquipInspectionTemplMapper;
import com.boyo.eam.service.IEquipInspectionTemplService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 点检表(EquipInspectionTempl)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-29 10:21:50
 */
@Service("equipInspectionTemplService")
@AllArgsConstructor
@Tenant

public class EquipInspectionTemplServiceImpl extends ServiceImpl<EquipInspectionTemplMapper, EquipInspectionTempl> implements IEquipInspectionTemplService {
    private final EquipInspectionTemplMapper equipInspectionTemplMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipInspectionTempl> selectEquipInspectionTemplList(EquipInspectionTempl equipInspectionTempl) {
        return equipInspectionTemplMapper.selectEquipInspectionTemplList(equipInspectionTempl);
    }

}
