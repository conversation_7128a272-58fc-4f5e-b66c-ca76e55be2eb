package com.boyo.project.controller;

import cn.hutool.core.util.IdUtil;
import com.boyo.project.entity.TeamProjectemplate;
import com.boyo.project.service.ITeamProjectemplateService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 项目类型表(TeamProjectemplate)表控制层
 *
 * <AUTHOR>
 * @since 2022-02-08 20:50:47
 */
@Api("项目类型表")
@RestController
@RequestMapping("/project/teamProjectemplate")
@AllArgsConstructor
public class TeamProjectemplateController extends BaseController{
    /**
     * 服务对象
     */
    private final ITeamProjectemplateService teamProjectemplateService;

    /**
     * 查询项目类型表列表
     *
     */
    @ApiOperation("查询项目类型表列表")
    @GetMapping("/list")
    public TableDataInfo list(TeamProjectemplate teamProjectemplate) {
        startPage();
        List<TeamProjectemplate> list = teamProjectemplateService.selectTeamProjectemplateList(teamProjectemplate);
        return getDataTable(list);
    }

    /**
     * 获取项目类型表详情
     */
    @ApiOperation("获取项目类型表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(teamProjectemplateService.getById(id));
    }

    /**
     * 新增项目类型表
     */
    @ApiOperation("新增项目类型表")
    @PostMapping
    public AjaxResult add(@RequestBody TeamProjectemplate teamProjectemplate) {
        teamProjectemplate.setCode(IdUtil.fastSimpleUUID());
        return toBooleanAjax(teamProjectemplateService.save(teamProjectemplate));
    }

    /**
     * 修改项目类型表
     */
    @ApiOperation("修改项目类型表")
    @PutMapping
    public AjaxResult edit(@RequestBody TeamProjectemplate teamProjectemplate) {
        return toBooleanAjax(teamProjectemplateService.updateById(teamProjectemplate));
    }

    /**
     * 删除项目类型表
     */
    @ApiOperation("删除项目类型表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(teamProjectemplateService.removeByIds(Arrays.asList(ids)));
    }

}
