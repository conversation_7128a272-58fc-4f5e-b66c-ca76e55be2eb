package com.boyo.system.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 系统类别管理
 * 表名 t_sys_category
 *
 * <AUTHOR>
 */
@ApiModel("系统类别表")
@Data
@TableName("t_sys_category")
public class TSysCategory extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "category_openid")
    private String categoryOpenid;
    /**
     * 类别名称
     */
    @ApiModelProperty("类别名称")
    @TableField(value = "category_name")
    private String categoryName;
    /**
     * 类别编码
     */
    @ApiModelProperty("类别编码")
    @TableField(value = "category_code")
    private String categoryCode;
    /**
     * 类别图标
     */
    @ApiModelProperty("类别图标")
    @TableField(value = "category_icon")
    private String categoryIcon;
    /**
     * 类别自定义图片
     */
    @ApiModelProperty("类别自定义图片")
    @TableField(value = "category_img")
    private String categoryImg;
    /**
     * 类别描述
     */
    @ApiModelProperty("类别描述")
    @TableField(value = "category_desc")
    private String categoryDesc;
    /**
     * 状态 1启用 0停用
     */
    @ApiModelProperty("状态 1启用 0停用")
    @TableField(value = "category_status")
    private String categoryStatus;
    /**
     * 序号
     */
    @ApiModelProperty("序号")
    @TableField(value = "category_order")
    private Integer categoryOrder;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(value = "update_time")
    private Date updateTime;
}
