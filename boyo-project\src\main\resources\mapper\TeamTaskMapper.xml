<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.project.mapper.TeamTaskMapper">

    <resultMap type="com.boyo.project.entity.TeamTask" id="TeamTaskResult">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="projectCode" column="project_code"/>
        <result property="name" column="name"/>
        <result property="pri" column="pri"/>
        <result property="executeStatus" column="execute_status"/>
        <result property="description" column="description"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="assignTo" column="assign_to"/>
        <result property="deleted" column="deleted"/>
        <result property="stageCode" column="stage_code"/>
        <result property="taskTag" column="task_tag"/>
        <result property="done" column="done"/>
        <result property="beginTime" column="begin_time"/>
        <result property="endTime" column="end_time"/>
        <result property="remindTime" column="remind_time"/>
        <result property="pcode" column="pcode"/>
        <result property="sort" column="sort"/>
        <result property="star" column="star"/>
        <result property="deletedTime" column="deleted_time"/>
        <result property="idNum" column="id_num"/>
        <result property="path" column="path"/>
        <result property="schedule" column="schedule"/>
        <result property="versionCode" column="version_code"/>
        <result property="featuresCode" column="features_code"/>
        <result property="workTime" column="work_time"/>
        <result property="status" column="status"/>
        <result property="liked" column="liked"/>
        <result property="projectName" column="project_name"/>
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectTeamTaskList" parameterType="com.boyo.project.entity.TeamTask" resultMap="TeamTaskResult">
        select
        id, code, project_code, `name`, pri, execute_status, description, create_by, create_time, assign_to, deleted,
        stage_code, task_tag, done, begin_time, end_time, remind_time, pcode, sort, star, deleted_time, `private`,
        id_num, path, schedule, version_code, features_code, work_time, status, liked
        from team_task
        <where>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="name != null and name != ''">
                and `name` = #{name}
            </if>
            <if test="pri != null">
                and pri = #{pri}
            </if>
            <if test="executeStatus != null and executeStatus != ''">
                and execute_status = #{executeStatus}
            </if>
            <if test="description != null and description != ''">
                and description = #{description}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="assignTo != null and assignTo != ''">
                and assign_to = #{assignTo}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="stageCode != null and stageCode != ''">
                and stage_code = #{stageCode}
            </if>
            <if test="taskTag != null and taskTag != ''">
                and task_tag = #{taskTag}
            </if>
            <if test="done != null">
                and done = #{done}
            </if>
            <if test="beginTime != null and beginTime != ''">
                and begin_time = #{beginTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and end_time = #{endTime}
            </if>
            <if test="remindTime != null and remindTime != ''">
                and remind_time = #{remindTime}
            </if>
            <if test="pcode != null and pcode != ''">
                and pcode = #{pcode}
            </if>
            <if test="sort != null">
                and sort = #{sort}
            </if>
            <if test="like != null">
                and `like` = #{like}
            </if>
            <if test="star != null">
                and star = #{star}
            </if>
            <if test="deletedTime != null and deletedTime != ''">
                and deleted_time = #{deletedTime}
            </if>
            <if test="private != null">
                and `private` = #{private}
            </if>
            <if test="idNum != null">
                and id_num = #{idNum}
            </if>
            <if test="path != null and path != ''">
                and `path` = #{path}
            </if>
            <if test="schedule != null">
                and schedule = #{schedule}
            </if>
            <if test="versionCode != null and versionCode != ''">
                and version_code = #{versionCode}
            </if>
            <if test="featuresCode != null and featuresCode != ''">
                and features_code = #{featuresCode}
            </if>
            <if test="workTime != null">
                and work_time = #{workTime}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="liked != null">
                and liked = #{liked}
            </if>
        </where>
    </select>

    <select id="listMyTask" resultMap="TeamTaskResult">

        select t1.*, t2.name as project_name
        from team_task t1,
             team_project t2
        where t1.assign_to = #{assignTo}
          and t1.done = '0'
          and t1.begin_time != '' and t1.begin_time is not null and t1.project_code = t2.code
        order by t1.id desc
    </select>
</mapper>

