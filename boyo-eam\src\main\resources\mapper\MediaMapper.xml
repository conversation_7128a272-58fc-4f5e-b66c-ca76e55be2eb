<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.MediaMapper">

    <resultMap type="com.boyo.eam.domain.Media" id="MediaResult">
        <result property="id" column="id" />
        <result property="fileName" column="file_name" />
        <result property="name" column="name" />
        <result property="url" column="url" />
        <result property="size" column="size" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectMediaList" parameterType="com.boyo.eam.domain.Media" resultMap="MediaResult">
        select
          id, file_name, name, url, size, create_by, create_time, update_by, update_time
        from t_media
        <where>
            <if test="fileName != null and fileName != ''">
                and file_name = #{fileName}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="url != null and url != ''">
                and url = #{url}
            </if>
            <if test="size != null">
                and size = #{size}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>
</mapper>

