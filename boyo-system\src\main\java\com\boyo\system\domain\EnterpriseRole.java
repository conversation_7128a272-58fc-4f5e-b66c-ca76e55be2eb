package com.boyo.system.domain;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 企业角色管理
 * 表名 t_enterprise_role
 *
 * <AUTHOR>
 */
@ApiModel("角色表")
@Data
@TableName("t_enterprise_role")
public class EnterpriseRole extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "role_openid")
    private String roleOpenid;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    @TableField(value = "enterprise_openid")
    private String enterpriseOpenid;
    /**
     * 角色名称
     */
    @ApiModelProperty("角色名称")
    @TableField(value = "role_name")
    private String roleName;

    @TableField(value = "data_scope")
    private String dataScope;
    /**
     * 角色编码
     */
    @ApiModelProperty("角色编码")
    @TableField(value = "role_code")
    private String roleCode;
    /**
     * 角色图标
     */
    @ApiModelProperty("角色图标")
    @TableField(value = "role_icon")
    private String roleIcon;
    /**
     * 角色描述
     */
    @ApiModelProperty("角色描述")
    @TableField(value = "role_desc")
    private String roleDesc;
    /**
     * 状态 1启用 0停用
     */
    @ApiModelProperty("状态 1启用 0停用")
    @TableField(value = "role_status")
    private String roleStatus;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 角色权限
     */
    @TableField(exist = false)
    private List<EnterpriseRoleFunction> functionList;

    @TableField(exist = false)
    private List<Integer> menus;
}
