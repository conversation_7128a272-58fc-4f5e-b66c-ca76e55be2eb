package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.EquipMaintTaskItem;
import com.boyo.eam.domain.EquipMaintTaskItemDetail;
import com.boyo.eam.mapper.EquipMaintTaskItemDetailMapper;
import com.boyo.eam.mapper.EquipMaintTaskItemMapper;
import com.boyo.eam.service.IEquipMaintTaskItemService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 维保任务管理-维保项目(EquipMaintTaskItem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:33
 */
@Service("EquipMaintTaskItemService")
@AllArgsConstructor
@Tenant
public class EquipMaintTaskItemServiceImpl extends ServiceImpl<EquipMaintTaskItemMapper, EquipMaintTaskItem> implements IEquipMaintTaskItemService {
    private final EquipMaintTaskItemMapper equipMaintTaskItemMapper;
    private final EquipMaintTaskItemDetailMapper equipMaintTaskItemDetailMapper;
    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<EquipMaintTaskItem> selectEquipMaintTaskItemList(EquipMaintTaskItem equipMaintTaskItem) {
        List<EquipMaintTaskItem> equipMaintTaskItems = equipMaintTaskItemMapper.selectEquipMaintTaskItemList(equipMaintTaskItem);
        for (EquipMaintTaskItem emti:equipMaintTaskItems){
            EquipMaintTaskItemDetail detail = new EquipMaintTaskItemDetail();
            detail.setEquipMaintTaskItemOpenid(emti.getOpenid());
            List<EquipMaintTaskItemDetail> detialList = equipMaintTaskItemDetailMapper.selectEquipMaintTaskItemDetailList(detail);
            emti.setDetailList(detialList);
        }
        return equipMaintTaskItems;
    }

}
