package com.boyo.mes.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.exception.CustomException;
import com.boyo.mes.entity.MesMould;
import com.boyo.mes.entity.MesProduction;
import com.boyo.mes.service.IMesProductionService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 生产产品表(MesProduction)表控制层
 *
 * <AUTHOR>
 * @since 2023-01-04 09:05:21
 */
@Api("生产产品表")
@RestController
@RequestMapping("/mes/mesProduction")
@AllArgsConstructor
public class MesProductionController extends BaseController{
    /**
     * 服务对象
     */
    private final IMesProductionService mesProductionService;

    /**
     * 查询生产产品表列表
     *
     */
    @ApiOperation("查询生产产品表列表")
    @GetMapping("/list")
    public TableDataInfo list(MesProduction mesProduction) {
        startPage();
        List<MesProduction> list = mesProductionService.selectMesProductionList(mesProduction);
        return getDataTable(list);
    }
    
    /**
     * 获取生产产品表详情
     */
    @ApiOperation("获取生产产品表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(mesProductionService.getById(id));
    }

    /**
     * 新增生产产品表
     */
    @ApiOperation("新增生产产品表")
    @PostMapping
    public AjaxResult add(@RequestBody MesProduction mesProduction) {
        QueryWrapper<MesProduction> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("production_code",mesProduction.getProductionCode());
        if(mesProductionService.count(queryWrapper) > 0){
            throw new CustomException("产品编码已存在");
        }
        return toBooleanAjax(mesProductionService.save(mesProduction));
    }

    /**
     * 修改生产产品表
     */
    @ApiOperation("修改生产产品表")
    @PutMapping
    public AjaxResult edit(@RequestBody MesProduction mesProduction) {
        QueryWrapper<MesProduction> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("production_code",mesProduction.getProductionCode()).ne("id",mesProduction.getId());
        if(mesProductionService.count(queryWrapper) > 0){
            throw new CustomException("产品编码已存在");
        }
        return toBooleanAjax(mesProductionService.updateById(mesProduction));
    }

    /**
     * 删除生产产品表
     */
    @ApiOperation("删除生产产品表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(mesProductionService.removeByIds(Arrays.asList(ids)));
    }

}
