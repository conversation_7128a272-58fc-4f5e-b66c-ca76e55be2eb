package com.boyo.mes.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.mes.entity.ProcessEquipment;
import com.boyo.mes.mapper.ProcessEquipmentMapper;
import com.boyo.mes.service.IProcessEquipmentService;
import java.util.List;

/**
 * 工序设备关联关系(ProcessEquipment)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Service("processEquipmentService")
@AllArgsConstructor
@Tenant
public class ProcessEquipmentServiceImpl extends ServiceImpl<ProcessEquipmentMapper, ProcessEquipment> implements IProcessEquipmentService {
    private final ProcessEquipmentMapper processEquipmentMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<ProcessEquipment> selectProcessEquipmentList(ProcessEquipment processEquipment) {
        return processEquipmentMapper.selectProcessEquipmentList(processEquipment);
    }

    @Override
    public void addProcessEquipment(JSONObject object) {
        Integer id = object.getInteger("id");
        QueryWrapper<ProcessEquipment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("process_id",id);
        processEquipmentMapper.delete(queryWrapper);
        String[] ids = object.getString("equipmentIds").split(",");
        for (String eq:ids) {
            if(StrUtil.isNotEmpty(eq)){
                ProcessEquipment temp = new ProcessEquipment();
                temp.setEquipmentId(Convert.toInt(eq));
                temp.setProcessId(id);
                processEquipmentMapper.insert(temp);
            }
        }
    }
}
