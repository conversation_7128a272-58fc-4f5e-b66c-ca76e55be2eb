package com.boyo.master.domain;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 租户数据字典管理
 * 表名 t_base_dict
 *
 * <AUTHOR>
 */
@ApiModel("租户数据字典")
@Data
@TableName("t_base_dict")
public class BaseDict extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "openid")
    private String openid;
    /**
     * 数据类型
     */
    @ApiModelProperty("数据类型")
    @TableField(value = "base_type")
    private String baseType;
    /**
     * 数据编码
     */
    @ApiModelProperty("数据编码")
    @TableField(value = "base_code")
    private String baseCode;
    /**
     * 数据描述
     */
    @ApiModelProperty("数据描述")
    @TableField(value = "base_desc")
    private String baseDesc;
    /**
     * 数据状态
     */
    @ApiModelProperty("数据状态")
    @TableField(value = "base_status")
    private String baseStatus = "1";
}
