package com.boyo.mes.controller;

import com.alibaba.fastjson.JSONObject;
import com.boyo.mes.entity.ProcessEquipment;
import com.boyo.mes.service.IProcessEquipmentService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 工序设备关联关系(ProcessEquipment)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Api("工序设备关联关系")
@RestController
@RequestMapping("/mes/processEquipment")
@AllArgsConstructor
public class ProcessEquipmentController extends BaseController{
    /**
     * 服务对象
     */
    private final IProcessEquipmentService processEquipmentService;

    /**
     * 查询工序设备关联关系列表
     *
     */
    @ApiOperation("查询工序设备关联关系列表")
    @GetMapping("/list")
    public TableDataInfo list(ProcessEquipment processEquipment) {
        startPage();
        List<ProcessEquipment> list = processEquipmentService.selectProcessEquipmentList(processEquipment);
        return getDataTable(list);
    }
    
    /**
     * 获取工序设备关联关系详情
     */
    @ApiOperation("获取工序设备关联关系详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(processEquipmentService.getById(id));
    }

    /**
     * 新增工序设备关联关系
     */
    @ApiOperation("新增工序设备关联关系")
    @PostMapping
    public AjaxResult add(@RequestBody JSONObject object) {
        processEquipmentService.addProcessEquipment(object);
        return AjaxResult.success();
    }

    /**
     * 修改工序设备关联关系
     */
    @ApiOperation("修改工序设备关联关系")
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessEquipment processEquipment) {
        return toBooleanAjax(processEquipmentService.updateById(processEquipment));
    }

    /**
     * 删除工序设备关联关系
     */
    @ApiOperation("删除工序设备关联关系")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(processEquipmentService.removeByIds(Arrays.asList(ids)));
    }

}
