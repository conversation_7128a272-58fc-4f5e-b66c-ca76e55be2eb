package com.boyo.master.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * 主数据-货位管理
 * 表名 t_model_allocation
 *
 * <AUTHOR>
 */
@ApiModel("主数据-货位")
@Data
@TableName("t_model_allocation")
public class ModelAllocation extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "allocation_openid")
    private String allocationOpenid;
    /**
     * 所属工厂
     */
    @ApiModelProperty("所属工厂")
    @TableField(value = "allocation_factory")
    private String allocationFactory;
    /**
     * 所属仓库
     */
    @ApiModelProperty("所属仓库")
    @TableField(value = "allocation_warehouse")
    private String allocationWarehouse;
    /**
     * 所属区域
     */
    @ApiModelProperty("所属区域")
    @TableField(value = "allocation_area")
    private String allocationArea;
    /**
     * 货位名称
     */
    @ApiModelProperty("货位名称")
    @TableField(value = "allocation_name")
    private String allocationName;
    /**
     * 货位简称
     */
    @ApiModelProperty("货位简称")
    @TableField(value = "allocation_abbreviation")
    private String allocationAbbreviation;
    /**
     * 货位编码
     */
    @ApiModelProperty("货位编码")
    @TableField(value = "allocation_code")
    private String allocationCode;
    /**
     * 货位类型
     */
    @ApiModelProperty("货位类型")
    @TableField(value = "allocation_type")
    private String allocationType;
    /**
     * 货位描述
     */
    @ApiModelProperty("货位描述")
    @TableField(value = "allocation_desc")
    private String allocationDesc;
    /**
     * 状态 1启用 0停用
     */
    @ApiModelProperty("状态 1启用 0停用")
    @TableField(value = "allocation_status")
    private String allocationStatus;
    /**
     * 货位logo
     */
    @ApiModelProperty("货位logo")
    @TableField(value = "allocation_img")
    private String allocationImg;
    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    @TableField(value = "allocation_contacts")
    private String allocationContacts;
    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    @TableField(value = "allocation_phone")
    private String allocationPhone;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "created_at")
    private Date createdAt;
    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    @TableField(value = "created_user")
    private String createdUser;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(value = "updated_at")
    private Date updatedAt;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "updated_user")
    private String updatedUser;
}
