package com.boyo.view.service.impl;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.view.entity.GoviewProjectData;
import com.boyo.view.mapper.GoviewProjectDataMapper;
import com.boyo.view.service.IGoviewProjectDataService;
import java.util.List;

/**
 * 项目数据关联表(GoviewProjectData)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-13 15:15:05
 */
@Service("goviewProjectDataService")
@AllArgsConstructor
public class GoviewProjectDataServiceImpl extends ServiceImpl<GoviewProjectDataMapper, GoviewProjectData> implements IGoviewProjectDataService {
    private final GoviewProjectDataMapper goviewProjectDataMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<GoviewProjectData> selectGoviewProjectDataList(GoviewProjectData goviewProjectData) {
        return goviewProjectDataMapper.selectGoviewProjectDataList(goviewProjectData);
    }

}
