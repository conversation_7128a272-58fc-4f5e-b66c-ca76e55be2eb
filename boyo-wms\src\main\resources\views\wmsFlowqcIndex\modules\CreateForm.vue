<template>
  <a-modal width="30%" :maskClosable="false" :visible="open" @cancel="cancel">
    <template #title>
      <a-icon type="security-scan" />
      {{ formTitle }}
    </template>
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      layout="horizontal"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
           <a-form-model-item label="出入库流水id">
        <a-input
          :size="formSize"
          v-model="form.flowId"
          :placeholder="$t('app.global.please.input') + '出入库流水id'"
        />
      </a-form-model-item>
           <a-form-model-item label="质检模板名称">
        <a-input
          :size="formSize"
          v-model="form.qcName"
          :placeholder="$t('app.global.please.input') + '质检模板名称'"
        />
      </a-form-model-item>
           <a-form-model-item label="质检时间">
        <a-input
          :size="formSize"
          v-model="form.createTime"
          :placeholder="$t('app.global.please.input') + '质检时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="质检人">
        <a-input
          :size="formSize"
          v-model="form.createUser"
          :placeholder="$t('app.global.please.input') + '质检人'"
        />
      </a-form-model-item>
        </a-form-model>
    <template #footer>
      <a-space>
        <a-button :size="formSize" icon="close" type="danger" @click="cancel">
          {{ $t('app.global.close') }}
        </a-button>
        <a-button :size="formSize" icon="save" type="primary" @click="submitForm">
          {{ $t('app.global.save') }}
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script>
import { addWmsFlowqcIndex, updateWmsFlowqcIndex, getWmsFlowqcIndex } from '@/api/wmsFlowqcIndex'
export default {
  data() {
    return {
      //新增或修改
      updateState: false,
      formTitle: '$tableInfo.comment',
      // 表单参数
      form: {
           id: '',
           flowId: '',
           qcName: '',
           createTime: '',
           createUser: '',
             },
      open: false,
      rules: {},
    }
  },
  created() {
    this.rules = {
    }
  },
  methods: {
    /**
     * 新增按钮操作
     * */
    handleAdd() {
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.add') + '$tableInfo.comment'
      this.form = {}
      this.updateState = false
    },
    /**
     * 修改按钮操作
     * */
    async handleUpdate($event, id) {
      $event.stopPropagation()
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.edit') + '$tableInfo.comment'
      const response = await getWmsFlowqcIndex(id)
      this.form = response.data
    },
    /**
     * 提交按钮
     * */
    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
            if (this.form.id) {
                await updateWmsFlowqcIndex(this.form)
                this.$alert.success(this.$t('app.global.edit.success'))
                this.open = false
                this.$emit('ok')
            } else {
                await addWmsFlowqcIndex(this.form)
                this.$alert.success(this.$t('app.global.add.success'))
                this.open = false
                this.$emit('ok')
            }
        } else {
            return false
        }
        })
    },
    /**
     * 取消按钮
     * */
    cancel() {
      this.open = false
      this.reset()
    },
    /**
     * 表单重置
     * */
    reset() {
      this.form = {}
      if (this.$refs.form) {
        this.$refs.form.resetFields()}
      }
    },
}
</script>

