package com.boyo.common.annotation;

import java.lang.annotation.*;

/**
 * 数据权限过滤注解
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CrmDataScope
{
    /**
     * 部门表的别名
     */
    String deptAlias() default "";

    /**
     * 用户表的别名
     */
    String userAlias() default "";

    /**
     * 处理字段名
     * @return
     */
    String columns() default "";
}
