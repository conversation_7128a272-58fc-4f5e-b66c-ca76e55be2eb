package com.boyo.mes.controller;

import com.boyo.mes.entity.MesCutting;
import com.boyo.mes.service.IMesCuttingService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * (MesCutting)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-10 15:46:44
 */
@Api("")
@RestController
@RequestMapping("/mes/mesCutting")
@AllArgsConstructor
public class MesCuttingController extends BaseController{
    /**
     * 服务对象
     */
    private final IMesCuttingService mesCuttingService;

    /**
     * 查询列表
     *
     */
    @ApiOperation("查询列表")
    @GetMapping("/list")
    public TableDataInfo list(MesCutting mesCutting) {
        startPage();
        List<MesCutting> list = mesCuttingService.selectMesCuttingList(mesCutting);
        return getDataTable(list);
    }
    
    /**
     * 获取详情
     */
    @ApiOperation("获取详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(mesCuttingService.getById(id));
    }

    /**
     * 新增
     */
    @ApiOperation("新增")
    @PostMapping
    public AjaxResult add(@RequestBody MesCutting mesCutting) {
        return toBooleanAjax(mesCuttingService.save(mesCutting));
    }

    /**
     * 修改
     */
    @ApiOperation("修改")
    @PutMapping
    public AjaxResult edit(@RequestBody MesCutting mesCutting) {
        return toBooleanAjax(mesCuttingService.updateById(mesCutting));
    }

    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(mesCuttingService.removeByIds(Arrays.asList(ids)));
    }

}
