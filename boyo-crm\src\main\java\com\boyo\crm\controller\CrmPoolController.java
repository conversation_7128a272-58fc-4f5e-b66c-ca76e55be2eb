package com.boyo.crm.controller;

import com.boyo.crm.entity.CrmPool;
import com.boyo.crm.service.ICrmPoolService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * 公海表(CrmPool)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-30 14:33:58
 */
@Api("公海表")
@RestController
@RequestMapping("/crm/crmPool")
@AllArgsConstructor
public class CrmPoolController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmPoolService crmPoolService;

    /**
     * 查询公海表列表
     *
     */
    @ApiOperation("查询公海表列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmPool crmPool) {
        startPage();
        List<CrmPool> list = crmPoolService.selectCrmPoolList(crmPool);
        return getDataTable(list);
    }
    
    /**
     * 获取公海表详情
     */
    @ApiOperation("获取公海表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(crmPoolService.getById(id));
    }

    /**
     * 新增公海表
     */
    @ApiOperation("新增公海表")
    @PostMapping
    public AjaxResult add(@RequestBody CrmPool crmPool) {
        return toBooleanAjax(crmPoolService.save(crmPool));
    }

    /**
     * 修改公海表
     */
    @ApiOperation("修改公海表")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmPool crmPool) {
        return toBooleanAjax(crmPoolService.updateById(crmPool));
    }

    /**
     * 删除公海表
     */
    @ApiOperation("删除公海表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmPoolService.removeByIds(Arrays.asList(ids)));
    }

}
