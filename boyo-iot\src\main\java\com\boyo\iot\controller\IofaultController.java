package com.boyo.iot.controller;

import com.boyo.iot.entity.Iofault;
import com.boyo.iot.service.IIofaultService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Arrays;

/**
 * IoT故障清单(Iofault)表控制层
 *
 * <AUTHOR>
 * @since 2022-04-07 15:16:28
 */
@Api("IoT故障清单")
@RestController
@RequestMapping("/iot/iofault")
@AllArgsConstructor
public class IofaultController extends BaseController{
    /**
     * 服务对象
     */
    private final IIofaultService iofaultService;

    /**
     * 查询IoT故障清单列表
     *
     */
    @ApiOperation("查询IoT故障清单列表")
    @GetMapping("/list")
    public TableDataInfo list(Iofault iofault) {
        startPage();
        List<Iofault> list = iofaultService.selectIofaultList(iofault);
        return getDataTable(list);
    }
    
    /**
     * 获取IoT故障清单详情
     */
    @ApiOperation("获取IoT故障清单详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(iofaultService.getById(id));
    }

    /**
     * 新增IoT故障清单
     */
    @ApiOperation("新增IoT故障清单")
    @PostMapping
    public AjaxResult add(@RequestBody Iofault iofault) {
        return toBooleanAjax(iofaultService.save(iofault));
    }

    /**
     * 修改IoT故障清单
     */
    @ApiOperation("修改IoT故障清单")
    @PutMapping
    public AjaxResult edit(@RequestBody Iofault iofault) {
        return toBooleanAjax(iofaultService.updateById(iofault));
    }

    /**
     * 删除IoT故障清单
     */
    @ApiOperation("删除IoT故障清单")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(iofaultService.removeByIds(Arrays.asList(ids)));
    }

}
