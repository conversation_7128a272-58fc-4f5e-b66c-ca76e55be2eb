package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 物模型属性表(ObjectInfoProperty)实体类
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Data
@TableName(value = "object_info_property")
public class ObjectInfoProperty implements Serializable {
    private static final long serialVersionUID = 500627165351596812L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 物模型openid：对应object_info中的openid
    */
    @TableField(value="model_openid")
    private String modelOpenid;
    /**
    * 功能名称
    */
    @TableField(value="name")
    private String name;
    /**
    * 标识符
    */
    @TableField(value="identifier")
    private String identifier;
    /**
    * 数据类型
    */
    @TableField(value="type")
    private String type;
    /**
    * 读写：0只读，1读写
    */
    @TableField(value="chmode")
    private Integer chmode;
    /**
    * 取值范围-最小值
    */
    @TableField(value="range_min")
    private Integer rangeMin;
    /**
    * 取值范围-最大值
    */
    @TableField(value="range_max")
    private Integer rangeMax;
    /**
    * 单位
    */
    @TableField(value="unit")
    private String unit;
    /**
    * 参数：{0开始,1结束}
    */
    @TableField(value="parameter")
    private String parameter;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

}
