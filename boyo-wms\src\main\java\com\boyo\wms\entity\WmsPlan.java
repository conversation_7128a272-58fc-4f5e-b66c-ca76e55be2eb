package com.boyo.wms.entity;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import com.boyo.wms.vo.WmsMaterielVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 出入库计划管理
 * 表名 t_wms_plan
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("出入库管理表-计划表")
@Data
@TableName("t_wms_plan")
public class WmsPlan extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "plan_openid")
    private String planOpenid;
    /**
     * 出入库类型，1-入库，2-出库
     */
    @ApiModelProperty("出入库类型，1-入库，2-出库")
    @TableField(value = "plan_type")
    private String planType;
    /**
     * 入库类型或出库类型，参照数据字典inType、outType
     */
    @ApiModelProperty("入库类型或出库类型，参照数据字典inType、outType")
    @TableField(value = "plan_category")
    private String planCategory;
    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    @TableField(value = "plan_order")
    private String planOrder;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField(value = "plan_remarks")
    private String planRemarks;
    /**
     * 订单状态，1-未开始，2-已下发进行中，3-已完成
     */
    @ApiModelProperty("订单状态， 1: '未开始', 2: '已下发', 3: '进行中', 4: '已完成'")
    @TableField(value = "plan_state")
    private String planState;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "created_at",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createdAt;
    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    @TableField(value = "created_user",fill = FieldFill.INSERT)
    private String createdUser;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(value = "updated_at",fill = FieldFill.UPDATE)
    private Date updatedAt;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "updated_user",fill = FieldFill.UPDATE)
    private String updatedUser;
    /**
     * 计划开始时间
     */
    @ApiModelProperty("计划开始时间")
    @TableField(value = "plan_starttime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date planStarttime;
    /**
     * 计划结束时间
     */
    @ApiModelProperty("计划结束时间")
    @TableField(value = "plan_endtime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date planEndtime;
    /**
     * 结束原因，1-已完成，2-提前结束
     */
    @ApiModelProperty("结束原因，1-已完成，2-提前结束")
    @TableField(value = "plan_endreason")
    private String planEndreason;

    /**
     * 物料明细
     */
    @ApiModelProperty("出入库物料明细")
    @TableField(exist = false)
    private List<WmsMaterielVO> materielList;
}
