package com.boyo.crm.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import com.boyo.common.core.domain.BoyoBaseEntity;
import com.boyo.framework.annotation.PropertyMsg;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 回访表(CrmVisit)实体类
 *
 * <AUTHOR>
 * @since 2022-03-28 17:06:59
 */
@Data
@TableName(value = "t_crm_visit")
public class CrmVisit extends BoyoBaseEntity implements Serializable {
    private static final long serialVersionUID = 381566120258123538L;
    /**
     * 回访id
     */
    @TableId
    private Integer id;

    /**
     * 回访编号
     */
    @PropertyMsg(value="回访编号")
    @TableField(value = "visit_number")
    private String visitNumber;
    /**
     * 回访时间
     */
    @PropertyMsg(value="回访时间")
    @TableField(value = "visit_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date visitTime;
    /**
     * 回访方式
     */
    @PropertyMsg(value="回访方式",type = "base")
    @TableField(value = "visit_type")
    private Integer visitType;
    /**
     * 客户满意度
     */
    @PropertyMsg(value="客户满意度",type = "base")
    @TableField(value = "satisfaction")
    private Integer satisfaction;
    /**
     * 客户意见反馈
     */
    @PropertyMsg(value="客户意见反馈")
    @TableField(value = "opinion")
    private String opinion;
    /**
     * 回访人id
     */
    @TableField(value = "owner_user_id")
    private Long ownerUserId;
    /**
     * 客户id
     */
    @PropertyMsg(value="客户",type = "customer")
    @TableField(value = "customer_id")
    private Integer customerId;
    /**
     * 合同id
     */
    @PropertyMsg(value="合同",type = "contract")
    @TableField(value = "contract_id")
    private Integer contractId;
    /**
     * 联系人id
     */
    @PropertyMsg(value="联系人",type = "contacts")
    @TableField(value = "contacts_id")
    private Integer contactsId;
    /**
     * 创建人id
     */
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private Long createUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 客户名称
     */
    @TableField(exist = false)
    private String customerName;

    /**
     * 合同编号
     */
    @TableField(exist = false)
    private String contractCode;

    /**
     * 回访方式
     */
    @TableField(exist = false)
    private String visitTypeName;

    /**
     * 客户满意度
     */
    @TableField(exist = false)
    private String satisfactionName;

    /**
     * 回访人
     */
    @TableField(exist = false)
    private String ownerUserName;

    /**
     * 联系人
     */
    @TableField(exist = false)
    private String contactsName;

    /**
     * 创建人
     */
    @TableField(exist = false)
    private String createUserName;

}
