<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipLedgerPropertyMapper">

    <resultMap type="com.boyo.eam.domain.EquipLedgerProperty" id="EquipLedgerPropertyResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="equipLedgerOpenid" column="equip_ledger_openid" />
        <result property="name" column="name" />
        <result property="value" column="value" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipLedgerPropertyList" parameterType="com.boyo.eam.domain.EquipLedgerProperty" resultMap="EquipLedgerPropertyResult">
        select
          id, openid, equip_ledger_openid, name, value, remark, create_by, create_time, update_by, update_time
        from equip_ledger_property
        <where>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
            <if test="equipLedgerOpenid != null and equipLedgerOpenid != ''">
                and equip_ledger_openid = #{equipLedgerOpenid}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="value != null and value != ''">
                and value = #{value}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>
</mapper>

