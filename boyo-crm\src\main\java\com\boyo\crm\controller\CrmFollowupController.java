package com.boyo.crm.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.core.text.Convert;
import com.boyo.crm.entity.CrmFollowup;
import com.boyo.crm.service.ICrmFollowupService;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

/**
 * (CrmFollowup)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-30 10:19:59
 */
@Api("")
@RestController
@RequestMapping("/crm/crmFollowup")
@AllArgsConstructor
public class CrmFollowupController extends BaseController{
    /**
     * 服务对象
     */
    private final ICrmFollowupService crmFollowupService;
    private final IEnterpriseUserService enterpriseUserService;

    /**
     * 查询列表
     *
     */
    @ApiOperation("查询列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmFollowup crmFollowup) {
        startPage();
        List<CrmFollowup> list = crmFollowupService.selectCrmFollowupList(crmFollowup);
        if(list != null && list.size() > 0){
            List<Long> ids = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                if(ObjectUtil.isNotNull(list.get(i).getCreateUserId())){
                    ids.add(list.get(i).getCreateUserId());
                }
            }
            List<EnterpriseUser> userList = enterpriseUserService.selectByIds(ids);
            if(userList != null && userList.size() > 0){
                for (int i = 0; i < list.size(); i++) {
                    for (int j = 0; j < userList.size(); j++) {
                        if(ObjectUtil.isNotNull(list.get(i).getCreateUserId()) && list.get(i).getCreateUserId().equals(userList.get(j).getId())){
                            list.get(i).setCreateUserName(userList.get(j).getUserFullName());
                            break;
                        }
                    }
                }
            }
        }
        return getDataTable(list);
    }
    
    /**
     * 获取详情
     */
    @ApiOperation("获取详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(crmFollowupService.getById(id));
    }

    /**
     * 新增
     */
    @ApiOperation("新增")
    @PostMapping
    public AjaxResult add(@RequestBody CrmFollowup crmFollowup) {
        return toBooleanAjax(crmFollowupService.save(crmFollowup));
    }

    /**
     * 修改
     */
    @ApiOperation("修改")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmFollowup crmFollowup) {
        return toBooleanAjax(crmFollowupService.updateById(crmFollowup));
    }

    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(crmFollowupService.removeByIds(Arrays.asList(ids)));
    }

}
