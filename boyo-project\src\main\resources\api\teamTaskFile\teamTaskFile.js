import request from '@/utils/request'

const prefix = ''

// 查询(TeamTaskFile)列表
export function listTeamTaskFile(query) {
    return request({
        url: prefix + '/teamTaskFile/list',
        method: 'get',
        params: query,
    })
}

// 查询(TeamTaskFile)详细
export function getTeamTaskFile(id) {
    return request({
        url: prefix + '/teamTaskFile/' + id,
        method: 'get',
    })
}

// 新增(TeamTaskFile)
export function addTeamTaskFile(data) {
    return request({
        url: prefix + '/teamTaskFile',
        method: 'post',
        data: data,
    })
}

// 修改(TeamTaskFile)
export function updateTeamTaskFile(data) {
    return request({
        url: prefix + '/teamTaskFile',
        method: 'put',
        data: data,
    })
}

// 删除(TeamTaskFile)
export function delTeamTaskFile(id) {
    return request({
        url: prefix + '/teamTaskFile/' + id,
        method: 'delete',
    })
}
