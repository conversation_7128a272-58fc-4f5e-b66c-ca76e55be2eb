package com.boyo.crm.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.crm.entity.CrmClue;
import java.util.List;

/**
 * CRM线索主表(CrmClue)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-15 10:48:28
 */
public interface ICrmClueService extends IService<CrmClue> {

    /**
     * 查询多条数据
     *
     * @param crmClue 对象信息
     * @return 对象列表
     */
    List<CrmClue> selectCrmClueList(CrmClue crmClue);

    void changeToCustomer(CrmClue crmClue);

}
