package com.boyo.master.service.impl;

import java.util.List;

import com.boyo.framework.annotation.Tenant;
import com.boyo.master.vo.ModelAllocatonVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.master.mapper.ModelAllocationMapper;
import com.boyo.master.domain.ModelAllocation;
import com.boyo.master.service.IModelAllocationService;

/**
 * 主数据-货位管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Tenant
public class ModelAllocationServiceImpl extends ServiceImpl<ModelAllocationMapper, ModelAllocation> implements IModelAllocationService {

    private final ModelAllocationMapper modelAllocationMapper;


    /**
     * 查询主数据-货位管理列表
     *
     * @param modelAllocation 主数据-货位管理
     * @return modelAllocation 列表
     */
    @Override
    public List<ModelAllocatonVO> selectModelAllocationList(ModelAllocation modelAllocation) {
        return modelAllocationMapper.selectModelAllocationList(modelAllocation);
    }

}
