package com.boyo.crm.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.boyo.common.core.text.Convert;
import com.boyo.crm.entity.CrmAction;
import com.boyo.crm.mapper.*;
import com.boyo.framework.annotation.PropertyMsg;
import com.boyo.framework.annotation.Tenant;
import com.boyo.master.mapper.BaseDictMapper;
import lombok.AllArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Service;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;

@Service
@AllArgsConstructor
@Tenant
public class ActionUtil {
    private final CrmActionMapper actionMapper;
    private final BaseDictMapper baseDictMapper;
    private final CrmCustomerMapper customerMapper;
    private final CrmContactsMapper contactsMapper;
    private final CrmContractMapper contractMapper;
    private final CrmBusinessMapper businessMapper;


    public void editRecord(Object original,Object current,ActionEnum actionEnum,Integer id,Class cls){
        if(ObjectUtil.isNull(original)){
            CrmAction action = new CrmAction();
            action.setActionId(id);
            action.setTypes(actionEnum.getType());
            action.setDetail("创建了"+actionEnum.getName());
            actionMapper.insert(action);
            return;
        }
        try{
            StringBuffer str = new StringBuffer("");
            boolean change = false;
            Field[] fields = cls.getDeclaredFields();
            for(Field field: fields){
                if(field.getName().equals("serialVersionUID") || !field.isAnnotationPresent(PropertyMsg.class)){
                    continue;
                }
                PropertyDescriptor pd = new PropertyDescriptor(field.getName(),cls);
                Method getMethod = pd.getReadMethod();
                String type = field.getType().getName();
                if(!"java.util.Set".equals(type)){
                    Object o1 = getMethod.invoke(original);
                    Object o2 = getMethod.invoke(current);
                    if(null != o2){
                        change = true;
                        String s1 = o1 == null ? "" :o1.toString();
                        String s2 = o2 == null ? "" :o2.toString();
                        if(field.getType().equals(Integer.class) || field.getType().equals(Double.class) || field.getType().equals(BigDecimal.class)){
                            if(Convert.toDouble(s1).equals(Convert.toDouble(s2))){
                                continue;
                            }
                        }
                        if(!s1.equals(s2)){
                            str.append("<p>").append(field.getAnnotation(PropertyMsg.class).value());
                            str.append("由");
                            switch (field.getAnnotation(PropertyMsg.class).type()){
                                case "str":
                                    if(field.getType().equals(new Date().getClass())){
                                        if(StrUtil.isEmpty(s1)){
                                            s1 = "";
                                        }else{
                                            s1 = DateUtil.format(new Date(s1),field.getAnnotation(DateTimeFormat.class).pattern());
                                        }
                                        if(StrUtil.isEmpty(s2)){
                                            s2 = "";
                                        }else{
                                            s2 = DateUtil.format(new Date(s2),field.getAnnotation(DateTimeFormat.class).pattern());
                                        }
                                        str.append("【").append(s1).append("】变更为【").append(s2).append("】");
                                    }else{
                                        str.append("【").append(s1).append("】变更为【").append(s2).append("】");
                                    }
                                    break;
                                case "base":
//                                    数据字典
                                    str.append("【").append(baseDictMapper.selectById(s1).getBaseDesc()).append("】变更为【").append(baseDictMapper.selectById(s2).getBaseDesc()).append("】");
                                    break;
                                case "customer":
//                                    客户
                                    str.append("【").append(customerMapper.selectById(s1).getCustomerName()).append("】变更为【").append(customerMapper.selectById(s2).getCustomerName()).append("】");
                                    break;
                                case "contract":
//                                    合同
                                    str.append("【").append(contractMapper.selectById(s1).getNum()).append("】变更为【").append(contractMapper.selectById(s2).getNum()).append("】");
                                    break;
                                case "contacts":
//                                    联系人
                                    str.append("【").append(contactsMapper.selectById(s1).getName()).append("】变更为【").append(contactsMapper.selectById(s2).getName()).append("】");
                                    break;
                                case "business":
//                                    商机
                                    str.append("【").append(businessMapper.selectById(s1).getBusinessName()).append("】变更为【").append(businessMapper.selectById(s2).getBusinessName()).append("】");
                                    break;
                                default:
                                    break;
                            }
                            str.append("</p>");
                        }
                    }
                }
            }
            if(change){
                CrmAction action = new CrmAction();
                action.setActionId(id);
                action.setTypes(actionEnum.getType());
                action.setDetail(str.toString());
                actionMapper.insert(action);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
