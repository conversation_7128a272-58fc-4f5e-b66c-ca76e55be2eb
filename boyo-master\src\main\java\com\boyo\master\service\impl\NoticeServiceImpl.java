package com.boyo.master.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.master.entity.Notice;
import com.boyo.master.mapper.NoticeMapper;
import com.boyo.master.service.INoticeService;
import java.util.List;

/**
 * 通知公告表(Notice)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-11 14:17:15
 */
@Service("noticeService")
@AllArgsConstructor
@Tenant
public class NoticeServiceImpl extends ServiceImpl<NoticeMapper, Notice> implements INoticeService {
    private final NoticeMapper noticeMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<Notice> selectNoticeList(Notice notice) {
        return noticeMapper.selectNoticeList(notice);
    }

}
