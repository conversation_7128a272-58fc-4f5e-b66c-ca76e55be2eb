package com.boyo.master.domain;


import com.boyo.common.annotation.Excel;
import com.boyo.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.Map;

/**
 * 大屏产量对象 screen_yield
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
public class ScreenYield extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 车间id
     */
    @Excel(name = "车间id")
    private Long workshopId;

    private String workshopName;


    public void setWorkshopName(String workshopName) {
        this.workshopName = workshopName;
    }

    /**
     * 产量
     */
    @Excel(name = "产量")
    private Long yield;



    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date yieldTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setWorkshopId(Long workshopId) {
        this.workshopId = workshopId;
    }

    public Long getWorkshopId() {
        return workshopId;
    }

    public void setYield(Long yield) {
        this.yield = yield;
    }

    public Long getYield() {
        return yield;
    }

    public void setYieldTime(Date yieldTime) {
        this.yieldTime = yieldTime;
    }

    public Date getYieldTime() {
        return yieldTime;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getWorkshopName() {
        return workshopName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("workshopId", getWorkshopId())
                .append("yield", getYield())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("yieldTime", getYieldTime())
                .toString();
    }
}
