package com.boyo.master.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数字资产公有字段
 */
@Data
public class DataAssetBase {
    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 同步源id
     */
    @TableField(value = "remote_id")
    private String remoteId;

    /**
     * 企业id，因企业与用户是一对一的关系，所以此处是企业用户id
     */
    
    @TableField(value = "enterprise_id")
    private Long enterpriseId;

    /**
     * 创建人
     */
    
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}
