package com.boyo.master.mapper;

import java.util.List;

import com.boyo.framework.annotation.Tenant;
import com.boyo.master.domain.BaseDict;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;


/**
 * 租户数据字典管理Mapper接口
 *
 * <AUTHOR>
 */
@Tenant
public interface BaseDictMapper extends BaseMapper<BaseDict> {

    /**
     * 查询租户数据字典管理列表
     *
     * @param baseDict 租户数据字典管理
     * @return BaseDict集合
     */
    public List<BaseDict> selectBaseDictList(BaseDict baseDict);

}
