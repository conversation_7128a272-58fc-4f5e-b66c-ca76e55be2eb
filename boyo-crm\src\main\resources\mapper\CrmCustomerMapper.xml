<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.crm.mapper.CrmCustomerMapper">

    <resultMap type="com.boyo.crm.entity.CrmCustomer" id="CrmCustomerResult">
        <result property="id" column="id"/>
        <result property="customerName" column="customer_name"/>
        <result property="followup" column="followup"/>
        <result property="isLock" column="is_lock"/>
        <result property="industry" column="industry"/>
        <result property="level" column="level"/>
        <result property="nextTime" column="next_time"/>
        <result property="dealStatus" column="deal_status"/>
        <result property="mobile" column="mobile"/>
        <result property="telephone" column="telephone"/>
        <result property="website" column="website"/>
        <result property="remark" column="remark"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="ownerUserId" column="owner_user_id"/>
        <result property="address" column="address"/>
        <result property="location" column="location"/>
        <result property="detailAddress" column="detail_address"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="levelName" column="level_name" />
        <result property="industryName" column="industry_name" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectCrmCustomerList" parameterType="com.boyo.crm.entity.CrmCustomer" resultMap="CrmCustomerResult">
        select t1.*,t3.base_desc as industry_name,t4.base_desc as level_name from (select
        id, customer_name, followup, is_lock,industry,level, next_time, deal_status, mobile, telephone, website, remark,
        create_user_id, owner_user_id, address, location, detail_address, lng, lat, create_time, update_time
        from t_crm_customer
        <where>
            owner_user_id is not null
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="customerName != null and customerName != ''">
                and customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="followup != null">
                and followup = #{followup}
            </if>
            <if test="isLock != null">
                and is_lock = #{isLock}
            </if>
            <if test="nextTime != null">
                and next_time = #{nextTime}
            </if>
            <if test="dealStatus != null">
                and deal_status = #{dealStatus}
            </if>
            <if test="mobile != null and mobile != ''">
                and mobile = #{mobile}
            </if>
            <if test="telephone != null and telephone != ''">
                and telephone = #{telephone}
            </if>
            <if test="website != null and website != ''">
                and website = #{website}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="ownerUserId != null">
                and owner_user_id = #{ownerUserId}
            </if>
            <if test="address != null and address != ''">
                and address = #{address}
            </if>
            <if test="location != null and location != ''">
                and location = #{location}
            </if>
            <if test="detailAddress != null and detailAddress != ''">
                and detail_address = #{detailAddress}
            </if>
            <if test="lng != null and lng != ''">
                and lng = #{lng}
            </if>
            <if test="lat != null and lat != ''">
                and lat = #{lat}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            ${params.dataScope}
        </where>
        ) t1 left join (select * from t_base_dict where base_type = 'CUSTOMER_INDUSTRY') t3 on t1.industry = t3.id
        left join (select * from t_base_dict where base_type = 'CUSTOMER_LEVEL') t4 on t1.level = t4.id
    </select>

    <select id="selectPoolCustomerList" parameterType="com.boyo.crm.entity.CrmCustomer" resultMap="CrmCustomerResult">
        select t1.*,t3.base_desc as industry_name,t4.base_desc as level_name from (select
        id, customer_name, followup, is_lock,industry,level, next_time, deal_status, mobile, telephone, website, remark,
        create_user_id, owner_user_id, address, location, detail_address, lng, lat, create_time, update_time
        from t_crm_customer
        <where>
            owner_user_id is null
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="customerName != null and customerName != ''">
                and customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="followup != null">
                and followup = #{followup}
            </if>
            <if test="isLock != null">
                and is_lock = #{isLock}
            </if>
            <if test="nextTime != null">
                and next_time = #{nextTime}
            </if>
            <if test="dealStatus != null">
                and deal_status = #{dealStatus}
            </if>
            <if test="mobile != null and mobile != ''">
                and mobile = #{mobile}
            </if>
            <if test="telephone != null and telephone != ''">
                and telephone = #{telephone}
            </if>
            <if test="website != null and website != ''">
                and website = #{website}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="address != null and address != ''">
                and address = #{address}
            </if>
            <if test="location != null and location != ''">
                and location = #{location}
            </if>
            <if test="detailAddress != null and detailAddress != ''">
                and detail_address = #{detailAddress}
            </if>
            <if test="lng != null and lng != ''">
                and lng = #{lng}
            </if>
            <if test="lat != null and lat != ''">
                and lat = #{lat}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
        ) t1 left join (select * from t_base_dict where base_type = 'CUSTOMER_INDUSTRY') t3 on t1.industry = t3.id
        left join (select * from t_base_dict where base_type = 'CUSTOMER_LEVEL') t4 on t1.level = t4.id
    </select>
</mapper>

