package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 点检-项目(EquipInspectionSpotItem)实体类
 *
 * <AUTHOR>
 * @since 2021-11-15 16:16:30
 */
@Data
@TableName(value = "equip_inspection_spot_item")
public class EquipInspectionSpotItem implements Serializable {
    private static final long serialVersionUID = -44441737708309655L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 关联equip_inspection_spot表的openid
    */
    @TableField(value="equip_inspection_spot_openid")
    private String equipInspectionSpotOpenid;
    /**
    * 项目
    */
    @TableField(value="item")
    private String item;
    /**
    * 检验基准
    */
    @TableField(value="basis")
    private String basis;
    /**
    * 检查方法
    */
    @TableField(value="method")
    private String method;
    /**
    * 处理办法
    */
    @TableField(value="way")
    private String way;
    /**
     * 时长
     */
    @TableField(value="use_time")
    private String useTime;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;

    /** 额外字段 */
    @TableField(exist = false)
    private String spotCode;//点检任务单号
    @TableField(exist = false)
    private String type;//检验类型（0点检/1巡检）
    @TableField(exist = false)
    private String equipCode;//设备编号
    @TableField(exist = false)
    private String equipName;//设备名称
    @TableField(exist = false)
    private String equipLocation;//设备位置
    @TableField(exist = false)
    private String equipType;//设备类型
    @TableField(exist = false)
    private String deptName;//部门名称
    @TableField(exist = false)
    private String staffName;//点巡检人员
    @TableField(exist = false)
    private String lineOpenid;//产线openid
    @TableField(exist = false)
    private String lineName;//产线名
    @TableField(exist = false)
    private Integer spotId;//点巡检主表id
    @TableField(exist = false)
    private String reviewerName;//审核人名字


    @TableField(exist = false)
    private Date inspectionDate;//点巡检记录：点巡检日期
    @TableField(exist = false)
    private String pass;//点巡检记录：是否通过
    @TableField(exist = false)
    private String recordRemark;//点巡检记录：说明
    @TableField(exist = false)
    private String mediaId;//点巡检记录：图片
    @TableField(exist = false)
    private List<String> mediaUrls;//点巡检记录：图片
    @TableField(exist = false)
    private String recordOpenid;

    //查询条件
    @TableField(exist = false)
    private Date beginDate;
    @TableField(exist = false)
    private Date endDate;
}
