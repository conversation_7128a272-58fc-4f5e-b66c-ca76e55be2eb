package com.boyo.eam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.eam.domain.EquipInspectionTempl;

import java.util.List;

/**
 * 点检表(EquipInspectionTempl)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-29 10:21:49
 */
public interface EquipInspectionTemplMapper extends BaseMapper<EquipInspectionTempl>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipInspectionTempl 实例对象
     * @return 对象列表
     */
    List<EquipInspectionTempl> selectEquipInspectionTemplList(EquipInspectionTempl equipInspectionTempl);


}

