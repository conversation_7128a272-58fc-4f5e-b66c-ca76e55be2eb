package com.boyo.eam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.eam.domain.ObjectInfoProperty;
import com.boyo.eam.mapper.ObjectInfoPropertyMapper;
import com.boyo.eam.service.IObjectInfoPropertyService;
import com.boyo.framework.annotation.Tenant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 物模型属性表(ObjectInfoProperty)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Service("objectInfoPropertyService")
@AllArgsConstructor
@Tenant
public class ObjectInfoPropertyServiceImpl extends ServiceImpl<ObjectInfoPropertyMapper, ObjectInfoProperty> implements IObjectInfoPropertyService {
    private final ObjectInfoPropertyMapper objectInfoPropertyMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<ObjectInfoProperty> selectObjectInfoPropertyList(ObjectInfoProperty objectInfoProperty) {
        return objectInfoPropertyMapper.selectObjectInfoPropertyList(objectInfoProperty);
    }

}
