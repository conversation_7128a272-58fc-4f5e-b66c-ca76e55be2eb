<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.TSupplierMapper">

    <resultMap type="com.boyo.master.vo.SupplierVO" id="TSupplierResult">
        <result property="id" column="id"/>
        <result property="supplierOpenid" column="supplier_openid"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="supplierAbbreviation" column="supplier_abbreviation"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="supplierType" column="supplier_type"/>
        <result property="supplierDesc" column="supplier_desc"/>
        <result property="supplierStatus" column="supplier_status"/>
        <result property="supplierImg" column="supplier_img"/>
        <result property="supplierContacts" column="supplier_contacts"/>
        <result property="supplierPhone" column="supplier_phone"/>
        <result property="supplierAddress" column="supplier_address"/>
        <result property="supplierFax" column="supplier_fax"/>
        <result property="createdAt" column="created_at"/>
        <result property="createdUser" column="created_user"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="updatedUser" column="updated_user"/>
        <result property="supplierTypeName" column="supplier_type_name"/>
    </resultMap>

    <sql id="selectTSupplierVo">
        select id,
               supplier_openid,
               supplier_name,
               supplier_abbreviation,
               supplier_code,
               supplier_type,
               supplier_desc,
               supplier_status,
               supplier_img,
               supplier_contacts,
               supplier_phone,
               supplier_address,
               supplier_fax,
               created_at,
               created_user,
               updated_at,
               updated_user
        from t_supplier
    </sql>

    <select id="selectTSupplierList" parameterType="com.boyo.master.domain.TSupplier" resultMap="TSupplierResult">
        select t1.*,t2.base_desc as supplier_type_name from t_supplier t1 LEFT JOIN t_base_dict t2 ON t1.supplier_type = t2.openid
        <where>
            <if test="supplierOpenid != null  and supplierOpenid != ''">
                and t1.supplier_openid = #{supplierOpenid}
            </if>
            <if test="supplierName != null  and supplierName != ''">
                and t1.supplier_name like concat('%', #{supplierName}, '%')
            </if>
            <if test="supplierAbbreviation != null  and supplierAbbreviation != ''">
                and t1.supplier_abbreviation = #{supplierAbbreviation}
            </if>
            <if test="supplierCode != null  and supplierCode != ''">
                and t1.supplier_code = #{supplierCode}
            </if>
            <if test="supplierType != null  and supplierType != ''">
                and t1.supplier_type = #{supplierType}
            </if>
            <if test="supplierDesc != null  and supplierDesc != ''">
                and t1.supplier_desc = #{supplierDesc}
            </if>
            <if test="supplierStatus != null  and supplierStatus != ''">
                and t1.supplier_status = #{supplierStatus}
            </if>
        </where>
    </select>
</mapper>
