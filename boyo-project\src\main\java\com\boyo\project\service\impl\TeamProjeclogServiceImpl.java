package com.boyo.project.service.impl;

import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.project.entity.TeamProjeclog;
import com.boyo.project.mapper.TeamProjeclogMapper;
import com.boyo.project.service.ITeamProjeclogService;
import java.util.List;

/**
 * 项目日志表(TeamProjeclog)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-17 19:34:44
 */
@Service("teamProjeclogService")
@AllArgsConstructor
@Tenant
public class TeamProjeclogServiceImpl extends ServiceImpl<TeamProjeclogMapper, TeamProjeclog> implements ITeamProjeclogService {
    private final TeamProjeclogMapper teamProjeclogMapper;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    public List<TeamProjeclog> selectTeamProjeclogList(TeamProjeclog teamProjeclog) {
        return teamProjeclogMapper.selectTeamProjeclogList(teamProjeclog);
    }

}
