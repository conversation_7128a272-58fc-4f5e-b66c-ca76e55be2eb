<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.project.mapper.TeamTaskFileMapper">

    <resultMap type="com.boyo.project.entity.TeamTaskFile" id="TeamTaskFileResult">
        <result property="id" column="id" />
        <result property="projectCode" column="project_code" />
        <result property="taskCode" column="task_code" />
        <result property="fileName" column="file_name" />
        <result property="fileUrl" column="file_url" />
        <result property="createTime" column="create_time" />
        <result property="createUser" column="create_user" />
        <result property="delFlag" column="del_flag" />
        <result property="delTime" column="del_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectTeamTaskFileList" parameterType="com.boyo.project.entity.TeamTaskFile" resultMap="TeamTaskFileResult">
        select
          id, project_code, task_code, file_name, file_url, create_time, create_user, del_flag, del_time
        from team_task_file
        <where>
            <if test="projectCode != null and projectCode != ''">
                and project_code = #{projectCode}
            </if>
            <if test="taskCode != null and taskCode != ''">
                and task_code = #{taskCode}
            </if>
            <if test="fileName != null and fileName != ''">
                and file_name = #{fileName}
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                and file_url = #{fileUrl}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUser != null and createUser != ''">
                and create_user = #{createUser}
            </if>
            <if test="delFlag != null and delFlag != ''">
                and del_flag = #{delFlag}
            </if>
            <if test="delTime != null">
                and del_time = #{delTime}
            </if>
        </where>
    </select>
</mapper>

