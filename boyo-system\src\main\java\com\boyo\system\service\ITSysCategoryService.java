package com.boyo.system.service;

import java.util.List;

import com.boyo.system.domain.TSysCategory;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 系统类别管理Service接口
 *
 * <AUTHOR>
 */
public interface ITSysCategoryService extends IService<TSysCategory> {
    /**
     * 根据条件查询查询系统类别管理列表
     *
     * @param tSysCategory 系统类别管理
     * @return 系统类别管理集合
     */
    List<TSysCategory> selectTSysCategoryList(TSysCategory tSysCategory);
}
