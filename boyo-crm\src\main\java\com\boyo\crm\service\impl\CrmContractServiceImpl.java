package com.boyo.crm.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.annotation.DataScope;
import com.boyo.common.core.text.Convert;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.crm.entity.*;
import com.boyo.crm.mapper.CrmContractProductMapper;
import com.boyo.crm.mapper.CrmCustomerMapper;
import com.boyo.crm.mapper.CrmReceivablesMapper;
import com.boyo.crm.util.ActionEnum;
import com.boyo.crm.util.ActionUtil;
import com.boyo.framework.annotation.Tenant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import com.boyo.crm.mapper.CrmContractMapper;
import com.boyo.crm.service.ICrmContractService;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 合同表(CrmContract)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-27 17:04:54
 */
@Service("crmContractService")
@AllArgsConstructor
public class CrmContractServiceImpl extends ServiceImpl<CrmContractMapper, CrmContract> implements ICrmContractService {
    private final CrmContractMapper crmContractMapper;
    private final CrmContractProductMapper contractProductMapper;
    private final CrmCustomerMapper customerMapper;
    private final CrmReceivablesMapper receivablesMapper;
    private final ActionUtil actionUtil;

    /**
     * 查询多条数据
     *
     * @return 对象列表
     */
    @Override
    @DataScope(columns = "owner_user_id,create_user_id")
    public List<CrmContract> selectCrmContractList(CrmContract crmContract) {
        return crmContractMapper.selectCrmContractList(crmContract);
    }

    @Override
    public void update(CrmContract crmContract) {
        super.updateById(crmContract);
    }

    @Override
    public boolean save(CrmContract entity) {
        List<CrmContractProduct> productList = entity.getProductList();
        if (productList == null || productList.size() == 0) {
            throw new CustomException("未添加商品信息，请先添加");
        }
        if (ObjectUtil.isNull(entity.getOwnerUserId())) {
            entity.setOwnerUserId(SecurityUtils.getUserId());
        }
        if (super.save(entity)) {
            for (CrmContractProduct product : productList) {
                product.setContractId(entity.getId());
                contractProductMapper.insert(product);
            }
            if(StrUtil.isEmpty(entity.getNum())){
                entity.setNum("HT-" + DateUtil.format(new Date(),"yyyyMMdd") + "-" + StrUtil.fillBefore(Convert.toStr(entity.getId()),'0',6));
                super.updateById(entity);
            }
        } else {
            return false;
        }
        actionUtil.editRecord(null,null, ActionEnum.CONTRACT,entity.getId(), null);
        return true;
    }

    @Override
    public boolean updateById(CrmContract entity) {
        List<CrmContractProduct> productList = entity.getProductList();
        if (productList == null || productList.size() == 0) {
            throw new CustomException("未添加商品信息，请先添加");
        }
        QueryWrapper<CrmContractProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("contract_id", entity.getId());
        contractProductMapper.delete(queryWrapper);
        for (CrmContractProduct product : productList) {
            product.setContractId(entity.getId());
            contractProductMapper.insert(product);
        }
        actionUtil.editRecord(super.getById(entity.getId()),entity,ActionEnum.CONTRACT,entity.getId(), CrmContract.class);
        return super.updateById(entity);
    }

    @Override
    public CrmContract getById(Serializable id) {
        CrmContract contract = super.getById(id);
        if (ObjectUtil.isNotNull(contract)) {
            CrmCustomer customer = customerMapper.selectById(contract.getCustomerId());
            contract.setCustomerName(customer.getCustomerName());
            QueryWrapper<CrmReceivables> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("contract_id", id);
            List<CrmReceivables> list = receivablesMapper.selectList(queryWrapper);
            if (list != null && list.size() > 0) {
                BigDecimal sum = new BigDecimal(0);
                for (CrmReceivables obj : list) {
                    sum = sum.add(obj.getMoney());
                }
                contract.setPaymentCollection(sum);
            }
        } else {
            return null;
        }
        return contract;
    }
}
