package com.boyo.eam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 点检表(EquipInspectionTempl)实体类
 *
 * <AUTHOR>
 * @since 2021-11-29 10:21:48
 */
@Data
@TableName(value = "equip_inspection_templ")
public class EquipInspectionTempl implements Serializable {
    private static final long serialVersionUID = 651260009844325979L;
        /**
    * 主键
    */
    @TableId
    private Integer id;

    /**
    * openid
    */
    @TableField(value="openid")
    private String openid;
    /**
    * 巡检编码
    */
    @TableField(value="code")
    private String code;
    /**
    * 设备openid：关联equip_ledger表的openid
    */
    @TableField(value="equip_ledger_openid")
    private String equipLedgerOpenid;
    /**
    * 点检人员：关联sys_user_id表的user_id
    */
    @TableField(value="sys_user_id")
    private Integer sysUserId;

    /**
     * 审核人：关联sys_user_id表的user_id
     */
    @TableField(value="reviewer")
    private Integer reviewer;
    /**
    * 分类：0点检，1巡检
    */
    @TableField(value="type")
    private String type;
    /**
     * 点检分类：0操作，1机械，2电器
     */
    @TableField(value="spot_type")
    private String spotType;
    /**
    * 产线：关联t_model_line表的openid
    */
    @TableField(value="line_openid")
    private String lineOpenid;
    /**
    * 任务状态: 0待处理，1进行中，2已完成，已延期
    */
    @TableField(value="state")
    private String state;
    /**
     * 周期
     */
    @TableField(value="cycle")
    private Integer cycle;
    /**
     * 单位: 0月,1月,2日
     */
    @TableField(value="cycle_unit")
    private String cycleUnit;

    @TableField(value="create_by")
    private String createBy;

    @TableField(value="create_time")
    private Date createTime;

    @TableField(value="update_by")
    private String updateBy;

    @TableField(value="update_time")
    private Date updateTime;


    /** 额外字段 */
    @TableField(exist = false)
    private List<EquipInspectionTemplItem> itemList; //项目列表

    @TableField(exist = false)
    private String equipCode;//设备编码
    @TableField(exist = false)
    private String equipName;//设备名称
    @TableField(exist = false)
    private String lineName;//产线名称
}
