package com.boyo.web.controller.haihui;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.core.text.Convert;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.domain.IotTsl;
import com.boyo.iot.domain.IotTslAttr;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.iot.service.IIotTslAttrService;
import com.boyo.iot.service.IIotTslService;
import com.boyo.iot.util.IoTDBUtil;
import com.boyo.master.domain.HaihuiSale;
import com.boyo.master.service.IHaihuiSaleService;
import com.boyo.web.lysso.controller.HttpRequest;
import com.boyo.web.lysso.controller.HttpResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2024-10-24
 */
@RestController
@RequestMapping("/haihui/sale")
public class HaihuiSaleController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(HaihuiSaleController.class);

    final String HAIHUI_URL = "http://pt.haihui.cn:8888/api/nmdxsservices/nmdxsservicesdate";
    final String NC_BASEURL = "http://************:8097/";
    final String NC_BASEURL_Test = "http://************:8097/";
    final String Authorization = "Basic bm1kdXNlcjpubWRwYXNzd29yZA==";
    final String Tenant = "2db4bfe2cbe14269b410e4747073ee47";
    private final RestTemplate restTemplate;


    @Autowired
    public HaihuiSaleController(RestTemplateBuilder restTemplateBuilder) {
        this.restTemplate = restTemplateBuilder.build();
    }

    @Autowired
    private IIotEquipmentService iotEquipmentService;
    @Autowired
    private IIotEquipmentService equipmentService;

    @Autowired
    private IIotTslService iotTslService;

    @Autowired
    private IIotTslAttrService iotTslAttrService;

    @Autowired
    private IoTDBUtil ioTDBUtil;


    @Autowired
    private IHaihuiSaleService haihuiSaleService;

    public HaihuiSaleController(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }


//    @PostMapping("/test")
//    public AjaxResult getInfo(@RequestBody HaihuiSale haihuiSale) {
//        if (haihuiSale == null || haihuiSale.getStartdate() == null || haihuiSale.getEnddate() == null) {
//            return AjaxResult.error("参数错误");
//        }
//
//        JSONObject jsonObject = new JSONObject();
//
//        try {
//            // 从海汇获取数据
//            HttpRequest request1 = HttpRequest.builder()
//                    .setUrl(HAIHUI_URL)
//                    .setMethod(HttpRequest.Method.POST)
//                    .addHeader("Authorization", Authorization)
//                    .addHeader("Content-Type", "application/json; charset=UTF-8")
//                    .addHeader("Accept", "*/*")
//                    .addHeader("Connection", "keep-alive")
//                    .addPostData("StartDate", haihuiSale.getStartdate())
//                    .addPostData("EndDate", haihuiSale.getEnddate());
//            HttpResponse response = request1.post();
//
//            jsonObject = JSONObject.parseObject(response.getText());
//            int successCount = saveSaleData(jsonObject);
//            jsonObject.put("successCount", successCount);
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return AjaxResult.success(jsonObject);
//
//    }

    @Scheduled(cron = "0 30 12,00 * * ?")//每天12：30和00：30执行各一次
    public void getSaleData() {

        Date newDate = new Date();
        final String end = DateUtil.format(newDate, "yyyy-MM-dd");
        final String start = DateUtil.format(DateUtil.offsetDay(newDate, -10), "yyyy-MM-dd");
        JSONObject jsonObject = new JSONObject();
        try {
            // 从海汇获取数据
            HttpRequest request1 = HttpRequest.builder()
                    .setUrl(HAIHUI_URL)
                    .setMethod(HttpRequest.Method.POST)
                    .addHeader("Authorization", Authorization)
                    .addHeader("Content-Type", "application/json; charset=UTF-8")
                    .addHeader("Accept", "*/*")
                    .addHeader("Connection", "keep-alive")
                    .addPostData("StartDate", start)
                    .addPostData("EndDate", end);
            HttpResponse response = request1.post();

            jsonObject = JSONObject.parseObject(response.getText());
            int saveSaleDataCount = saveSaleData(jsonObject);
            log.info(DateUtil.format(newDate, "yyyy-MM-dd HH:mm:ss") + " 定时获取海汇crm数据，从" + start + "-" + end + " 插入 " + saveSaleDataCount);
        } catch (Exception e) {
            log.info(DateUtil.format(newDate, "yyyy-MM-dd HH:mm:ss") + " 定时获取海汇crm数据失败!!!!!，从" + start + "-" + end);
            e.printStackTrace();
        }

    }

    public void getSaleData(String startdate, String enddate) {
        Date newDate = new Date();
        if (StringUtils.isAnyBlank(startdate, enddate)) {

            enddate = DateUtil.format(newDate, "yyyy-MM-dd");
            startdate = DateUtil.format(DateUtil.offsetDay(newDate, -10), "yyyy-MM-dd");
        }
        JSONObject jsonObject = new JSONObject();
        try {
            // 从海汇获取数据
            HttpRequest request1 = HttpRequest.builder()
                    .setUrl(HAIHUI_URL)
                    .setMethod(HttpRequest.Method.POST)
                    .addHeader("Authorization", Authorization)
                    .addHeader("Content-Type", "application/json; charset=UTF-8")
                    .addHeader("Accept", "*/*")
                    .addHeader("Connection", "keep-alive")
                    .addPostData("StartDate", startdate)
                    .addPostData("EndDate", enddate);
            HttpResponse response = request1.post();

            jsonObject = JSONObject.parseObject(response.getText());
            int saveSaleDataCount = saveSaleData(jsonObject);
            log.info(DateUtil.format(newDate, "yyyy-MM-dd HH:mm:ss") + " 主动获取海汇crm数据，从" + startdate + "-" + enddate + " 插入 " + saveSaleDataCount);
        } catch (Exception e) {
            log.info(DateUtil.format(newDate, "yyyy-MM-dd HH:mm:ss") + " 主动获取海汇crm数据失败!!!!!，从" + startdate + "-" + enddate);
            e.printStackTrace();
        }

    }


    /**
     * 保存销售数据
     * 该方法解析JSON对象，提取销售相关信息，并将这些信息保存或更新在数据库中
     * 如果服务器响应的结果为"ok"，则进一步处理数据，否则不进行任何操作
     *
     * @param jsonObject 包含销售数据的JSON对象
     */
    private int saveSaleData(JSONObject jsonObject) {
        int ans = 0;
        if (jsonObject.get("result").equals("ok")) {
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            List<HaihuiSale> datasList = new ArrayList<>();

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject1 = jsonArray.getJSONObject(i);
                HaihuiSale data = new HaihuiSale();
                // 设置销售数据对象的各个字段
                data.setHtNo(jsonObject1.getString("HTNO"));
                data.setHtlxzt(jsonObject1.getString("HTLXZT"));
                data.setCustName(jsonObject1.getString("CUSTNAME"));
                data.setStartdate(jsonObject1.getString("STARTDATE"));
                data.setEnddate(jsonObject1.getString("ENDDATE"));
                data.setPayment(jsonObject1.getString("PAYMENT"));
                data.setAmount(jsonObject1.getDouble("AMOUNT"));
                data.setPayament(jsonObject1.getDouble("PAYAMOUNT"));
                data.setNopayment(jsonObject1.getDouble("NOPAYAMOUNT"));
                data.setSignatory(jsonObject1.getString("SIGNATORY"));
                data.setState(jsonObject1.getString("STATE"));

                datasList.add(data);
            }
            // 遍历销售数据列表，进行数据库的插入或更新操作
            for (HaihuiSale data : datasList) {
                String htNo = data.getHtNo();
                HaihuiSale sale = haihuiSaleService.selectHaihuiSaleByHtNo(htNo);
                // 判断数据是否已存在于数据库中
                if (sale != null) {
                    // 如果存在，则更新数据
                    haihuiSaleService.updateHaihuiSale(data);
                } else {
                    data.setSaveTime(new Date());
                    // 如果不存在，则插入数据，并检查插入是否成功
                    final int i = haihuiSaleService.insertHaihuiSale(data);
                    ans += i;
                    // 如果插入失败，记录错误日志
                    if (i == 0) {
                        logger.error("数据插入失败" + data);
                    }
                }
            }
        }
        return ans;
    }


    /**
     * 查询【请填写功能名称】列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HaihuiSale haihuiSale) {

        if (haihuiSale != null) {
            //同步到本地数据库
            getSaleData(haihuiSale.getStartdate(), haihuiSale.getEnddate());

            if (haihuiSale.getStartdate() != null) {
                final String startdate = haihuiSale.getStartdate();
                final DateTime dateTime = DateUtil.parseDate(startdate);
                final DateTime dateTime1 = DateUtil.beginOfDay(dateTime);
                final String s = dateTime1.toString();
                haihuiSale.setStartdate(s);
            }
            if (haihuiSale.getEnddate() != null) {
                final String startdate = haihuiSale.getEnddate();
                final DateTime dateTime = DateUtil.parseDate(startdate);
                final DateTime dateTime1 = DateUtil.endOfDay(dateTime);
                final String s = dateTime1.toString();
                haihuiSale.setEnddate(s);
            }
        }
        startPage();
        List<HaihuiSale> list = haihuiSaleService.selectHaihuiSaleList(haihuiSale);
        return getDataTable(list);
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @GetMapping(value = "/{htNo}")
    public AjaxResult getInfo(@PathVariable("htNo") String htNo) {
        return AjaxResult.success(haihuiSaleService.selectHaihuiSaleByHtNo(htNo));
    }

    /**
     * 新增【请填写功能名称】
     */
//    @PostMapping
//    public AjaxResult add(@RequestBody HaihuiSale haihuiSale) {
//        return toAjax(haihuiSaleService.insertHaihuiSale(haihuiSale));
//    }

    /**
     * 修改【请填写功能名称】
     */
//    @PutMapping
//    public AjaxResult edit(@RequestBody HaihuiSale haihuiSale) {
//        return toAjax(haihuiSaleService.updateHaihuiSale(haihuiSale));
//    }

    /**
     * 删除【请填写功能名称】
     */
//    @DeleteMapping("/{htNos}")
//    public AjaxResult remove(@PathVariable String[] htNos) {
//        return toAjax(haihuiSaleService.deleteHaihuiSaleByHtNos(htNos));
//    }


    //获取当前设备信息列表
    @GetMapping("bank/equipment/equipmentInfo")
    public TableDataInfo equipmentInfo(IotEquipment iotEquipment) {
        startPage();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
        return getDataTable(list);
    }

    //获取当前设备信息列表和实时数据
    @GetMapping("bank/equipment/currentInfo")
    public TableDataInfo listOnly(IotEquipment iotEquipment) {
        startPage();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
        if (list != null && list.size() > 0) {
            for (IotEquipment temp : list) {
                for (IotTslAttr attr : temp.getAttrList()) {
                    if ((attr.getAttrType().equalsIgnoreCase("Double") || attr.getAttrType().equalsIgnoreCase("Integer")) && StrUtil.isNotEmpty(attr.getLastVal())) {
                        attr.setLastVal(Convert.toStr(NumberUtil.round(Convert.toDouble(attr.getLastVal()) * attr.getAttrMultiple(), 2)));
                    }
                    if ("工作状态".equals(attr.getAttrName()) && attr.getLastUpdateTime() != null) {
                        final Date lastUpdateTime = attr.getLastUpdateTime();
                        final long between = DateUtil.between(lastUpdateTime, new Date(), DateUnit.MINUTE);
                        if (between > 5L) {
                            attr.setLastVal("0");
                        }
                    }
                }
            }
        }
        return getDataTable(list);
    }


    /**
     * 获取设备数据历史记录
     *
     * @param device 设备代码
     * @param start  开始日期时间字符串，如果为空，则默认为当前时间的前一小时
     * @param end    结束日期时间字符串，如果为空，则默认为当前时间
     * @return 返回包含设备属性和历史数据的AjaxResult对象
     */
    @GetMapping(value = "bank/equipment/getDataHistory")
    public AjaxResult getDataHistoryV2(String device, String start, String end) {
        // 初始化开始和结束日期时间
        Date s, e;
        // 如果开始时间为空，则设置为当前时间的前一小时
        if (StrUtil.isEmpty(start)) {
            s = DateUtil.offset(new Date(), DateField.HOUR_OF_DAY, -1);
        } else {
            s = DateUtil.parse(start);
        }
        // 如果结束时间为空，则设置为当前时间
        if (StrUtil.isEmpty(end)) {
            e = new Date();
        } else {
            e = DateUtil.parse(end);
        }
        try {
            JSONObject result = new JSONObject();
            // 根据设备代码获取设备信息
            IotEquipment equipment = equipmentService.getEquipmentByCode(device);
            if (equipment != null) {
                // 获取设备详细信息中的属性列表
                List<IotTslAttr> attrs = equipmentService.getEquipmentDetail(Convert.toInt(equipment.getId())).getAttrList();
                result.put("attrs", attrs);
                // 初始化属性代码列表
                List<String> codes = new ArrayList<>();
                for (IotTslAttr attr : attrs) {
                    codes.add(attr.getAttrCode());
                }
                // 从IoT数据库中获取设备的历史数据
                JSONArray list = ioTDBUtil.listDataHistoryV2(Tenant, device, codes, DateUtil.formatDateTime(s), DateUtil.formatDateTime(e));

                // 遍历设备属性，对特定类型的数据进行处理
                for (IotTslAttr attr : attrs) {
                    if (attr != null && (attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("integer"))) {
                        for (int i = 0; i < list.size(); i++) {
                            JSONObject temp = list.getJSONObject(i);
                            for (String tag : temp.keySet()) {
                                if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                                    try {
                                        // 对数据进行乘以属性倍数的处理，并保留两位小数
                                        temp.put(tag, NumberUtil.round(Convert.toDouble(temp.getString(tag)) * attr.getAttrMultiple(), 2));
                                        list.set(i, temp);
                                        break;
                                    } catch (Exception e1) {
                                        // 异常处理
                                    }
                                }
                            }
                        }
                    }
                }
                result.put("list", list);
            }
            // 返回成功结果
            return AjaxResult.success(result);
        } catch (IoTDBConnectionException ex) {
            // IoT数据库连接异常处理
            ex.printStackTrace();
        } catch (StatementExecutionException ex) {
            // SQL语句执行异常处理
            ex.printStackTrace();
        }
        // 返回错误结果
        return AjaxResult.error();
    }

    @GetMapping("bank/iotTsl/list")
    public TableDataInfo list(IotTsl iotTsl) {
        startPage();
        List<IotTsl> list = iotTslService.selectIotTslList(iotTsl);
        return getDataTable(list);
    }

    @GetMapping("bank/attr/list")
    public AjaxResult listAttr(Long tslId) {
        return AjaxResult.success(iotTslAttrService.listAttr(tslId));
    }

    //采购订单
    @GetMapping("nc57/index")
    public AjaxResult purchaseOrder(String url) {
        url = NC_BASEURL + url;
        try {
            HttpRequest request1 = HttpRequest.builder()
                    .setUrl(url)
                    .setMethod(HttpRequest.Method.POST)
                    .addHeader("Content-Type", "application/json; charset=UTF-8")
                    .addHeader("Accept", "*/*")
                    .addHeader("Connection", "keep-alive");
            HttpResponse response = request1.post();
            JSONObject jsonObject = JSONObject.parseObject(response.getText());

            return AjaxResult.success(jsonObject);

        } catch (Exception e) {
            AjaxResult.error(e.getMessage());
        }
        return null;
    }


    @PostMapping("/ncData")
    public ResponseEntity<?> postData(@RequestBody Map<String, Object> requestBody) {

        String url = NC_BASEURL + requestBody.get("url");

        // 构造请求头（如需要）
        HttpHeaders headers = new HttpHeaders();
        // headers.set(...); // 根据需要设置请求头

        // 使用HttpEntity封装请求体和请求头
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

        try {
            // 发送POST请求并获取响应
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
            // 将内网服务的响应直接返回
            return ResponseEntity.status(response.getStatusCode()).body(response.getBody());
        } catch (Exception e) {
            // 捕获异常并处理，例如记录日志或返回错误响应
            return ResponseEntity.status(500).body("Internal Server Error: " + e.getMessage());
        }
    }
    @PostMapping("/ncData/test")
    public ResponseEntity<?> postData3(@RequestBody Map<String, Object> requestBody) {

        String url = NC_BASEURL_Test + requestBody.get("url");

        // 构造请求头（如需要）
        HttpHeaders headers = new HttpHeaders();
        // headers.set(...); // 根据需要设置请求头

        // 使用HttpEntity封装请求体和请求头
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

        try {
            // 发送POST请求并获取响应
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
            // 将内网服务的响应直接返回
            return ResponseEntity.status(response.getStatusCode()).body(response.getBody());
        } catch (Exception e) {
            // 捕获异常并处理，例如记录日志或返回错误响应
            return ResponseEntity.status(500).body("Internal Server Error: " + e.getMessage());
        }
    }


}