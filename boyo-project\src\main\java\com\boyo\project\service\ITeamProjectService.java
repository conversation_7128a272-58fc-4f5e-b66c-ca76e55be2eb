package com.boyo.project.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.project.entity.TeamProject;
import java.util.List;

/**
 * 项目表(TeamProject)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-09 11:07:52
 */
public interface ITeamProjectService extends IService<TeamProject> {

    /**
     * 查询多条数据
     *
     * @param teamProject 对象信息
     * @return 对象列表
     */
    List<TeamProject> selectTeamProjectList(TeamProject teamProject);

    List<TeamProject> selectAllProjectList(TeamProject teamProject);


}
