package com.boyo.master.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boyo.common.core.domain.GenBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 主数据-物料
 * 表名 t_material
 *
 * <AUTHOR>
 */
@ApiModel("主数据-物料")
@Data
@TableName("t_material")
public class TMaterial extends GenBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId
    private Long id;
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    @TableField(value = "materiel_openid")
    private String materielOpenid;
    /**
     * 物料名称
     */
    @ApiModelProperty("物料名称")
    @TableField(value = "materiel_name")
    private String materielName;
    /**
     * 物料简称
     */
    @ApiModelProperty("物料简称")
    @TableField(value = "materiel_abbreviation")
    private String materielAbbreviation;
    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @TableField(value = "materiel_code")
    private String materielCode;
    /**
     * 物料类型
     */
    @ApiModelProperty("物料类型")
    @TableField(value = "materiel_type")
    private String materielType;
    /**
     * 物料描述
     */
    @ApiModelProperty("物料描述")
    @TableField(value = "materiel_desc")
    private String materielDesc;
    /**
     * 计量单位
     */
    @ApiModelProperty("计量单位")
    @TableField(value = "materiel_unit")
    private String materielUnit;
    /**
     * 物料类别
     */
    @ApiModelProperty("物料类别")
    @TableField(value = "materiel_abc")
    private String materielAbc;
    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    @TableField(value = "materiel_norms")
    private String materielNorms;
    /**
     * 物料来源 生产、采购、外协
     */
    @ApiModelProperty("物料来源 生产、采购、外协")
    @TableField(value = "materiel_source")
    private String materielSource;
    /**
     * 供应商
     */
    @ApiModelProperty("供应商")
    @TableField(value = "materiel_supplier")
    private String materielSupplier;
    /**
     * 状态 1启用 0停用
     */
    @ApiModelProperty("状态 1启用 0停用")
    @TableField(value = "materiel_status")
    private String materielStatus;
    /**
     * 物料图片
     */
    @ApiModelProperty("物料图片")
    @TableField(value = "materiel_img")
    private String materielImg;
    /**
     * 次要单位
     */
    @ApiModelProperty("次要单位")
    @TableField(value = "materiel_sunit")
    private String materielSunit;
    /**
     * 净重
     */
    @ApiModelProperty("净重")
    @TableField(value = "materiel_net_weight")
    private String materielNetWeight;
    /**
     * 物料管理方式，1-批次，2-关键件
     */
    @ApiModelProperty("物料管理方式，1-批次，2-关键件")
    @TableField(value = "materiel_key_parts")
    private String materielKeyParts;
    /**
     * 毛重
     */
    @ApiModelProperty("毛重")
    @TableField(value = "materiel_gross_weight")
    private String materielGrossWeight;
    /**
     * 生产标准值
     */
    @ApiModelProperty("生产标准值")
    @TableField(value = "materiel_mpq")
    private String materielMpq;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "created_at")
    private Date createdAt;
    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    @TableField(value = "created_user")
    private String createdUser;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(value = "updated_at")
    private Date updatedAt;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "updated_user")
    private String updatedUser;
    /**
     * 所属仓库编码
     */
    @ApiModelProperty("所属仓库编码")
    @TableField(value = "materiel_warehouse_code")
    private String materielWarehouseCode;
    /**
     * 所属仓库名称
     */
    @ApiModelProperty("所属仓库名称")
    @TableField(value = "materiel_warehouse_openid")
    private String materielWarehouseOpenid;

    @ApiModelProperty("最小库存")
    @TableField(value = "min_stock")
    private BigDecimal minStock;

    @ApiModelProperty("最小库存警戒值")
    @TableField(value = "min_warning")
    private BigDecimal minWarning;

    @ApiModelProperty("最大库存")
    @TableField(value = "max_stock")
    private BigDecimal maxStock;

    @ApiModelProperty("最大库存警戒值")
    @TableField(value = "max_warning")
    private BigDecimal maxWarning;
    @ApiModelProperty("最短存储时间（天）")
    @TableField(value = "min_time")
    private Integer minTime;
    @ApiModelProperty("最长存储时间（天）")
    @TableField(value = "max_time")
    private Integer maxTime;

    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remark;
}
