<template>
  <a-modal width="30%" :maskClosable="false" :visible="open" @cancel="cancel">
    <template #title>
      <a-icon type="security-scan" />
      {{ formTitle }}
    </template>
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      layout="horizontal"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
           <a-form-model-item label="工单类型 如IoT">
        <a-input
          :size="formSize"
          v-model="form.type"
          :placeholder="$t('app.global.please.input') + '工单类型 如IoT'"
        />
      </a-form-model-item>
           <a-form-model-item label="工单编号">
        <a-input
          :size="formSize"
          v-model="form.num"
          :placeholder="$t('app.global.please.input') + '工单编号'"
        />
      </a-form-model-item>
           <a-form-model-item label="对应故障">
        <a-input
          :size="formSize"
          v-model="form.faultId"
          :placeholder="$t('app.global.please.input') + '对应故障'"
        />
      </a-form-model-item>
           <a-form-model-item label="工单描述">
        <a-input
          :size="formSize"
          v-model="form.orderMsg"
          :placeholder="$t('app.global.please.input') + '工单描述'"
        />
      </a-form-model-item>
           <a-form-model-item label="负责人id">
        <a-input
          :size="formSize"
          v-model="form.ownerUserId"
          :placeholder="$t('app.global.please.input') + '负责人id'"
        />
      </a-form-model-item>
           <a-form-model-item label="接收时间">
        <a-input
          :size="formSize"
          v-model="form.receiveTime"
          :placeholder="$t('app.global.please.input') + '接收时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="完成时间">
        <a-input
          :size="formSize"
          v-model="form.completeTime"
          :placeholder="$t('app.global.please.input') + '完成时间'"
        />
      </a-form-model-item>
           <a-form-model-item label="工单完工内容">
        <a-input
          :size="formSize"
          v-model="form.remark"
          :placeholder="$t('app.global.please.input') + '工单完工内容'"
        />
      </a-form-model-item>
        </a-form-model>
    <template #footer>
      <a-space>
        <a-button :size="formSize" icon="close" type="danger" @click="cancel">
          {{ $t('app.global.close') }}
        </a-button>
        <a-button :size="formSize" icon="save" type="primary" @click="submitForm">
          {{ $t('app.global.save') }}
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script>
import { addWorkOrder, updateWorkOrder, getWorkOrder } from '@/api/workOrder'
export default {
  data() {
    return {
      //新增或修改
      updateState: false,
      formTitle: '系统工单',
      // 表单参数
      form: {
           id: '',
           type: '',
           num: '',
           faultId: '',
           orderMsg: '',
           ownerUserId: '',
           receiveTime: '',
           completeTime: '',
           remark: '',
             },
      open: false,
      rules: {},
    }
  },
  created() {
    this.rules = {
    }
  },
  methods: {
    /**
     * 新增按钮操作
     * */
    handleAdd() {
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.add') + '系统工单'
      this.form = {}
      this.updateState = false
    },
    /**
     * 修改按钮操作
     * */
    async handleUpdate($event, id) {
      $event.stopPropagation()
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.edit') + '系统工单'
      const response = await getWorkOrder(id)
      this.form = response.data
    },
    /**
     * 提交按钮
     * */
    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
            if (this.form.id) {
                await updateWorkOrder(this.form)
                this.$alert.success(this.$t('app.global.edit.success'))
                this.open = false
                this.$emit('ok')
            } else {
                await addWorkOrder(this.form)
                this.$alert.success(this.$t('app.global.add.success'))
                this.open = false
                this.$emit('ok')
            }
        } else {
            return false
        }
        })
    },
    /**
     * 取消按钮
     * */
    cancel() {
      this.open = false
      this.reset()
    },
    /**
     * 表单重置
     * */
    reset() {
      this.form = {}
      if (this.$refs.form) {
        this.$refs.form.resetFields()}
      }
    },
}
</script>

