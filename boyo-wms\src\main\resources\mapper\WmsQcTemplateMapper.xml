<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.wms.mapper.WmsQcTemplateMapper">

    <resultMap type="com.boyo.wms.entity.WmsQcTemplate" id="WmsQcTemplateResult">
        <result property="id" column="id" />
        <result property="templateCode" column="template_code" />
        <result property="templateName" column="template_name" />
        <result property="status" column="status" />
        <result property="createTime" column="create_time" />
        <result property="createUser" column="create_user" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectWmsQcTemplateList" parameterType="com.boyo.wms.entity.WmsQcTemplate" resultMap="WmsQcTemplateResult">
        select
          id, template_code, template_name, status, create_time, create_user
        from t_wms_qc_template
        <where>
            <if test="templateCode != null and templateCode != ''">
                and template_code = #{templateCode}
            </if>
            <if test="templateName != null and templateName != ''">
                and template_name  like concat('%',#{templateName},'%')
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUser != null and createUser != ''">
                and create_user = #{createUser}
            </if>
        </where>
    </select>
</mapper>

