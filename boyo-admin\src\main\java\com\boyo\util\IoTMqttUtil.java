package com.boyo.util;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.redis.RedisCache;
import com.boyo.common.core.text.Convert;
import com.boyo.iot.entity.IorealData;
import com.boyo.iot.mapper.IorealDataMapper;
import com.boyo.iot.service.IIotEquipmentPropService;
import com.boyo.iot.util.IoTDBUtil;
import com.boyo.processor.TenantProcessor;
import com.boyo.system.domain.TSysEnterprise;
import com.boyo.system.service.ITSysEnterpriseService;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class IoTMqttUtil implements MqttCallback {

    private static ITSysEnterpriseService sysEnterpriseService;
    public static TenantProcessor tenantProcessor;
    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    public void init() {
        sysEnterpriseService = applicationContext.getBean(ITSysEnterpriseService.class);
        tenantProcessor = applicationContext.getBean(TenantProcessor.class);
    }
    @Autowired
    private IorealDataMapper iorealDataMapper;

    @Autowired
    private IoTDBUtil ioTDBUtil;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private IIotEquipmentPropService equipmentPropService;
    @Value("${mqtt.url}")
    public String HOST;
    @Value("${mqtt.topic}")
    public String TOPIC;
    @Value("${mqtt.username}")
    private String name;
    @Value("${mqtt.password}")
    private String passWord;

    private MqttClient client;
    private MqttConnectOptions options;
    String clientid = String.valueOf(System.currentTimeMillis());

    @PostConstruct
    public void connect() {
        try {
            // host为主机名，clientid即连接MQTT的客户端ID，一般以唯一标识符表示，MemoryPersistence设置clientid的保存形式，默认为以内存保存
            client = new MqttClient(HOST, clientid, new MemoryPersistence());
            // MQTT的连接设置
            options = new MqttConnectOptions();
            // 设置是否清空session,这里如果设置为false表示服务器会保留客户端的连接记录，这里设置为true表示每次连接到服务器都以新的身份连接
            options.setCleanSession(false);
            // 设置连接的用户名
            options.setUserName(name);
            // 设置连接的密码
            options.setPassword(passWord.toCharArray());
            // 设置超时时间 单位为秒
            options.setConnectionTimeout(10);
            // 设置会话心跳时间 单位为秒 服务器会每隔1.5*20秒的时间向客户端发送个消息判断客户端是否在线，但这个方法并没有重连的机制
            options.setKeepAliveInterval(3600);
            log.info("mqtt用户名:" + name + "  password:" + passWord);
            // 设置回调
            client.setCallback(this);
            client.connect(options);
            //订阅消息
            int[] Qos = {1, 1, 1, 1, 1, 1, 1};
            String[] topic1 = TOPIC.split(",");
            client.subscribe(topic1, Qos);
            log.info("连接成功");
        } catch (Exception e) {
            e.printStackTrace();
            log.info("ReportMqtt客户端连接异常，异常信息：" + e);
        }

    }

    @Override
    public void connectionLost(Throwable throwable) {
        try {
            throwable.printStackTrace();
            log.info("程序出现异常，DReportMqtt断线！正在重新连接...:");
            client.close();
            this.connect();
            log.info("ReportMqtt重新连接成功");
        } catch (MqttException e) {
            log.info(e.getMessage());
        }
    }

    ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Override
    public void messageArrived(String topic, MqttMessage message) {
        // 在线程池中执行代码
        executorService.execute(() -> {
            try {
                if (topic.startsWith("room")) {
                    executeRoom(topic, message);
                } else if (topic.startsWith("mengdou")) {
                    executeMengdou(topic, message);
                } else if (topic.startsWith("cnc")) {
                    executeCNC(topic, message);
                } else if (topic.startsWith("ali")) {
                    executeAli(topic, message);
                } else if (topic.startsWith("qimiyi")) {
                    executeQimiyi(topic, message);
                } else if (topic.startsWith("transpond")) {
                    executeLemonTranspond(topic, message);
                } else if (topic.startsWith("ningmeng")) {
                    executeLemon(topic, message);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    private void executeCNC(String topic, MqttMessage message) {
        String[] topics = topic.split("/");
        String enterpriseCode = topics[1];
        String deviceCode = "";
        if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
            //          手动切换数据源
            DynamicDataSourceContextHolder.push(enterpriseCode);
            try {
                JSONObject msg = JSONObject.parseObject(new String(message.getPayload()));
                for (Map.Entry<String, Object> entry : msg.entrySet()) {
                    deviceCode = entry.getKey();
                }
                List<IorealData> tempList = new ArrayList<>();
                List<IorealData> realList = new ArrayList<>();
                JSONObject data = msg.getJSONArray(deviceCode).getJSONObject(0).getJSONObject("values");
                StringBuffer str = new StringBuffer("");
                if (data.containsKey("Count_count")) {
                    data.put("Number", data.get("Count_count"));
                }
                if (data.containsKey("CNCStatus_cncStatus")) {
                    if (data.getString("CNCStatus_cncStatus").contains("MANUAL")) {
                        data.put("Work_Status", "2");
                        data.put("Product_Status", "1");
                    } else if (data.getString("CNCStatus_cncStatus").contains("AUTO")) {
                        data.put("Work_Status", "1");
                        data.put("Product_Status", "1");
                    } else {
                        data.put("Work_Status", "0");
                        data.put("Product_Status", "0");
                    }
                }
                for (Map.Entry<String, Object> entry : data.entrySet()) {
                    IorealData obj = new IorealData();
                    String key = entry.getKey();
                    obj.setTag(key);
                    obj.setDeviceCode(deviceCode);
                    try {
                        final double value = (double) entry.getValue();
                        if (value < 0) {
                            continue;
                        }
                        obj.setVal(value + "");
                    } catch (Exception e) {
                        obj.setVal(Convert.toStr(entry.getValue()));
                    }
                    obj.setKey(deviceCode + "-" + key);
                    str.setLength(0);
                    str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(entry.getKey());
                    Object temp = redisCache.getCacheObject(str.toString());
                    if (ObjectUtil.isNotNull(temp) && !key.contains("OEE")) {
                        if (!Convert.toStr(temp).equals(obj.getVal())) {
//                                        数据发生变化
                            realList.add(obj);
                        }
                    } else {
                        realList.add(obj);
                        redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                    }
                    tempList.add(obj);
                }
                if (tempList.size() > 0) {
                    iorealDataMapper.saveOrUpdate(tempList);
                    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                }
                if (realList.size() > 0) {
                    ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
//                                    iorealDataMapper.saveOrUpdate(realList);
//                                    equipmentPropService.executeEquipmentFaultProp(deviceCode,realList);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void executeLemon(String topic, MqttMessage message) throws Exception {
        String[] topics = topic.split("/");
//        System.out.println(new String(message.getPayload()));
        if (topics.length == 2) {
            String enterpriseCode = topics[1];
            if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
                //          手动切换数据源
                DynamicDataSourceContextHolder.push(enterpriseCode);
                try {
                    JSONObject msg = JSONObject.parseObject(new String(message.getPayload()));
                    if (msg.containsKey("Body")) {
                        String entityNo = msg.getString("EntityNo");
                        JSONArray array = msg.getJSONArray("Body");
                        if (array != null && array.size() > 0) {
                            for (int i = 0; i < array.size(); i++) {
                                List<IorealData> realList = new ArrayList<>();
                                List<IorealData> tempList = new ArrayList<>();
                                StringBuffer str = new StringBuffer("");
                                JSONObject object = array.getJSONObject(i);
                                String deviceCode = entityNo + "_" + object.getString("DeviceId");
                                for (Map.Entry entry : object.entrySet()) {
                                    if (entry.getKey().equals("DeviceId")) {
                                        continue;
                                    }
                                    IorealData obj = new IorealData();
                                    obj.setTag(Convert.toStr(entry.getKey()));
                                    obj.setDeviceCode(deviceCode);
                                    try {
                                        final double value = (double) entry.getValue();
                                        if (value < 0) {
                                            continue;
                                        }
                                        obj.setVal(value + "");
                                    } catch (Exception e) {
                                        obj.setVal(Convert.toStr(entry.getValue()));
                                    }
                                    obj.setKey(deviceCode + "-" + Convert.toStr(entry.getKey()));
                                    str.setLength(0);
                                    str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(Convert.toStr(entry.getKey()));
                                    Object temp = redisCache.getCacheObject(str.toString());
                                    if (ObjectUtil.isNotNull(temp)) {
                                        if (!Convert.toStr(temp).equals(obj.getVal())) {
//                                        数据发生变化
                                            realList.add(obj);
                                            redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                                        }
                                    } else {
                                        realList.add(obj);
                                        redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                                    }
                                    tempList.add(obj);
                                }
                                if (tempList.size() > 0) {
                                    iorealDataMapper.saveOrUpdate(tempList);
                                    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                                }
                                if (realList.size() > 0) {
                                    ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
//                                    iorealDataMapper.saveOrUpdate(realList);
//                                    equipmentPropService.executeEquipmentFaultProp(deviceCode,realList);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info(e.getMessage());
                } finally {
                    DynamicDataSourceContextHolder.poll();
                }
            }
        }
    }

    private void executeLemonTranspond(String topic, MqttMessage message) throws Exception {
        String[] topics = topic.split("/");
        if (topics.length == 2) {
            String enterpriseCode = topics[1];
            if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
                //          手动切换数据源
                DynamicDataSourceContextHolder.push(enterpriseCode);
                try {
                    JSONObject msg = JSONObject.parseObject(new String(message.getPayload()));
                    if (msg.containsKey("Body")) {
                        String entityNo = msg.getString("DeviceNo");
                        JSONArray array = msg.getJSONArray("Body");
                        if (array != null && array.size() > 0) {
                            for (int i = 0; i < array.size(); i++) {
                                List<IorealData> realList = new ArrayList<>();
                                List<IorealData> tempList = new ArrayList<>();
                                StringBuffer str = new StringBuffer("");
                                JSONObject object = array.getJSONObject(i);
                                String deviceCode = entityNo + "_" + object.getString("Module");
                                for (Map.Entry entry : object.entrySet()) {
                                    IorealData obj = new IorealData();
                                    obj.setTag(Convert.toStr(entry.getKey()));
                                    obj.setDeviceCode(deviceCode);
                                    try {
                                        final double value = (double) entry.getValue();
                                        if (value < 0) {
                                            continue;
                                        }
                                        obj.setVal(value + "");
                                    } catch (Exception e) {
                                        obj.setVal(Convert.toStr(entry.getValue()));
                                    }
                                    obj.setKey(deviceCode + "-" + Convert.toStr(entry.getKey()));
                                    str.setLength(0);
                                    str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(Convert.toStr(entry.getKey()));
                                    Object temp = redisCache.getCacheObject(str.toString());
                                    if (ObjectUtil.isNotNull(temp)) {
                                        if (!Convert.toStr(temp).equals(obj.getVal())) {
//                                        数据发生变化
                                            realList.add(obj);
                                            redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                                        }
                                    } else {
                                        realList.add(obj);
                                        redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                                    }
                                    tempList.add(obj);
                                }
                                if (tempList.size() > 0) {
                                    iorealDataMapper.saveOrUpdate(tempList);
                                    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                                }
                                if (realList.size() > 0) {
                                    ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info(e.getMessage());
                } finally {
                    DynamicDataSourceContextHolder.poll();
                }
            }
        }
    }

    private void executeRoom(String topic, MqttMessage message) throws Exception {
        String[] topics = topic.split("/");
        if (topics.length == 2) {
            String enterpriseCode = topics[1];
            if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
                //          手动切换数据源
                DynamicDataSourceContextHolder.push(enterpriseCode);
                try {
                    JSONObject msg = JSONObject.parseObject(new String(message.getPayload()));
                    JSONArray array = msg.getJSONArray("params");
                    if (array != null && array.size() > 0) {
                        for (int i = 0; i < array.size(); i++) {
                            List<IorealData> realList = new ArrayList<>();
                            List<IorealData> tempList = new ArrayList<>();
                            StringBuffer str = new StringBuffer("");
                            JSONObject object = array.getJSONObject(i);
                            JSONArray propertiesArray = object.getJSONArray("properties");
                            String deviceCode = array.getJSONObject(i).getString("clientID");
                            if (propertiesArray != null && propertiesArray.size() > 0) {
                                for (int i1 = 0; i1 < propertiesArray.size(); i1++) {
                                    propertiesArray.getJSONObject(i1).getString("name");
                                    IorealData obj = new IorealData();
                                    obj.setTag(Convert.toStr(propertiesArray.getJSONObject(i1).getString("name")));
                                    obj.setDeviceCode(deviceCode);
                                    obj.setVal(Convert.toStr(propertiesArray.getJSONObject(i1).getString("value")));
                                    obj.setKey(deviceCode + "-" + Convert.toStr(propertiesArray.getJSONObject(i1).getString("name")));
                                    str.setLength(0);
                                    str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(Convert.toStr(propertiesArray.getJSONObject(i1).getString("name")));
                                    Object temp = redisCache.getCacheObject(str.toString());
                                    if (ObjectUtil.isNotNull(temp)) {
                                        if (!Convert.toStr(temp).equals(obj.getVal())) {
//                                        数据发生变化
                                            realList.add(obj);
                                            redisCache.setCacheObject(str.toString(), Convert.toStr(propertiesArray.getJSONObject(i1).getString("name")), 60, TimeUnit.SECONDS);
                                        }
                                    } else {
                                        realList.add(obj);
                                        redisCache.setCacheObject(str.toString(), Convert.toStr(propertiesArray.getJSONObject(i1).getString("value")), 60, TimeUnit.SECONDS);
                                    }
                                    tempList.add(obj);
                                }
                            }
                            if (tempList.size() > 0) {
                                iorealDataMapper.saveOrUpdate(tempList);
                                equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                            }
                            if (realList.size() > 0) {
                                ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info(e.getMessage());
                } finally {
                    DynamicDataSourceContextHolder.poll();
                }
            }
        }
    }

    private void executeMengdou(String topic, MqttMessage message) throws Exception {
        String[] topics = topic.split("/");
//        System.out.println(topic);
//        System.out.println(new String(message.getPayload()));
        if (topics.length == 2) {
            String enterpriseCode = topics[1];
            if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
                //          手动切换数据源
                DynamicDataSourceContextHolder.push(enterpriseCode);
                try {
                    JSONObject msg = JSONObject.parseObject(new String(message.getPayload()));
                    if (msg.containsKey("Body")) {
                        String entityNo = msg.getString("GwId");
                        JSONArray array = msg.getJSONArray("Body");
//                        System.out.println(array.toJSONString());
                        if (array != null && array.size() > 0) {
                            for (int i = 0; i < array.size(); i++) {
                                List<IorealData> realList = new ArrayList<>();
                                List<IorealData> tempList = new ArrayList<>();
                                StringBuffer str = new StringBuffer("");
                                JSONObject object = array.getJSONObject(i);
                                String deviceCode = entityNo + "_" + object.getString("Module");
                                //System.out.println(object);
                                JSONObject vals = object.getJSONObject("Reg_Val");
                                if (vals != null) {
                                    for (Map.Entry entry : vals.entrySet()) {
                                        IorealData obj = new IorealData();
                                        obj.setTag(Convert.toStr(entry.getKey()));
                                        obj.setDeviceCode(deviceCode);
                                        try {
                                            final double value = (double) entry.getValue();
                                            if (value < 0) {
                                                continue;
                                            }
                                            obj.setVal(value + "");
                                        } catch (Exception e) {
                                            obj.setVal(Convert.toStr(entry.getValue()));
                                        }
                                        obj.setKey(deviceCode + "-" + Convert.toStr(entry.getKey()));
                                        str.setLength(0);
                                        str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(Convert.toStr(entry.getKey()));
//                                    System.out.println(str.toString());
                                        Object temp = redisCache.getCacheObject(str.toString());
                                        if (ObjectUtil.isNotNull(temp)) {
                                            if (!Convert.toStr(temp).equals(obj.getVal())) {
//                                        数据发生变化
                                                realList.add(obj);
                                                redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                                            }
                                        } else {
                                            realList.add(obj);
                                            redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                                        }
                                        tempList.add(obj);
                                    }
                                }

                                if (tempList.size() > 0) {
                                    iorealDataMapper.saveOrUpdate(tempList);
                                    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                                }
//                                System.out.println(JSONObject.toJSONString(realList));
                                if (realList.size() > 0) {
//                                    System.out.println(JSONObject.toJSONString(realList));
                                    ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
//                                    iorealDataMapper.saveOrUpdate(realList);
//                                    equipmentPropService.executeEquipmentFaultProp(deviceCode,realList);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info(e.getMessage());
                } finally {
                    DynamicDataSourceContextHolder.poll();
                }
            }
        }
    }

    private void executeAli(String topic, MqttMessage message) throws Exception {
        String[] topics = topic.split("/");
        String enterpriseCode = topics[1];
        String deviceCode = topics[2];
        if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
            //          手动切换数据源
            DynamicDataSourceContextHolder.push(enterpriseCode);
            try {
                JSONObject msg = JSONObject.parseObject(new String(message.getPayload())).getJSONObject("params");
                List<IorealData> tempList = new ArrayList<>();
//                msg.remove("id");
//                msg.remove("dtime");
                if (msg.size() == 0) {
                    return;
                }
//                System.out.println(msg);
                List<IorealData> realList = new ArrayList<>();
                StringBuffer str = new StringBuffer("");
                for (Map.Entry<String, Object> entry : msg.entrySet()) {
                    IorealData obj = new IorealData();
                    obj.setTag(entry.getKey());
                    obj.setDeviceCode(deviceCode);
                    try {
                        final double value = (double) entry.getValue();
                        if (value < 0) {
                            continue;
                        }
                        obj.setVal(value + "");
                    } catch (Exception e) {
                        obj.setVal(Convert.toStr(entry.getValue()));
                    }
                    obj.setKey(deviceCode + "-" + entry.getKey());
                    str.setLength(0);
                    str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(entry.getKey());
//                    System.out.println(obj);
                    Object temp = redisCache.getCacheObject(str.toString());
                    if (ObjectUtil.isNotNull(temp) && !entry.getKey().contains("OEE")) {
                        if (!Convert.toStr(temp).equals(obj.getVal())) {
//                                        数据发生变化
                            realList.add(obj);
                        }
                    } else {
                        realList.add(obj);
                        redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                    }
                    tempList.add(obj);
                }
                if (tempList.size() > 0) {
                    iorealDataMapper.saveOrUpdate(tempList);
                    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                }
                if (realList.size() > 0) {
                    ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
//                                    iorealDataMapper.saveOrUpdate(realList);
//                                    equipmentPropService.executeEquipmentFaultProp(deviceCode,realList);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void executeQimiyi(String topic, MqttMessage message) throws Exception {
        String[] topics = topic.split("/");
        String enterpriseCode = topics[1];
        String deviceCode = topics[2];
        if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
            //          手动切换数据源
            DynamicDataSourceContextHolder.push(enterpriseCode);
            try {
                JSONObject msg = JSONObject.parseObject(new String(message.getPayload())).getJSONObject("params");
                List<IorealData> tempList = new ArrayList<>();
                if (msg.size() == 0) {
                    return;
                }
                StringBuffer str = new StringBuffer("");
                str.append(enterpriseCode).append("-").append(deviceCode).append("-").append(msg.getString("id"));
                Object exist = redisCache.getCacheObject(str.toString());
                if (ObjectUtil.isNotNull(exist)) {
                    return;
                }
                redisCache.setCacheObject(str.toString(), "1", 20, TimeUnit.DAYS);
//                System.out.println(msg);
                List<IorealData> realList = new ArrayList<>();
                str.setLength(0);
                for (Map.Entry<String, Object> entry : msg.entrySet()) {
                    IorealData obj = new IorealData();
                    obj.setTag(entry.getKey());
                    obj.setDeviceCode(deviceCode);
                    obj.setVal("'" + Convert.toStr(entry.getValue()).replaceAll("[\t\n\r]", "") + "'");
                    obj.setKey(deviceCode + "-" + entry.getKey());
                    str.setLength(0);
                    str.append(enterpriseCode).append(".").append(deviceCode).append(".").append(entry.getKey());
//                    System.out.println(obj);
                    Object temp = redisCache.getCacheObject(str.toString());
                    if (ObjectUtil.isNotNull(temp) && !entry.getKey().contains("OEE")) {
                        if (!Convert.toStr(temp).equals(obj.getVal())) {
//                                        数据发生变化
                            realList.add(obj);
                        }
                    } else {
                        realList.add(obj);
                        redisCache.setCacheObject(str.toString(), Convert.toStr(entry.getValue()), 60, TimeUnit.SECONDS);
                    }
                    tempList.add(obj);
                }
                if (tempList.size() > 0) {
                    iorealDataMapper.saveOrUpdate(tempList);
                    equipmentPropService.executeEquipmentFaultProp(deviceCode, tempList);
                }
                if (realList.size() > 0) {
                    ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
//                                    iorealDataMapper.saveOrUpdate(realList);
//                                    equipmentPropService.executeEquipmentFaultProp(deviceCode,realList);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
        //log.info("消息发送成功");
    }

    public static boolean checkEnterprise(String enterpriseCode) {
        if (existList.containsKey(enterpriseCode)) {
            return true;
        } else {
            if (errorList.containsKey(enterpriseCode) && Convert.toInt(errorList.get(enterpriseCode)) < 100) {
//                重新获取企业信息
                errorList.put(enterpriseCode, Convert.toStr(Convert.toInt(errorList.get(enterpriseCode)) + 1));
                return false;
            } else {
                DynamicDataSourceContextHolder.push("master");
                QueryWrapper<TSysEnterprise> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("enterprise_openid", enterpriseCode);
                TSysEnterprise enterprise = sysEnterpriseService.getOne(queryWrapper);
                if (ObjectUtil.isNotNull(enterprise) && enterprise.getEnterpriseInit().equals("1")) {
                    existList.put(enterpriseCode, "1");
                    return true;
                } else {
                    errorList.put(enterpriseCode, "1");
                    return false;
                }
            }
        }
    }

   static LinkedHashMap<String, String> existList = new LinkedHashMap<String, String>() {
        private static final long serialVersionUID = 1L;

        @Override
        protected boolean removeEldestEntry(Map.Entry<String, String> eldest) {
            return size() > 200;
        }
    };

   static LinkedHashMap<String, String> errorList = new LinkedHashMap<String, String>() {
        private static final long serialVersionUID = 1L;

        @Override
        protected boolean removeEldestEntry(Map.Entry<String, String> eldest) {
            return size() > 200;
        }
    };
}
