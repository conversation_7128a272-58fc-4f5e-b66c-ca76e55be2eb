<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.eam.mapper.EquipMaintTaskMapper">

    <resultMap type="com.boyo.eam.domain.EquipMaintTask" id="EquipMaintTaskResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="planCode" column="plan_code" />
        <result property="date" column="date" />
        <result property="equipLedgerOpenid" column="equip_ledger_openid" />
        <result property="equipMaintTemplOpenid" column="equip_maint_templ_openid" />
        <result property="sysUserId" column="sys_user_id" />
        <result property="cycle" column="cycle" />
        <result property="day" column="day" />
        <result property="state" column="state" />
        <result property="taskCode" column="task_code" />
        <result property="taskCode" column="task_code" />
        <result property="taskCode" column="task_code" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectEquipMaintTaskList" parameterType="com.boyo.eam.domain.EquipMaintTask" resultMap="EquipMaintTaskResult">
        select
            EMT.*,TML.line_name as lineName,EL.name as equipName,EL.code as equipCode
        from
        equip_maint_task EMT left join
        equip_ledger EL on EMT.equip_ledger_openid=EL.openid left join
        t_model_line TML on EMT.line_openid=TML.line_openid
        <where>
            <if test="openid != null and openid != ''">
                and EMT.openid = #{openid}
            </if>
            <if test="planCode != null and planCode != ''">
                and EMT.plan_code = #{planCode}
            </if>
            <if test="date != null">
                and EMT.date = #{date}
            </if>
            <if test="equipLedgerOpenid != null and equipLedgerOpenid != ''">
                and EMT.equip_ledger_openid like CONCAT('%',#{equipLedgerOpenid},'%')
            </if>
            <if test="equipMaintTemplOpenid != null and equipMaintTemplOpenid != ''">
                and EMT.equip_maint_templ_openid = #{equipMaintTemplOpenid}
            </if>
            <if test="sysUserId != null">
                and EMT.sys_user_id like CONCAT('%',#{sysUserId},'%')
            </if>
            <if test="cycle != null and cycle != ''">
                and EMT.cycle = #{cycle}
            </if>
            <if test="day != null">
                and EMT.day = #{day}
            </if>
            <if test="state != null and state != ''">
                and find_in_set(EMT.state,#{state})
            </if>
            <if test="taskCode != null and taskCode != ''">
                and EMT.task_code = #{taskCode}
            </if>
            <if test="lineOpenid != null and lineOpenid != ''">
                and EMT.line_openid = #{lineOpenid}
            </if>
            <if test="createBy != null and createBy != ''">
                and EMT.create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and EMT.create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and EMT.update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
            and EMT.update_time = #{updateTime}
        </if>
            <if test="beginDate!=null and endDate!=null">
                and #{beginDate} &lt;= EMT.date
                and EMT.date &lt;= #{endDate}
            </if>
            <if test="taskContent!=null and taskContent!=''">
                and CONCAT(EL.name,EL.code,EMT.task_code) like CONCAT('%',#{taskContent},'%')
            </if>
        </where>
    </select>
</mapper>

