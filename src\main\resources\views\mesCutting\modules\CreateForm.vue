<template>
  <a-modal width="30%" :maskClosable="false" :visible="open" @cancel="cancel">
    <template #title>
      <a-icon type="security-scan" />
      {{ formTitle }}
    </template>
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      layout="horizontal"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
           <a-form-model-item label="订单id">
        <a-input
          :size="formSize"
          v-model="form.orderId"
          :placeholder="$t('app.global.please.input') + '订单id'"
        />
      </a-form-model-item>
           <a-form-model-item label="下料长度">
        <a-input
          :size="formSize"
          v-model="form.allLength"
          :placeholder="$t('app.global.please.input') + '下料长度'"
        />
      </a-form-model-item>
           <a-form-model-item label="切割长度">
        <a-input
          :size="formSize"
          v-model="form.cuttingLength"
          :placeholder="$t('app.global.please.input') + '切割长度'"
        />
      </a-form-model-item>
           <a-form-model-item label="数量">
        <a-input
          :size="formSize"
          v-model="form.cuttingCount"
          :placeholder="$t('app.global.please.input') + '数量'"
        />
      </a-form-model-item>
           <a-form-model-item label="余量">
        <a-input
          :size="formSize"
          v-model="form.surplus"
          :placeholder="$t('app.global.please.input') + '余量'"
        />
      </a-form-model-item>
           <a-form-model-item label="">
        <a-input
          :size="formSize"
          v-model="form.createTime"
          :placeholder="$t('app.global.please.input') + ''"
        />
      </a-form-model-item>
           <a-form-model-item label="">
        <a-input
          :size="formSize"
          v-model="form.createBy"
          :placeholder="$t('app.global.please.input') + ''"
        />
      </a-form-model-item>
        </a-form-model>
    <template #footer>
      <a-space>
        <a-button :size="formSize" icon="close" type="danger" @click="cancel">
          {{ $t('app.global.close') }}
        </a-button>
        <a-button :size="formSize" icon="save" type="primary" @click="submitForm">
          {{ $t('app.global.save') }}
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script>
import { addMesCutting, updateMesCutting, getMesCutting } from '@/api/mesCutting'
export default {
  data() {
    return {
      //新增或修改
      updateState: false,
      formTitle: '$tableInfo.comment',
      // 表单参数
      form: {
           id: '',
           orderId: '',
           allLength: '',
           cuttingLength: '',
           cuttingCount: '',
           surplus: '',
           createTime: '',
           createBy: '',
             },
      open: false,
      rules: {},
    }
  },
  created() {
    this.rules = {
    }
  },
  methods: {
    /**
     * 新增按钮操作
     * */
    handleAdd() {
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.add') + '$tableInfo.comment'
      this.form = {}
      this.updateState = false
    },
    /**
     * 修改按钮操作
     * */
    async handleUpdate($event, id) {
      $event.stopPropagation()
      this.reset()
      this.open = true
      this.formTitle = this.$t('app.global.edit') + '$tableInfo.comment'
      const response = await getMesCutting(id)
      this.form = response.data
    },
    /**
     * 提交按钮
     * */
    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
            if (this.form.id) {
                await updateMesCutting(this.form)
                this.$alert.success(this.$t('app.global.edit.success'))
                this.open = false
                this.$emit('ok')
            } else {
                await addMesCutting(this.form)
                this.$alert.success(this.$t('app.global.add.success'))
                this.open = false
                this.$emit('ok')
            }
        } else {
            return false
        }
        })
    },
    /**
     * 取消按钮
     * */
    cancel() {
      this.open = false
      this.reset()
    },
    /**
     * 表单重置
     * */
    reset() {
      this.form = {}
      if (this.$refs.form) {
        this.$refs.form.resetFields()}
      }
    },
}
</script>

