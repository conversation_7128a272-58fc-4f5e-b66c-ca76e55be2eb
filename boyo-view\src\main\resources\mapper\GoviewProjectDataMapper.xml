<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.view.mapper.GoviewProjectDataMapper">

    <resultMap type="com.boyo.view.entity.GoviewProjectData" id="GoviewProjectDataResult">
        <result property="id" column="id" />
        <result property="projectId" column="project_id" />
        <result property="createTime" column="create_time" />
        <result property="createUserId" column="create_user_id" />
        <result property="content" column="content" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectGoviewProjectDataList" parameterType="com.boyo.view.entity.GoviewProjectData" resultMap="GoviewProjectDataResult">
        select
          id, project_id, create_time, create_user_id, content
        from t_goview_project_data
        <where>
            <if test="projectId != null and projectId != ''">
                and project_id = #{projectId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUserId != null and createUserId != ''">
                and create_user_id = #{createUserId}
            </if>
            <if test="content != null and content != ''">
                and content = #{content}
            </if>
        </where>
    </select>
</mapper>

