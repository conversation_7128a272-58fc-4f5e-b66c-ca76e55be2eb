package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipWorkshopSection;

import java.util.List;

/**
 * 工段表(EquipWorkshopSection)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-21 15:23:24
 */
public interface IEquipWorkshopSectionService extends IService<EquipWorkshopSection> {

    /**
     * 查询多条数据
     *
     * @param equipWorkshopSection 对象信息
     * @return 对象列表
     */
    List<EquipWorkshopSection> selectEquipWorkshopSectionList(EquipWorkshopSection equipWorkshopSection);


}
