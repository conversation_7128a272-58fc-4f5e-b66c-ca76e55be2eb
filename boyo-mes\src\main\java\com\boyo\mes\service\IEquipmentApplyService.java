package com.boyo.mes.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.mes.entity.EquipmentApply;
import java.util.List;

/**
 * 上下班记录(EquipmentApply)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-03 16:47:21
 */
public interface IEquipmentApplyService extends IService<EquipmentApply> {

    /**
     * 查询多条数据
     *
     * @param equipmentApply 对象信息
     * @return 对象列表
     */
    List<EquipmentApply> selectEquipmentApplyList(EquipmentApply equipmentApply);

    EquipmentApply scanApply(String openid);

    List<IotEquipment> listUsedEquipment();


}
