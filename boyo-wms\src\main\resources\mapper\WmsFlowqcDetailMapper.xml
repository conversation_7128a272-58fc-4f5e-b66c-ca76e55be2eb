<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.wms.mapper.WmsFlowqcDetailMapper">

    <resultMap type="com.boyo.wms.entity.WmsFlowqcDetail" id="WmsFlowqcDetailResult">
        <result property="id" column="id" />
        <result property="pId" column="p_id" />
        <result property="itemName" column="item_name" />
        <result property="itemCode" column="item_code" />
        <result property="itemResult" column="item_result" />
    </resultMap>

    <!--通过实体作为筛选条件查询-->
    <select id="selectWmsFlowqcDetailList" parameterType="com.boyo.wms.entity.WmsFlowqcDetail" resultMap="WmsFlowqcDetailResult">
        select
          id, p_id, item_name, item_code, item_result
        from t_wms_flowqc_detail
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="pId != null">
                and p_id = #{pId}
            </if>
            <if test="itemName != null and itemName != ''">
                and item_name = #{itemName}
            </if>
            <if test="itemCode != null and itemCode != ''">
                and item_code = #{itemCode}
            </if>
            <if test="itemResult != null and itemResult != ''">
                and item_result = #{itemResult}
            </if>
        </where>
    </select>
</mapper>

