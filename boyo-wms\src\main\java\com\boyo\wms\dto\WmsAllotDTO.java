package com.boyo.wms.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 物料调拨
 * <AUTHOR>
 */
@Data
public class WmsAllotDTO {

    /**
     * 库存记录id
     */
    private Integer stockId;

    /**
     * 物料openid
     */
    private String materielOpenid;

    /**
     * 批次号
     */
    private String materielBatch;

    /**
     * 仓库openid
     */
    private String warehouseOpenid;

    /**
     * 区域openid
     */
    private String areaOpenid;

    /**
     * 货位openid
     */
    private String allocationOpenid;

    /**
     * 目标仓库openid
     */
    private String aimWarehouseOpenid;

    /**
     * 目标区域openid
     */
    private String aimAreaOpenid;

    /**
     * 目标货位openid
     */
    private String aimAllocationOpenid;

    /**
     * 调拨数量
     */
    private BigDecimal quantity;
}
