<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.ModelWorkshopMapper">

    <resultMap type="com.boyo.master.domain.ModelWorkshop" id="ModelWorkshopResult">
        <result property="id" column="id"/>
        <result property="workshopOpenid" column="workshop_openid"/>
        <result property="workshopFactory" column="workshop_factory"/>
        <result property="workshopName" column="workshop_name"/>
        <result property="workshopAbbreviation" column="workshop_abbreviation"/>
        <result property="workshopCode" column="workshop_code"/>
        <result property="workshopStatus" column="workshop_status"/>
        <result property="workshopImg" column="workshop_img"/>
        <result property="workshopContacts" column="workshop_contacts"/>
        <result property="workshopPhone" column="workshop_phone"/>
        <result property="createdAt" column="created_at"/>
        <result property="createdUser" column="created_user"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="updatedUser" column="updated_user"/>
        <result property="remark" column="remark"/>
        <result property="factoryName" column="factoryName"></result>
    </resultMap>

    <select id="selectModelWorkshopList" parameterType="com.boyo.master.domain.ModelWorkshop"
            resultMap="ModelWorkshopResult">
        select t1.*,t2.factory_name as factoryName
        from t_model_workshop t1 left join t_model_factory t2
        on t1.workshop_factory = t2.factory_openid
        <where>
            <if test="workshopOpenid != null  and workshopOpenid != ''">
                and t1.workshop_openid = #{workshopOpenid}
            </if>
            <if test="workshopFactory != null  and workshopFactory != ''">
                and t1.workshop_factory = #{workshopFactory}
            </if>
            <if test="workshopName != null  and workshopName != ''">
                and t1.workshop_name like concat('%', #{workshopName}, '%')
            </if>
            <if test="workshopAbbreviation != null  and workshopAbbreviation != ''">
                and t1.workshop_abbreviation like concat('%', #{workshopAbbreviation}, '%')
            </if>
            <if test="workshopCode != null  and workshopCode != ''">
                and t1.workshop_code = #{workshopCode}
            </if>
            <if test="workshopStatus != null  and workshopStatus != ''">
                and t1.workshop_status = #{workshopStatus}
            </if>
            <if test="workshopImg != null  and workshopImg != ''">
                and t1.workshop_img = #{workshopImg}
            </if>
        </where>
    </select>
</mapper>
