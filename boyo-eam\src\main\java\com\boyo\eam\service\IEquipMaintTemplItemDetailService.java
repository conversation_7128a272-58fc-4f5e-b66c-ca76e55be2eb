package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipMaintTemplItemDetail;

import java.util.List;

/**
 * 设备-维保模板-维保明细(EquipMaintTemplItemDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:31
 */
public interface IEquipMaintTemplItemDetailService extends IService<EquipMaintTemplItemDetail> {

    /**
     * 查询多条数据
     *
     * @param equipMaintTemplItemDetail 对象信息
     * @return 对象列表
     */
    List<EquipMaintTemplItemDetail> selectEquipMaintTemplItemDetailList(EquipMaintTemplItemDetail equipMaintTemplItemDetail);


}
